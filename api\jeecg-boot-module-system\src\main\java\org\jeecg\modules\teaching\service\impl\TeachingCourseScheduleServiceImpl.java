package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;

import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.teaching.entity.TeachingClassroom;
import org.jeecg.modules.teaching.entity.TeachingCourse;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.mapper.TeachingCourseScheduleMapper;
import org.jeecg.modules.teaching.service.ITeachingClassroomService;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleService;
import org.jeecg.modules.teaching.service.ITeachingCourseService;
import org.jeecg.modules.teaching.vo.CourseScheduleExcelVO;
import org.jeecg.modules.teaching.vo.CourseScheduleVO;
import org.jeecgframework.poi.excel.ExcelExportUtil;

import org.jeecgframework.poi.excel.entity.ExportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

/**
 * @Description: 课程排期
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
@Slf4j
public class TeachingCourseScheduleServiceImpl extends ServiceImpl<TeachingCourseScheduleMapper, TeachingCourseSchedule> implements ITeachingCourseScheduleService {

    @Autowired
    private ISysDepartService sysDepartService;
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private ITeachingCourseService teachingCourseService;
    
    @Autowired
    private ITeachingClassroomService teachingClassroomService;
    
    @Autowired
    private ISysUserDepartService sysUserDepartService;
    
    @Override
    public IPage<CourseScheduleVO> getScheduleList(Page<CourseScheduleVO> page, QueryWrapper<TeachingCourseSchedule> queryWrapper) {
        List<CourseScheduleVO> list = baseMapper.getScheduleList(page, queryWrapper);
        page.setRecords(list);
        return page;
    }
    
    @Override
    public Result<Object> checkConflict(TeachingCourseSchedule schedule) {
        Map<String, Object> result = new HashMap<>();
        Integer count = baseMapper.checkConflict(schedule);
        
        if (count > 0) {
            result.put("conflict", true);
            
            // 查询冲突详情
            String message = "";
            List<String> conflictDetails = new ArrayList<>();
            
            // 查询班级冲突
            if (schedule.getClassId() != null) {
                Integer classConflictCount = baseMapper.checkClassConflict(schedule);
                if (classConflictCount > 0) {
                String deptName = sysDepartService.getById(schedule.getClassId()).getDepartName();
                    conflictDetails.add("班级「" + deptName + "」已有其他课程安排");
                }
            }
            
            // 查询教师冲突
            if (schedule.getTeacherId() != null) {
                Integer teacherConflictCount = baseMapper.checkTeacherConflict(schedule);
                if (teacherConflictCount > 0) {
                    String teacherName = sysUserService.getById(schedule.getTeacherId()).getRealname();
                    conflictDetails.add("教师「" + teacherName + "」已有其他课程安排");
                }
            }
            
            // 查询教室冲突
            if (schedule.getClassroomId() != null) {
                Integer roomConflictCount = baseMapper.checkRoomConflict(schedule);
                if (roomConflictCount > 0) {
                    String roomName = teachingClassroomService.getById(schedule.getClassroomId()).getClassroomName();
                    conflictDetails.add("教室「" + roomName + "」已被占用");
                }
            }
            
            // 如果没有详细冲突信息（可能是因为没有实现详细检查），则提供通用信息
            if (conflictDetails.isEmpty()) {
                message += "当前时间段已有相关课程安排，请调整时间或检查资源冲突";
            } else {
                message += String.join("；", conflictDetails);
            }
            
            result.put("message", message);
        } else {
            result.put("conflict", false);
        }
        
        return Result.ok(result);
    }
    
    @Override
    @Transactional
    public Result<Object> importScheduleList(List<CourseScheduleExcelVO> excelVOList) {
        if (excelVOList == null || excelVOList.isEmpty()) {
            return Result.error("导入数据为空");
        }
        
        // 存储导入结果
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        // 批量获取关联数据
        Map<String, SysDepart> departCache = new HashMap<>();
        Map<String, TeachingCourse> courseCache = new HashMap<>();
        Map<String, SysUser> userCache = new HashMap<>();
        Map<String, TeachingClassroom> roomCache = new HashMap<>();
        
        // 处理每一行数据
        for (int i = 0; i < excelVOList.size(); i++) {
            CourseScheduleExcelVO excelVO = excelVOList.get(i);
            try {
                // 数据校验
                if (excelVO.getStartTime() == null || excelVO.getEndTime() == null) {
                    errorMessages.add("第" + (i + 2) + "行：开始时间或结束时间不能为空");
                    failCount++;
                    continue;
                }
                
                if (excelVO.getStartTime().after(excelVO.getEndTime())) {
                    errorMessages.add("第" + (i + 2) + "行：开始时间不能晚于结束时间");
                    failCount++;
                    continue;
                }
                
                // 查找班级ID
                String classId = null;
                if (StringUtils.isNotBlank(excelVO.getDeptName())) {
                    // 先从缓存查找
                    SysDepart depart = departCache.get(excelVO.getDeptName());
                    if (depart == null) {
                        // 从数据库查找
                        QueryWrapper<SysDepart> departQuery = new QueryWrapper<>();
                        departQuery.eq("depart_name", excelVO.getDeptName());
                        departQuery.last("limit 1");
                        depart = sysDepartService.getOne(departQuery);
                        
                        if (depart != null) {
                            departCache.put(excelVO.getDeptName(), depart);
                        } else {
                            errorMessages.add("第" + (i + 2) + "行：找不到名为'" + excelVO.getDeptName() + "'的班级");
                            failCount++;
                            continue;
                        }
                    }
                    classId = depart.getId();
                } else {
                    errorMessages.add("第" + (i + 2) + "行：班级名称不能为空");
                    failCount++;
                    continue;
                }
                
                // 查找课程ID
                String courseId = null;
                if (StringUtils.isNotBlank(excelVO.getCourseName())) {
                    // 先从缓存查找
                    TeachingCourse course = courseCache.get(excelVO.getCourseName());
                    if (course == null) {
                        // 从数据库查找
                        QueryWrapper<TeachingCourse> courseQuery = new QueryWrapper<>();
                        courseQuery.eq("course_name", excelVO.getCourseName());
                        courseQuery.last("limit 1");
                        course = teachingCourseService.getOne(courseQuery);
                        
                        if (course != null) {
                            courseCache.put(excelVO.getCourseName(), course);
                        } else {
                            errorMessages.add("第" + (i + 2) + "行：找不到名为'" + excelVO.getCourseName() + "'的课程");
                            failCount++;
                            continue;
                        }
                    }
                    courseId = course.getId();
                } else {
                    errorMessages.add("第" + (i + 2) + "行：课程名称不能为空");
                    failCount++;
                    continue;
                }
                
                // 查找教师ID
                String teacherId = null;
                if (StringUtils.isNotBlank(excelVO.getTeacherName())) {
                    // 先从缓存查找
                    SysUser teacher = userCache.get(excelVO.getTeacherName());
                    if (teacher == null) {
                        // 从数据库查找
                        QueryWrapper<SysUser> teacherQuery = new QueryWrapper<>();
                        teacherQuery.eq("realname", excelVO.getTeacherName());
                        teacherQuery.last("limit 1");
                        teacher = sysUserService.getOne(teacherQuery);
                        
                        if (teacher != null) {
                            userCache.put(excelVO.getTeacherName(), teacher);
                        } else {
                            errorMessages.add("第" + (i + 2) + "行：找不到名为'" + excelVO.getTeacherName() + "'的教师");
                            failCount++;
                            continue;
                        }
                    }
                    teacherId = teacher.getId();
                } else {
                    errorMessages.add("第" + (i + 2) + "行：教师姓名不能为空");
                    failCount++;
                    continue;
                }
                
                // 查找教室ID
                String classroomId = null;
                if (StringUtils.isNotBlank(excelVO.getRoomName())) {
                    // 先从缓存查找
                    TeachingClassroom room = roomCache.get(excelVO.getRoomName());
                    if (room == null) {
                        // 从数据库查找
                        QueryWrapper<TeachingClassroom> roomQuery = new QueryWrapper<>();
                        roomQuery.eq("classroom_name", excelVO.getRoomName());
                        roomQuery.last("limit 1");
                        room = teachingClassroomService.getOne(roomQuery);
                        
                        if (room != null) {
                            roomCache.put(excelVO.getRoomName(), room);
                        } else {
                            errorMessages.add("第" + (i + 2) + "行：找不到名为'" + excelVO.getRoomName() + "'的教室");
                            failCount++;
                            continue;
                        }
                    }
                    classroomId = room.getId();
                } else {
                    errorMessages.add("第" + (i + 2) + "行：教室名称不能为空");
                    failCount++;
                    continue;
                }
                
                // 创建排课对象
                TeachingCourseSchedule schedule = new TeachingCourseSchedule();
                schedule.setScheduleTitle(excelVO.getScheduleTitle());
                schedule.setClassId(classId);
                schedule.setCourseId(courseId);
                schedule.setTeacherId(teacherId);
                schedule.setClassroomId(classroomId);
                schedule.setStartTime(excelVO.getStartTime());
                schedule.setEndTime(excelVO.getEndTime());
                schedule.setRepeatType(excelVO.getRepeatType());
                schedule.setRepeatEndDate(excelVO.getRepeatEndDate());
                schedule.setWeekdays(excelVO.getWeekdays());
                schedule.setDescription(excelVO.getDescription());
                
                // 检查时间冲突
                Result<Object> checkResult = checkConflict(schedule);
                if (checkResult.isSuccess()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) checkResult.getResult();
                    boolean hasConflict = (boolean) data.get("conflict");
                    
                    if (hasConflict) {
                        errorMessages.add("第" + (i + 2) + "行：" + data.get("message"));
                        failCount++;
                        continue;
                    }
                }
                
                // 保存排课
                this.save(schedule);
                successCount++;
            } catch (Exception e) {
                log.error("导入第{}行数据失败", i + 2, e);
                errorMessages.add("第" + (i + 2) + "行：导入错误 - " + e.getMessage());
                failCount++;
            }
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages);
        
        if (failCount > 0) {
            return Result.ok("成功导入" + successCount + "条数据，失败" + failCount + "条");
        } else {
            return Result.ok("成功导入" + successCount + "条数据");
        }
    }
    
    @Override
    public String generateTemplate() {
        // 创建Excel模板
        List<CourseScheduleExcelVO> list = new ArrayList<>();
        CourseScheduleExcelVO template = new CourseScheduleExcelVO();
        template.setScheduleTitle("示例课程");
        template.setDeptName("示例班级");
        template.setCourseName("示例课程");
        template.setTeacherName("示例教师");
        template.setRoomName("示例教室");
        template.setStartTime(new Date());
        template.setEndTime(new Date());
        template.setRepeatType(0);
        template.setWeekdays("周一,周三,周五");
        template.setDescription("示例备注");
        list.add(template);
        
        try {
            // 生成Excel文件
            String filename = "课程排期导入模板_" + System.currentTimeMillis() + ".xlsx";
            String savePath = "upload" + File.separator + filename;
            File file = new File(savePath);
            
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            
            FileOutputStream fos = new FileOutputStream(file);
            org.apache.poi.ss.usermodel.Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("课程排期导入模板", "模板"), CourseScheduleExcelVO.class, list);
            workbook.write(fos);
            fos.close();
            
            return savePath;
        } catch (Exception e) {
            log.error("生成Excel模板失败", e);
            return null;
        }
    }
    
    /**
     * 判断用户是否是课程排期的教师
     * @param scheduleId 课程排期ID
     * @param teacherId 教师ID
     * @return 如果是课程排期的教师返回true，否则返回false
     */
    @Override
    public boolean isTeacherOfSchedule(String scheduleId, String teacherId) {
        log.info("检查教师权限: scheduleId={}, teacherId={}", scheduleId, teacherId);
        
        if (scheduleId == null || teacherId == null) {
            log.warn("scheduleId或teacherId为空，权限检查失败");
            return false;
        }
        
        TeachingCourseSchedule schedule = this.getById(scheduleId);
        if (schedule == null) {
            log.warn("未找到ID为{}的课程排期", scheduleId);
            return false;
        }
        
        boolean isTeacher = teacherId.equals(schedule.getTeacherId());
        log.info("课程排期教师ID: {}, 当前教师ID: {}, 权限检查结果: {}", 
                 schedule.getTeacherId(), teacherId, isTeacher);
        
        return isTeacher;
    }
    
    /**
     * 判断学生是否与课程排期相关
     * @param scheduleId 课程排期ID
     * @param studentId 学生ID
     * @return 如果学生与课程排期相关返回true，否则返回false
     */
    @Override
    public boolean isStudentRelatedToSchedule(String scheduleId, String studentId) {
        if (scheduleId == null || studentId == null) {
            return false;
        }
        
        // 获取课程排期信息
        TeachingCourseSchedule schedule = this.getById(scheduleId);
        if (schedule == null) {
            return false;
        }
        
        // 获取学生所在的班级
        QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", studentId);
        List<SysUserDepart> userDepartList = sysUserDepartService.list(queryWrapper);
        
        if (userDepartList == null || userDepartList.isEmpty()) {
            return false;
        }
        
        // 检查学生是否在此课程排期的班级中
        for (SysUserDepart userDepart : userDepartList) {
            if (userDepart.getDepId().equals(schedule.getClassId())) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public Map<String, List<String>> getDeletedInstances() {
        // 从数据库获取所有课程排期
        List<TeachingCourseSchedule> scheduleList = this.list(new QueryWrapper<TeachingCourseSchedule>()
            .isNotNull("deleted_instances")
            .ne("deleted_instances", ""));
        
        Map<String, List<String>> result = new HashMap<>();
        
        // 处理每个课程排期的删除实例记录
        for (TeachingCourseSchedule schedule : scheduleList) {
            if (StringUtils.isNotBlank(schedule.getDeletedInstances())) {
                List<String> dateList = Arrays.asList(schedule.getDeletedInstances().split(","));
                result.put(schedule.getId(), dateList);
            }
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public boolean deleteSingleInstance(String parentId, String instanceDate) {
        // 参数验证
        if (StringUtils.isBlank(parentId) || StringUtils.isBlank(instanceDate)) {
            return false;
        }
        
        // 获取父课程记录
        TeachingCourseSchedule parentSchedule = this.getById(parentId);
        if (parentSchedule == null) {
            return false;
        }
        
        // 处理删除记录
        String deletedInstances = parentSchedule.getDeletedInstances();
        
        // 初始化删除实例列表
        List<String> deletedList = new ArrayList<>();
        
        // 如果已有删除记录，解析现有记录
        if (StringUtils.isNotBlank(deletedInstances)) {
            deletedList = new ArrayList<>(Arrays.asList(deletedInstances.split(",")));
        }
        
        // 如果日期记录不在列表中，添加到列表
        if (!deletedList.contains(instanceDate)) {
            deletedList.add(instanceDate);
            
            // 更新到数据库
            parentSchedule.setDeletedInstances(String.join(",", deletedList));
            return this.updateById(parentSchedule);
        }
        
        // 日期已在删除列表中，无需操作
        return true;
    }

    @Override
    public List<TeachingCourseSchedule> findUpcomingCoursesWithoutReminder(Date startTime, Date endTime) {
        // 构建查询条件
        QueryWrapper<TeachingCourseSchedule> queryWrapper = new QueryWrapper<>();
        // 课程开始时间在指定范围内
        queryWrapper.ge("start_time", startTime);
        queryWrapper.le("start_time", endTime);
        // 课程状态为正常
        queryWrapper.eq("status", 1);
        // 未创建提醒通知或提醒通知标记为空
        queryWrapper.and(wrapper -> wrapper.eq("reminder_created", 0).or().isNull("reminder_created"));
        
        return this.list(queryWrapper);
    }

    @Override
    public boolean markCourseReminderCreated(String courseId) {
        if (StringUtils.isBlank(courseId)) {
            return false;
        }
        
        TeachingCourseSchedule schedule = this.getById(courseId);
        if (schedule == null) {
            return false;
        }
        
        // 更新提醒创建标记
        schedule.setReminderCreated(1);
        return this.updateById(schedule);
    }
} 