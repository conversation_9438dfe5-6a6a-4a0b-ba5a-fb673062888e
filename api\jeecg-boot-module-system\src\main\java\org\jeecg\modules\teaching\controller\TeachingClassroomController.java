package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.teaching.entity.TeachingClassroom;
import org.jeecg.modules.teaching.service.ITeachingClassroomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 教室
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Api(tags="教室")
@RestController
@RequestMapping("/teaching/classroom")
@Slf4j
public class TeachingClassroomController extends JeecgController<TeachingClassroom, ITeachingClassroomService> {
    @Autowired
    private ITeachingClassroomService teachingClassroomService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "教室-分页列表查询")
    @ApiOperation(value="教室-分页列表查询", notes="教室-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingClassroom teachingClassroom,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        log.info("查询教室列表请求参数: pageNo={}, pageSize={}, 查询条件={}", pageNo, pageSize, teachingClassroom);
        
        QueryWrapper<TeachingClassroom> queryWrapper = QueryGenerator.initQueryWrapper(teachingClassroom, req.getParameterMap());
        Page<TeachingClassroom> page = new Page<TeachingClassroom>(pageNo, pageSize);
        IPage<TeachingClassroom> pageList = teachingClassroomService.page(page, queryWrapper);
        
        if (pageList.getRecords() == null || pageList.getRecords().isEmpty()) {
            log.warn("条件查询未找到教室数据，尝试不带条件查询所有教室");
            QueryWrapper<TeachingClassroom> emptyWrapper = new QueryWrapper<>();
            pageList = teachingClassroomService.page(page, emptyWrapper);
        }
        
        log.info("查询教室列表结果: 总记录数={}, 当前页数据条数={}", 
                pageList.getTotal(), 
                pageList.getRecords() != null ? pageList.getRecords().size() : 0);
        
        return Result.ok(pageList);
    }
    
    /**
     * 获取所有教室（不分页）
     */
    @AutoLog(value = "教室-获取所有教室")
    @ApiOperation(value="教室-获取所有教室", notes="教室-获取所有教室")
    @GetMapping(value = "/listAll")
    public Result<?> listAll() {
        log.info("查询所有教室");
        List<TeachingClassroom> list = teachingClassroomService.list();
        log.info("查询所有教室结果: 记录数={}", list != null ? list.size() : 0);
        return Result.ok(list);
    }
    
    /**
     * 获取所有可用教室
     */
    @AutoLog(value = "教室-获取所有可用教室")
    @ApiOperation(value="教室-获取所有可用教室", notes="教室-获取所有可用教室")
    @GetMapping(value = "/listAvailable")
    public Result<?> listAvailable() {
        List<TeachingClassroom> list = teachingClassroomService.getAvailableClassrooms();
        return Result.ok(list);
    }
    
    /**
     * 更新教室状态
     */
    @AutoLog(value = "教室-更新教室状态")
    @ApiOperation(value="教室-更新教室状态", notes="教室-更新教室状态")
    @PutMapping(value = "/updateStatus")
    public Result<?> updateStatus(@RequestParam(name="id") String id, 
                                 @RequestParam(name="status") Integer status) {
        boolean success = teachingClassroomService.updateClassroomStatus(id, status);
        if (success) {
            return Result.ok("状态更新成功");
        } else {
            return Result.error("状态更新失败");
        }
    }
    
    /**
     * 添加
     */
    @AutoLog(value = "教室-添加")
    @ApiOperation(value="教室-添加", notes="教室-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TeachingClassroom teachingClassroom) {
        log.info("添加教室: {}", teachingClassroom);
        teachingClassroomService.save(teachingClassroom);
        return Result.ok("添加成功！");
    }
    
    /**
     * 编辑
     */
    @AutoLog(value = "教室-编辑")
    @ApiOperation(value="教室-编辑", notes="教室-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody TeachingClassroom teachingClassroom) {
        teachingClassroomService.updateById(teachingClassroom);
        return Result.ok("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @AutoLog(value = "教室-通过id删除")
    @ApiOperation(value="教室-通过id删除", notes="教室-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        teachingClassroomService.removeById(id);
        return Result.ok("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @AutoLog(value = "教室-批量删除")
    @ApiOperation(value="教室-批量删除", notes="教室-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.teachingClassroomService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }
} 