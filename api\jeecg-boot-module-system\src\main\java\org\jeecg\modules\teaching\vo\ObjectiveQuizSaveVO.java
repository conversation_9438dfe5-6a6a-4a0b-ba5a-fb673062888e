package org.jeecg.modules.teaching.vo;

import lombok.Data;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizRecord;

import java.io.Serializable;
import java.util.List;

/**
 * 客观题答题记录保存VO
 */
@Data
public class ObjectiveQuizSaveVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 答题记录
     */
    private TeachingObjectiveQuizRecord record;
    
    /**
     * 答题详情列表
     */
    private List<TeachingObjectiveQuizDetail> detailList;
} 
 