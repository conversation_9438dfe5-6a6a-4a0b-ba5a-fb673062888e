package org.jeecg.modules.teaching.service;

import org.jeecg.modules.teaching.dto.TestJudgeRequestDTO;

import java.util.Map;

/**
 * 判题服务接口
 */
public interface ITeachingJudgeService {
    
    /**
     * 提交测试判题
     * @param request 测试判题请求
     * @return 判题结果
     */
    Map<String, Object> testJudge(TestJudgeRequestDTO request);
    
    /**
     * 提交代码判题
     * @param request 判题请求
     * @return 判题结果，包含提交ID
     */
    Map<String, Object> submitTestJudge(TestJudgeRequestDTO request);
    
    /**
     * 获取判题结果
     * @param submissionId 提交ID
     * @return 判题结果
     */
    Map<String, Object> getTestJudgeResult(String submissionId);
    
    /**
     * 获取在线自测的判题结果
     * @param testJudgeKey 判题任务key
     * @return 判题结果
     */
    Map<String, Object> getSelfTestJudgeResult(String testJudgeKey);
} 
 