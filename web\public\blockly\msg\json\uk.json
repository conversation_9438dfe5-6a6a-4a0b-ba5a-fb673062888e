{"@metadata": {"authors": ["Andriykopanytsia", "Base", "Gzhegozh", "<PERSON>", "Lxlalexlxl", "<PERSON><PERSON><PERSON>", "Piramidion", "SimondR", "<PERSON><PERSON><PERSON>", "Ата", "아라"]}, "VARIABLES_DEFAULT_NAME": "елемент", "UNNAMED_KEY": "без назви", "TODAY": "Сьогодні", "DUPLICATE_BLOCK": "Дублювати", "ADD_COMMENT": "Додати коментар", "REMOVE_COMMENT": "Видалити коментар", "DUPLICATE_COMMENT": "Дублювати примітку", "EXTERNAL_INPUTS": "Зовнішні входи", "INLINE_INPUTS": "Вбудовані входи", "DELETE_BLOCK": "Видалити блок", "DELETE_X_BLOCKS": "Видалити %1 блоків", "DELETE_ALL_BLOCKS": "Вилучити всі блоки %1?", "CLEAN_UP": "Очистити блоки", "COLLAPSE_BLOCK": "Згорнути блок", "COLLAPSE_ALL": "Згорнути блоки", "EXPAND_BLOCK": "Розгорнути блок", "EXPAND_ALL": "Розгорнути блоки", "DISABLE_BLOCK": "Вимкнути блок", "ENABLE_BLOCK": "Увімкнути блок", "HELP": "Довідка", "UNDO": "Скасувати", "REDO": "Повторити", "CHANGE_VALUE_TITLE": "Змінити значення:", "RENAME_VARIABLE": "Перейменувати змінну...", "RENAME_VARIABLE_TITLE": "Перейменувати усі змінні \"%1\" до:", "NEW_VARIABLE": "Створити змінну...", "NEW_STRING_VARIABLE": "Створити рядкову змінну...", "NEW_NUMBER_VARIABLE": "Створити числову змінну...", "NEW_COLOUR_VARIABLE": "Створити колірну змінну...", "NEW_VARIABLE_TYPE_TITLE": "Тип нової змінної:", "NEW_VARIABLE_TITLE": "Нова назва змінної:", "VARIABLE_ALREADY_EXISTS": "Змінна з назвою '%1' вже існує.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Змінна з назвою '%1' вже існує в іншому типі: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Вилучити %1 використання змінної '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Неможливо видалити змінну '%1', тому що це частина визначення функції '%2'", "DELETE_VARIABLE": "Вилучити змінну '%1'", "COLOUR_PICKER_HELPURL": "https://uk.wikipedia.org/wiki/Колір", "COLOUR_PICKER_TOOLTIP": "Вибрати колір з палітри.", "COLOUR_RANDOM_TITLE": "випадковий колір", "COLOUR_RANDOM_TOOLTIP": "Вибрати колір навмання.", "COLOUR_RGB_HELPURL": "https://www.december.com/html/spec/colorpercompact.html", "COLOUR_RGB_TITLE": "колір з", "COLOUR_RGB_RED": "червоний", "COLOUR_RGB_GREEN": "зелений", "COLOUR_RGB_BLUE": "син<PERSON>й", "COLOUR_RGB_TOOLTIP": "Створити колір зі вказаними рівнями червоного, зеленого та синього. Усі значення мають бути від 0 до 100.", "COLOUR_BLEND_HELPURL": "https://meyerweb.com/eric/tools/color-blend/#:::rgbp", "COLOUR_BLEND_TITLE": "змішати", "COLOUR_BLEND_COLOUR1": "колір 1", "COLOUR_BLEND_COLOUR2": "колір 2", "COLOUR_BLEND_RATIO": "співвідношення", "COLOUR_BLEND_TOOLTIP": "Змішує два кольори разом у вказаному співвідношені (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://uk.wikipedia.org/wiki/Цикл_(програмування)#.D0.A6.D0.B8.D0.BA.D0.BB_.D0.B7_.D0.BB.D1.96.D1.87.D0.B8.D0.BB.D1.8C.D0.BD.D0.B8.D0.BA.D0.BE.D0.BC", "CONTROLS_REPEAT_TITLE": "повторити %1 разів", "CONTROLS_REPEAT_INPUT_DO": "виконати", "CONTROLS_REPEAT_TOOLTIP": "Виконати певні дії декілька разів.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "повторювати поки", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "повторювати, доки не", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Поки значення істинне, виконувати певні дії.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Поки значення хибне, виконувати певні дії.", "CONTROLS_FOR_TOOLTIP": "Наявна змінна \"%1\" набуває значень від початкового до кінцевого, враховуючи заданий інтервал, і виконуються вказані блоки.", "CONTROLS_FOR_TITLE": "рахувати з %1 від %2 до %3 через %4", "CONTROLS_FOREACH_TITLE": "для кожного елемента %1 у списку %2", "CONTROLS_FOREACH_TOOLTIP": "Для кожного елемента в списку змінна '%1' отримує значення елемента, а потім виконуються певні дії.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "перервати цикл", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "продовжити з наступної ітерації циклу", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Перервати виконання циклу.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Пропустити залишок цього циклу і перейти до виконання наступної ітерації.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Попередження: цей блок може бути використаний тільки в межах циклу.", "CONTROLS_IF_TOOLTIP_1": "Якщо значення істинне, то виконати певні дії.", "CONTROLS_IF_TOOLTIP_2": "Якщо значення істинне, то виконується перший блок операторів. В іншому випадку виконується другий блок операторів.", "CONTROLS_IF_TOOLTIP_3": "Якщо перше значення істинне, то виконується перший блок операторів. В іншому випадку, якщо друге значення істина, то виконується другий блок операторів.", "CONTROLS_IF_TOOLTIP_4": "Якщо перше значення істинне, то виконується перший блок операторів. В іншому випадку, якщо друге значення істинне, то виконується другий блок операторів. Якщо жодне із значень не є істинним, то виконується останній блок операторів.", "CONTROLS_IF_MSG_IF": "якщо", "CONTROLS_IF_MSG_ELSEIF": "інакше якщо", "CONTROLS_IF_MSG_ELSE": "інакше", "CONTROLS_IF_IF_TOOLTIP": "Додайте, вилучіть або змініть порядок секцій, щоб переналаштувати цей блок 'якщо'.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Додайте умову до блока 'якщо'.", "CONTROLS_IF_ELSE_TOOLTIP": "Додати остаточну, всеосяжну умову до блоку 'якщо'.", "IOS_OK": "Готово", "IOS_CANCEL": "Скасувати", "IOS_ERROR": "Помилка", "IOS_PROCEDURES_INPUTS": "ВХОДИ", "IOS_PROCEDURES_ADD_INPUT": "+ Додати вхідну змінну", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Дозволити оператори", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "У цієї функції є вхідні змінні, що дублюються.", "IOS_VARIABLES_ADD_VARIABLE": "+ Додати змінну", "IOS_VARIABLES_ADD_BUTTON": "Додати", "IOS_VARIABLES_RENAME_BUTTON": "Перейменувати", "IOS_VARIABLES_DELETE_BUTTON": "Видалити", "IOS_VARIABLES_VARIABLE_NAME": "Назва змінної", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Ви не можете використовувати порожню назву змінної.", "LOGIC_COMPARE_HELPURL": "https://uk.wikipedia.org/wiki/Нерівність", "LOGIC_COMPARE_TOOLTIP_EQ": "Повертає істину, якщо обидва входи рівні один одному.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Повертає істину, якщо обидва входи не дорівнюють один одному.", "LOGIC_COMPARE_TOOLTIP_LT": "Повертає істину, якщо перше вхідне значення менше, ніж друге.", "LOGIC_COMPARE_TOOLTIP_LTE": "Повертає істину, якщо перше вхідне значення менше або дорівнює другому.", "LOGIC_COMPARE_TOOLTIP_GT": "Повертає істину, якщо перше вхідне значення більше, ніж друге.", "LOGIC_COMPARE_TOOLTIP_GTE": "Повертає істину, якщо перше вхідне значення більше або дорівнює другому.", "LOGIC_OPERATION_TOOLTIP_AND": "Повертає істину, якщо обидва входи істинні.", "LOGIC_OPERATION_AND": "та", "LOGIC_OPERATION_TOOLTIP_OR": "Повертає істину, якщо принаймні один з входів істинний.", "LOGIC_OPERATION_OR": "або", "LOGIC_NEGATE_TITLE": "не %1", "LOGIC_NEGATE_TOOLTIP": "Повертає істину, якщо вхідне значення хибне. Повертає хибність, якщо вхідне значення істинне.", "LOGIC_BOOLEAN_TRUE": "істина", "LOGIC_BOOLEAN_FALSE": "хибність", "LOGIC_BOOLEAN_TOOLTIP": "Повертає значення істина або хибність.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "ніщо", "LOGIC_NULL_TOOLTIP": "Повертає ніщо.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "тест", "LOGIC_TERNARY_IF_TRUE": "якщо істина", "LOGIC_TERNARY_IF_FALSE": "якщо хибність", "LOGIC_TERNARY_TOOLTIP": "Перевіряє умову в 'тест'. Якщо умова істинна, то повертає  значення 'якщо істина'; в іншому випадку повертає значення 'якщо хибність'.", "MATH_NUMBER_HELPURL": "https://uk.wikipedia.org/wiki/Число", "MATH_NUMBER_TOOLTIP": "Число.", "MATH_ARITHMETIC_HELPURL": "https://uk.wikipedia.org/wiki/Арифметика", "MATH_ARITHMETIC_TOOLTIP_ADD": "Повертає суму двох чисел.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Повертає різницю двох чисел.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Повертає добуток двох чисел.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Повертає частку двох чисел.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Повертає перше число, піднесене до степеня, вираженого другим числом.", "MATH_SINGLE_HELPURL": "https://uk.wikipedia.org/wiki/Квадратний_корінь", "MATH_SINGLE_OP_ROOT": "квадра<PERSON><PERSON><PERSON> корінь", "MATH_SINGLE_TOOLTIP_ROOT": "Повертає квадратний корінь з числа.", "MATH_SINGLE_OP_ABSOLUTE": "модуль", "MATH_SINGLE_TOOLTIP_ABS": "Повертає модуль числа.", "MATH_SINGLE_TOOLTIP_NEG": "Повертає протилежне число.", "MATH_SINGLE_TOOLTIP_LN": "Повертає натуральний логарифм числа.", "MATH_SINGLE_TOOLTIP_LOG10": "Повертає десятковий логарифм числа.", "MATH_SINGLE_TOOLTIP_EXP": "Повертає e у степені.", "MATH_SINGLE_TOOLTIP_POW10": "Повертає 10 у степені.", "MATH_TRIG_HELPURL": "https://uk.wikipedia.org/wiki/Тригонометричні_функції", "MATH_TRIG_TOOLTIP_SIN": "Повертає синус кута в градусах (не в радіанах).", "MATH_TRIG_TOOLTIP_COS": "Повертає косинус кута в градусах (не в радіанах).", "MATH_TRIG_TOOLTIP_TAN": "Повертає тангенс кута в градусах (не в радіанах).", "MATH_TRIG_TOOLTIP_ASIN": "Повертає арксинус числа.", "MATH_TRIG_TOOLTIP_ACOS": "Повертає арккосинус числа.", "MATH_TRIG_TOOLTIP_ATAN": "Повертає арктангенс числа.", "MATH_CONSTANT_HELPURL": "https://uk.wikipedia.org/wiki/Математична_константа", "MATH_CONSTANT_TOOLTIP": "Повертає одну з поширених констант: π (3.141...), e (2.718...), φ (1,618...), sqrt(2) (1.414...), sqrt(½) (0.707...) або ∞ (нескінченність).", "MATH_IS_EVEN": "парне", "MATH_IS_ODD": "непарне", "MATH_IS_PRIME": "просте", "MATH_IS_WHOLE": "ціле", "MATH_IS_POSITIVE": "додатне", "MATH_IS_NEGATIVE": "від'ємне", "MATH_IS_DIVISIBLE_BY": "ділиться на", "MATH_IS_TOOLTIP": "Перевіряє, чи число парне, непарне, просте, ціле, додатне, від'ємне або чи воно ділиться на певне число без остачі. Повертає істину або хибність.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "змінити %1 на %2", "MATH_CHANGE_TOOLTIP": "Додати число до змінної '%1'.", "MATH_ROUND_HELPURL": "https://uk.wikipedia.org/wiki/Округлення", "MATH_ROUND_TOOLTIP": "Округлення числа до більшого або до меншого.", "MATH_ROUND_OPERATOR_ROUND": "округлити", "MATH_ROUND_OPERATOR_ROUNDUP": "округлити до більшого", "MATH_ROUND_OPERATOR_ROUNDDOWN": "округлити до меншого", "MATH_ONLIST_HELPURL": "http://www.mapleprimes.com/questions/100441-Applying-Function-To-List-Of-Numbers", "MATH_ONLIST_OPERATOR_SUM": "сума списку", "MATH_ONLIST_TOOLTIP_SUM": "Повертає суму всіх чисел у списку.", "MATH_ONLIST_OPERATOR_MIN": "мінімум списку", "MATH_ONLIST_TOOLTIP_MIN": "Повертає найменше число у списку.", "MATH_ONLIST_OPERATOR_MAX": "максимум списку", "MATH_ONLIST_TOOLTIP_MAX": "Повертає найбільше число у списку.", "MATH_ONLIST_OPERATOR_AVERAGE": "середнє списку", "MATH_ONLIST_TOOLTIP_AVERAGE": "Повертає середнє (арифметичне) числових значень у списку.", "MATH_ONLIST_OPERATOR_MEDIAN": "медіана списку", "MATH_ONLIST_TOOLTIP_MEDIAN": "Повертає медіану списку.", "MATH_ONLIST_OPERATOR_MODE": "моди списку", "MATH_ONLIST_TOOLTIP_MODE": "Повертає перелік найпоширеніших елементів у списку.", "MATH_ONLIST_OPERATOR_STD_DEV": "стандартне відхилення списку", "MATH_ONLIST_TOOLTIP_STD_DEV": "Повертає стандартне відхилення списку.", "MATH_ONLIST_OPERATOR_RANDOM": "випадковий елемент списку", "MATH_ONLIST_TOOLTIP_RANDOM": "Повертає випадковий елемент зі списку.", "MATH_MODULO_HELPURL": "https://uk.wikipedia.org/wiki/Ділення_з_остачею", "MATH_MODULO_TITLE": "остача від %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Повертає остачу від ділення двох чисел.", "MATH_CONSTRAIN_TITLE": "обмежити %1 від %2 до %3", "MATH_CONSTRAIN_TOOLTIP": "Обмежує число вказаними межами (включно).", "MATH_RANDOM_INT_HELPURL": "https://uk.wikipedia.org/wiki/Генерація_випадкових_чисел", "MATH_RANDOM_INT_TITLE": "випадкове ціле число від %1 до %2", "MATH_RANDOM_INT_TOOLTIP": "Повертає випадкове ціле число між двома заданими межами включно.", "MATH_RANDOM_FLOAT_HELPURL": "https://uk.wikipedia.org/wiki/Генерація_випадкових_чисел", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "випадковий дріб", "MATH_RANDOM_FLOAT_TOOLTIP": "Повертає випадковий дріб від 0,0 (включно) та 1.0 (не включно).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 по X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Повертає арктангенс точки (X, Y) у градусах від -180 до 180.", "TEXT_TEXT_HELPURL": "https://uk.wikipedia.org/wiki/Рядок_(програмування)", "TEXT_TEXT_TOOLTIP": "Символ, слово або рядок тексту.", "TEXT_JOIN_HELPURL": "https://github.com/google/blockly/wiki/Text#text-creation", "TEXT_JOIN_TITLE_CREATEWITH": "створити текст з", "TEXT_JOIN_TOOLTIP": "Створити фрагмент тексту шляхом з'єднування будь-якого числа елементів.", "TEXT_CREATE_JOIN_TITLE_JOIN": "приєднати", "TEXT_CREATE_JOIN_TOOLTIP": "Додайте, вилучіть або змініть порядок секцій для переналаштування текстового блоку.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Додати елемент до тексту.", "TEXT_APPEND_TITLE": "до %1 додати текст %2", "TEXT_APPEND_TOOLTIP": "Додати деякий текст до змінної '%1'.", "TEXT_LENGTH_TITLE": "довжина %1", "TEXT_LENGTH_TOOLTIP": "Повертає число символів (включно з пропусками) у даному тексті.", "TEXT_ISEMPTY_TITLE": "%1 є порожнім", "TEXT_ISEMPTY_TOOLTIP": "Повертає істину, якщо вказаний текст порожній.", "TEXT_INDEXOF_TOOLTIP": "Повертає індекс першого/останнього входження першого тексту в другий. Повертає %1, якщо текст не знайдено.", "TEXT_INDEXOF_TITLE": "у тексті %1 %2 %3.", "TEXT_INDEXOF_OPERATOR_FIRST": "знайти перше входження тексту", "TEXT_INDEXOF_OPERATOR_LAST": "знайти останнє входження тексту", "TEXT_CHARAT_HELPURL": "https://github.com/google/blockly/wiki/Text#extracting-text", "TEXT_CHARAT_TITLE": "з тексту %1 %2", "TEXT_CHARAT_FROM_START": "отримати символ #", "TEXT_CHARAT_FROM_END": "отримати символ # з кінця", "TEXT_CHARAT_FIRST": "отримати перший символ", "TEXT_CHARAT_LAST": "отримати останній символ", "TEXT_CHARAT_RANDOM": "отримати випадковий символ", "TEXT_CHARAT_TAIL": "-ий.", "TEXT_CHARAT_TOOLTIP": "Повертає символ у зазначеній позиції.", "TEXT_GET_SUBSTRING_TOOLTIP": "Повертає заданий фрагмент тексту.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "у тексті", "TEXT_GET_SUBSTRING_START_FROM_START": "отримати підрядок від символу #", "TEXT_GET_SUBSTRING_START_FROM_END": "отримати підрядок від символу # з кінця", "TEXT_GET_SUBSTRING_START_FIRST": "отримати підрядок від першого символу", "TEXT_GET_SUBSTRING_END_FROM_START": "до символу #", "TEXT_GET_SUBSTRING_END_FROM_END": "до символу # з кінця", "TEXT_GET_SUBSTRING_END_LAST": "до останнього символу", "TEXT_GET_SUBSTRING_TAIL": "-ого.", "TEXT_CHANGECASE_TOOLTIP": "В іншому випадку повертає копію тексту.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "до ВЕРХНЬОГО регістру", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "до нижнього регістру", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "Великі Перші Букви", "TEXT_TRIM_TOOLTIP": "Повертає копію тексту з вилученими пропусками з одного або обох кінців.", "TEXT_TRIM_OPERATOR_BOTH": "вилучити крайні пропуски з обох кінців", "TEXT_TRIM_OPERATOR_LEFT": "вилучити пропуски з лівого боку", "TEXT_TRIM_OPERATOR_RIGHT": "вилучити пропуски з правого боку", "TEXT_PRINT_TITLE": "друк %1", "TEXT_PRINT_TOOLTIP": "Надрукувати заданий текст, числа або інші значення.", "TEXT_PROMPT_TYPE_TEXT": "запит тексту з повідомленням", "TEXT_PROMPT_TYPE_NUMBER": "запит числа з повідомленням", "TEXT_PROMPT_TOOLTIP_NUMBER": "Запитати у користувача число.", "TEXT_PROMPT_TOOLTIP_TEXT": "Запитати у користувача деякий текст.", "TEXT_COUNT_MESSAGE0": "кількість %1 в %2", "TEXT_COUNT_TOOLTIP": "Підраховує, скільки разів деякий текст з'являється в іншому тексті.", "TEXT_REPLACE_MESSAGE0": "замінити %1 на %2 в %3", "TEXT_REPLACE_TOOLTIP": "Замінює всі входження деякого тексту іншим текстом.", "TEXT_REVERSE_MESSAGE0": "розвернути %1", "TEXT_REVERSE_TOOLTIP": "Змінює на протилежний порядок символів у тексті.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "створити порожній список", "LISTS_CREATE_EMPTY_TOOLTIP": "Повертає список, довж<PERSON>ною 0, що не містить записів даних", "LISTS_CREATE_WITH_TOOLTIP": "Створює список з будь-якою кількістю елементів.", "LISTS_CREATE_WITH_INPUT_WITH": "створити список з", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "список", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "Додайте, вилучіть або змініть порядок секцій для переналаштування блока списку.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Додати елемент до списку.", "LISTS_REPEAT_TOOLTIP": "Створює список, що складається з заданого значення повтореного задану кількість разів.", "LISTS_REPEAT_TITLE": "створити список з елемента %1 повтореного %2 разів", "LISTS_LENGTH_TITLE": "довжина %1", "LISTS_LENGTH_TOOLTIP": "Повертає довжину списку.", "LISTS_ISEMPTY_TITLE": "%1 є порожнім", "LISTS_ISEMPTY_TOOLTIP": "Повертає істину, якщо список порожній.", "LISTS_INLIST": "у списку", "LISTS_INDEX_OF_FIRST": "знайти перше входження елемента", "LISTS_INDEX_OF_LAST": "знайти останнє входження елемента", "LISTS_INDEX_OF_TOOLTIP": "Повертає індекс першого/останнього входження елемента у списку. Повертає %1, якщо елемент не знайдено.", "LISTS_GET_INDEX_GET": "отримати", "LISTS_GET_INDEX_GET_REMOVE": "отримати і вилучити", "LISTS_GET_INDEX_REMOVE": "вилучити", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# з кінця", "LISTS_GET_INDEX_FIRST": "пер<PERSON><PERSON>", "LISTS_GET_INDEX_LAST": "остан<PERSON><PERSON>й", "LISTS_GET_INDEX_RANDOM": "випадковий", "LISTS_GET_INDEX_TAIL": "-ий.", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 - це перший елемент.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 - це останній елемент.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Повертає елемент у заданій позиції у списку.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Повертає перший елемент списку.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Повертає останній елемент списку.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Повертає випадковий елемент списку.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Видаляє і повертає елемент у заданій позиції у списку.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Видаляє і повертає перший елемент списку.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Видаляє і повертає останній елемент списку.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Видаляє і повертає випадковий елемент списоку.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Вилучає зі списку елемент у вказаній позиції.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Вилучає перший елемент списку.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Вилучає останній елемент списку.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Вилучає випадковий елемент списку.", "LISTS_SET_INDEX_SET": "встановити", "LISTS_SET_INDEX_INSERT": "вставити в", "LISTS_SET_INDEX_INPUT_TO": "як", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Задає елемент списку у вказаній позиції.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Задає перший елемент списку.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Задає останній елемент списку.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Задає випадковий елемент у списку.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Вставка елемента у вказану позицію списку.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Вставляє елемент на початок списку.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Додає елемент у кінці списку.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Випадковим чином вставляє елемент у список.", "LISTS_GET_SUBLIST_START_FROM_START": "отримати вкладений список з #", "LISTS_GET_SUBLIST_START_FROM_END": "отримати вкладений список від # з кінця", "LISTS_GET_SUBLIST_START_FIRST": "отримати вкладений список з першого", "LISTS_GET_SUBLIST_END_FROM_START": "до #", "LISTS_GET_SUBLIST_END_FROM_END": "до # з кінця", "LISTS_GET_SUBLIST_END_LAST": "до останнього", "LISTS_GET_SUBLIST_TAIL": "символу.", "LISTS_GET_SUBLIST_TOOLTIP": "Створює копію вказаної частини списку.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "сортувати %3 %1 %2", "LISTS_SORT_TOOLTIP": "Сортувати копію списку.", "LISTS_SORT_ORDER_ASCENDING": "за зростанням", "LISTS_SORT_ORDER_DESCENDING": "за спаданням", "LISTS_SORT_TYPE_NUMERIC": "як числа", "LISTS_SORT_TYPE_TEXT": "за абеткою", "LISTS_SORT_TYPE_IGNORECASE": "за абеткою, ігноруючи регістр", "LISTS_SPLIT_LIST_FROM_TEXT": "зробити з тексту список", "LISTS_SPLIT_TEXT_FROM_LIST": "зробити зі списку текст", "LISTS_SPLIT_WITH_DELIMITER": "з розділювачем", "LISTS_SPLIT_TOOLTIP_SPLIT": "Поділити текст на список текстів, розриваючи на кожному розділювачі.", "LISTS_SPLIT_TOOLTIP_JOIN": "Злити список текстів у єдиний текст, відокремивши розділювачами.", "LISTS_REVERSE_MESSAGE0": "розвернути %1", "LISTS_REVERSE_TOOLTIP": "Змінити порядок копії списку на зворотний.", "ORDINAL_NUMBER_SUFFIX": "-ий.", "VARIABLES_GET_TOOLTIP": "Повертає значення цієї змінної.", "VARIABLES_GET_CREATE_SET": "Створити 'встановити %1'", "VARIABLES_SET": "встановити %1 до %2", "VARIABLES_SET_TOOLTIP": "Задає цю змінну рівною входу.", "VARIABLES_SET_CREATE_GET": "Створити 'отримати %1'", "PROCEDURES_DEFNORETURN_HELPURL": "https://uk.wikipedia.org/wiki/Підпрограма", "PROCEDURES_DEFNORETURN_TITLE": "до", "PROCEDURES_DEFNORETURN_PROCEDURE": "щось зробити", "PROCEDURES_BEFORE_PARAMS": "з:", "PROCEDURES_CALL_BEFORE_PARAMS": "з:", "PROCEDURES_DEFNORETURN_DO": "блок тексту", "PROCEDURES_DEFNORETURN_TOOLTIP": "Створює функцію без виводу.", "PROCEDURES_DEFNORETURN_COMMENT": "Опишіть цю функцію...", "PROCEDURES_DEFRETURN_HELPURL": "https://uk.wikipedia.org/wiki/Підпрограма", "PROCEDURES_DEFRETURN_RETURN": "повернути", "PROCEDURES_DEFRETURN_TOOLTIP": "Створює функцію з виводом.", "PROCEDURES_ALLOW_STATEMENTS": "дозволити дії", "PROCEDURES_DEF_DUPLICATE_WARNING": "Увага: ця функція має дубльовані параметри.", "PROCEDURES_CALLNORETURN_HELPURL": "https://uk.wikipedia.org/wiki/Підпрограма", "PROCEDURES_CALLNORETURN_TOOLTIP": "Запустити користувацьку функцію \"%1\".", "PROCEDURES_CALLRETURN_HELPURL": "https://uk.wikipedia.org/wiki/Підпрограма", "PROCEDURES_CALLRETURN_TOOLTIP": "Запустити користувацьку функцію \"%1\" і використати її вивід.", "PROCEDURES_MUTATORCONTAINER_TITLE": "входи", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "Додайте, вилучіть або змініть порядок вхідних параметрів для цієї функції.", "PROCEDURES_MUTATORARG_TITLE": "назва входу:", "PROCEDURES_MUTATORARG_TOOLTIP": "Додати до функції вхідні параметри.", "PROCEDURES_HIGHLIGHT_DEF": "Підсвітити визначення функції", "PROCEDURES_CREATE_DO": "Створити \"%1\"", "PROCEDURES_IFRETURN_TOOLTIP": "Якщо значення істинне, то повернути друге значення.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Попередження: цей блок може використовуватися лише в межах визначення функції.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Скажіть щось...", "WORKSPACE_ARIA_LABEL": "Робоча область Blockly", "COLLAPSED_WARNINGS_WARNING": "Звернуті блоки містять попередження.", "DIALOG_OK": "Гаразд", "DIALOG_CANCEL": "Скасувати"}