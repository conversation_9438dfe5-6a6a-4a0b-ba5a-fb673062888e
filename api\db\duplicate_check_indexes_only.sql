-- 题目重复检测机制 - 仅索引优化脚本
-- 创建日期: 2025-01-19
-- 说明: 仅创建必要的索引以提高重复检测查询性能，不创建额外的表

-- 1. 为题目表创建索引以提高重复检测性能
-- 如果索引已存在会报错，可以忽略这些错误

-- 题目标题索引
CREATE INDEX idx_question_title ON exam_question (title);

-- 题目重复检测组合索引（标题+科目+级别+类型）
CREATE INDEX idx_question_duplicate_check ON exam_question (title, subject, level, question_type);

-- 2. 为试卷表创建索引以提高重复检测性能

-- 试卷标题索引
CREATE INDEX idx_paper_title ON exam_paper (title);

-- 试卷重复检测组合索引（标题+科目+级别+年份）
CREATE INDEX idx_paper_duplicate_check ON exam_paper (title, subject, level, year);

-- 3. 显示完成信息
SELECT 'Duplicate check indexes created successfully!' as result;
SELECT 'Index creation errors can be ignored if indexes already exist.' as notice;
SELECT 'Your duplicate check system is ready to use!' as status;
