package org.jeecg.modules.teaching.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户部门关联表Mapper接口
 * 用于查询学生是否属于某个班级
 */
@Repository
public interface TeachingSysUserDepartMapper extends BaseMapper<Object> {
    
    /**
     * 查询用户与部门的关联记录数
     * 用于判断用户是否属于某个班级
     *
     * @param userId 用户ID
     * @param departId 部门ID
     * @return 关联记录数
     */
    @Select("SELECT COUNT(1) FROM sys_user_depart WHERE user_id = #{userId} AND dep_id = #{departId}")
    Integer countUserDepart(@Param("userId") String userId, @Param("departId") String departId);
    
    /**
     * 查询用户所属的班级ID列表
     *
     * @param userId 用户ID
     * @return 班级ID列表
     */
    @Select("SELECT dep_id FROM sys_user_depart WHERE user_id = #{userId}")
    List<String> getUserClassIds(@Param("userId") String userId);
} 