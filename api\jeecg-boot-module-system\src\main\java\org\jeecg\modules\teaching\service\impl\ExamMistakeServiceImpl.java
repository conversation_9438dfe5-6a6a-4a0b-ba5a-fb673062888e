package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.teaching.entity.ExamMistake;
import org.jeecg.modules.teaching.mapper.ExamMistakeMapper;
import org.jeecg.modules.teaching.service.IExamMistakeService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * @Description: 错题记录
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@Service
public class ExamMistakeServiceImpl extends ServiceImpl<ExamMistakeMapper, ExamMistake> implements IExamMistakeService {

    @Override
    public void recordMistake(String userId, String questionId, String answer, Date mistakeTime) {
        // 查询是否已有此错题记录
        LambdaQueryWrapper<ExamMistake> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamMistake::getUserId, userId)
                .eq(ExamMistake::getQuestionId, questionId);
        
        ExamMistake mistake = this.getOne(queryWrapper);
        
        if (mistake == null) {
            // 新建错题记录
            mistake = new ExamMistake();
            mistake.setUserId(userId);
            mistake.setQuestionId(questionId);
            mistake.setLastAnswer(answer);
            mistake.setMistakeCount(1);
            mistake.setLastMistakeTime(mistakeTime);
            this.save(mistake);
        } else {
            // 更新现有错题记录
            mistake.setLastAnswer(answer);
            mistake.setMistakeCount(mistake.getMistakeCount() + 1);
            mistake.setLastMistakeTime(mistakeTime);
            this.updateById(mistake);
        }
    }

    @Override
    public List<ExamMistake> getUserMistakes(String userId) {
        // 查询用户的所有错题
        QueryWrapper<ExamMistake> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .orderByDesc("last_mistake_time");
        
        return this.list(queryWrapper);
    }
} 