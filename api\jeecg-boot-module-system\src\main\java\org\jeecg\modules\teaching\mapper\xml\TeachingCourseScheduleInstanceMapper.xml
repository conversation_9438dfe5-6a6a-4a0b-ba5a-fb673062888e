<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.teaching.mapper.TeachingCourseScheduleInstanceMapper">
    
    <!-- 根据父课程ID和实例日期查询实例 -->
    <select id="selectByParentIdAndDate" resultType="org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance">
        SELECT * FROM teaching_course_schedule_instance 
        WHERE parent_id = #{parentId} 
        AND DATE_FORMAT(instance_date, '%Y-%m-%d') = #{instanceDate}
    </select>
</mapper> 