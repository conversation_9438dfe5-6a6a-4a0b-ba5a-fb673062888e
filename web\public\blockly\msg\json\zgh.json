{"@metadata": {"authors": ["Amara-Amaziɣ", "<PERSON><PERSON><PERSON> moh<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hakim1bal"]}, "VARIABLES_DEFAULT_NAME": "ⴰⴼⵔⴷⵉⵙ", "UNNAMED_KEY": "ⴰⵔⵉⵙⵎ", "TODAY": "ⴰⵙⵙⴰ", "ADD_COMMENT": "ⵔⵏⵓ ⴰⵅⴼⴰⵡⴰⵍ", "REMOVE_COMMENT": "ⴽⴽⵙ ⴰⵅⴼⴰⵡⴰⵍ", "DELETE_BLOCK": "ⴽⴽⵙ ⴰⴱⵍⵓⴽ", "DELETE_X_BLOCKS": "ⴽⴽⵙ %1 ⵉⴱⵍⵓⴽⵏ", "DELETE_ALL_BLOCKS": "ⴽⴽⵙ %1 ⵉⴱⵍⵓⴽⵏ ⵎⴰⵕⵕⴰ?", "HELP": "ⵜⵉⵡⵉⵙⵉ", "UNDO": "ⵙⵔ", "REDO": "ⴰⵍⵙ", "CHANGE_VALUE_TITLE": "ⵙⵏⴼⵍ ⴰⵣⴰⵍ:", "RENAME_VARIABLE": "ⵙⵏⴼⵍ ⵉⵙⵎ ⵏ ⵓⵎⵙⴽⵉⵍ...", "RENAME_VARIABLE_TITLE": "ⵙⵏⴼⵍ ⵉⵎⵙⴽⵉⵍⵏ ⴰⴽⴽ '%1' ⵖⵔ:", "NEW_VARIABLE": "ⵙⴽⵔ ⴰⵎⵙⴽⵉⵍ...", "NEW_NUMBER_VARIABLE": "ⵙⴽⵔ ⴰⵎⴹⴰⵏ ⴰⵎⵙⴽⵉⵍ...", "NEW_COLOUR_VARIABLE": "ⵙⴽⵔ ⴰⴽⵍⵓ ⴰⵎⵙⴽⵉⵍ...", "NEW_VARIABLE_TYPE_TITLE": "ⴰⵏⴰⵡ ⴰⵎⴰⵢⵏⵓ ⵏ ⵓⵎⵙⴽⵉⵍ:", "NEW_VARIABLE_TITLE": "ⵉⵙⵎ ⵏ ⵓⵎⵙⴽⵉⵍ ⴰⵎⴰⵢⵏⵓ:", "DELETE_VARIABLE": "ⴽⴽⵙ ⴰⵎⵙⴽⵉⵍ '%1'", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_RGB_TITLE": "ⴽⵍⵓ ⵙ", "COLOUR_RGB_RED": "ⴰⵣⴳⴳⵯⴰⵖ", "COLOUR_RGB_GREEN": "ⴰⵣⴳⵣⴰ", "COLOUR_RGB_BLUE": "ⴰⵏⵉⵍⵉ", "COLOUR_RGB_TOOLTIP": "ⴰⵣⴰⵍⵏ ⵉⴼⵓⴽⴽ ⴰⴷ ⵉⵍⵉⵏ ⴳⵔ 0 ⴷ 100.", "COLOUR_BLEND_TITLE": "ⵙⵎⵔⴽⵙ", "COLOUR_BLEND_COLOUR1": "ⴰⴽⵍⵓ 1", "COLOUR_BLEND_COLOUR2": "ⴰⴽⵍⵓ 2", "COLOUR_BLEND_RATIO": "ⴰⵙⵙⴰⵖ", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "ⴰⵍⵙ %1 ⵜⵉⴽⴽⴰⵍ", "CONTROLS_REPEAT_INPUT_DO": "ⴳ", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "ⴰⵍⵙ ⴰⴷⴷⴰⴳ", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "ⴰⵍⵙ ⴰⵔⴷ", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "ⴽⵓⴷ ⵉⴷⵜⵜⴰ ⵡⴰⵣⴰⵍ, ⵙⵙⵍⴽⵎ ⴽⵔⴰ ⵏ ⵡⴰⵏⴰⴹⵏ.", "CONTROLS_IF_MSG_IF": "ⵎⴽ", "CONTROLS_IF_MSG_ELSEIF": "ⵉⵙ", "CONTROLS_IF_MSG_ELSE": "ⵎⴽ ⴷ ⵓⵀⵓ", "IOS_OK": "ⵡⴰⵅⵅⴰ", "IOS_CANCEL": "ⵙⵔ", "IOS_ERROR": "ⴰⵣⴳⴰⵍ", "IOS_PROCEDURES_INPUTS": "ⵉⵏⴽⵛⵓⵎⵏ", "IOS_PROCEDURES_ADD_INPUT": "+ ⵔⵏⵓ ⵢⴰⵜ ⵜⵏⴽⵛⵓⵎⵜ", "IOS_VARIABLES_ADD_VARIABLE": "+ ⵔⵏⵓ ⴰⵎⵙⴽⵉⵍ", "IOS_VARIABLES_ADD_BUTTON": "ⵔⵏⵓ", "IOS_VARIABLES_RENAME_BUTTON": "ⵙⵏⴼⵍ ⵉⵙⵎ", "IOS_VARIABLES_DELETE_BUTTON": "ⴽⴽⵙ", "IOS_VARIABLES_VARIABLE_NAME": "ⵉⵙⵎ ⵏ ⵓⵎⵙⴽⵉⵍ", "IOS_VARIABLES_EMPTY_NAME_ERROR": "ⵓⵔ ⵜⵓⴼⵉⴷ ⴰⴷ ⵜⵙⵙⵎⵔⵙⴷ ⵢⴰⵏ ⵢⵉⵙⵎ ⵏ ⵓⵎⵙⴽⵉⵍ ⵢⵓⵔⴰⵏ.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_OPERATION_AND": "ⴷ", "LOGIC_OPERATION_OR": "ⵏⵖ", "LOGIC_NEGATE_TITLE": "ⵓⵔ ⴷ %1", "LOGIC_BOOLEAN_TRUE": "ⴰⵎⵉⴷⵉ", "LOGIC_BOOLEAN_FALSE": "ⴰⵔⵎⵉⴷⵉ", "LOGIC_NULL": "ⵢⵓⵔⴰ", "LOGIC_TERNARY_CONDITION": "ⴰⵔⵎ", "LOGIC_TERNARY_IF_TRUE": "ⵎⴽ ⵉⴷⵜⵜⴰ", "LOGIC_TERNARY_IF_FALSE": "ⵎⴽ ⵓⵔ ⵉⴷⵜⵜⵉ", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "ⴽⵔⴰ ⵏ ⵓⵎⴹⴰⵏ.", "MATH_ARITHMETIC_HELPURL": "https://en.wikipedia.org/wiki/Arithmetic", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_CHANGE_TITLE": "ⵙⵏⴼⵍ %1 ⵙ %2", "MATH_CHANGE_TOOLTIP": "ⵔⵏⵓ ⵢⴰⵏ ⵓⵎⴹⴰⵏ ⵖⵔ ⵓⵎⵙⴽⵉⵍ '%1'", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 ⵙⴳ X:%1 Y:%2", "TEXT_JOIN_TITLE_CREATEWITH": "ⵙⵏⴼⵍⵓⵍ ⴰⴹⵕⵉⵚ ⵙ", "TEXT_CREATE_JOIN_TITLE_JOIN": "ⵍⴽⵎ", "TEXT_LENGTH_TITLE": "ⵜⵉⵖⵣⵉ ⵏ %1", "TEXT_INDEXOF_TITLE": "ⴳ ⵓⴹⵕⵉⵚ %1 %2 %3", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ⴳ ⵓⴹⵕⵉⵚ", "TEXT_GET_SUBSTRING_END_LAST": "ⴰⵔ ⴰⵙⴽⴽⵉⵍ ⴰⵏⴳⴳⴰⵔⵓ", "LISTS_LENGTH_TITLE": "ⵜⵉⵖⵣⵉ ⵏ %1", "LISTS_INLIST": "ⴳ ⵜⵍⴳⴰⵎⵜ", "LISTS_GET_INDEX_REMOVE": "ⵙⵉⵜⵜⵢ", "LISTS_GET_INDEX_FROM_END": "# ⵙⴳ ⵜⵉⴳⵉⵔⴰ", "LISTS_GET_INDEX_FIRST": "ⴰⵎⵣⵡⴰⵔⵓ", "LISTS_GET_INDEX_LAST": "ⴰⵎⴳⴳⴰⵔⵓ", "LISTS_SET_INDEX_INPUT_TO": "ⴰⵎ", "LISTS_GET_SUBLIST_END_FROM_START": "ⴰⵔ #", "LISTS_GET_SUBLIST_END_LAST": "ⴰⵔ ⵜⴳⵉⵔⴰ", "LISTS_SORT_TYPE_NUMERIC": "ⴰⵎⵓⵟⵟⵓⵏ", "PROCEDURES_DEFNORETURN_TITLE": "ⵉ", "PROCEDURES_DEFNORETURN_PROCEDURE": "ⴳ ⴽⵔⴰ", "PROCEDURES_BEFORE_PARAMS": "ⵙ:", "PROCEDURES_CALL_BEFORE_PARAMS": "ⵙ:", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_MUTATORARG_TOOLTIP": "ⵔⵏⵓ ⴰⵏⴽⵛⴰⵎ ⵖⵔ ⵜⵙⵖⵏⵜ.", "DIALOG_OK": "ⵡⴰⵅⵅⴰ", "DIALOG_CANCEL": "ⵙⵔ"}