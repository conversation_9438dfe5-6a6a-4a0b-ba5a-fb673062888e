package org.jeecg.modules.teaching.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.mapper.ExamPaperMapper;
import org.jeecg.modules.teaching.service.IExamPaperService;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.jeecg.modules.teaching.util.PaperImportUtil;
import org.jeecg.modules.teaching.util.QuestionImportUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;

import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date:   2023-06-20
 * @Version: V1.0
 */
@Service
public class ExamPaperServiceImpl extends ServiceImpl<ExamPaperMapper, ExamPaper> implements IExamPaperService {

    private static final Logger logger = LoggerFactory.getLogger(ExamPaperServiceImpl.class);
    
    @Autowired
    private IExamQuestionService examQuestionService;

    @Override
    @Transactional
    public String createPaperWithQuestions(ExamPaper examPaper, List<String> questionIds, List<Integer> scores) {
        if (examPaper == null || questionIds == null || scores == null || questionIds.size() != scores.size()) {
            throw new IllegalArgumentException("参数错误：试卷信息、题目ID列表和分数列表不能为空，且题目ID列表和分数列表长度必须相同");
        }

        // 构建试卷内容JSON
        JSONObject contentJson = new JSONObject();
        JSONArray questionsArray = new JSONArray();

        for (int i = 0; i < questionIds.size(); i++) {
            JSONObject questionJson = new JSONObject();
            questionJson.put("questionId", questionIds.get(i));
            questionJson.put("score", scores.get(i));
            questionsArray.add(questionJson);
        }

        contentJson.put("questions", questionsArray);
        examPaper.setContent(contentJson.toJSONString());

        // 保存试卷
        this.save(examPaper);
        
        return examPaper.getId();
    }

    @Override
    @Transactional
    public Map<String, Object> importPaperFromFile(MultipartFile file, String author, Map<String, Object> metadata) {
        return importPaperFromFile(file, author, metadata, false);
    }

    /**
     * 预览试卷导入（只检测不实际导入）
     * @param file 文本文件
     * @param author 作者
     * @param metadata 额外的元数据参数
     * @return 预览结果，包含检测统计信息
     */
    @Override
    public Map<String, Object> previewPaperFromFile(MultipartFile file, String author, Map<String, Object> metadata) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }

            // 解析试卷内容，但不实际导入
            PaperImportUtil.ImportResult importResult = PaperImportUtil.parsePaperText(content.toString());

            if (importResult == null || importResult.getPaperInfo() == null) {
                result.put("success", false);
                result.put("message", "试卷解析失败，请检查格式是否正确");
                return result;
            }

            List<ExamQuestion> questions = importResult.getQuestions();
            if (questions.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件中没有找到有效的题目");
                return result;
            }

            // 首先检测文件内部重复
            Map<String, Object> internalDuplicateResult = checkInternalDuplicates(questions);
            boolean hasInternalDuplicates = (Boolean) internalDuplicateResult.get("hasInternalDuplicates");

            if (hasInternalDuplicates) {
                result.put("success", false);
                result.put("hasInternalDuplicates", true);
                result.put("internalDuplicateInfo", internalDuplicateResult);
                result.put("message", "检测到文件内部存在重复题目！请检查文件内容，确保每道题目只出现一次。");
                return result;
            }

            // 统计信息
            int totalQuestions = questions.size();
            int newQuestionsCount = 0;
            int duplicateQuestionsCount = 0;
            int similarQuestionsCount = 0;

            // 检测每道题目的状态（但不保存）
            for (ExamQuestion question : questions) {
                Map<String, Object> duplicateCheckResult = examQuestionService.checkDuplicateBeforeAdd(question);

                if (duplicateCheckResult.containsKey("exactDuplicate") && (Boolean) duplicateCheckResult.get("exactDuplicate")) {
                    duplicateQuestionsCount++;
                } else if (duplicateCheckResult.containsKey("similarQuestions")) {
                    @SuppressWarnings("unchecked")
                    List<QuestionImportUtil.SimilarQuestion> similar =
                            (List<QuestionImportUtil.SimilarQuestion>) duplicateCheckResult.get("similarQuestions");
                    if (!similar.isEmpty()) {
                        similarQuestionsCount++;
                    } else {
                        newQuestionsCount++;
                    }
                } else {
                    newQuestionsCount++;
                }
            }

            // 构建预览结果
            result.put("success", true);
            result.put("totalQuestions", totalQuestions);
            result.put("newQuestions", newQuestionsCount);
            result.put("duplicateQuestions", duplicateQuestionsCount);
            result.put("similarQuestions", similarQuestionsCount);

            // 试卷基本信息
            ExamPaper paperInfo = importResult.getPaperInfo();
            if (paperInfo != null) {
                result.put("paperTitle", paperInfo.getTitle());
                result.put("paperSubject", paperInfo.getSubject());
                result.put("paperLevel", paperInfo.getLevel());
                result.put("paperYear", paperInfo.getYear());
                result.put("paperType", paperInfo.getType());
            }

            // 判断是否可以直接导入（所有题目都是新的）
            boolean canDirectImport = (duplicateQuestionsCount == 0 && similarQuestionsCount == 0);
            result.put("canDirectImport", canDirectImport);

            if (canDirectImport) {
                result.put("message", String.format("预览完成！共检测到 %d 道题目，全部为新题目，可以直接导入创建试卷。", totalQuestions));
            } else {
                result.put("message", String.format("预览完成！共检测到 %d 道题目，其中新题目 %d 道，重复题目 %d 道，相似题目 %d 道。请选择导入方式。",
                    totalQuestions, newQuestionsCount, duplicateQuestionsCount, similarQuestionsCount));
            }

        } catch (Exception e) {
            logger.error("预览试卷导入失败", e);
            result.put("success", false);
            result.put("message", "预览失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 导入试卷（支持引用模式）
     * @param file 试卷文件
     * @param author 作者
     * @param metadata 元数据
     * @param allowReference 是否允许引用重复题目（true=完整模式，false=新题模式）
     * @return 导入结果
     */
    @Override
    @Transactional
    public Map<String, Object> importPaperFromFile(MultipartFile file, String author, Map<String, Object> metadata, boolean allowReference) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
            
            // 解析试卷
            PaperImportUtil.ImportResult importResult = PaperImportUtil.parsePaperText(content.toString());
            
            if (importResult == null || importResult.getPaperInfo() == null) {
                throw new RuntimeException("试卷解析失败，请检查格式是否正确");
            }
            
            // 设置试卷作者和元数据
            ExamPaper paperInfo = importResult.getPaperInfo();
            
            // 应用前端传入的元数据，如果文件中没有相应字段
            if (metadata != null) {
                // 处理标题
                if (StringUtils.isBlank(paperInfo.getTitle()) && metadata.containsKey("title")) {
                    paperInfo.setTitle((String) metadata.get("title"));
                }
                
                // 处理科目
                if (StringUtils.isBlank(paperInfo.getSubject()) && metadata.containsKey("subject")) {
                    paperInfo.setSubject((String) metadata.get("subject"));
                }
                
                // 处理级别
                if (StringUtils.isBlank(paperInfo.getLevel()) && metadata.containsKey("level")) {
                    // 获取级别并标准化格式
                    String level = (String) metadata.get("level");
                    paperInfo.setLevel(standardizeLevel(level));
                } else if (paperInfo.getLevel() != null) {
                    // 如果已有级别，也进行标准化
                    paperInfo.setLevel(standardizeLevel(paperInfo.getLevel()));
                }
                
                // 处理难度
                if (paperInfo.getDifficulty() == null && metadata.containsKey("difficulty")) {
                    paperInfo.setDifficulty((Integer) metadata.get("difficulty"));
                }
                
                // 处理类型
                if (StringUtils.isBlank(paperInfo.getType()) && metadata.containsKey("type")) {
                    paperInfo.setType((String) metadata.get("type"));
                }
                
                // 处理年份
                if (paperInfo.getYear() == null && metadata.containsKey("year")) {
                    String yearStr = (String) metadata.get("year");
                    try {
                        paperInfo.setYear(Integer.parseInt(yearStr));
                    } catch (NumberFormatException e) {
                        logger.warn("解析年份失败: " + yearStr);
                    }
                }
                
                // 处理作者
                if (StringUtils.isBlank(paperInfo.getAuthor()) && metadata.containsKey("author")) {
                    paperInfo.setAuthor((String) metadata.get("author"));
                }
                
                // 集中处理考试时长设置 - 优先级：前端参数 > 基于级别自动设置
                if (metadata.containsKey("examDuration")) {
                    // 前端指定了考试时长，优先使用
                    Integer examDuration = (Integer) metadata.get("examDuration");
                    paperInfo.setExamDuration(examDuration);
                    logger.info("使用前端指定的考试时长: " + examDuration + "分钟");
                } else if (paperInfo.getLevel() != null) {
                    // 根据级别自动设置考试时长
                    int levelNum = parseLevelToNumber(paperInfo.getLevel());
                    logger.info("根据级别自动设置考试时长 - 级别:" + paperInfo.getLevel() + ", 解析为数字:" + levelNum);
                    
                    if (levelNum >= 5 && levelNum <= 8) {
                        paperInfo.setExamDuration(180); // 5-8级: 3小时
                        logger.info("根据级别(" + paperInfo.getLevel() + ")设置考试时长为180分钟");
                    } else if (levelNum > 0) {
                        paperInfo.setExamDuration(120); // 1-4级: 2小时
                        logger.info("根据级别(" + paperInfo.getLevel() + ")设置考试时长为120分钟");
                    } else {
                        // 无法解析的级别，默认2小时
                        paperInfo.setExamDuration(120);
                        logger.info("无法解析级别(" + paperInfo.getLevel() + ")，设置默认考试时长为120分钟");
                    }
                } else {
                    // 级别为空，设置默认值
                    paperInfo.setExamDuration(120);
                    logger.info("未指定级别，设置默认考试时长为120分钟");
                }
            } else if (paperInfo.getLevel() != null) {
                // 如果没有元数据但有级别，也进行标准化并设置时长
                paperInfo.setLevel(standardizeLevel(paperInfo.getLevel()));
                logger.info("文件中包含级别信息: " + paperInfo.getLevel());
                
                // 根据级别设置考试时长
                int levelNum = parseLevelToNumber(paperInfo.getLevel());
                logger.info("根据文件级别自动设置考试时长 - 级别:" + paperInfo.getLevel() + ", 解析为数字:" + levelNum);
                
                if (levelNum >= 5 && levelNum <= 8) {
                    paperInfo.setExamDuration(180); // 5-8级: 3小时
                    logger.info("根据文件级别(" + paperInfo.getLevel() + ")设置考试时长为180分钟");
                } else if (levelNum > 0) {
                    paperInfo.setExamDuration(120); // 1-4级: 2小时
                    logger.info("根据文件级别(" + paperInfo.getLevel() + ")设置考试时长为120分钟");
                } else {
                    paperInfo.setExamDuration(120); // 默认2小时
                    logger.info("无法解析文件级别(" + paperInfo.getLevel() + ")，设置默认考试时长为120分钟");
                }
            }
            
            // 如果author参数直接提供，优先使用它
            if (StringUtils.isNotBlank(author)) {
                paperInfo.setAuthor(author);
            }
            
            // 数据验证 - 检查必要字段
            if (StringUtils.isBlank(paperInfo.getTitle())) {
                throw new RuntimeException("试卷标题不能为空，请检查【试卷标题】字段或在表单中提供");
            }
            if (StringUtils.isBlank(paperInfo.getSubject())) {
                throw new RuntimeException("所属科目不能为空，请检查【所属科目】字段或在表单中提供");
            }
            if (StringUtils.isBlank(paperInfo.getLevel())) {
                throw new RuntimeException("所属级别不能为空，请检查【所属级别】字段或在表单中提供");
            }
            
            List<ExamQuestion> questions = importResult.getQuestions();

            // 检查是否有题目
            if (questions == null || questions.isEmpty()) {
                throw new RuntimeException("未解析到任何题目，请检查试卷格式是否正确");
            }

            // 检测文件内部重复
            Map<String, Object> internalDuplicateResult = checkInternalDuplicates(questions);
            boolean hasInternalDuplicates = (Boolean) internalDuplicateResult.get("hasInternalDuplicates");

            if (hasInternalDuplicates) {
                int duplicateCount = (Integer) internalDuplicateResult.get("duplicateCount");
                int uniqueQuestions = (Integer) internalDuplicateResult.get("uniqueQuestions");

                String errorMessage = String.format(
                    "文件内部存在重复题目！共检测到 %d 道题目，其中 %d 道重复，实际只有 %d 道不同的题目。请检查文件内容，确保每道题目只出现一次。",
                    questions.size(), duplicateCount, uniqueQuestions
                );

                throw new RuntimeException(errorMessage);
            }
            
            // 检查题目元数据是否完整并应用试卷的元数据
            for (ExamQuestion question : questions) {
                if (StringUtils.isBlank(question.getSubject())) {
                    question.setSubject(paperInfo.getSubject());
                }
                if (StringUtils.isBlank(question.getLevel())) {
                    question.setLevel(paperInfo.getLevel());
                }
                if (question.getDifficulty() == null) {
                    question.setDifficulty(paperInfo.getDifficulty());
                }
                // 添加作者设置，确保题目作者与试卷作者一致
                if (StringUtils.isBlank(question.getAuthor())) {
                    question.setAuthor(paperInfo.getAuthor());
                }
                
                // 记录当前处理的题目
                if (logger.isDebugEnabled()) {
                    String debugMsg = "处理题目: type=" + question.getQuestionType() + 
                                     ", subject=" + question.getSubject() +
                                     ", level=" + question.getLevel() + 
                                     ", difficulty=" + question.getDifficulty() +
                                     ", author=" + question.getAuthor();
                    logger.debug(debugMsg);
                }
            }
            
            // 重复检测逻辑 - 支持两种模式
            List<ExamQuestion> newQuestions = new ArrayList<>();
            List<ExamQuestion> duplicateQuestions = new ArrayList<>(); // 重复题目列表
            List<Map<String, Object>> similarQuestionsList = new ArrayList<>();
            int skippedCount = 0;

            for (ExamQuestion question : questions) {
                // 进行严格的重复检测
                Map<String, Object> duplicateCheckResult = examQuestionService.checkDuplicateBeforeAdd(question);

                logger.info("题目检测结果 - 标题: {}, 精确重复: {}, 相似题目数: {}",
                    question.getTitle(),
                    duplicateCheckResult.getOrDefault("exactDuplicate", false),
                    duplicateCheckResult.containsKey("similarQuestions") ?
                        ((List<?>) duplicateCheckResult.get("similarQuestions")).size() : 0);

                if (duplicateCheckResult.containsKey("exactDuplicate") && (Boolean) duplicateCheckResult.get("exactDuplicate")) {
                    // 发现精确重复
                    if (allowReference) {
                        // 完整模式：记录重复题目，稍后引用
                        duplicateQuestions.add(question);
                        logger.info("试卷导入时发现重复题目，将引用已存在题目：{}", question.getTitle());
                    } else {
                        // 新题模式：跳过重复题目
                        skippedCount++;
                        logger.warn("试卷导入时跳过重复题目：{} (科目：{}，级别：{}，类型：{})",
                                question.getTitle(), question.getSubject(), question.getLevel(), question.getQuestionType());
                    }
                    continue;
                }

                if (duplicateCheckResult.containsKey("similarQuestions")) {
                    @SuppressWarnings("unchecked")
                    List<QuestionImportUtil.SimilarQuestion> similar =
                            (List<QuestionImportUtil.SimilarQuestion>) duplicateCheckResult.get("similarQuestions");
                    if (!similar.isEmpty()) {
                        // 发现相似题目
                        if (allowReference) {
                            // 完整模式：将相似题目视为重复题目，引用已有题目
                            duplicateQuestions.add(question);
                            logger.info("完整模式：发现相似题目，将引用已存在题目：{} (相似度: {})",
                                question.getTitle(), similar.get(0).getSimilarity());
                        } else {
                            // 新题模式：跳过相似题目
                            skippedCount++;
                            logger.warn("新题模式：跳过相似题目：{} (相似题目数: {})", question.getTitle(), similar.size());
                            Map<String, Object> similarInfo = new HashMap<>();
                            similarInfo.put("newQuestion", question);
                            similarInfo.put("similarQuestions", similar);
                            similarQuestionsList.add(similarInfo);
                        }
                        continue;
                    }
                }

                // 没有重复或相似题目，可以导入
                logger.info("添加新题目：{}", question.getTitle());
                newQuestions.add(question);
            }

            // 批量保存新题目，使用分批处理
            if (!newQuestions.isEmpty()) {
                int batchSize = 500;
                for (int i = 0; i < newQuestions.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, newQuestions.size());
                    List<ExamQuestion> batch = newQuestions.subList(i, endIndex);
                    try {
                    examQuestionService.saveBatch(batch);
                    } catch (Exception e) {
                        String errorMsg = "保存题目批次失败: " + i + " - " + endIndex;
                        logger.error(errorMsg);
                        logger.error("异常详情:", e);
                        // 记录导致失败的题目详情，方便诊断
                        for (ExamQuestion q : batch) {
                            String qErrorMsg = "题目信息: type=" + q.getQuestionType() + 
                                            ", subject=" + q.getSubject() +
                                            ", level=" + q.getLevel() + 
                                            ", difficulty=" + q.getDifficulty() + 
                                            ", title=" + StringUtils.abbreviate(q.getTitle(), 50);
                            logger.error(qErrorMsg);
                        }
                        throw e;
                    }
                }
            }
            
            // 构建试卷内容（使用新题目和已有题目的ID）
            JSONObject contentJson = new JSONObject();
            JSONArray questionsArray = new JSONArray();

            // 用于检测试卷内部重复题目的Set
            Set<String> addedQuestionIds = new HashSet<>();
            
            // 添加新题目
            for (ExamQuestion question : newQuestions) {
                // 检查试卷内部是否已添加此题目
                if (addedQuestionIds.contains(question.getId())) {
                    logger.warn("试卷内部发现重复题目，跳过：{} (ID: {})", question.getTitle(), question.getId());
                    continue;
                }

                JSONObject questionJson = new JSONObject();
                questionJson.put("questionId", question.getId());

                // 根据题目类型设置默认分数
                switch (question.getQuestionType()) {
                    case 1: // 单选题
                        questionJson.put("score", 2);
                        break;
                    case 2: // 判断题
                        questionJson.put("score", 2);
                        break;
                    case 3: // 编程题
                        questionJson.put("score", 25);
                        break;
                    default:
                        questionJson.put("score", 2);
                }

                questionsArray.add(questionJson);
                addedQuestionIds.add(question.getId());
            }

            // 添加重复题目的引用（完整模式）
            if (allowReference && !duplicateQuestions.isEmpty()) {
                for (ExamQuestion duplicateQuestion : duplicateQuestions) {
                    // 查找已存在的相同题目
                    ExamQuestion existingQuestion = findExistingQuestion(duplicateQuestion);
                    if (existingQuestion != null) {
                        // 检查试卷内部是否已添加此题目
                        if (addedQuestionIds.contains(existingQuestion.getId())) {
                            logger.warn("试卷内部发现重复题目引用，跳过：{} (ID: {})", existingQuestion.getTitle(), existingQuestion.getId());
                            continue;
                        }

                        JSONObject questionJson = new JSONObject();
                        questionJson.put("questionId", existingQuestion.getId());

                        // 根据题目类型设置默认分数
                        switch (existingQuestion.getQuestionType()) {
                            case 1: // 单选题
                                questionJson.put("score", 2);
                                break;
                            case 2: // 判断题
                                questionJson.put("score", 2);
                                break;
                            case 3: // 编程题
                                questionJson.put("score", 25);
                                break;
                            default:
                                questionJson.put("score", 2);
                        }

                        questionsArray.add(questionJson);
                        addedQuestionIds.add(existingQuestion.getId());
                        logger.info("引用已存在题目：{} (ID: {})", existingQuestion.getTitle(), existingQuestion.getId());
                    }
                }
            }
            
            // 添加相似题（选择使用已有的题目）
            if (similarQuestionsList != null && !similarQuestionsList.isEmpty()) {
                for (Map<String, Object> similarInfo : similarQuestionsList) {
                    @SuppressWarnings("unchecked")
                    List<QuestionImportUtil.SimilarQuestion> similarQuestions =
                        (List<QuestionImportUtil.SimilarQuestion>) similarInfo.get("similarQuestions");
                    
                    if (!similarQuestions.isEmpty()) {
                        // 使用第一个相似题
                        QuestionImportUtil.SimilarQuestion similarQuestion = similarQuestions.get(0);
                    JSONObject questionJson = new JSONObject();
                        questionJson.put("questionId", similarQuestion.getQuestion().getId());
                    
                    // 根据题目类型设置默认分数
                        switch (similarQuestion.getQuestion().getQuestionType()) {
                            case 1: // 单选题
                                questionJson.put("score", 2);
                                break;
                            case 2: // 判断题
                                questionJson.put("score", 2);
                                break;
                            case 3: // 编程题
                                questionJson.put("score", 25);
                                break;
                            default:
                                questionJson.put("score", 2);
                        }
                        
                    questionsArray.add(questionJson);
                    }
                }
            }
            
            // 业务逻辑检查：根据导入结果决定是否创建试卷
            int totalQuestions = importResult.getQuestions().size();
            int newQuestionsCount = newQuestions.size();

            // 在新题模式下，如果所有题目都重复，不创建试卷
            if (!allowReference && newQuestionsCount == 0) {
                result.put("success", false); // 标记为失败
                result.put("paper", null);
                result.put("imported", newQuestions);
                result.put("total", totalQuestions);
                result.put("imported_count", 0);
                result.put("similar_count", similarQuestionsList.size());
                result.put("skipped_count", skippedCount);
                result.put("error_type", "ALL_DUPLICATE");

                // 提供更友好的错误信息
                String friendlyMessage = String.format(
                    "新题模式导入失败！共检测到 %d 道题目，全部为重复或相似题目，没有新题目可导入。建议使用「完整模式」创建试卷并引用已有题目。",
                    totalQuestions
                );

                result.put("message", friendlyMessage);

                if (!similarQuestionsList.isEmpty()) {
                    result.put("similar", similarQuestionsList);
                }
                return result;
            }



            contentJson.put("questions", questionsArray);
            paperInfo.setContent(contentJson.toJSONString());

            // 试卷级别重复检测
            ExamPaper duplicatePaper = checkDuplicatePaper(paperInfo);
            if (duplicatePaper != null) {
                // 发现重复试卷，返回警告信息
                result.put("success", false);
                result.put("isDuplicatePaper", true);

                // 构建上传试卷信息（解析出的试卷信息）
                Map<String, Object> uploadedPaper = new HashMap<>();
                uploadedPaper.put("title", paperInfo.getTitle());
                uploadedPaper.put("subject", paperInfo.getSubject());
                uploadedPaper.put("level", paperInfo.getLevel());
                if (paperInfo.getYear() != null) {
                    uploadedPaper.put("year", paperInfo.getYear());
                }
                result.put("uploadedPaper", uploadedPaper);

                // 调试信息
                System.out.println("=== 重复检测调试信息 ===");
                System.out.println("paperInfo.getTitle(): " + paperInfo.getTitle());
                System.out.println("uploadedPaper: " + uploadedPaper);
                System.out.println("result中的uploadedPaper: " + result.get("uploadedPaper"));

                // 构建安全的重复试卷信息（不包含敏感ID）
                Map<String, Object> safeDuplicatePaper = new HashMap<>();
                safeDuplicatePaper.put("title", duplicatePaper.getTitle());
                safeDuplicatePaper.put("subject", duplicatePaper.getSubject());
                safeDuplicatePaper.put("level", duplicatePaper.getLevel());
                if (duplicatePaper.getYear() != null) {
                    safeDuplicatePaper.put("year", duplicatePaper.getYear());
                }
                result.put("duplicatePaper", safeDuplicatePaper);

                // 简化的错误消息
                result.put("message", "检测到重复试卷！系统中已存在相同的试卷，请检查后重新导入。");
                return result;
            }

            // 保存试卷（只有在有新题目时才保存）
            this.save(paperInfo);
            
            // 构建成功消息
            String successMessage;
            if (allowReference) {
                // 完整模式
                if (newQuestions.size() == 0) {
                    // 全部重复的情况
                    successMessage = String.format("完整模式导入成功！试卷「%s」已创建，共包含 %d 道题目，全部引用已有题目（避免重复存储）。",
                        paperInfo.getTitle(), totalQuestions);
                } else if (duplicateQuestions.size() > 0) {
                    // 部分重复的情况（包括精确重复和相似题目）
                    successMessage = String.format("完整模式导入成功！试卷「%s」已创建，共包含 %d 道题目，其中新导入 %d 道，引用已有 %d 道。",
                        paperInfo.getTitle(), totalQuestions, newQuestions.size(), duplicateQuestions.size());
                } else {
                    // 全部新题的情况
                    successMessage = String.format("完整模式导入成功！试卷「%s」已创建，共导入 %d 道全新题目。",
                        paperInfo.getTitle(), newQuestions.size());
                }
            } else {
                // 新题模式
                if (skippedCount > 0) {
                    // 部分重复的情况
                    successMessage = String.format("新题模式导入成功！试卷「%s」已创建，共导入 %d 道新题目，跳过 %d 道重复题目。",
                        paperInfo.getTitle(), newQuestions.size(), skippedCount);
                } else {
                    // 全部新题的情况
                    successMessage = String.format("新题模式导入成功！试卷「%s」已创建，共导入 %d 道全新题目。",
                        paperInfo.getTitle(), newQuestions.size());
                }
            }

            // 返回结果 - 使用标准的统计字段名
            result.put("success", true); // 标记为成功
            result.put("message", successMessage);
            result.put("paper", paperInfo);
            result.put("imported", newQuestions); // 新导入的题目列表
            result.put("total", importResult.getQuestions().size()); // 总解析题目数
            result.put("imported_count", newQuestions.size()); // 成功导入的题目数
            result.put("similar_count", allowReference ? 0 : similarQuestionsList.size()); // 相似题目数（新题模式才有）
            result.put("skipped_count", skippedCount); // 跳过的题目数
            result.put("referenced_count", allowReference ? duplicateQuestions.size() : 0); // 引用的题目数
            result.put("allow_reference", allowReference); // 是否允许引用模式

            if (!similarQuestionsList.isEmpty()) {
                result.put("similar", similarQuestionsList);
            }
            return result;
            
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException("导入试卷失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> importPaperFromFile(MultipartFile file, String author) {
        // 调用带元数据的方法，不传递额外元数据
        return importPaperFromFile(file, author, null);
    }

    @Override
    public String exportPaperToText(String paperId) {
        // 获取试卷信息
        ExamPaper paper = this.getById(paperId);
        if (paper == null) {
            throw new RuntimeException("试卷不存在");
        }
        
        // 获取试卷的所有题目
        List<Map<String, Object>> paperQuestions = getPaperQuestions(paperId);
        if (paperQuestions == null || paperQuestions.isEmpty()) {
            throw new RuntimeException("试卷中没有题目");
        }
        
        // 优化：使用StringBuilder而不是String拼接，提高性能
        StringBuilder content = new StringBuilder(10240); // 预分配足够大的初始容量
        
        // 添加试卷元数据
        content.append("【试卷标题】").append(paper.getTitle()).append("\n");
        content.append("【所属科目】").append(paper.getSubject()).append("\n");
        content.append("【所属级别】").append(paper.getLevel()).append("\n");
        content.append("【难度】").append(getDifficultyText(paper.getDifficulty())).append("\n");
        content.append("【类型】").append(paper.getType()).append("\n");
        content.append("【年份】").append(paper.getYear()).append("\n\n");
        
        // 优化：按题型分组题目，使用并行处理
        Map<Integer, List<Map<String, Object>>> questionsByType = paperQuestions.stream()
                .collect(Collectors.groupingBy(q -> ((ExamQuestion)q.get("question")).getQuestionType()));
        
        // 添加单选题
        processSingleChoiceQuestions(content, questionsByType.getOrDefault(1, new ArrayList<>()));
        
        // 添加判断题
        processJudgmentQuestions(content, questionsByType.getOrDefault(2, new ArrayList<>()));
        
        // 添加编程题
        processProgrammingQuestions(content, questionsByType.getOrDefault(3, new ArrayList<>()));
        
        return content.toString();
    }

    @Override
    public List<Map<String, Object>> getPaperQuestions(String paperId) {
        // 获取试卷信息
        ExamPaper paper = this.getById(paperId);
        if (paper == null || StringUtils.isBlank(paper.getContent())) {
            return new ArrayList<>();
        }
        
        // 解析试卷内容
        JSONObject contentJson = JSONObject.parseObject(paper.getContent());
        JSONArray questionsArray = contentJson.getJSONArray("questions");
        
        if (questionsArray == null || questionsArray.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 收集题目ID和分数
        List<String> questionIds = new ArrayList<>();
        Map<String, Integer> questionScores = new HashMap<>();
        
        for (int i = 0; i < questionsArray.size(); i++) {
            JSONObject questionObj = questionsArray.getJSONObject(i);
            String questionId = questionObj.getString("questionId");
            Integer score = questionObj.getInteger("score");
            
            questionIds.add(questionId);
            questionScores.put(questionId, score);
        }
        
        // 批量查询题目信息
        List<ExamQuestion> questions = (List<ExamQuestion>) examQuestionService.listByIds(questionIds);
        
        // 组装结果
        List<Map<String, Object>> result = new ArrayList<>();
        for (ExamQuestion question : questions) {
            Map<String, Object> map = new HashMap<>();
            map.put("question", question);
            map.put("score", questionScores.getOrDefault(question.getId(), 0));
            result.add(map);
        }
        
        // 按题目类型和ID排序
        result.sort((a, b) -> {
            ExamQuestion qa = (ExamQuestion) a.get("question");
            ExamQuestion qb = (ExamQuestion) b.get("question");
            
            if (qa.getQuestionType().equals(qb.getQuestionType())) {
                return qa.getId().compareTo(qb.getId());
            } else {
                return qa.getQuestionType().compareTo(qb.getQuestionType());
            }
        });
        
        return result;
    }
    
    /**
     * 获取难度文本表示
     */
    private String getDifficultyText(Integer difficulty) {
        if (difficulty == null) {
            return "简单";
        }
        switch (difficulty) {
            case 1:
                return "简单";
            case 2:
                return "中等";
            case 3:
                return "困难";
            default:
                return "未知";
        }
    }
    
    /**
     * 处理单选题导出
     * @param content 内容构建器
     * @param questions 题目列表
     */
    private void processSingleChoiceQuestions(StringBuilder content, List<Map<String, Object>> questions) {
        if (questions.isEmpty()) {
            return;
        }
        
        content.append("【一、单选题】\n\n");
        
        for (int i = 0; i < questions.size(); i++) {
            Map<String, Object> qMap = questions.get(i);
            ExamQuestion question = (ExamQuestion) qMap.get("question");
            
            content.append("【").append(i + 1).append(".】").append(question.getTitle()).append("\n");
            
            // 解析JSON内容获取选项和答案
            JSONObject jsonObject = JSONObject.parseObject(question.getContent());
            List<String> options = jsonObject.getJSONArray("options").toJavaList(String.class);
            String answer = jsonObject.getString("answer");
            String analysis = jsonObject.getString("analysis");
            
            // 添加选项
            String[] optionLabels = {"A", "B", "C", "D", "E", "F", "G", "H"};
            for (int j = 0; j < options.size(); j++) {
                content.append("【").append(optionLabels[j]).append(". 】").append(options.get(j)).append("\n");
            }
            
            // 添加答案和解析
            content.append("【答案】").append(answer).append("\n");
            if (analysis != null && !analysis.isEmpty()) {
                content.append("【解析】").append(analysis).append("\n");
            }
            content.append("\n");
        }
    }
    
    /**
     * 处理判断题导出
     * @param content 内容构建器
     * @param questions 题目列表
     */
    private void processJudgmentQuestions(StringBuilder content, List<Map<String, Object>> questions) {
        if (questions.isEmpty()) {
            return;
        }
        
        content.append("【二、判断题】\n\n");
        
        for (int i = 0; i < questions.size(); i++) {
            Map<String, Object> qMap = questions.get(i);
            ExamQuestion question = (ExamQuestion) qMap.get("question");
            
            content.append("【").append(i + 1).append(".】").append(question.getTitle()).append("\n");
            
            // 解析JSON内容获取答案
            JSONObject jsonObject = JSONObject.parseObject(question.getContent());
            String answer = jsonObject.getString("answer");
            String analysis = jsonObject.getString("analysis");
            
            // 将T/F转换为正确/错误
            String displayAnswer = "T".equals(answer) ? "正确" : "错误";
            content.append("【答案】").append(displayAnswer).append("\n");
            
            if (analysis != null && !analysis.isEmpty()) {
                content.append("【解析】").append(analysis).append("\n");
            }
            content.append("\n");
        }
    }
    
    /**
     * 处理编程题导出
     * @param content 内容构建器
     * @param questions 题目列表
     */
    private void processProgrammingQuestions(StringBuilder content, List<Map<String, Object>> questions) {
        if (questions.isEmpty()) {
            return;
        }
        
        content.append("【三、编程题】\n\n");
        
        for (int i = 0; i < questions.size(); i++) {
            Map<String, Object> qMap = questions.get(i);
            ExamQuestion question = (ExamQuestion) qMap.get("question");
            
            content.append("【").append(i + 1).append(".】").append(question.getTitle()).append("\n");
            
            // 解析JSON内容
            JSONObject json = JSONObject.parseObject(question.getContent());
            
            // 添加时间和内存限制
            int timeLimit = json.getIntValue("time_limit");
            int memoryLimit = json.getIntValue("memory_limit");
            content.append("【- 时间限制：").append(timeLimit / 1000.0).append(" s - 内存限制：")
                    .append(memoryLimit).append(" MB】\n");
            
            // 添加题目描述
            content.append("【题目描述】\n").append(json.getString("description")).append("\n\n");
            
            // 添加输入输出格式
            content.append("【输入格式】\n").append(json.getString("input_format")).append("\n\n");
            content.append("【输出格式】\n").append(json.getString("output_format")).append("\n\n");
            
            // 添加样例
            List<?> rawSamples = json.getJSONArray("sample_cases").toJavaList(Map.class);
            for (int j = 0; j < rawSamples.size(); j++) {
                @SuppressWarnings("unchecked")
                Map<String, String> sample = (Map<String, String>) rawSamples.get(j);
                content.append("【输入样例 ").append(j + 1).append("】\n").append(sample.get("input")).append("\n\n");
                content.append("【输出样例 ").append(j + 1).append("】\n").append(sample.get("output")).append("\n\n");
            }
            
            // 添加提示或数据范围
            String hint = json.getString("hint");
            if (hint != null && !hint.isEmpty()) {
                content.append("【数据范围】\n").append(hint).append("\n\n");
            }
            
            content.append("\n");
        }
    }

    /**
     * 检测试卷是否重复
     * @param paperInfo 待检测的试卷信息
     * @return 如果发现重复试卷则返回已存在的试卷，否则返回null
     */
    private ExamPaper checkDuplicatePaper(ExamPaper paperInfo) {
        if (paperInfo == null) {
            return null;
        }

        // 构建查询条件：标题、科目、级别、年份都相同的试卷
        QueryWrapper<ExamPaper> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("title", paperInfo.getTitle())
                   .eq("subject", paperInfo.getSubject())
                   .eq("level", paperInfo.getLevel());

        // 如果有年份信息，也加入比较
        if (paperInfo.getYear() != null) {
            queryWrapper.eq("year", paperInfo.getYear());
        }

        // 如果有类型信息，也加入比较
        if (StringUtils.isNotBlank(paperInfo.getType())) {
            queryWrapper.eq("type", paperInfo.getType());
        }

        // 查询是否存在相同的试卷
        List<ExamPaper> existingPapers = this.list(queryWrapper);

        if (!existingPapers.isEmpty()) {
            // 返回第一个匹配的试卷
            return existingPapers.get(0);
        }

        return null;
    }

    /**
     * 将级别文本转换为数字
     * @param level 级别文本，如"六级"或"6"或"六"等
     * @return 对应的数字级别
     */
    private static int parseLevelToNumber(String level) {
        if (level == null || level.isEmpty()) {
            return 0;
        }
        
        // 记录原始输入，方便调试
        String originalLevel = level;
        logger.debug("解析级别: " + originalLevel);
        
        // 如果是纯数字，直接解析
        try {
            return Integer.parseInt(level);
        } catch (NumberFormatException e) {
            // 不是纯数字，继续处理
            logger.debug("不是纯数字，继续处理");
        }
        
        // 处理"X级"格式
        if (level.endsWith("级")) {
            String levelChar = level.substring(0, level.length() - 1);
            logger.debug("处理X级格式，提取前缀: " + levelChar);
            
            // 递归调用，处理前缀部分
            int result = parseLevelToNumber(levelChar);
            if (result > 0) {
                logger.debug("解析前缀成功: " + result);
                return result;
            }
        }
        
        // 处理中文数字
        switch (level) {
            case "一": return 1;
            case "二": return 2;
            case "三": return 3;
            case "四": return 4;
            case "五": return 5;
            case "六": return 6;
            case "七": return 7;
            case "八": return 8;
            case "九": return 9;
            default:
                // 尝试更灵活的匹配
                if (level.contains("一")) return 1;
                if (level.contains("二")) return 2;
                if (level.contains("三")) return 3;
                if (level.contains("四")) return 4;
                if (level.contains("五")) return 5;
                if (level.contains("六")) return 6;
                if (level.contains("七")) return 7;
                if (level.contains("八")) return 8;
                if (level.contains("九")) return 9;
                
                // 尝试匹配阿拉伯数字
                if (level.contains("1")) return 1;
                if (level.contains("2")) return 2;
                if (level.contains("3")) return 3;
                if (level.contains("4")) return 4;
                if (level.contains("5")) return 5;
                if (level.contains("6")) return 6;
                if (level.contains("7")) return 7;
                if (level.contains("8")) return 8;
                if (level.contains("9")) return 9;
                
                // 记录无法识别的级别
                logger.warn("无法识别的级别格式: " + originalLevel);
                return 0;
        }
    }
    
    /**
     * 将各种格式的级别标准化为"X级"格式
     * @param level 原始级别文本，如"6"、"六"、"6级"、"六级"等
     * @return 标准化的级别文本，如"六级"
     */
    private static String standardizeLevel(String level) {
        if (level == null || level.isEmpty()) {
            return level;
        }
        
        // 如果已经是"X级"格式，检查是否需要转换数字部分
        if (level.endsWith("级")) {
            String prefix = level.substring(0, level.length() - 1);
            // 如果前缀是数字，转换为中文
            try {
                int num = Integer.parseInt(prefix);
                return numberToChinese(num) + "级";
            } catch (NumberFormatException e) {
                // 前缀不是数字，可能已经是中文
                return level;
            }
        }
        
        // 如果是纯数字，转换为"X级"格式
        try {
            int num = Integer.parseInt(level);
            return numberToChinese(num) + "级";
        } catch (NumberFormatException e) {
            // 不是纯数字，检查是否是单个中文数字
            switch (level) {
                case "一": return "一级";
                case "二": return "二级";
                case "三": return "三级";
                case "四": return "四级";
                case "五": return "五级";
                case "六": return "六级";
                case "七": return "七级";
                case "八": return "八级";
                case "九": return "九级";
                default: return level; // 无法识别，返回原始值
            }
        }
    }
    
    /**
     * 将数字转换为中文数字
     * @param num 数字
     * @return 中文数字
     */
    private static String numberToChinese(int num) {
        if (num <= 0 || num > 9) {
            return String.valueOf(num); // 超出范围，返回原始数字
        }
        
        switch (num) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "七";
            case 8: return "八";
            case 9: return "九";
            default: return String.valueOf(num);
        }
    }

    @Override
    public String autoFormatPaperTemplate(MultipartFile file, String subject, String level, Integer difficulty, String type, String year) throws Exception {
        logger.info("=== 开始自动格式化试卷 ===");
        logger.info("文件名: {}, 科目: {}, 级别: {}, 难度: {}, 类型: {}, 年份: {}", file.getOriginalFilename(), subject, level, difficulty, type, year);

        // 读取文件内容
        String content;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            content = reader.lines().collect(Collectors.joining("\n"));
        }

        logger.info("读取到的文件内容长度: {}", content != null ? content.length() : 0);
        if (content != null && content.length() > 0) {
            logger.info("文件内容前100字符: {}", content.substring(0, Math.min(100, content.length())));
        } else {
            logger.info("文件内容为空");
        }

        if (StringUtils.isBlank(content)) {
            throw new RuntimeException("文件内容为空");
        }

        // 构建格式化后的内容
        StringBuilder formattedContent = new StringBuilder();

        // 添加元数据
        formattedContent.append("【元数据】\n");
        formattedContent.append("【试卷标题】请填写试卷标题，例如：").append(subject).append(level).append("级").append(year).append("年03月\n");
        formattedContent.append("【所属科目】").append(subject).append("\n");
        formattedContent.append("【所属级别】").append(level).append("\n");
        formattedContent.append("【难度】").append(getDifficultyText(difficulty)).append("\n");
        formattedContent.append("【类型】").append(type).append("\n");
        formattedContent.append("【年份】").append(year).append("\n\n");

        // 分析并格式化题目内容
        if (content == null) {
            content = "";
        }
        String[] lines = content.split("\n");

        // 状态变量
        String currentSection = "";
        int singleChoiceCount = 0;
        int judgmentCount = 0;
        int programmingCount = 0;
        boolean waitingForQuestionContent = false;
        boolean afterDataRange = false;  // 标记是否刚处理完数据范围内容

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // 处理空行
            if (StringUtils.isBlank(line)) {
                // 如果在编程题部分且刚处理完数据范围，空行后可能是新编程题
                if ("编程题".equals(currentSection) && afterDataRange) {
                    waitingForQuestionContent = true;
                    afterDataRange = false;
                }
                continue;
            }

            // 检测题型标识
            if (line.equals("单选题")) {
                currentSection = "单选题";
                formattedContent.append("【一、单选题】\n\n");
                singleChoiceCount = 0;
                waitingForQuestionContent = true;
                continue;
            } else if (line.equals("判断题")) {
                currentSection = "判断题";
                formattedContent.append("【二、判断题】\n\n");
                judgmentCount = 0;
                waitingForQuestionContent = true;
                continue;
            } else if (line.equals("编程题")) {
                currentSection = "编程题";
                formattedContent.append("【三、编程题】\n\n");
                programmingCount = 0;
                waitingForQuestionContent = true;
                afterDataRange = false;
                continue;
            }

            // 处理不同题型的内容
            if ("单选题".equals(currentSection)) {
                if (waitingForQuestionContent && isQuestionLine(line)) {
                    // 题目内容
                    singleChoiceCount++;
                    formattedContent.append("【").append(singleChoiceCount).append(".】").append(line).append("\n");
                    waitingForQuestionContent = false;
                } else if (isOption(line)) {
                    // 选项格式化：只包裹选项标识符，不包裹内容
                    String formattedOption = formatOption(line);
                    formattedContent.append(formattedOption).append("\n");
                } else if (isAnswer(line)) {
                    // 答案
                    String answer = extractAnswer(line);
                    formattedContent.append("【答案】").append(answer).append("\n");
                } else if (isExplanation(line)) {
                    // 解析
                    String explanation = extractExplanation(line);
                    formattedContent.append("【解析】").append(explanation).append("\n\n");
                    waitingForQuestionContent = true;
                }
            } else if ("判断题".equals(currentSection)) {
                if (waitingForQuestionContent && isQuestionLine(line)) {
                    // 题目内容
                    judgmentCount++;
                    formattedContent.append("【").append(judgmentCount).append(".】").append(line).append("\n");
                    waitingForQuestionContent = false;
                } else if (isAnswer(line)) {
                    // 答案
                    String answer = extractAnswer(line);
                    formattedContent.append("【答案】").append(answer).append("\n");
                } else if (isExplanation(line)) {
                    // 解析
                    String explanation = extractExplanation(line);
                    formattedContent.append("【解析】").append(explanation).append("\n\n");
                    waitingForQuestionContent = true;
                }
            } else if ("编程题".equals(currentSection)) {
                // 检测是否是新的编程题开始
                if (isProgrammingQuestionTitle(line, waitingForQuestionContent)) {
                    // 新编程题标题
                    programmingCount++;
                    // 如果不是第一道编程题，在前面添加空行
                    if (programmingCount > 1) {
                        formattedContent.append("\n");
                    }
                    formattedContent.append("【").append(programmingCount).append(".】").append(line).append("\n");
                    waitingForQuestionContent = false;
                    afterDataRange = false;
                } else if (line.startsWith("- 时间限制：") || line.startsWith("-时间限制：")) {
                    // 时间内存限制
                    formattedContent.append("【").append(line).append("】\n");
                } else if (line.equals("题目描述")) {
                    formattedContent.append("【题目描述】\n");
                } else if (line.equals("输入格式")) {
                    formattedContent.append("【输入格式】\n");
                } else if (line.equals("输出格式")) {
                    formattedContent.append("【输出格式】\n");
                } else if (line.equals("输入样例") || line.startsWith("输入样例")) {
                    // 处理输入样例，可能有编号
                    if (line.equals("输入样例")) {
                        formattedContent.append("【输入样例 1】\n");
                    } else {
                        // 提取样例编号，如"输入样例1" -> "【输入样例 1】"
                        String sampleNumber = line.replace("输入样例", "").trim();
                        if (sampleNumber.isEmpty()) {
                            sampleNumber = "1";
                        }
                        formattedContent.append("【输入样例 ").append(sampleNumber).append("】\n");
                    }
                } else if (line.equals("输出样例") || line.startsWith("输出样例")) {
                    // 处理输出样例，可能有编号
                    if (line.equals("输出样例")) {
                        formattedContent.append("【输出样例 1】\n");
                    } else {
                        // 提取样例编号，如"输出样例 1" -> "【输出样例 1】"
                        String sampleNumber = line.replace("输出样例", "").trim();
                        if (sampleNumber.isEmpty()) {
                            sampleNumber = "1";
                        }
                        formattedContent.append("【输出样例 ").append(sampleNumber).append("】\n");
                    }
                } else if (line.equals("数据范围")) {
                    formattedContent.append("【数据范围】\n");
                } else if (line.startsWith("对于所有测试点") || line.contains("保证")) {
                    // 数据范围的具体内容，不编号
                    formattedContent.append(line).append("\n");
                    // 标记刚处理完数据范围内容
                    afterDataRange = true;
                } else {
                    // 普通内容，不编号
                    formattedContent.append(line).append("\n");
                }
            } else {
                // 如果没有明确的题型标识，尝试智能识别
                if (isQuestionLine(line)) {
                    // 可能是题目，但需要判断是什么类型
                    if (isOption(lines[Math.min(i + 1, lines.length - 1)].trim())) {
                        // 下一行是选项，说明这是单选题
                        if (currentSection.isEmpty()) {
                            currentSection = "单选题";
                            formattedContent.append("【一、单选题】\n\n");
                        }
                        singleChoiceCount++;
                        formattedContent.append("【").append(singleChoiceCount).append(".】").append(line).append("\n");
                    } else {
                        // 可能是判断题或编程题
                        if (line.contains("？") || line.contains("?")) {
                            // 包含问号，可能是判断题
                            if (currentSection.isEmpty()) {
                                currentSection = "判断题";
                                formattedContent.append("【二、判断题】\n\n");
                            }
                            judgmentCount++;
                            formattedContent.append("【").append(judgmentCount).append(".】").append(line).append("\n");
                        } else {
                            // 可能是编程题
                            if (currentSection.isEmpty()) {
                                currentSection = "编程题";
                                formattedContent.append("【三、编程题】\n\n");
                            }
                            programmingCount++;
                            if (programmingCount > 1) {
                                formattedContent.append("\n");
                            }
                            formattedContent.append("【").append(programmingCount).append(".】").append(line).append("\n");
                        }
                    }
                } else {
                    // 普通内容行
                    formattedContent.append(line).append("\n");
                }
            }
        }

        String result = formattedContent.toString();
        logger.info("=== 格式化完成 ===");
        logger.info("格式化结果长度: {}", result.length());
        if (result.length() > 0) {
            logger.info("格式化结果前200字符: {}", result.substring(0, Math.min(200, result.length())));
        } else {
            logger.info("格式化结果为空");
        }

        return result;
    }



    /**
     * 判断是否为编程题标题
     * @param line 当前行内容
     * @param waitingForQuestionContent 是否正在等待新题目内容（即刚处理完数据范围且遇到空行后）
     */
    private boolean isProgrammingQuestionTitle(String line, boolean waitingForQuestionContent) {
        // 如果正在等待新题目内容，且当前行符合题目标题特征，则认为是新编程题
        if (waitingForQuestionContent) {
            return isPotentialProgrammingTitle(line);
        }

        // 如果不在等待状态，则不是新题目标题
        return false;
    }

    /**
     * 判断一行内容是否可能是编程题标题
     */
    private boolean isPotentialProgrammingTitle(String line) {
        // 排除已知的格式标识符
        if (line.equals("题目描述") || line.equals("输入格式") || line.equals("输出格式") ||
            line.equals("输入样例") || line.equals("输出样例") || line.equals("数据范围") ||
            line.startsWith("- 时间限制：") || line.startsWith("-时间限制：") ||
            line.startsWith("【") || line.startsWith("输入样例") || line.startsWith("输出样例")) {
            return false;
        }

        // 排除纯数字行（通常是样例数据）
        if (line.trim().matches("^\\d+$")) {
            return false;
        }

        // 排除数据范围描述行
        if (line.startsWith("对于所有测试点") || line.contains("保证") || line.contains("≤") || line.contains("≥")) {
            return false;
        }

        // 排除题目描述内容（通常包含具体的描述性语句）
        if (line.contains("行，") || line.contains("个整数") || line.contains("表示") ||
            line.contains("需要") || line.contains("计算") || line.contains("输出") ||
            line.contains("输入") || line.contains("第一行") || line.contains("第二行") ||
            line.contains("第三行") || line.contains("每行") || line.contains("共") ||
            line.contains("接下来") || line.contains("一个正整数") || line.contains("一个整数")) {
            return false;
        }

        // 编程题标题通常是简短的中文名称，不包含复杂的描述
        // 长度在2-20字符之间，包含中文，不包含标点符号（除了可能的中文标点）
        if (line.length() >= 2 && line.length() <= 20 && containsChinese(line) &&
            !line.contains("，") && !line.contains("。") && !line.contains("；") &&
            !line.contains("：") && !line.contains("？") && !line.contains("！")) {
            return true;
        }

        return false;
    }

    /**
     * 判断字符串是否包含中文
     */
    private boolean containsChinese(String str) {
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为题目行（包含问号或者是编程题标题）
     */
    private boolean isQuestionLine(String line) {
        // 包含问号的是题目
        if (line.contains("？") || line.contains("?")) {
            return true;
        }
        // 不包含特殊标识符的普通文本可能是编程题标题
        return !isOption(line) && !isAnswer(line) && !isExplanation(line);
    }

    /**
     * 判断是否为选项
     */
    private boolean isOption(String line) {
        return line.matches("^[A-D][.、]\\s*.*") || line.matches("^[ABCD][:：]\\s*.*");
    }

    /**
     * 判断是否为答案
     */
    private boolean isAnswer(String line) {
        return line.startsWith("答案：") || line.startsWith("答案:") || line.toLowerCase().startsWith("answer:");
    }

    /**
     * 判断是否为解析
     */
    private boolean isExplanation(String line) {
        return line.startsWith("解析：") || line.startsWith("解析:") || line.toLowerCase().startsWith("explanation:");
    }

    /**
     * 提取答案内容
     */
    private String extractAnswer(String line) {
        if (line.startsWith("答案：")) {
            return line.substring(3).trim();
        } else if (line.startsWith("答案:")) {
            return line.substring(3).trim();
        } else if (line.toLowerCase().startsWith("answer:")) {
            return line.substring(7).trim();
        }
        return line;
    }

    /**
     * 提取解析内容
     */
    private String extractExplanation(String line) {
        if (line.startsWith("解析：")) {
            return line.substring(3).trim();
        } else if (line.startsWith("解析:")) {
            return line.substring(3).trim();
        } else if (line.toLowerCase().startsWith("explanation:")) {
            return line.substring(12).trim();
        }
        return line;
    }

    /**
     * 格式化选项：只包裹选项标识符部分
     */
    private String formatOption(String line) {
        // 匹配 "A. 内容" 或 "A、内容" 格式
        if (line.matches("^[A-D][.、]\\s*.*")) {
            // 找到选项标识符的结束位置
            int separatorIndex = -1;
            if (line.contains(". ")) {
                separatorIndex = line.indexOf(". ") + 2;
            } else if (line.contains(".")) {
                separatorIndex = line.indexOf(".") + 1;
            } else if (line.contains("、")) {
                separatorIndex = line.indexOf("、") + 1;
            }

            if (separatorIndex > 0) {
                String optionLabel = line.substring(0, separatorIndex);
                String optionContent = line.substring(separatorIndex).trim();
                return "【" + optionLabel + "】" + optionContent;
            }
        }

        // 匹配 "A: 内容" 或 "A：内容" 格式
        if (line.matches("^[ABCD][:：]\\s*.*")) {
            int separatorIndex = -1;
            if (line.contains(": ")) {
                separatorIndex = line.indexOf(": ") + 2;
            } else if (line.contains(":")) {
                separatorIndex = line.indexOf(":") + 1;
            } else if (line.contains("：")) {
                separatorIndex = line.indexOf("：") + 1;
            }

            if (separatorIndex > 0) {
                String optionLabel = line.substring(0, separatorIndex);
                String optionContent = line.substring(separatorIndex).trim();
                return "【" + optionLabel + "】" + optionContent;
            }
        }

        // 如果格式不匹配，返回原始内容
        return line;
    }

    /**
     * 检测文件内部重复题目
     * @param questions 题目列表
     * @return 检测结果
     */
    private Map<String, Object> checkInternalDuplicates(List<ExamQuestion> questions) {
        Map<String, Object> result = new HashMap<>();
        Map<String, List<ExamQuestion>> duplicateGroups = new HashMap<>();
        Set<String> processedKeys = new HashSet<>();
        boolean hasInternalDuplicates = false;

        for (int i = 0; i < questions.size(); i++) {
            ExamQuestion question1 = questions.get(i);
            String key1 = generateQuestionKey(question1);

            if (processedKeys.contains(key1)) {
                continue; // 已经处理过的题目
            }

            List<ExamQuestion> duplicateGroup = new ArrayList<>();
            duplicateGroup.add(question1);

            // 查找与当前题目重复的其他题目
            for (int j = i + 1; j < questions.size(); j++) {
                ExamQuestion question2 = questions.get(j);
                String key2 = generateQuestionKey(question2);

                if (key1.equals(key2)) {
                    duplicateGroup.add(question2);
                    hasInternalDuplicates = true;
                }
            }

            if (duplicateGroup.size() > 1) {
                duplicateGroups.put(key1, duplicateGroup);
            }

            processedKeys.add(key1);
        }

        result.put("hasInternalDuplicates", hasInternalDuplicates);
        result.put("duplicateGroups", duplicateGroups);
        result.put("totalQuestions", questions.size());
        result.put("uniqueQuestions", processedKeys.size());
        result.put("duplicateCount", questions.size() - processedKeys.size());

        return result;
    }

    /**
     * 生成题目的唯一标识键
     */
    private String generateQuestionKey(ExamQuestion question) {
        return String.format("%s|%s|%s|%d",
            question.getTitle().trim(),
            question.getSubject(),
            question.getLevel(),
            question.getQuestionType());
    }

    /**
     * 查找已存在的相同题目
     */
    private ExamQuestion findExistingQuestion(ExamQuestion question) {
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("title", question.getTitle())
                   .eq("subject", question.getSubject())
                   .eq("level", question.getLevel())
                   .eq("question_type", question.getQuestionType());

        List<ExamQuestion> existingQuestions = examQuestionService.list(queryWrapper);
        return existingQuestions.isEmpty() ? null : existingQuestions.get(0);
    }
}