package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.teaching.entity.TeachingCourse;
import org.jeecg.modules.teaching.entity.TeachingCourseUnit;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizRecord;
import org.jeecg.modules.teaching.entity.TeachingStudentCoinRecord;
import org.jeecg.modules.teaching.mapper.TeachingObjectiveQuizDetailMapper;
import org.jeecg.modules.teaching.mapper.TeachingObjectiveQuizRecordMapper;
import org.jeecg.modules.teaching.service.ITeachingCourseService;
import org.jeecg.modules.teaching.service.ITeachingCourseUnitService;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuizRecordService;
import org.jeecg.modules.teaching.service.ITeachingStudentCoinRecordService;
import org.jeecg.modules.teaching.service.ITeachingStudentCoinService;
import org.jeecg.modules.teaching.vo.ObjectiveQuizSaveVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 客观题答题记录 Service实现
 */
@Slf4j
@Service
public class TeachingObjectiveQuizRecordServiceImpl extends ServiceImpl<TeachingObjectiveQuizRecordMapper, TeachingObjectiveQuizRecord> implements ITeachingObjectiveQuizRecordService {
    
    @Autowired
    private TeachingObjectiveQuizDetailMapper detailMapper;
    
    @Autowired
    private ITeachingStudentCoinService teachingStudentCoinService;
    
    @Autowired
    private ITeachingStudentCoinRecordService teachingStudentCoinRecordService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveQuizRecord(ObjectiveQuizSaveVO quizSaveVO) {
        try {
            // 保存答题记录
            TeachingObjectiveQuizRecord record = quizSaveVO.getRecord();
            
            // 设置ID和创建时间
            String recordId = UUID.randomUUID().toString().replace("-", "");
            record.setId(recordId);
            record.setCreateTime(new Date());
            
            // 确保答题时间已设置
            if (record.getQuizTime() == null) {
                record.setQuizTime(new Date());
            }
            
            // 获取当前登录用户信息
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser != null) {
                record.setCreateBy(loginUser.getUsername());
            }
            
            // 插入记录
            boolean saveResult = save(record);
            if (!saveResult) {
                return false;
            }
            
            // 保存答题详情
            List<TeachingObjectiveQuizDetail> detailList = quizSaveVO.getDetailList();
            if (detailList != null && !detailList.isEmpty()) {
                for (TeachingObjectiveQuizDetail detail : detailList) {
                    // 设置记录ID和ID
                    detail.setRecordId(recordId);
                    detail.setId(UUID.randomUUID().toString().replace("-", ""));
                    detail.setCreateTime(new Date());
                    
                    // 插入详情
                    detailMapper.insert(detail);
                }
            }
            
            // 完成客观题后增加金币奖励
            if (loginUser != null) {
                String userId = loginUser.getId();
                String courseId = record.getCourseId();
                String unitId = record.getUnitId();
                
                // 检查是否已经奖励过该单元的客观题金币
                boolean alreadyAwarded = checkIfAlreadyAwarded(userId, unitId);
                
                // 只有没奖励过时才给予金币
                if (!alreadyAwarded) {
                    // 查询课程名称和单元名称
                    String courseName = "";
                    String unitName = "";
                    
                    try {
                        // 查询课程信息
                        if (courseId != null && !courseId.isEmpty()) {
                            // 通过Spring上下文获取课程服务
                            ITeachingCourseService courseService = SpringContextUtils.getBean(ITeachingCourseService.class);
                            TeachingCourse course = courseService.getById(courseId);
                            if (course != null) {
                                courseName = course.getCourseName();
                            }
                        }
                        
                        // 查询单元信息
                        if (unitId != null && !unitId.isEmpty()) {
                            // 通过Spring上下文获取单元服务
                            ITeachingCourseUnitService unitService = SpringContextUtils.getBean(ITeachingCourseUnitService.class);
                            TeachingCourseUnit unit = unitService.getById(unitId);
                            if (unit != null) {
                                unitName = unit.getUnitName();
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取课程或单元信息失败", e);
                    }
                    
                    // 提取课程中的技术名称
                    String techName = extractTechName(courseName);
                    
                    // 构建带有课程和单元信息的描述
                    String description = "完成客观题作业奖励";
                    if (!techName.isEmpty() && !unitName.isEmpty()) {
                        description = String.format("完成%s-%s的客观题作业奖励", techName, unitName);
                    } else if (!techName.isEmpty()) {
                        description = String.format("完成%s的客观题作业奖励", techName);
                    } else if (!unitName.isEmpty()) {
                        description = String.format("完成%s的客观题作业奖励", unitName);
                    }
                    
                    // 给用户增加5个金币
                    teachingStudentCoinService.addUserCoin(
                        userId,             // 用户ID
                        5,                  // 增加5金币
                        2,                  // 来源：2表示客观题
                        description,        // 增加了课程和单元信息的描述
                        unitId              // 关联ID使用单元ID
                    );
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("保存答题记录失败", e);
            return false;
        }
    }
    
    /**
     * 检查是否已经奖励过该单元的客观题金币
     * 
     * @param userId 用户ID
     * @param unitId 单元ID
     * @return 是否已奖励
     */
    private boolean checkIfAlreadyAwarded(String userId, String unitId) {
        // 查询该用户是否有该单元的客观题答题记录
        QueryWrapper<TeachingObjectiveQuizRecord> quizRecordQuery = new QueryWrapper<>();
        quizRecordQuery.eq("user_id", userId)
                      .eq("unit_id", unitId)
                      .lt("create_time", new Date()); // 查询之前的记录
        
        // 统计答题次数，如果不是第一次答题，则意味着已经奖励过
        int count = (int) this.count(quizRecordQuery); // 阶段二：MyBatis-Plus 3.4.x兼容性修复
        if (count > 1) {
            return true; // 已有记录，说明之前答过题，已奖励
        }
        
        // 再检查金币奖励记录表
        QueryWrapper<TeachingStudentCoinRecord> coinRecordQuery = new QueryWrapper<>();
        coinRecordQuery.eq("user_id", userId)
                       .eq("related_id", unitId)
                       .eq("operation_type", 1) // 1表示获取金币
                       .like("description", "完成客观题作业奖励");
        
        return teachingStudentCoinRecordService.count(coinRecordQuery) > 0;
    }

    /**
     * 从课程名称中提取技术名称
     * 例如："C++代码编程课程" -> "C++"
     *      "Python人工智能编程" -> "Python"
     * 
     * @param courseName 课程名称
     * @return 提取的技术名称
     */
    private String extractTechName(String courseName) {
        if (courseName == null || courseName.isEmpty()) {
            return "";
        }
        
        // 常见编程语言和技术名称列表
        String[] techPatterns = {"C\\+\\+", "Python", "Java", "JavaScript", "TypeScript", "PHP", "C#", "Go", "Rust", 
                               "Swift", "Kotlin", "Ruby", "HTML", "CSS", "SQL", "R", "Scala", "Perl", "Shell", 
                               "React", "Vue", "Angular", "Node.js", "Spring", "Django", "Flask", "Laravel"};
        
        // 遍历技术名称列表，查找匹配项
        for (String pattern : techPatterns) {
            if (courseName.matches(".*" + pattern + ".*")) {
                return pattern.replace("\\+\\+", "++"); // 还原C++的正则表达式
            }
        }
        
        // 如果没有匹配到预定义的技术名称，返回课程名称的前5个字符或整个名称（取短者）
        return courseName.length() > 5 ? courseName.substring(0, 5) : courseName;
    }
} 