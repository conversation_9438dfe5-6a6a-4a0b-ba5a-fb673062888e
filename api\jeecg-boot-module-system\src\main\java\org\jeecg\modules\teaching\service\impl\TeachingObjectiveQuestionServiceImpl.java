package org.jeecg.modules.teaching.service.impl;

import org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion;
import org.jeecg.modules.teaching.entity.TeachingCourse;
import org.jeecg.modules.teaching.mapper.TeachingObjectiveQuestionMapper;
import org.jeecg.modules.teaching.mapper.TeachingCourseMapper;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 客观题
 * @Author: jeecg-boot
 * @Date:   2023-05-10
 * @Version: V1.0
 */
@Service
public class TeachingObjectiveQuestionServiceImpl extends ServiceImpl<TeachingObjectiveQuestionMapper, TeachingObjectiveQuestion> implements ITeachingObjectiveQuestionService {
    
    @Autowired
    private TeachingCourseMapper teachingCourseMapper;
    
    @Override
    public List<TeachingObjectiveQuestion> queryListByUnitId(String unitId, String courseId) {
        return baseMapper.queryListByUnitId(unitId, courseId);
    }
    
    @Override
    public IPage<TeachingObjectiveQuestion> pageWithCourseName(Page<TeachingObjectiveQuestion> page, QueryWrapper<TeachingObjectiveQuestion> queryWrapper) {
        // 先使用原始方法获取分页数据
        IPage<TeachingObjectiveQuestion> pageList = this.page(page, queryWrapper);
        List<TeachingObjectiveQuestion> records = pageList.getRecords();
        
        if (records != null && !records.isEmpty()) {
            // 收集所有非空的课程ID
            List<String> courseIds = records.stream()
                .map(TeachingObjectiveQuestion::getCourseId)
                .filter(id -> id != null && !id.isEmpty())
                .distinct()
                .collect(Collectors.toList());
            
            if (!courseIds.isEmpty()) {
                // 查询这些课程ID对应的课程信息
                QueryWrapper<TeachingCourse> courseQueryWrapper = new QueryWrapper<>();
                courseQueryWrapper.in("id", courseIds);
                List<TeachingCourse> courses = teachingCourseMapper.selectList(courseQueryWrapper);
                
                // 创建课程ID到课程名称的映射
                Map<String, String> courseIdToNameMap = courses.stream()
                    .collect(Collectors.toMap(TeachingCourse::getId, TeachingCourse::getCourseName));
                
                // 为每个客观题设置课程名称
                records.forEach(question -> {
                    if (question.getCourseId() != null && courseIdToNameMap.containsKey(question.getCourseId())) {
                        question.setCourseName(courseIdToNameMap.get(question.getCourseId()));
                    }
                });
            }
        }
        
        return pageList;
    }
} 