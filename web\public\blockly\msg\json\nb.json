{"@metadata": {"authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "SuperPotato", "아라"]}, "VARIABLES_DEFAULT_NAME": "element", "TODAY": "I dag", "DUPLICATE_BLOCK": "duplikat", "ADD_COMMENT": "Legg til kommentar", "REMOVE_COMMENT": "<PERSON><PERSON><PERSON> kommentar", "DUPLICATE_COMMENT": "Du<PERSON><PERSON><PERSON> kommentar", "EXTERNAL_INPUTS": "Eksterne kilder", "INLINE_INPUTS": "Interne kilder", "DELETE_BLOCK": "Slett blokk", "DELETE_X_BLOCKS": "Slett %1 blokker", "DELETE_ALL_BLOCKS": "Slett alle %1 blokker?", "CLEAN_UP": "Rydd opp Blocks", "COLLAPSE_BLOCK": "Skjul blokk", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON><PERSON> blokker", "EXPAND_BLOCK": "<PERSON><PERSON><PERSON> blo<PERSON>k", "EXPAND_ALL": "<PERSON><PERSON><PERSON>", "DISABLE_BLOCK": "<PERSON>ak<PERSON>r blokk", "ENABLE_BLOCK": "Aktiver blokk", "HELP": "<PERSON><PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON>", "REDO": "<PERSON><PERSON><PERSON><PERSON> om", "CHANGE_VALUE_TITLE": "<PERSON><PERSON> verdi:", "RENAME_VARIABLE": "Gi nytt navn til variabel…", "RENAME_VARIABLE_TITLE": "<PERSON>re navnet til alle '%1' variabler til:", "NEW_VARIABLE": "<PERSON><PERSON><PERSON><PERSON> variabel…", "NEW_STRING_VARIABLE": "<PERSON><PERSON><PERSON><PERSON> streng<PERSON> …", "NEW_NUMBER_VARIABLE": "<PERSON><PERSON><PERSON><PERSON> …", "NEW_COLOUR_VARIABLE": "<PERSON><PERSON><PERSON><PERSON> farge<PERSON> …", "NEW_VARIABLE_TYPE_TITLE": "Ny variabeltype:", "NEW_VARIABLE_TITLE": "Nytt variabelnavn:", "VARIABLE_ALREADY_EXISTS": "En variabel med navn «%1» finnes allerede.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "En variabel med navn «%1» finnes allerede for en annen type: «%2».", "DELETE_VARIABLE_CONFIRMATION": "Slett %1 bruk av variabelen «%2»?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Kan ikke slette variabelen «%1» fordi den er del av definisjonen for funksjonen «%2»", "DELETE_VARIABLE": "Slett variabelen «%1»", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Velg en farge fra paletten.", "COLOUR_RANDOM_TITLE": "til<PERSON><PERSON> farge", "COLOUR_RANDOM_TOOLTIP": "Velg en tilfeldig farge.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "farge med", "COLOUR_RGB_RED": "rød", "COLOUR_RGB_GREEN": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_RGB_BLUE": "blå", "COLOUR_RGB_TOOLTIP": "Lag en farge med angitt verdi av rød, grø<PERSON> og blå. Alle verdier må være mellom 0 og 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "blande", "COLOUR_BLEND_COLOUR1": "farge 1", "COLOUR_BLEND_COLOUR2": "farge 2", "COLOUR_BLEND_RATIO": "forhold", "COLOUR_BLEND_TOOLTIP": "Blander to farger sammen med et gitt forhold (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "gjenta %1 ganger", "CONTROLS_REPEAT_INPUT_DO": "gjø<PERSON>", "CONTROLS_REPEAT_TOOLTIP": "Gjenta noen instruksjoner flere ganger.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "gjenta mens", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "gjenta til", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "<PERSON><PERSON> lenge et utsagn stemmer, utfør noen instruksjoner.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "<PERSON><PERSON> lenge et utsagn ikke stemmer, gjør noen instruksjoner.", "CONTROLS_FOR_TOOLTIP": "Ha variabel \"%1\" ta verdiene fra start nummer til slutt nummer, telle med spesifisert intervall og lag de spesifiserte blokkene.", "CONTROLS_FOR_TITLE": "tell med %1 fra %2 til %3 med %4", "CONTROLS_FOREACH_TITLE": "for hvert element %1 i listen %2", "CONTROLS_FOREACH_TOOLTIP": "For hvert element i en liste, angi variabelen '%1' til elementet, og deretter lag noen setninger.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "bryt ut av løkken", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "fortsett med neste gjentakelse av løkken", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Bryt ut av den gjeldende løkken.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "<PERSON><PERSON> over resten av denne løkken og fortsett med neste gjentakelse.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Advarsel: <PERSON>ne blokken kan kun brukes innenfor en løkke.", "CONTROLS_IF_TOOLTIP_1": "<PERSON><PERSON> dette er sant, så gjør følgende.", "CONTROLS_IF_TOOLTIP_2": "<PERSON><PERSON> dette er sant, så utfør den første blokken av instruksjoner. <PERSON><PERSON> i<PERSON>, utfør den andre blokken.", "CONTROLS_IF_TOOLTIP_3": "<PERSON>vis det første stemmer, så utfør den første blokken av instruksjoner. <PERSON><PERSON>, hvis det andre stemmer, utfør den andre blokken av instruksjoner.", "CONTROLS_IF_TOOLTIP_4": "<PERSON>vis den første verdien er sann, så utfør den første blokken med setninger. <PERSON><PERSON>, hvis den andre verdien er sann, så utfør den andre blokken med setninger. Hvis ingen av verdiene er sanne, så utfør den siste blokken med setninger.", "CONTROLS_IF_MSG_IF": "hvis", "CONTROLS_IF_MSG_ELSEIF": "el<PERSON> hvis", "CONTROLS_IF_MSG_ELSE": "ellers", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON> til, fjern eller flytt seksjoner i denne hvis-blokken.", "CONTROLS_IF_ELSEIF_TOOLTIP": "<PERSON><PERSON> til en betingelse til hvis blokken.", "CONTROLS_IF_ELSE_TOOLTIP": "<PERSON>gg til hva som skal skje hvis de andre ikke slår til.", "IOS_OK": "OK", "IOS_CANCEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IOS_ERROR": "<PERSON><PERSON>", "IOS_PROCEDURES_INPUTS": "PARAMETERE", "IOS_PROCEDURES_ADD_INPUT": "+ Legg til parameter", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Tillat kommandoer", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "<PERSON><PERSON> funk<PERSON>jonen har duplikatparametere.", "IOS_VARIABLES_ADD_VARIABLE": "+ Legg til variabel", "IOS_VARIABLES_ADD_BUTTON": "Legg til", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON> navn", "IOS_VARIABLES_DELETE_BUTTON": "<PERSON><PERSON>", "IOS_VARIABLES_VARIABLE_NAME": "Variabelnavn", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Du kan ikke bruke et tomt variabelnavn.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "Returnerer sann hvis begge inputene er like h<PERSON>and<PERSON>.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Returnerer sant hvis begge argumentene er ulike hverandre.", "LOGIC_COMPARE_TOOLTIP_LT": "Returnerer sant hvis det første argumentet er mindre enn det andre argumentet.", "LOGIC_COMPARE_TOOLTIP_LTE": "Returnerer sant hvis det første argumentet er mindre enn eller likt det andre argumentet.", "LOGIC_COMPARE_TOOLTIP_GT": "Returnerer sant hvis det første argumentet er større enn den andre argumentet.", "LOGIC_COMPARE_TOOLTIP_GTE": "Returnerer sant hvis det første argumentet er større enn eller likt det andre argumentet.", "LOGIC_OPERATION_TOOLTIP_AND": "Returnerer sant hvis begge argumentene er sanne.", "LOGIC_OPERATION_AND": "og", "LOGIC_OPERATION_TOOLTIP_OR": "Returnerer sant hvis minst ett av argumentene er sant.", "LOGIC_OPERATION_OR": "eller", "LOGIC_NEGATE_TITLE": "ikke %1", "LOGIC_NEGATE_TOOLTIP": "Returnerer sant hvis argumentet er usant. Returnerer usant hvis argumentet er sant.", "LOGIC_BOOLEAN_TRUE": "sann", "LOGIC_BOOLEAN_FALSE": "usann", "LOGIC_BOOLEAN_TOOLTIP": "Returnerer enten sann eller usann.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "null", "LOGIC_NULL_TOOLTIP": "Returnerer null.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "hvis sant", "LOGIC_TERNARY_IF_FALSE": "hvis usant", "LOGIC_TERNARY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> betingelsen i 'test'. <PERSON><PERSON> betinge<PERSON>en er sann, da returneres 'hvis sant' verdien. Hvis ikke returneres 'hvis usant' verdien.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "Et tall.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "x", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "asin", "MATH_TRIG_ACOS": "acos", "MATH_TRIG_ATAN": "atan", "MATH_ARITHMETIC_HELPURL": "https://no.wikipedia.org/wiki/Aritmetikk", "MATH_ARITHMETIC_TOOLTIP_ADD": "Returnerer summen av to tall.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Returner differansen mellom to tall.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Returner produktet av to tall.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Returner k<PERSON><PERSON><PERSON> av to tall.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Returner det første tallet opphøyd i den andre tallet.", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "kvadratrot", "MATH_SINGLE_TOOLTIP_ROOT": "Returner k<PERSON><PERSON><PERSON><PERSON> av et tall.", "MATH_SINGLE_OP_ABSOLUTE": "absoluttverdi", "MATH_SINGLE_TOOLTIP_ABS": "Returner absoluttverdien av et tall.", "MATH_SINGLE_TOOLTIP_NEG": "Returner det negative tallet.", "MATH_SINGLE_TOOLTIP_LN": "Returner den naturlige logaritmen til et tall.", "MATH_SINGLE_TOOLTIP_LOG10": "Returner base-10 logaritmen til et tall.", "MATH_SINGLE_TOOLTIP_EXP": "Returner e opphøyd i et tall.", "MATH_SINGLE_TOOLTIP_POW10": "Returner 10 opphøyd i et tall.", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "Returner sinus av en vinkel (ikke radian).", "MATH_TRIG_TOOLTIP_COS": "Returner cosinus av en vinkel (ikke radian).", "MATH_TRIG_TOOLTIP_TAN": "Returner tangenten av en vinkel (ikke radian).", "MATH_TRIG_TOOLTIP_ASIN": "Returner arc<PERSON>us til et tall.", "MATH_TRIG_TOOLTIP_ACOS": "Returner <PERSON><PERSON><PERSON> til et tall.", "MATH_TRIG_TOOLTIP_ATAN": "Returner arctangens til et tall.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant", "MATH_CONSTANT_TOOLTIP": "Returner en av felleskonstantene π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), el<PERSON> ∞ (u<PERSON><PERSON><PERSON>).", "MATH_IS_EVEN": "er et partall", "MATH_IS_ODD": "er et oddetall", "MATH_IS_PRIME": "er et primtall", "MATH_IS_WHOLE": "er et heltall", "MATH_IS_POSITIVE": "er positivt", "MATH_IS_NEGATIVE": "er <PERSON>r negativt", "MATH_IS_DIVISIBLE_BY": "er delelig med", "MATH_IS_TOOLTIP": "<PERSON><PERSON>kk om et tall er et partall, oddetall, prim<PERSON>l, heltall, positivt, negativt, eller om det er delelig med et annet tall. Returnerer sant eller usant.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "endre %1 ved %2", "MATH_CHANGE_TOOLTIP": "Addere et tall til variabelen '%1'.", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> et tall ned eller opp.", "MATH_ROUND_OPERATOR_ROUND": "avrunding", "MATH_ROUND_OPERATOR_ROUNDUP": "rund opp", "MATH_ROUND_OPERATOR_ROUNDDOWN": "rund ned", "MATH_ONLIST_OPERATOR_SUM": "summen av listen", "MATH_ONLIST_TOOLTIP_SUM": "Returner summen av alle tallene i listen.", "MATH_ONLIST_OPERATOR_MIN": "minimum av listen", "MATH_ONLIST_TOOLTIP_MIN": "Returner det minste tallet i listen.", "MATH_ONLIST_OPERATOR_MAX": "maksimum av liste", "MATH_ONLIST_TOOLTIP_MAX": "Returner det stø<PERSON>e tallet i listen.", "MATH_ONLIST_OPERATOR_AVERAGE": "gjennomsnittet av listen", "MATH_ONLIST_TOOLTIP_AVERAGE": "Returner det aritmetiske gjennomsnittet av tallene i listen.", "MATH_ONLIST_OPERATOR_MEDIAN": "medianen til listen", "MATH_ONLIST_TOOLTIP_MEDIAN": "Returner listens median.", "MATH_ONLIST_OPERATOR_MODE": "Listens typetall", "MATH_ONLIST_TOOLTIP_MODE": "Returner en liste av de vanligste elementene i listen.", "MATH_ONLIST_OPERATOR_STD_DEV": "standardavviket til listen", "MATH_ONLIST_TOOLTIP_STD_DEV": "Returner listens <PERSON><PERSON><PERSON>.", "MATH_ONLIST_OPERATOR_RANDOM": "tilfeldig element i listen", "MATH_ONLIST_TOOLTIP_RANDOM": "Returner et tilfeldig element fra listen.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "resten av %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Returner resten fra delingen av to tall.", "MATH_CONSTRAIN_TITLE": "begrense %1 lav %2 høy %3", "MATH_CONSTRAIN_TOOLTIP": "Begrens et tall til å være mellom de angitte grenseverdiene (inklusiv).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "Et tilfeldig heltall mellom %1 og %2", "MATH_RANDOM_INT_TOOLTIP": "Returner et tilfeldig tall mellom de to spesifiserte grensene, inkludert de to.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "<PERSON><PERSON><PERSON> flyttall", "MATH_RANDOM_FLOAT_TOOLTIP": "Returner et tilfeldig flyttall mellom 0.0 (inkludert) og 1.0 (ikke inkludert).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 av X:%1 Y:%2", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "<PERSON> bok<PERSON>, ett ord eller en linje med tekst.", "TEXT_JOIN_TITLE_CREATEWITH": "lag tekst med", "TEXT_JOIN_TOOLTIP": "Opprett en tekst ved å sette sammen et antall elementer.", "TEXT_CREATE_JOIN_TITLE_JOIN": "føy sammen", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON> til, fjern eller forandre rekkefølgen for å forandre på denne tekstblokken.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Legg til et element til teksten.", "TEXT_APPEND_TITLE": "til %1, legg til teksten %2", "TEXT_APPEND_TOOLTIP": "Tilføy tekst til variabelen '%1'.", "TEXT_LENGTH_TITLE": "lengden av %1", "TEXT_LENGTH_TOOLTIP": "Returnerer antall bokstaver (inkludert mellomrom) i den angitte teksten.", "TEXT_ISEMPTY_TITLE": "%1 er tom", "TEXT_ISEMPTY_TOOLTIP": "Returnerer sann hvis den angitte teksten er tom.", "TEXT_INDEXOF_TOOLTIP": "Returnerer posisjonen for første/siste forekomsten av den første tekst i den andre teksten.  Returnerer %1 hvis teksten ikke blir funnet.", "TEXT_INDEXOF_TITLE": "i teksten %1, %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "finn første forekomst av tekst", "TEXT_INDEXOF_OPERATOR_LAST": "finn siste forekomst av tekst", "TEXT_CHARAT_TITLE": "i teksten %1, %2", "TEXT_CHARAT_FROM_START": "hent bokstav #", "TEXT_CHARAT_FROM_END": "hent bokstav # fra slutten", "TEXT_CHARAT_FIRST": "hent første bok<PERSON>v", "TEXT_CHARAT_LAST": "hent den siste bokstaven", "TEXT_CHARAT_RANDOM": "hent en tilfeldig bokstav", "TEXT_CHARAT_TAIL": "", "TEXT_CHARAT_TOOLTIP": "Returnerer bokstaven på angitt plassering.", "TEXT_GET_SUBSTRING_TOOLTIP": "Returnerer den angitte delen av teksten.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "i tekst", "TEXT_GET_SUBSTRING_START_FROM_START": "hent delstreng fra bokstav #", "TEXT_GET_SUBSTRING_START_FROM_END": "hent delstreng fra bokstav # fra slutten", "TEXT_GET_SUBSTRING_START_FIRST": "hent delstreng fra første bokstav", "TEXT_GET_SUBSTRING_END_FROM_START": "til bokstav #", "TEXT_GET_SUBSTRING_END_FROM_END": "til bokstav # fra slutten", "TEXT_GET_SUBSTRING_END_LAST": "til siste bokstav", "TEXT_GET_SUBSTRING_TAIL": "", "TEXT_CHANGECASE_TOOLTIP": "Returnerer en kopi av teksten der store og små bokstaver er byttet om.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "til STORE BOKSTAVER", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "til små bokstaver", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "til store forbokstaver", "TEXT_TRIM_TOOLTIP": "Returner en kopi av teksten med mellomrom fjernet fra en eller begge sidene.", "TEXT_TRIM_OPERATOR_BOTH": "fjern mello<PERSON>rom fra begge sider av", "TEXT_TRIM_OPERATOR_LEFT": "fjern mellomrom fra venstre side av", "TEXT_TRIM_OPERATOR_RIGHT": "fjern mellomrom fra høyre side av", "TEXT_PRINT_TITLE": "skriv ut %1", "TEXT_PRINT_TOOLTIP": "<PERSON>k<PERSON>v ut angitt tekst, tall eller annet innhold.", "TEXT_PROMPT_TYPE_TEXT": "spør om tekst med en melding", "TEXT_PROMPT_TYPE_NUMBER": "spør om et tall med en melding", "TEXT_PROMPT_TOOLTIP_NUMBER": "Be brukeren om et tall.", "TEXT_PROMPT_TOOLTIP_TEXT": "<PERSON><PERSON><PERSON><PERSON> brukeren om tekst.", "TEXT_COUNT_MESSAGE0": "tell %1 i %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Tell hvor mange ganger noe tekst dukker opp i annen tekst.", "TEXT_REPLACE_MESSAGE0": "erstatt %1 med %2 i %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "Erstatter alle forekomster av noe tekst i en annen tekst.", "TEXT_REVERSE_MESSAGE0": "reverser %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Reverserer rekkefølgen på tegnene i teksten.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "opprett en tom liste", "LISTS_CREATE_EMPTY_TOOLTIP": "Returnerer en tom liste, altså med lengde 0", "LISTS_CREATE_WITH_TOOLTIP": "Lag en liste med et vilkårlig antall elementer.", "LISTS_CREATE_WITH_INPUT_WITH": "lag en liste med", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "liste", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON> til, fjern eller endre rekkefølgen for å endre på denne delen av listen.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Til<PERSON><PERSON><PERSON> et element til listen.", "LISTS_REPEAT_TOOLTIP": "Lager en liste hvor den gitte verdien gjentas et antall ganger.", "LISTS_REPEAT_TITLE": "Lag en liste hvor elementet %1 forekommer %2 ganger", "LISTS_LENGTH_TITLE": "lengden på %1", "LISTS_LENGTH_TOOLTIP": "Returnerer lengden til en liste.", "LISTS_ISEMPTY_TITLE": "%1 er tom", "LISTS_ISEMPTY_TOOLTIP": "Returnerer sann hvis listen er tom.", "LISTS_INLIST": "i listen", "LISTS_INDEX_OF_FIRST": "finn første forekomst av elementet", "LISTS_INDEX_OF_LAST": "finn siste forekomst av elementet", "LISTS_INDEX_OF_TOOLTIP": "Returnerer indeksen av den første/siste forekomsten av elementet i lista. Returnerer %1 hvis ikke funnet.", "LISTS_GET_INDEX_GET": "hent", "LISTS_GET_INDEX_GET_REMOVE": "hent og fjern", "LISTS_GET_INDEX_REMOVE": "fjern", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# fra slutten", "LISTS_GET_INDEX_FIRST": "<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_LAST": "siste", "LISTS_GET_INDEX_RANDOM": "<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_TAIL": "", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 er det første elementet.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 er det siste elementet.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Returner elementet på den angitte posisjonen i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Returnerer det første elementet i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Returnerer det siste elementet i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Returnerer et tilfeldig element i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Fjerner og returnerer elementet ved en gitt posisjon i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Fjerner og returnerer det første elementet i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "F<PERSON>ner og returnerer det siste elementet i en liste.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "<PERSON><PERSON>ner og returnerer et tilfeldig element i en liste.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "<PERSON><PERSON><PERSON> et element ved en gitt posisjon i en liste.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "<PERSON><PERSON>ner det første elementet i en liste.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "<PERSON><PERSON><PERSON> det siste elementet i en liste.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "<PERSON><PERSON>ner et tilfeldig element i en liste.", "LISTS_SET_INDEX_SET": "sett", "LISTS_SET_INDEX_INSERT": "sett inn ved", "LISTS_SET_INDEX_INPUT_TO": "som", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Setter inn elementet ved den angitte posisjonen i en liste.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Angir det første elementet i en liste.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "<PERSON>ir det siste elementet i en liste.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Angir et tilfeldig element i en liste.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Setter inn elementet ved den angitte posisjonen i en liste.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Setter inn elementet i starten av en liste.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Tilføy elementet til slutten av en liste.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Setter inn elementet ved en tilfeldig posisjon i en liste.", "LISTS_GET_SUBLIST_START_FROM_START": "Hent del-listen fra #", "LISTS_GET_SUBLIST_START_FROM_END": "Hent de siste # elementene", "LISTS_GET_SUBLIST_START_FIRST": "Hent en del av listen", "LISTS_GET_SUBLIST_END_FROM_START": "til #", "LISTS_GET_SUBLIST_END_FROM_END": "til # fra slutten", "LISTS_GET_SUBLIST_END_LAST": "til siste", "LISTS_GET_SUBLIST_TAIL": "", "LISTS_GET_SUBLIST_TOOLTIP": "Kopiérer en ønsket del av en liste.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sorter %1 %2 %3", "LISTS_SORT_TOOLTIP": "Sorter en kopi av en liste.", "LISTS_SORT_ORDER_ASCENDING": "stigende", "LISTS_SORT_ORDER_DESCENDING": "synkende", "LISTS_SORT_TYPE_NUMERIC": "numerisk", "LISTS_SORT_TYPE_TEXT": "alfabetisk", "LISTS_SORT_TYPE_IGNORECASE": "alfabetisk, ignorert store/små bokstaver", "LISTS_SPLIT_LIST_FROM_TEXT": "lag liste av tekst", "LISTS_SPLIT_TEXT_FROM_LIST": "lag tekst av liste", "LISTS_SPLIT_WITH_DELIMITER": "med avgrenser", "LISTS_SPLIT_TOOLTIP_SPLIT": "<PERSON>t teksten til en liste med tekster, brutt ved hver avgrenser.", "LISTS_SPLIT_TOOLTIP_JOIN": "<PERSON><PERSON><PERSON> sammen en liste tekster til én tekst, avskilt av en avgrenser.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "reverser %1", "LISTS_REVERSE_TOOLTIP": "Reverser en kopi av ei liste.", "VARIABLES_GET_TOOLTIP": "Returnerer verdien av denne variabelen.", "VARIABLES_GET_CREATE_SET": "<PERSON><PERSON><PERSON><PERSON> 'sett %1'", "VARIABLES_SET": "sett %1 til %2", "VARIABLES_SET_TOOLTIP": "Setter verdien av denne variablen lik parameteren.", "VARIABLES_SET_CREATE_GET": "<PERSON><PERSON><PERSON><PERSON> 'hent %1'", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "til", "PROCEDURES_DEFNORETURN_PROCEDURE": "gjør noe", "PROCEDURES_BEFORE_PARAMS": "med:", "PROCEDURES_CALL_BEFORE_PARAMS": "med:", "PROCEDURES_DEFNORETURN_DO": "", "PROCEDURES_DEFNORETURN_TOOLTIP": "Opprett en funksjon som ikke har noe resultat.", "PROCEDURES_DEFNORETURN_COMMENT": "<PERSON>sk<PERSON>v denne <PERSON>…", "PROCEDURES_DEFRETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFRETURN_RETURN": "returner", "PROCEDURES_DEFRETURN_TOOLTIP": "Oppretter en funksjon som har et resultat.", "PROCEDURES_ALLOW_STATEMENTS": "tillat utta<PERSON>er", "PROCEDURES_DEF_DUPLICATE_WARNING": "Advarsel: <PERSON><PERSON> funk<PERSON>en har duplikate parametere.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Kj<PERSON>r den brukerdefinerte funksjonen '%1'.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Kjør den brukerdefinerte funksjonen'%1' og bruk resultatet av den.", "PROCEDURES_MUTATORCONTAINER_TITLE": "parametere", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON> til, fjern eller endre rekkefølgen på <PERSON> til denne funk<PERSON>jonen.", "PROCEDURES_MUTATORARG_TITLE": "Navn på parameter:", "PROCEDURES_MUTATORARG_TOOLTIP": "Legg til en input til funksjonen.", "PROCEDURES_HIGHLIGHT_DEF": "<PERSON><PERSON>", "PROCEDURES_CREATE_DO": "Opprett '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "<PERSON><PERSON> en verdi er sann, returner da en annen verdi.", "PROCEDURES_IFRETURN_WARNING": "Advarsel: <PERSON><PERSON> blokken kan bare benyttes innenfor en funksjonsdefinisjon.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Si noe …", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}