# 测试与部署规范

## 测试策略

### 1. 单元测试规范
```java
@SpringBootTest
@Transactional
@Rollback
class ExamQuestionServiceTest {
    
    @Autowired
    private IExamQuestionService examQuestionService;
    
    @Test
    @DisplayName("测试题目创建功能")
    void testCreateQuestion() {
        // Given
        ExamQuestion question = new ExamQuestion();
        question.setTitle("测试题目");
        question.setQuestionType("SINGLE_CHOICE");
        question.setSubject("Java");
        question.setLevel("1");
        question.setDifficulty("EASY");
        question.setContent("这是一道测试题目");
        
        // When
        boolean result = examQuestionService.save(question);
        
        // Then
        assertTrue(result);
        assertNotNull(question.getId());
        
        // 验证数据库中的数据
        ExamQuestion saved = examQuestionService.getById(question.getId());
        assertEquals("测试题目", saved.getTitle());
        assertEquals("SINGLE_CHOICE", saved.getQuestionType());
    }
    
    @Test
    @DisplayName("测试题目查询功能")
    void testQueryQuestions() {
        // Given - 准备测试数据
        createTestQuestions();
        
        // When
        ExamQuestion queryParam = new ExamQuestion();
        queryParam.setSubject("Java");
        IPage<ExamQuestion> result = examQuestionService.queryPageList(queryParam, 1, 10);
        
        // Then
        assertNotNull(result);
        assertTrue(result.getTotal() > 0);
        assertEquals("Java", result.getRecords().get(0).getSubject());
    }
    
    private void createTestQuestions() {
        List<ExamQuestion> questions = Arrays.asList(
            createQuestion("Java基础题1", "Java", "SINGLE_CHOICE"),
            createQuestion("Java基础题2", "Java", "MULTIPLE_CHOICE"),
            createQuestion("Python基础题1", "Python", "SINGLE_CHOICE")
        );
        examQuestionService.saveBatch(questions);
    }
    
    private ExamQuestion createQuestion(String title, String subject, String type) {
        ExamQuestion question = new ExamQuestion();
        question.setTitle(title);
        question.setSubject(subject);
        question.setQuestionType(type);
        question.setLevel("1");
        question.setDifficulty("EASY");
        question.setContent("测试内容");
        return question;
    }
}
```

### 2. 集成测试规范
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
class ExamQuestionControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Test
    @DisplayName("测试题目API完整流程")
    void testQuestionApiFlow() {
        // 1. 创建题目
        ExamQuestion question = new ExamQuestion();
        question.setTitle("集成测试题目");
        question.setQuestionType("SINGLE_CHOICE");
        question.setSubject("Java");
        
        ResponseEntity<Result> createResponse = restTemplate.postForEntity(
            "/teaching/examQuestion/add", question, Result.class);
        
        assertEquals(HttpStatus.OK, createResponse.getStatusCode());
        assertTrue(createResponse.getBody().isSuccess());
        
        // 2. 查询题目列表
        ResponseEntity<Result> listResponse = restTemplate.getForEntity(
            "/teaching/examQuestion/list?pageNo=1&pageSize=10", Result.class);
        
        assertEquals(HttpStatus.OK, listResponse.getStatusCode());
        assertTrue(listResponse.getBody().isSuccess());
        
        // 3. 更新题目
        question.setTitle("更新后的题目");
        ResponseEntity<Result> updateResponse = restTemplate.exchange(
            "/teaching/examQuestion/edit", HttpMethod.PUT, 
            new HttpEntity<>(question), Result.class);
        
        assertEquals(HttpStatus.OK, updateResponse.getStatusCode());
        assertTrue(updateResponse.getBody().isSuccess());
    }
}
```

### 3. Mock测试
```java
@ExtendWith(MockitoExtension.class)
class ExamQuestionServiceMockTest {
    
    @Mock
    private ExamQuestionMapper examQuestionMapper;
    
    @InjectMocks
    private ExamQuestionServiceImpl examQuestionService;
    
    @Test
    @DisplayName("测试相似题目查找")
    void testFindSimilarQuestions() {
        // Given
        String title = "Java基础题目";
        String content = "关于Java的基础知识";
        Double threshold = 0.8;
        
        List<ExamQuestion> mockQuestions = Arrays.asList(
            createMockQuestion("Java基础题目1", "关于Java的基础知识点"),
            createMockQuestion("Java进阶题目", "关于Java的高级特性")
        );
        
        when(examQuestionMapper.selectList(any(QueryWrapper.class)))
            .thenReturn(mockQuestions);
        
        // When
        List<ExamQuestion> result = examQuestionService.findSimilarQuestions(title, content, threshold);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Java基础题目1", result.get(0).getTitle());
        
        verify(examQuestionMapper, times(1)).selectList(any(QueryWrapper.class));
    }
    
    private ExamQuestion createMockQuestion(String title, String content) {
        ExamQuestion question = new ExamQuestion();
        question.setId(UUID.randomUUID().toString());
        question.setTitle(title);
        question.setContent(content);
        return question;
    }
}
```

## 性能测试

### 1. JMeter测试脚本
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Teaching System Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
    </TestPlan>
    
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Question API Load Test">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">100</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">50</stringProp>
        <stringProp name="ThreadGroup.ramp_time">10</stringProp>
      </ThreadGroup>
      
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Query Questions">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="pageNo" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">1</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <stringProp name="Argument.name">pageNo</stringProp>
              </elementProp>
              <elementProp name="pageSize" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">10</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <stringProp name="Argument.name">pageSize</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">localhost</stringProp>
          <stringProp name="HTTPSampler.port">8081</stringProp>
          <stringProp name="HTTPSampler.path">/api/teaching/examQuestion/list</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
        </HTTPSamplerProxy>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

### 2. 性能监控
```java
@Component
@Slf4j
public class PerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    public PerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    @EventListener
    public void handleMethodExecution(MethodExecutionEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("method.execution.time")
            .tag("class", event.getClassName())
            .tag("method", event.getMethodName())
            .register(meterRegistry));
    }
    
    public void recordDatabaseQuery(String operation, long duration) {
        Timer.builder("database.query.time")
            .tag("operation", operation)
            .register(meterRegistry)
            .record(duration, TimeUnit.MILLISECONDS);
    }
}
```

## 部署配置

### 1. Docker配置
```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine

LABEL maintainer="teaching-system"

# 设置时区
RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 创建应用目录
WORKDIR /app

# 复制应用文件
COPY target/teaching-open-*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+PrintGCDetails -Xloggc:/app/logs/gc.log"

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/api/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 2. Docker Compose配置
```yaml
version: '3.8'

services:
  teaching-app:
    build: .
    container_name: teaching-app
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=**********************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=123456
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
    volumes:
      - ./logs:/app/logs
      - ./upload:/app/upload
    depends_on:
      - mysql
      - redis
    networks:
      - teaching-network

  mysql:
    image: mysql:5.7
    container_name: teaching-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=teaching
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    volumes:
      - mysql-data:/var/lib/mysql
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - teaching-network

  redis:
    image: redis:6-alpine
    container_name: teaching-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - teaching-network

  nginx:
    image: nginx:alpine
    container_name: teaching-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./web/dist:/usr/share/nginx/html
    depends_on:
      - teaching-app
    networks:
      - teaching-network

volumes:
  mysql-data:
  redis-data:

networks:
  teaching-network:
    driver: bridge
```

### 3. Nginx配置
```nginx
upstream teaching-backend {
    server teaching-app:8081;
}

server {
    listen 80;
    server_name localhost;
    
    # 前端静态资源
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://teaching-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 文件上传大小限制
        client_max_body_size 100M;
    }
    
    # WebSocket代理
    location /api/websocket/ {
        proxy_pass http://teaching-backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 监控和日志

### 1. 应用监控
```yaml
# application-prod.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    org.jeecg.modules.teaching: INFO
    org.springframework.web: WARN
    com.baomidou.mybatisplus: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/teaching.log
    max-size: 100MB
    max-history: 30
```

### 2. 日志收集
```yaml
# docker-compose.yml 添加日志收集
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    container_name: teaching-filebeat
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/app/logs:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - teaching-app
    networks:
      - teaching-network
```

### 3. 健康检查
```java
@Component
public class TeachingHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            checkDatabase();
            
            // 检查Redis连接
            checkRedis();
            
            // 检查HOJ服务连接
            checkHojService();
            
            return Health.up()
                .withDetail("database", "UP")
                .withDetail("redis", "UP")
                .withDetail("hoj", "UP")
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
    
    private void checkDatabase() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            connection.isValid(5);
        }
    }
    
    private void checkRedis() {
        redisTemplate.opsForValue().get("health-check");
    }
    
    private void checkHojService() {
        // HOJ服务健康检查逻辑
    }
}
```
