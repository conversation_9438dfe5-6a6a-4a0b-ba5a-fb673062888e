{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Zoranzoki21"]}, "VARIABLES_DEFAULT_NAME": "stavka", "TODAY": "<PERSON><PERSON>", "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "ADD_COMMENT": "<PERSON><PERSON><PERSON> k<PERSON>", "REMOVE_COMMENT": "Ukloni komentar", "EXTERNAL_INPUTS": "<PERSON><PERSON><PERSON><PERSON>", "INLINE_INPUTS": "Unutraš<PERSON> ulazi", "DELETE_BLOCK": "Obriši blok", "DELETE_X_BLOCKS": "Obriši %1 blokova", "DELETE_ALL_BLOCKS": "Da obrišem svih %1 blokova?", "CLEAN_UP": "Ukloni blokove", "COLLAPSE_BLOCK": "Skupi blok", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON> b<PERSON>", "EXPAND_BLOCK": "Proširi blok", "EXPAND_ALL": "<PERSON><PERSON><PERSON> b<PERSON>", "DISABLE_BLOCK": "Onemogući blok", "ENABLE_BLOCK": "Omogući blok", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "Opozovi", "REDO": "<PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Promeni vrednost:", "RENAME_VARIABLE": "Preimenuj promenljivu…", "RENAME_VARIABLE_TITLE": "Preimenuj sve „%1“ promenljive u:", "NEW_VARIABLE": "<PERSON><PERSON><PERSON> pro<PERSON>…", "NEW_VARIABLE_TITLE": "Ime nove promenljive:", "VARIABLE_ALREADY_EXISTS": "Promenljiva pod imenom '%1' već postoji.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Varijabla po imenu '%1' već postoji za drugu varijablu tipa '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Da obrišem %1 upotreba promenljive '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Ne mogu da obrišem varijablu ’%1’ jer je deo definicije funkcije ’%2’", "DELETE_VARIABLE": "Obriši promenljivu '%1'", "COLOUR_PICKER_HELPURL": "https://sr.wikipedia.org/wiki/<PERSON>ja", "COLOUR_PICKER_TOOLTIP": "Izaberite boju sa palete.", "COLOUR_RANDOM_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> boja", "COLOUR_RANDOM_TOOLTIP": "Izaberite boju nasumice.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "boja sa", "COLOUR_RGB_RED": "crvena", "COLOUR_RGB_GREEN": "zelena", "COLOUR_RGB_BLUE": "plava", "COLOUR_RGB_TOOLTIP": "Kreiraj boju sa određenom količinom crvene,zelene, i plave. Sve vrednosti moraju biti između 0 i 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_BLEND_COLOUR1": "boja 1", "COLOUR_BLEND_COLOUR2": "boja 2", "COLOUR_BLEND_RATIO": "odnos", "COLOUR_BLEND_TOOLTIP": "Pomešati dve boje zajedno sa datim odnosom (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://sr.wikipedia.org/wiki/For_petlja", "CONTROLS_REPEAT_TITLE": "ponovi %1 puta", "CONTROLS_REPEAT_INPUT_DO": "izvrši", "CONTROLS_REPEAT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> neke naredbe nekoliko puta.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "ponavljati dok", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "ponavljati do", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Dok je vrednost tačna, onda izvršite neke naredbe.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Dok vrednost nije tačna, onda izvršiti neke naredbe.", "CONTROLS_FOR_TOOLTIP": "Imaj promenjivu \"%1\" uzmi vrednosti od početnog broja do zadnjeg broja, brojeći po određenom intervalu, i izvrši određene blokove.", "CONTROLS_FOR_TITLE": "prebroj sa %1 od %2 do %3 od %4", "CONTROLS_FOREACH_TITLE": "za svaku stavku %1 na spisku %2", "CONTROLS_FOREACH_TOOLTIP": "Za svaku stavku unutar liste, podesi promenjivu '%1' po stavci, i onda načini neke izjave-naredbe.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "Izađite iz petlje", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "nastavi sa sledećom iteracijom petlje", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Napusti sadr<PERSON>.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Preskoči ostatak ove petlje, i nastavi sa sledećom iteracijom(ponavljanjem).", "CONTROLS_FLOW_STATEMENTS_WARNING": "Upozorenje: <PERSON><PERSON><PERSON> blok može da se upotrebi samo unutar petlje.", "CONTROLS_IF_TOOLTIP_1": "ako je vrednost tačna, onda izvrši neke naredbe-izjave.", "CONTROLS_IF_TOOLTIP_2": "ako je vrednost tačna, onda izvrši prvi blok naredbi, U suprotnom, izvrši drugi blok naredbi.", "CONTROLS_IF_TOOLTIP_3": "Ako je prva vrednost tačna, onda izvrši prvi blok naredbi, u suprotnom, ako je druga vrednost tačna , izvrši drugi blok naredbi.", "CONTROLS_IF_TOOLTIP_4": "Ako je prva vrednost tačna, onda izvrši prvi blok naredbi, u suprotnom, ako je druga vrednost tačna , izvrši drugi blok naredbi. Ako ni jedna od vrednosti nije tačna, izvrši poslednji blok naredbi.", "CONTROLS_IF_MSG_IF": "ako", "CONTROLS_IF_MSG_ELSEIF": "inače-ako", "CONTROLS_IF_MSG_ELSE": "in<PERSON>č<PERSON>", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ili pre<PERSON>i delove kako bih rekonfigurisali ovaj if blok.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Do<PERSON><PERSON><PERSON> uslov bloku „ako“.", "CONTROLS_IF_ELSE_TOOLTIP": "<PERSON><PERSON><PERSON>, catch-all  (uhvati sve) uslove if bloka.", "IOS_OK": "U redu", "IOS_CANCEL": "Ot<PERSON>ži", "IOS_ERROR": "Greška", "IOS_PROCEDURES_INPUTS": "UNOSI", "IOS_PROCEDURES_ADD_INPUT": "+ <PERSON><PERSON><PERSON> un<PERSON>", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Dozvoli izjave", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "<PERSON><PERSON> funk<PERSON>ja ima duplicirane unose.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON><PERSON>", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_VARIABLE_NAME": "<PERSON><PERSON> varijable", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Ne možete da koristite prazno ime varijable.", "LOGIC_COMPARE_HELPURL": "https://sr.wikipedia.org/wiki/Nejednakost", "LOGIC_COMPARE_TOOLTIP_EQ": "Vraća vrednost „tačno“ ako su oba ulaza jednaka.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Vraća vrednost „tačno“ ako su oba ulaza nejednaka.", "LOGIC_COMPARE_TOOLTIP_LT": "Vraća vrednost „tačno“ ako je prvi ulaz manji od drugog.", "LOGIC_COMPARE_TOOLTIP_LTE": "Vraća vrednost „tačno“ ako je prvi ulaz manji ili jednak drugom.", "LOGIC_COMPARE_TOOLTIP_GT": "Vraća vrednost „tačno“ ako je prvi ulaz veći od drugog.", "LOGIC_COMPARE_TOOLTIP_GTE": "Vraća vrednost „tačno“ ako je prvi ulaz veći ili jednak drugom.", "LOGIC_OPERATION_TOOLTIP_AND": "Vraća vrednost „tačno“ ako su oba ulaza tačna.", "LOGIC_OPERATION_AND": "i", "LOGIC_OPERATION_TOOLTIP_OR": "Vraća vrednost „tačno“ ako je bar jedan od ulaza tačan.", "LOGIC_OPERATION_OR": "ili", "LOGIC_NEGATE_TITLE": "nije %1", "LOGIC_NEGATE_TOOLTIP": "Vraća vrednost „tačno“ ako je ulaz netačan. Vraća vrednost „netačno“ ako je ulaz tačan.", "LOGIC_BOOLEAN_TRUE": "tačno", "LOGIC_BOOLEAN_FALSE": "netačno", "LOGIC_BOOLEAN_TOOLTIP": "Vraća ili tačno ili netačno.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "bez vrednosti", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> „bez vrednosti“.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "proba", "LOGIC_TERNARY_IF_TRUE": "ako je ta<PERSON>no", "LOGIC_TERNARY_IF_FALSE": "ako je netačno", "LOGIC_TERNARY_TOOLTIP": "<PERSON>ver<PERSON> uslov u 'test'. <PERSON><PERSON> je uslov ta<PERSON>, tada vraća 'if true' vrednost; u drugom slučaju vraća 'if false' vrednost.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "<PERSON><PERSON><PERSON> broj.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "arc sin", "MATH_TRIG_ACOS": "arc cos", "MATH_TRIG_ATAN": "arc tan", "MATH_ARITHMETIC_HELPURL": "https://en.wikipedia.org/wiki/Arithmetic", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON>rat<PERSON> zbir dva broja.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Vraća razliku dva broja.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Vraća proizvod dva broja.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Vraća količnik dva broja.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Vraća prvi broj <PERSON><PERSON><PERSON> drug<PERSON>.", "MATH_SINGLE_HELPURL": "https://sr.wikipedia.org/wiki/Kvadratni_koren", "MATH_SINGLE_OP_ROOT": "kvadratni koren", "MATH_SINGLE_TOOLTIP_ROOT": "Vraća kvadratni koren broja.", "MATH_SINGLE_OP_ABSOLUTE": "apsolutan", "MATH_SINGLE_TOOLTIP_ABS": "Vraća apsolutnu vrednost broja.", "MATH_SINGLE_TOOLTIP_NEG": "Vraća negaciju broja.", "MATH_SINGLE_TOOLTIP_LN": "Vraća prirodni logaritam broja.", "MATH_SINGLE_TOOLTIP_LOG10": "Vraća logaritam broja sa osnovom 10.", "MATH_SINGLE_TOOLTIP_EXP": "vratiti e na vlasti broja.", "MATH_SINGLE_TOOLTIP_POW10": "Vraća 10-ti step<PERSON> broja.", "MATH_TRIG_HELPURL": "https://sr.wikipedia.org/wiki/Trigonometrijske_funkcije", "MATH_TRIG_TOOLTIP_SIN": "<PERSON><PERSON><PERSON><PERSON> sinus stepena (ne radijan).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> (ne radijan).", "MATH_TRIG_TOOLTIP_TAN": "<PERSON><PERSON><PERSON><PERSON> tangens stepena (ne radijan).", "MATH_TRIG_TOOLTIP_ASIN": "Vraća a<PERSON> broja.", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON><PERSON><PERSON> arkus kos<PERSON> broja.", "MATH_TRIG_TOOLTIP_ATAN": "Vraća arkus tangens broja.", "MATH_CONSTANT_HELPURL": "https://sr.wikipedia.org/wiki/Matematička_konstanta", "MATH_CONSTANT_TOOLTIP": "vrati jednu od zajedničkih konstanti: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), ili ∞ (infinity).", "MATH_IS_EVEN": "je paran", "MATH_IS_ODD": "je neparan", "MATH_IS_PRIME": "je prost", "MATH_IS_WHOLE": "je ceo", "MATH_IS_POSITIVE": "je pozitivan", "MATH_IS_NEGATIVE": "je negativan", "MATH_IS_DIVISIBLE_BY": "je deljiv sa", "MATH_IS_TOOLTIP": "Provjer<PERSON> da li je broj paran, ne<PERSON><PERSON>, prost, cio, pozitivan, negat<PERSON>n, ili da li je deljiv sa određenim brojem. Vraća tačno ili netačno.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "promeni %1 za %2", "MATH_CHANGE_TOOLTIP": "Dodajte broj promenljivoj „%1“.", "MATH_ROUND_HELPURL": "https://sr.wikipedia.org/wiki/Zaokruživanje", "MATH_ROUND_TOOLTIP": "Zaokružite broj na veću ili manju vrednost.", "MATH_ROUND_OPERATOR_ROUND": "zaokruži", "MATH_ROUND_OPERATOR_ROUNDUP": "zaokruži naviše", "MATH_ROUND_OPERATOR_ROUNDDOWN": "zaokruži naniže", "MATH_ONLIST_OPERATOR_SUM": "zbir spiska", "MATH_ONLIST_TOOLTIP_SUM": "Vraća zbir svih brojeva sa spiska.", "MATH_ONLIST_OPERATOR_MIN": "min. spiska", "MATH_ONLIST_TOOLTIP_MIN": "Vraća najmanji broj sa spiska.", "MATH_ONLIST_OPERATOR_MAX": "maks. spiska", "MATH_ONLIST_TOOLTIP_MAX": "Vraća najveći broj sa spiska.", "MATH_ONLIST_OPERATOR_AVERAGE": "prosek spiska", "MATH_ONLIST_TOOLTIP_AVERAGE": "Vraća prosek numeričkih vrednosti sa spiska.", "MATH_ONLIST_OPERATOR_MEDIAN": "medijana spiska", "MATH_ONLIST_TOOLTIP_MEDIAN": "Vraća medijanu sa spiska.", "MATH_ONLIST_OPERATOR_MODE": "modus spiska", "MATH_ONLIST_TOOLTIP_MODE": "Vraća najčešće stavke sa spiska.", "MATH_ONLIST_OPERATOR_STD_DEV": "standardna devijacija spiska", "MATH_ONLIST_TOOLTIP_STD_DEV": "Vraća standardnu devijaciju spiska.", "MATH_ONLIST_OPERATOR_RANDOM": "slučajna stavka spiska", "MATH_ONLIST_TOOLTIP_RANDOM": "Vraća slučajni element sa spiska.", "MATH_MODULO_HELPURL": "https://sr.wikipedia.org/wiki/Kongruencija", "MATH_MODULO_TITLE": "podsetnik od %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Vraća podsetnik od deljenja dva broja.", "MATH_CONSTRAIN_TITLE": "ograniči %1 nisko %2 visoko %3", "MATH_CONSTRAIN_TOOLTIP": "Ograničava broj na donje i gornje granice (uključivo).", "MATH_RANDOM_INT_HELPURL": "https://sr.wikipedia.org/wiki/Generator_slu<PERSON><PERSON><PERSON><PERSON>_brojeva", "MATH_RANDOM_INT_TITLE": "sličajno odabrani cijeli broj od %1 do %2", "MATH_RANDOM_INT_TOOLTIP": "Vraća slučajno odabrani celi broj između dve određene grani<PERSON>, uključivo.", "MATH_RANDOM_FLOAT_HELPURL": "https://sr.wikipedia.org/wiki/Generator_slu<PERSON><PERSON><PERSON><PERSON>_brojeva", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "slučajni razlomak", "MATH_RANDOM_FLOAT_TOOLTIP": "Vraća slučajni razlomak između 0.0 (uključivo) i 1.0 (isključivo).", "TEXT_TEXT_HELPURL": "https://sr.wikipedia.org/wiki/Niska", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON><PERSON>, reč ili red teksta.", "TEXT_JOIN_TITLE_CREATEWITH": "napiši tekst sa", "TEXT_JOIN_TOOLTIP": "Napraviti dio teksta spajajući različite stavke.", "TEXT_CREATE_JOIN_TITLE_JOIN": "<PERSON>janje<PERSON>", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ili drug<PERSON>či<PERSON> p<PERSON>aj odjelke kako bi iznova postavili ovaj tekst blok.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Dodajte stavku u tekst.", "TEXT_APPEND_TITLE": "na %1 dodaj tekst %2", "TEXT_APPEND_TOOLTIP": "Dodajte tekst na promenljivu „%1“.", "TEXT_LENGTH_TITLE": "dužina teksta %1", "TEXT_LENGTH_TOOLTIP": "Vraća broj slova (uključujući razmake) u datom tekstu.", "TEXT_ISEMPTY_TITLE": "%1 je prazan", "TEXT_ISEMPTY_TOOLTIP": "Vraća tačno ako je dostavljeni tekst prazan.", "TEXT_INDEXOF_TOOLTIP": "Vraća odnos prvog/zadnjeg pojavljivanja teksta u drugom tekstu. Vrađa %1 ako tekst nije pronađen.", "TEXT_INDEXOF_TITLE": "u tekstu %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "pronađi prvo pojavljivanje teksta", "TEXT_INDEXOF_OPERATOR_LAST": "pronađi poslednje pojavljivanje teksta", "TEXT_CHARAT_TITLE": "u tekstu %1 %2", "TEXT_CHARAT_FROM_START": "<PERSON>uzmi slovo #", "TEXT_CHARAT_FROM_END": "preuzmi slovo # sa kraja", "TEXT_CHARAT_FIRST": "preuzmi prvo slovo", "TEXT_CHARAT_LAST": "preuzmi poslednje slovo", "TEXT_CHARAT_RANDOM": "preuzmi slučajno slovo", "TEXT_CHARAT_TOOLTIP": "Vraća slovo na određeni položaj.", "TEXT_GET_SUBSTRING_TOOLTIP": "Vraća određeni deo te<PERSON>.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "u tekstu", "TEXT_GET_SUBSTRING_START_FROM_START": "preuzmi podnisku iz slova #", "TEXT_GET_SUBSTRING_START_FROM_END": "preuzmi podnisku iz slova # sa kraja", "TEXT_GET_SUBSTRING_START_FIRST": "preuzmi podnisku iz prvog slova", "TEXT_GET_SUBSTRING_END_FROM_START": "slovu #", "TEXT_GET_SUBSTRING_END_FROM_END": "slovu # sa kraja", "TEXT_GET_SUBSTRING_END_LAST": "pos<PERSON><PERSON><PERSON><PERSON> slovu", "TEXT_CHANGECASE_TOOLTIP": "Vraća primerak teksta sa drugačijom veličinom slova.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "velikim slov<PERSON>", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "malim slovima", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "svaka reč velikim slovom", "TEXT_TRIM_TOOLTIP": "Vraća kopiju teksta sa uklonjenim prostorom sa jednog od dva kraja.", "TEXT_TRIM_OPERATOR_BOTH": "trim praznine sa obe strane", "TEXT_TRIM_OPERATOR_LEFT": "skratiti prostor sa leve strane", "TEXT_TRIM_OPERATOR_RIGHT": "skratiti prostor sa desne strane", "TEXT_PRINT_TITLE": "prikaži %1", "TEXT_PRINT_TOOLTIP": "Prikažite određeni tekst, broj ili drugu vrednost na ekranu.", "TEXT_PROMPT_TYPE_TEXT": "pitaj za tekst sa porukom", "TEXT_PROMPT_TYPE_NUMBER": "pitaj za broj sa porukom", "TEXT_PROMPT_TOOLTIP_NUMBER": "Pitajte korisnika za broj.", "TEXT_PROMPT_TOOLTIP_TEXT": "Pitajte korisnika za unos teksta.", "TEXT_COUNT_MESSAGE0": "broj %1 u %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "B<PERSON>ji koliko puta se neki tekst pojavljuje unutar nekog drugog teksta.", "TEXT_REPLACE_MESSAGE0": "zamena %1 sa %2 u %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "Zamena svih pojava nekog teksta unutar nekog drugog teksta.", "TEXT_REVERSE_MESSAGE0": "obrnuto %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Obrć<PERSON> redosled karaktera u tekstu.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "<PERSON><PERSON><PERSON> prazan spisak", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> listu, dužine 0, ne sadržavajući  evidenciju podataka", "LISTS_CREATE_WITH_TOOLTIP": "Kreiraj listu sa bilo kojim brojem stavki.", "LISTS_CREATE_WITH_INPUT_WITH": "<PERSON><PERSON>vi spisak sa", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "spisak", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ili preuredite delove kako bi se reorganizovali ovaj blok liste.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Dodajte stavku na spisak.", "LISTS_REPEAT_TOOLTIP": "Pravi listu koja se sastoji od zadane vrednosti koju ponavljamo određeni broj šuta.", "LISTS_REPEAT_TITLE": "Napraviti listu sa stavkom %1 koja se ponavlja %2 puta", "LISTS_LENGTH_TITLE": "dužina spiska %1", "LISTS_LENGTH_TOOLTIP": "<PERSON>raća duž<PERSON>u spiska.", "LISTS_ISEMPTY_TITLE": "%1 je prazan", "LISTS_ISEMPTY_TOOLTIP": "Vraća vrednost tačno ako je lista prazna.", "LISTS_INLIST": "na spisku", "LISTS_INDEX_OF_FIRST": "pronađi prvo pojavljivanje stavke", "LISTS_INDEX_OF_LAST": "pronađi poslednje pojavljivanje stavke", "LISTS_INDEX_OF_TOOLTIP": "Vraća broj prvog i/poslednjeg ulaska elementa u listu. Vraća %1 Ako element nije pronađen.", "LISTS_GET_INDEX_GET": "<PERSON>uz<PERSON>", "LISTS_GET_INDEX_GET_REMOVE": "preuzmi i ukloni", "LISTS_GET_INDEX_REMOVE": "ukloni", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# sa kraja", "LISTS_GET_INDEX_FIRST": "prva", "LISTS_GET_INDEX_LAST": "poslednja", "LISTS_GET_INDEX_RANDOM": "slučajna", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 je prva stavka.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 je poslednja stavka.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Vraća stavku na određenu poziciju na listi.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Vraća prvu stavku na spisku.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Vraća poslednju stavku na spisku.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Vraća slučajnu stavku sa spiska.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Uklanja i vraća stavku sa određenog položaja na spisku.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Uklanja i vraća prvu stavku sa spiska.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Uklanja i vraća poslednju stavku sa spiska.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Uklanja i vraća slučajnu stavku sa spiska.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Uklanja stavku sa određenog položaja na spisku.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Uklanja prvu stavku sa spiska.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Uklanja poslednju stavku sa spiska.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Uklanja slučajnu stavku sa spiska.", "LISTS_SET_INDEX_SET": "<PERSON><PERSON>", "LISTS_SET_INDEX_INSERT": "ubaci na", "LISTS_SET_INDEX_INPUT_TO": "kao", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Postavlja stavku na određeni položaj na spisku.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Postavlja prvu stavku na spisku.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Postavlja poslednju stavku na spisku.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Postavlja slučajnu stavku na spisku.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Ubacuje stavku na određeni položaj na spisku.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Ubacuje stavku na početak spiska.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Dodajte stavku na kraj spiska.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Ubacuje stavku na slučajno mesto na spisku.", "LISTS_GET_SUBLIST_START_FROM_START": "preuzmi podspisak od #", "LISTS_GET_SUBLIST_START_FROM_END": "preuzmi podspisak iz # sa kraja", "LISTS_GET_SUBLIST_START_FIRST": "preuzmi podspisak od prve", "LISTS_GET_SUBLIST_END_FROM_START": "do #", "LISTS_GET_SUBLIST_END_FROM_END": "do # od kraja", "LISTS_GET_SUBLIST_END_LAST": "do poslednje", "LISTS_GET_SUBLIST_TOOLTIP": "Pravi kopiju određenog dela liste.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sortiraj %1 %2 %3", "LISTS_SORT_TOOLTIP": "Sortirajte kopiju spiska.", "LISTS_SORT_ORDER_ASCENDING": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_ORDER_DESCENDING": "opadajuće", "LISTS_SORT_TYPE_NUMERIC": "kao brojeve", "LISTS_SORT_TYPE_TEXT": "azbučno", "LISTS_SORT_TYPE_IGNORECASE": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ignoriši mala i velika slova", "LISTS_SPLIT_LIST_FROM_TEXT": "napravite listu sa teksta", "LISTS_SPLIT_TEXT_FROM_LIST": "da tekst iz liste", "LISTS_SPLIT_WITH_DELIMITER": "sa razdvajanje", "LISTS_SPLIT_TOOLTIP_SPLIT": "Podeliti tekst u listu tekstova, razbijanje na svakom graničnik.", "LISTS_SPLIT_TOOLTIP_JOIN": "Da se pridruži listu tekstova u jedan tekst, podeljenih za razdvajanje.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "obrnuto %1", "LISTS_REVERSE_TOOLTIP": "Obrni kopiju spiska.", "VARIABLES_GET_TOOLTIP": "Vraća vrednost ove promenljive.", "VARIABLES_GET_CREATE_SET": "<PERSON><PERSON><PERSON> „postavi %1“", "VARIABLES_SET": "postavi %1 u %2", "VARIABLES_SET_TOOLTIP": "Postavlja promenljivu tako da bude jednaka ulazu.", "VARIABLES_SET_CREATE_GET": "<PERSON><PERSON><PERSON> „preuzmi %1“", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "da", "PROCEDURES_DEFNORETURN_PROCEDURE": "uradite ne<PERSON>", "PROCEDURES_BEFORE_PARAMS": "sa:", "PROCEDURES_CALL_BEFORE_PARAMS": "sa:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Pravi funkciju bez izlaza.", "PROCEDURES_DEFNORETURN_COMMENT": "Opisati ovu funkciju...", "PROCEDURES_DEFRETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFRETURN_RETURN": "vrati", "PROCEDURES_DEFRETURN_TOOLTIP": "Pravi funkciju sa izlazom.", "PROCEDURES_ALLOW_STATEMENTS": "dozvoliti izreke", "PROCEDURES_DEF_DUPLICATE_WARNING": "Upozorenje: <PERSON><PERSON> ima duplikate parametara.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Pokrenite prilagođenu funkciju „%1“.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Pokrenite prilagođenu funkciju „%1“ i koristi njen izlaz.", "PROCEDURES_MUTATORCONTAINER_TITLE": "<PERSON><PERSON>", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON> do<PERSON>, uklonite ili pereuporяdočitь ulaza za ovu funkciju.", "PROCEDURES_MUTATORARG_TITLE": "naziv ulaza:", "PROCEDURES_MUTATORARG_TOOLTIP": "Dodajte ulazna funkci<PERSON>.", "PROCEDURES_HIGHLIGHT_DEF": "Istakni definiciju funkcije", "PROCEDURES_CREATE_DO": "<PERSON><PERSON><PERSON> „%1“", "PROCEDURES_IFRETURN_TOOLTIP": "Ukoliko je vrednost tačna, vrati drugu vrednost.", "PROCEDURES_IFRETURN_WARNING": "Upozorenje: <PERSON><PERSON>j blok se može koristiti jedino u definiciji funkcije.", "DIALOG_OK": "U redu", "DIALOG_CANCEL": "Ot<PERSON>ži"}