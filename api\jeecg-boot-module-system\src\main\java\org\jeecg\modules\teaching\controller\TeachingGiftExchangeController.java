package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.common.controller.BaseController;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.teaching.entity.TeachingGiftExchange;
import org.jeecg.modules.teaching.service.ITeachingGiftExchangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;


/**
 * 礼品兑换管理
 */
@Api(tags = "礼品兑换管理")
@RestController
@RequestMapping("/teaching/giftExchange")
@Slf4j
public class TeachingGiftExchangeController extends BaseController {
    
    @Autowired
    private ITeachingGiftExchangeService teachingGiftExchangeService;
    
    @Autowired
    private ISysUserService sysUserService;
    
    /**
     * 分页列表查询
     *
     * @param teachingGiftExchange
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "礼品兑换-分页列表查询")
    @ApiOperation(value = "礼品兑换-分页列表查询", notes = "礼品兑换-分页列表查询")
    @GetMapping(value = "/list")
    @PermissionData(pageComponent = "teaching/TeachingGiftExchangeList")
    public Result<?> queryPageList(TeachingGiftExchange teachingGiftExchange,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<TeachingGiftExchange> queryWrapper = QueryGenerator.initQueryWrapper(teachingGiftExchange, req.getParameterMap());
        Page<TeachingGiftExchange> page = new Page<TeachingGiftExchange>(pageNo, pageSize);
        IPage<TeachingGiftExchange> pageList = teachingGiftExchangeService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 获取指定学生的礼物兑换记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 兑换记录
     */
    @AutoLog(value = "获取学生礼物兑换记录")
    @ApiOperation(value = "获取学生礼物兑换记录", notes = "获取学生礼物兑换记录")
    @GetMapping(value = "/studentExchangeList")
    public Result<?> getStudentExchangeRecords(@RequestParam String userId,
                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        // 验证用户是否存在
        SysUser user = sysUserService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        // 查询该用户的礼物兑换记录
        QueryWrapper<TeachingGiftExchange> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("exchange_time"); // 按兑换时间降序排序
        
        Page<TeachingGiftExchange> page = new Page<>(pageNo, pageSize);
        IPage<TeachingGiftExchange> pageList = teachingGiftExchangeService.page(page, queryWrapper);
        
        return Result.ok(pageList);
    }
    
    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "礼品兑换-通过id删除")
    @ApiOperation(value = "礼品兑换-通过id删除", notes = "礼品兑换-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        teachingGiftExchangeService.removeById(id);
        return Result.ok("删除成功!");
    }
    
    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "礼品兑换-批量删除")
    @ApiOperation(value = "礼品兑换-批量删除", notes = "礼品兑换-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.teachingGiftExchangeService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }
    
    /**
     * 更新礼品领取状态
     *
     * @param id 礼品兑换记录ID
     * @return 操作结果
     */
    @AutoLog(value = "礼品兑换-更新领取状态")
    @ApiOperation(value = "礼品兑换-更新领取状态", notes = "礼品兑换-更新领取状态")
    @PostMapping(value = "/updateStatus")
    public Result<?> updateGiftStatus(@RequestParam(name = "id", required = true) String id) {
        TeachingGiftExchange giftExchange = teachingGiftExchangeService.getById(id);
        if (giftExchange == null) {
            return Result.error("礼品兑换记录不存在");
        }
        
        // 只有待领取状态(0)的记录才能更改为已领取状态(1)
        if (giftExchange.getStatus() != 0) {
            return Result.error("只有待领取状态的礼品才能更改为已领取");
        }
        
        // 更新状态为已领取
        giftExchange.setStatus(1);
        // 设置领取时间
        giftExchange.setReceiveTime(new Date());
        
        boolean success = teachingGiftExchangeService.updateById(giftExchange);
        if (success) {
            return Result.ok("礼品状态已更新为已领取");
        } else {
            return Result.error("更新礼品状态失败");
        }
    }
} 