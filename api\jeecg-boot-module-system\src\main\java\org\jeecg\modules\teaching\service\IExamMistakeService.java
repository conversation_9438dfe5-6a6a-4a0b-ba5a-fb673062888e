package org.jeecg.modules.teaching.service;

import org.jeecg.modules.teaching.entity.ExamMistake;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * @Description: 错题记录
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
public interface IExamMistakeService extends IService<ExamMistake> {

    /**
     * 记录错题
     * @param userId 用户ID
     * @param questionId 题目ID
     * @param answer 错误答案
     * @param mistakeTime 错误时间
     */
    void recordMistake(String userId, String questionId, String answer, Date mistakeTime);

    /**
     * 获取用户的错题列表
     * @param userId 用户ID
     * @return 错题列表
     */
    List<ExamMistake> getUserMistakes(String userId);
} 