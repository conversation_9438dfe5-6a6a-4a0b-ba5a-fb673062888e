package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="exam_collection对象", description="用户题目收藏表")
@Data
@TableName("exam_collection")
public class ExamCollection implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("user_id")
    @ApiModelProperty(value = "用户ID")
    private String userId;

    @TableField("question_id")
    @ApiModelProperty(value = "题目ID")
    private String questionId;

    @TableField("collection_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "收藏时间")
    private Date collectionTime;

    @ApiModelProperty(value = "标签")
    private String tags;

    @ApiModelProperty(value = "笔记")
    private String notes;
    
    @TableField(exist = false)
    @ApiModelProperty(value = "题目标题")
    private String questionTitle;

    // 新增：题目类型（非数据库字段，用于前端显示）
    @TableField(exist = false)
    @ApiModelProperty(value = "题目类型")
    private Integer questionType;
}