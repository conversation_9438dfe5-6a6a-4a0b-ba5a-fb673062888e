package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.teaching.entity.TeachingCourseNotification;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;

import java.util.Date;
import java.util.List;

/**
 * @Description: 课程通知
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
public interface ITeachingCourseNotificationService extends IService<TeachingCourseNotification> {
    
    /**
     * 创建新增课程通知
     * @param schedule 课程排期
     * @param notifyTeachers 是否通知教师
     * @param notifyStudents 是否通知学生
     * @return 是否成功
     */
    boolean createNewCourseNotification(TeachingCourseSchedule schedule, boolean notifyTeachers, boolean notifyStudents);
    
    /**
     * 创建更新课程通知
     * @param newSchedule 新课程排期
     * @param oldSchedule 旧课程排期
     * @param notifyTeachers 是否通知教师
     * @param notifyStudents 是否通知学生
     * @return 是否成功
     */
    boolean createUpdateCourseNotification(TeachingCourseSchedule newSchedule, TeachingCourseSchedule oldSchedule, boolean notifyTeachers, boolean notifyStudents);
    
    /**
     * 创建取消课程通知
     * @param schedule 课程排期
     * @param notifyTeachers 是否通知教师
     * @param notifyStudents 是否通知学生
     * @return 是否成功
     */
    boolean createCancelCourseNotification(TeachingCourseSchedule schedule, boolean notifyTeachers, boolean notifyStudents);
    
    /**
     * 创建课程提醒通知
     * @param schedule 课程排期
     * @param remindTime 提醒时间
     * @param notifyTeachers 是否通知教师
     * @param notifyStudents 是否通知学生
     * @return 是否成功
     */
    boolean createCourseReminderNotification(TeachingCourseSchedule schedule, Date remindTime, boolean notifyTeachers, boolean notifyStudents);
    
    /**
     * 处理待发送的通知
     * @return 处理成功的通知数量
     */
    int processNotifications();
    
    /**
     * 处理待发送的课程提醒通知
     * @return 处理成功的通知数量
     */
    int processReminderNotifications();
    
    /**
     * 查询待发送的课程提醒通知
     * @return 待发送的通知列表
     */
    List<TeachingCourseNotification> getPendingReminders();
    
    /**
     * 发送通知
     * @param notification 通知对象
     * @return 是否成功
     */
    boolean sendNotification(TeachingCourseNotification notification);
    
    /**
     * 获取课程排期的相关用户IDs
     * @param schedule 课程排期
     * @param includeTeacher 是否包括教师
     * @param includeStudents 是否包括学生
     * @return 用户ID列表，以逗号分隔
     */
    String getScheduleRelatedUserIds(TeachingCourseSchedule schedule, boolean includeTeacher, boolean includeStudents);
    
    /**
     * 获取用户的课程通知
     * @param page 分页参数
     * @param userId 用户ID
     * @param notificationType 通知类型（可选）
     * @return 用户的课程通知分页列表
     */
    com.baomidou.mybatisplus.core.metadata.IPage<TeachingCourseNotification> getUserNotifications(
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<TeachingCourseNotification> page,
        String userId,
        String notificationType);
        
    /**
     * 获取用户的最近通知
     * @param page 分页参数
     * @param userId 用户ID
     * @param notificationType 通知类型（可选）
     * @return 用户的最近课程通知分页列表
     */
    com.baomidou.mybatisplus.core.metadata.IPage<TeachingCourseNotification> getUserRecentNotifications(
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<TeachingCourseNotification> page,
        String userId,
        String notificationType);
        
    /**
     * 标记通知为已读
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAsRead(String notificationId, String userId);
    
    /**
     * 获取用户的所有通知（不分页）
     * @param userId 用户ID
     * @return 用户的所有通知列表
     */
    List<TeachingCourseNotification> getUserAllNotifications(String userId);
} 