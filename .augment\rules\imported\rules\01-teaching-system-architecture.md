# Teaching系统架构规范

## 系统概述

Teaching是一个基于Spring Boot + Vue.js的在线教学管理平台，集成了HOJ在线判题系统，提供完整的教学、考试、练习解决方案。

## 技术栈

### 后端技术栈
- **框架**: Spring Boot 2.1.3.RELEASE
- **ORM**: MyBatis-Plus 3.1.2
- **安全**: Apache Shiro 1.4.0 + JWT 3.7.0
- **数据库**: MySQL 5.6+
- **缓存**: Redis
- **连接池**: Druid 1.1.10
- **任务调度**: Quartz
- **文档**: Swagger-UI
- **工具**: Lombok, FastJSON, POI

### 前端技术栈
- **框架**: Vue.js 2.x
- **UI库**: Ant Design Vue
- **构建工具**: Webpack
- **包管理**: npm/yarn
- **HTTP客户端**: Axios

### 集成服务
- **判题服务**: <PERSON>OJ (Himit Online Judge)
- **文件存储**: 七牛云存储
- **消息通知**: WebSocket

## 项目结构

```
teaching/
├── api/                          # 后端服务
│   ├── jeecg-boot-base-common/   # 公共基础模块
│   └── jeecg-boot-module-system/ # 系统核心模块
│       └── src/main/java/org/jeecg/modules/
│           ├── system/           # 系统管理模块
│           └── teaching/         # 教学业务模块
├── web/                          # 前端应用
│   └── src/
│       ├── api/                  # API接口定义
│       ├── components/           # 公共组件
│       ├── views/                # 页面视图
│       └── utils/                # 工具函数
└── HOJ/                          # 判题服务
    ├── hoj-springboot/           # HOJ后端
    └── hoj-vue/                  # HOJ前端
```

## 核心业务模块

### 1. 教学管理模块 (teaching)
- **课程管理**: 课程创建、编辑、发布
- **班级管理**: 班级创建、学员管理
- **排课系统**: 可视化排课、教室管理
- **通知系统**: 课程通知、消息推送

### 2. 考试系统模块 (examSystem)
- **题库管理**: 题目CRUD、批量导入导出
- **试卷管理**: 组卷、试卷发布
- **在线考试**: 考试监控、防作弊
- **成绩管理**: 成绩统计、分析

### 3. 练习系统模块 (practice)
- **在线刷题**: 按条件筛选练习
- **错题管理**: 错题收集、复习
- **进度跟踪**: 学习进度统计

### 4. 判题集成模块 (judge)
- **HOJ集成**: 与HOJ判题服务通信
- **代码提交**: 代码编译、运行
- **结果处理**: 判题结果解析、存储

## 数据库设计原则

### 表命名规范
- 系统表: `sys_*` (如: sys_user, sys_role)
- 教学表: `teaching_*` (如: teaching_course, teaching_class)
- 考试表: `exam_*` (如: exam_question, exam_paper)

### 字段规范
- 主键: `id` (varchar(36), UUID)
- 创建人: `create_by` (varchar(50))
- 创建时间: `create_time` (datetime)
- 更新人: `update_by` (varchar(50))
- 更新时间: `update_time` (datetime)
- 删除标志: `del_flag` (tinyint, 0-未删除, 1-已删除)

## API设计规范

### RESTful风格
- GET: 查询操作
- POST: 创建操作
- PUT: 更新操作
- DELETE: 删除操作

### 统一响应格式
```java
public class Result<T> {
    private boolean success;    // 成功标志
    private String message;     // 返回消息
    private Integer code;       // 状态码
    private T result;          // 返回数据
    private long timestamp;    // 时间戳
}
```

### 状态码规范
- 200: 成功
- 500: 服务器内部错误
- 510: 权限认证未通过

## 权限控制

### Shiro + JWT
- 基于角色的访问控制(RBAC)
- JWT Token认证
- 接口级权限控制

### 权限注解
```java
@RequiresPermissions("teaching:course:add")
@RequiresRoles("teacher")
```
