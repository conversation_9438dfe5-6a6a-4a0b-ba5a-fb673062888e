package org.jeecg.modules.teaching.service;

import org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 单次课程实例
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
public interface ITeachingCourseScheduleInstanceService extends IService<TeachingCourseScheduleInstance> {
    
    /**
     * 根据父课程ID和实例日期查询课程实例
     * 
     * @param parentId 父课程ID
     * @param instanceDate 实例日期 (格式: yyyy-MM-dd)
     * @return 课程实例对象，如果不存在返回null
     */
    TeachingCourseScheduleInstance getInstanceByParentIdAndDate(String parentId, String instanceDate);
    
    /**
     * 添加或更新单次课程实例
     * 
     * @param instance 课程实例
     * @param sendNotification 是否发送通知
     * @return 处理结果
     */
    Result<?> addOrUpdateInstance(TeachingCourseScheduleInstance instance, boolean sendNotification);
    
    /**
     * 添加或更新单次课程实例，处理拖拽场景
     * 
     * @param instance 课程实例
     * @param originalDate 原始日期 (格式: yyyy-MM-dd)，用于拖拽场景
     * @param sendNotification 是否发送通知
     * @return 处理结果
     */
    Result<?> addOrUpdateInstanceWithOriginalDate(TeachingCourseScheduleInstance instance, String originalDate, boolean sendNotification);
    
    /**
     * 删除单次课程实例
     * 
     * @param id 实例ID
     * @param sendNotification 是否发送通知
     * @return 处理结果
     */
    Result<?> deleteInstance(String id, boolean sendNotification);
} 