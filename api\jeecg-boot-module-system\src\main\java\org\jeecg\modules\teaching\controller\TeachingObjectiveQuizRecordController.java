package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizRecord;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;
import org.jeecg.modules.teaching.entity.TeachingCourse;
import org.jeecg.modules.teaching.entity.TeachingCourseUnit;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuizRecordService;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuizDetailService;
import org.jeecg.modules.teaching.service.ITeachingCourseService;
import org.jeecg.modules.teaching.service.ITeachingCourseUnitService;
import org.jeecg.modules.teaching.vo.ObjectiveQuizSaveVO;
import org.jeecg.modules.teaching.vo.QuizRecordExportVO;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.web.servlet.ModelAndView;

import org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuestionService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客观题答题记录 Controller
 */
@Slf4j
@Api(tags = "客观题答题记录")
@RestController
@RequestMapping("/teaching/objectiveQuizRecord")
public class TeachingObjectiveQuizRecordController extends JeecgController<TeachingObjectiveQuizRecord, ITeachingObjectiveQuizRecordService> {
    
    @Autowired
    private ITeachingObjectiveQuizRecordService recordService;
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private ITeachingObjectiveQuizDetailService detailService;
    
    @Autowired
    private ITeachingCourseService courseService;
    
    @Autowired
    private ITeachingCourseUnitService unitService;
    
    @Autowired
    private ISysDepartService sysDepartService;
    
    @Autowired
    private ITeachingObjectiveQuestionService objectiveQuestionService;
    
    /**
     * 分页列表查询
     */
    @AutoLog(value = "客观题答题记录-分页列表查询")
    @ApiOperation(value = "客观题答题记录-分页列表查询", notes = "客观题答题记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingObjectiveQuizRecord record,
                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                HttpServletRequest req) {
        QueryWrapper<TeachingObjectiveQuizRecord> queryWrapper = QueryGenerator.initQueryWrapper(record, req.getParameterMap());
        Page<TeachingObjectiveQuizRecord> page = new Page<>(pageNo, pageSize);
        IPage<TeachingObjectiveQuizRecord> pageList = recordService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 保存答题记录
     */
    @AutoLog(value = "客观题答题记录-保存")
    @ApiOperation(value = "客观题答题记录-保存", notes = "保存客观题答题记录及详情")
    @PostMapping(value = "/save")
    public Result<?> save(@RequestBody ObjectiveQuizSaveVO quizSaveVO) {
        try {
            boolean success = recordService.saveQuizRecord(quizSaveVO);
            if (success) {
                return Result.ok("保存成功，获得5个金币奖励！");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存答题记录失败", e);
            return Result.error("操作失败，" + e.getMessage());
        }
    }
    
    /**
     * 删除答题记录
     */
    @AutoLog(value = "客观题答题记录-删除")
    @ApiOperation(value = "客观题答题记录-删除", notes = "客观题答题记录-删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            recordService.removeById(id);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error("删除失败", e);
            return Result.error("删除失败!");
        }
    }
    
    /**
     * 批量删除答题记录
     */
    @AutoLog(value = "客观题答题记录-批量删除")
    @ApiOperation(value = "客观题答题记录-批量删除", notes = "客观题答题记录-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            recordService.removeByIds(Arrays.asList(ids.split(",")));
            return Result.ok("批量删除成功!");
        } catch (Exception e) {
            log.error("批量删除失败", e);
            return Result.error("批量删除失败!");
        }
    }
    
    /**
     * 根据班级ID查询学生答题记录
     */
    @AutoLog(value = "客观题答题记录-班级查询")
    @ApiOperation(value = "客观题答题记录-班级查询", notes = "根据班级ID查询学生答题记录")
    @GetMapping(value = "/listByDepart")
    public Result<?> listByDepart(
            @RequestParam(name = "departId", required = true) String departId,
            @RequestParam(name = "courseId", required = false) String courseId,
            @RequestParam(name = "unitId", required = false) String unitId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            // 验证当前教师是否有权限查看该班级的学生答题记录
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            // 查询条件
            QueryWrapper<TeachingObjectiveQuizRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("depart_id", departId);
            
            if (courseId != null && !courseId.isEmpty()) {
                queryWrapper.eq("course_id", courseId);
            }
            
            if (unitId != null && !unitId.isEmpty()) {
                queryWrapper.eq("unit_id", unitId);
            }
            
            // 分页查询
            Page<TeachingObjectiveQuizRecord> page = new Page<>(pageNo, pageSize);
            IPage<TeachingObjectiveQuizRecord> pageList = recordService.page(page, queryWrapper);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("查询班级答题记录失败", e);
            return Result.error("查询失败，" + e.getMessage());
        }
    }
    
    /**
     * 查询当前学生的答题记录
     */
    @AutoLog(value = "客观题答题记录-学生查询")
    @ApiOperation(value = "客观题答题记录-学生查询", notes = "查询当前学生的答题记录")
    @GetMapping(value = "/listByStudent")
    public Result<?> listByStudent(
            @RequestParam(name = "courseId", required = false) String courseId,
            @RequestParam(name = "unitId", required = false) String unitId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            // 获取当前用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            // 查询条件
            QueryWrapper<TeachingObjectiveQuizRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", loginUser.getId());
            
            if (courseId != null && !courseId.isEmpty()) {
                queryWrapper.eq("course_id", courseId);
            }
            
            if (unitId != null && !unitId.isEmpty()) {
                queryWrapper.eq("unit_id", unitId);
            }
            
            // 按时间倒序排序
            queryWrapper.orderByDesc("create_time");
            
            // 分页查询
            Page<TeachingObjectiveQuizRecord> page = new Page<>(pageNo, pageSize);
            IPage<TeachingObjectiveQuizRecord> pageList = recordService.page(page, queryWrapper);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("查询学生答题记录失败", e);
            return Result.error("查询失败，" + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取查询参数
            String departId = request.getParameter("departId");
            String courseId = request.getParameter("courseId");
            String unitId = request.getParameter("unitId");
            
            // 构造查询条件
            TeachingObjectiveQuizRecord record = new TeachingObjectiveQuizRecord();
            if (oConvertUtils.isNotEmpty(departId)) {
                record.setDepartId(departId);
            }
            if (oConvertUtils.isNotEmpty(courseId)) {
                record.setCourseId(courseId);
            }
            if (oConvertUtils.isNotEmpty(unitId)) {
                record.setUnitId(unitId);
            }

            // 查询条件
            QueryWrapper<TeachingObjectiveQuizRecord> queryWrapper = QueryGenerator.initQueryWrapper(record, request.getParameterMap());
            
            // 获取导出数据
            List<TeachingObjectiveQuizRecord> recordList = recordService.list(queryWrapper);
            List<TeachingObjectiveQuizRecord> filteredList = null;

            // 过滤选中数据
            String selections = request.getParameter("selections");
            if (oConvertUtils.isNotEmpty(selections)) {
                List<String> selectionList = Arrays.asList(selections.split(","));
                filteredList = recordList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
            } else {
                filteredList = recordList;
            }
            
            // 转换为导出VO
            List<QuizRecordExportVO> exportList = convertToExportVO(filteredList);
            
            // 获取当前用户
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            // 导出文件名
            String title = "客观题答题记录";
            
            // 导出Excel
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, title);
            mv.addObject(NormalExcelConstants.CLASS, QuizRecordExportVO.class);
            ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
            mv.addObject(NormalExcelConstants.PARAMS, exportParams);
            mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
            
            return mv;
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            return new ModelAndView();
        }
    }
    
    /**
     * 将答题记录转换为导出VO
     */
    private List<QuizRecordExportVO> convertToExportVO(List<TeachingObjectiveQuizRecord> recordList) {
        if (recordList == null || recordList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 收集所有需要的ID
        Set<String> userIds = new HashSet<>();
        Set<String> departIds = new HashSet<>();
        Set<String> courseIds = new HashSet<>();
        Set<String> unitIds = new HashSet<>();
        Set<String> recordIds = new HashSet<>();
        Set<String> questionIds = new HashSet<>();
        
        for (TeachingObjectiveQuizRecord record : recordList) {
            if (oConvertUtils.isNotEmpty(record.getUserId())) {
                userIds.add(record.getUserId());
            }
            if (oConvertUtils.isNotEmpty(record.getDepartId())) {
                departIds.add(record.getDepartId());
            }
            if (oConvertUtils.isNotEmpty(record.getCourseId())) {
                courseIds.add(record.getCourseId());
            }
            if (oConvertUtils.isNotEmpty(record.getUnitId())) {
                unitIds.add(record.getUnitId());
            }
            recordIds.add(record.getId());
        }
        
        // 查询相关数据
        Map<String, SysUser> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            Collection<SysUser> userCollection = sysUserService.listByIds(userIds);
            userMap = userCollection.stream().collect(Collectors.toMap(SysUser::getId, user -> user));
        }
        
        // 查询部门/班级名称
        Map<String, String> departMap = new HashMap<>();
        if (!departIds.isEmpty()) {
            Collection<SysDepart> departCollection = sysDepartService.listByIds(departIds);
            departMap = departCollection.stream().collect(Collectors.toMap(SysDepart::getId, SysDepart::getDepartName));
        }
        
        Map<String, TeachingCourse> courseMap = new HashMap<>();
        if (!courseIds.isEmpty()) {
            Collection<TeachingCourse> courseCollection = courseService.listByIds(courseIds);
            courseMap = courseCollection.stream().collect(Collectors.toMap(TeachingCourse::getId, course -> course));
        }
        
        Map<String, TeachingCourseUnit> unitMap = new HashMap<>();
        if (!unitIds.isEmpty()) {
            Collection<TeachingCourseUnit> unitCollection = unitService.listByIds(unitIds);
            unitMap = unitCollection.stream().collect(Collectors.toMap(TeachingCourseUnit::getId, unit -> unit));
        }
        
        // 查询答题详情
        Map<String, List<TeachingObjectiveQuizDetail>> detailsMap = new HashMap<>();
        for (String recordId : recordIds) {
            List<TeachingObjectiveQuizDetail> details = detailService.listByRecordIdOrderByQuestionCreateTime(recordId);
            detailsMap.put(recordId, details);
            
            // 收集所有题目ID
            for (TeachingObjectiveQuizDetail detail : details) {
                if (detail.getQuestionId() != null) {
                    questionIds.add(detail.getQuestionId());
                }
            }
        }
        
        // 查询所有题目信息，获取选项
        Map<String, TeachingObjectiveQuestion> questionMap = new HashMap<>();
        if (!questionIds.isEmpty()) {
            Collection<TeachingObjectiveQuestion> questionCollection = objectiveQuestionService.listByIds(questionIds);
            questionMap = questionCollection.stream().collect(Collectors.toMap(TeachingObjectiveQuestion::getId, question -> question));
        }
        
        // 转换为导出VO
        List<QuizRecordExportVO> exportList = new ArrayList<>();
        for (TeachingObjectiveQuizRecord record : recordList) {
            QuizRecordExportVO exportVO = new QuizRecordExportVO();
            
            // 设置学生信息
            if (record.getUserId() != null && userMap.containsKey(record.getUserId())) {
                exportVO.setStudentName(userMap.get(record.getUserId()).getRealname());
            }
            
            // 设置班级信息
            if (record.getDepartId() != null && departMap.containsKey(record.getDepartId())) {
                exportVO.setDepartName(departMap.get(record.getDepartId()));
            } else {
                exportVO.setDepartName(record.getDepartId());
            }
            
            // 设置课程信息
            if (record.getCourseId() != null && courseMap.containsKey(record.getCourseId())) {
                exportVO.setCourseName(courseMap.get(record.getCourseId()).getCourseName());
            }
            
            // 设置单元信息
            if (record.getUnitId() != null && unitMap.containsKey(record.getUnitId())) {
                exportVO.setUnitName(unitMap.get(record.getUnitId()).getUnitName());
            }
            
            // 设置题目数量和得分
            exportVO.setTotalCount(record.getTotalCount());
            exportVO.setCorrectCount(record.getCorrectCount());
            exportVO.setCorrectRate(record.getCorrectRate());
            exportVO.setQuizTime(record.getQuizTime());
            exportVO.setRecordId(record.getId());
            
            // 设置做题详情
            List<TeachingObjectiveQuizDetail> details = detailsMap.get(record.getId());
            if (details != null && !details.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                int count = 1;
                for (TeachingObjectiveQuizDetail detail : details) {
                    String questionType = detail.getQuestionType() == 1 ? "单选题" : "判断题";
                    String isCorrect = detail.getIsCorrect() == 1 ? "正确" : "错误";
                    
                    sb.append(count).append(". [").append(questionType).append("] ");
                    sb.append(detail.getQuestionContent()).append("\n");
                    
                    // 对于单选题，添加选项信息
                    if (detail.getQuestionType() == 1 && detail.getQuestionId() != null) {
                        TeachingObjectiveQuestion question = questionMap.get(detail.getQuestionId());
                        if (question != null && question.getOptions() != null) {
                            sb.append("   选项: ");
                            
                            // 处理选项数据，参考TeachingObjectiveQuizRecordList.vue中的处理方式
                            try {
                                Map<String, String> optionsMap = parseOptions(question.getOptions());
                                if (!optionsMap.isEmpty()) {
                                    for (Map.Entry<String, String> entry : optionsMap.entrySet()) {
                                        sb.append(entry.getKey()).append(": ").append(entry.getValue());
                                        if (entry.getKey().equals(detail.getCorrectAnswer())) {
                                            sb.append(" (正确答案)");
                                        }
                                        if (entry.getKey().equals(detail.getUserAnswer()) && !entry.getKey().equals(detail.getCorrectAnswer())) {
                                            sb.append(" (用户选择)");
                                        }
                                        sb.append("; ");
                                    }
                                    sb.append("\n");
                                }
                            } catch (Exception e) {
                                log.error("解析选项失败: " + e.getMessage());
                                sb.append("选项解析失败; \n");
                            }
                        }
                    }
                    
                    sb.append("   用户答案: ").append(detail.getUserAnswer());
                    sb.append(", 正确答案: ").append(detail.getCorrectAnswer());
                    sb.append(", 结果: ").append(isCorrect).append("\n");
                    count++;
                }
                exportVO.setQuizDetail(sb.toString());
            } else {
                exportVO.setQuizDetail("无详情记录");
            }
            
            exportList.add(exportVO);
        }
        
        return exportList;
    }
    
    /**
     * 解析选项数据，参考TeachingObjectiveQuizRecordList.vue中的解析方法
     */
    private Map<String, String> parseOptions(Object options) {
        Map<String, String> result = new HashMap<>();
        if (options == null) {
            return result;
        }
        
        // 如果已经是Map类型，直接转换
        if (options instanceof Map) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, String> map = (Map<String, String>) options;
                return map;
            } catch (Exception e) {
                log.error("选项转换Map失败: " + e.getMessage());
            }
        }
        
        // 如果是字符串，尝试解析JSON
        if (options instanceof String) {
            String optionsStr = (String) options;
            
            // 尝试直接解析为JSON对象
            try {
                JSONObject jsonObject = JSON.parseObject(optionsStr);
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    result.put(entry.getKey(), String.valueOf(entry.getValue()));
                }
                return result;
            } catch (Exception e) {
                // 解析失败，尝试其他方法
                log.debug("JSON解析失败，尝试其他方法: " + e.getMessage());
            }
            
            // 尝试替换引号后解析
            try {
                String fixedJson = optionsStr
                    .replace("'", "\"")
                    .replace("\\\"", "'");
                JSONObject jsonObject = JSON.parseObject(fixedJson);
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    result.put(entry.getKey(), String.valueOf(entry.getValue()));
                }
                return result;
            } catch (Exception e) {
                // 继续尝试其他方法
                log.debug("修复引号后JSON解析失败: " + e.getMessage());
            }
            
            // 使用正则表达式匹配键值对
            try {
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("[\"']?([A-Z])[\"']?\\s*:\\s*[\"']?([^,\"'}]*)[\"']?");
                java.util.regex.Matcher matcher = pattern.matcher(optionsStr);
                
                while (matcher.find()) {
                    result.put(matcher.group(1), matcher.group(2).trim());
                }
                
                if (!result.isEmpty()) {
                    return result;
                }
            } catch (Exception e) {
                log.debug("正则解析失败: " + e.getMessage());
            }
        }
        
        // 如果所有方法都失败，返回空Map
        return result;
    }
} 