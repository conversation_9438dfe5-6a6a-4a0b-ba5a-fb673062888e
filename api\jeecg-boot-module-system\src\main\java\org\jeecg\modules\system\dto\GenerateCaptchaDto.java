package org.jeecg.modules.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 生成验证码DTO
 */
@ApiModel(value = "验证码生成参数", description = "用于生成验证码的参数对象")
public class GenerateCaptchaDto {

    @ApiModelProperty(value = "验证码类型", example = "1", notes = "1-滑块，2-旋转，3-拼图，4-点选")
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "GenerateCaptchaDto{" +
                "type=" + type +
                '}';
    }
} 