package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.service.ISysConfigService;
import org.jeecg.modules.teaching.entity.TeachingDailyTask;
import org.jeecg.modules.teaching.mapper.TeachingDailyTaskMapper;
import org.jeecg.modules.teaching.service.ITeachingDailyTaskService;
import org.jeecg.modules.teaching.service.ITeachingStudentCoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 每日任务服务实现类
 */
@Service
public class TeachingDailyTaskServiceImpl extends ServiceImpl<TeachingDailyTaskMapper, TeachingDailyTask> implements ITeachingDailyTaskService {

    @Autowired
    private ITeachingStudentCoinService studentCoinService;
    
    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public Result<List<Map<String, Object>>> getUserDailyTasks(String userId) {
        Result<List<Map<String, Object>>> result = new Result<>();
        
        try {
            // 获取今天的日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String today = dateFormat.format(new Date());
            Date taskDate = dateFormat.parse(today);
            
            // 查询用户今日任务完成状态
            QueryWrapper<TeachingDailyTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("task_date", taskDate);
            List<TeachingDailyTask> taskList = this.list(queryWrapper);
            
            // 从配置中获取每日任务奖励金币数
            int signInCoin = this.getConfigCoinValue("daily_task_signin_coin", 10);
            int commentCoin = this.getConfigCoinValue("daily_task_comment_coin", 10);
            int likeCoin = this.getConfigCoinValue("daily_task_like_coin", 10);
            
            // 构造返回结果
            List<Map<String, Object>> resultList = new ArrayList<>();
            
            // 签到任务
            Map<String, Object> signInTask = new HashMap<>();
            signInTask.put("taskId", 1);
            signInTask.put("taskName", "每日签到");
            signInTask.put("taskDesc", "完成每日签到");
            signInTask.put("coinReward", signInCoin);
            signInTask.put("completed", false);
            
            // 评论任务
            Map<String, Object> commentTask = new HashMap<>();
            commentTask.put("taskId", 2);
            commentTask.put("taskName", "评论作品");
            commentTask.put("taskDesc", "给其他作品发表评论");
            commentTask.put("coinReward", commentCoin);
            commentTask.put("completed", false);
            
            // 点赞任务
            Map<String, Object> likeTask = new HashMap<>();
            likeTask.put("taskId", 3);
            likeTask.put("taskName", "点赞作品");
            likeTask.put("taskDesc", "给其他作品点赞");
            likeTask.put("coinReward", likeCoin);
            likeTask.put("completed", false);
            
            // 更新任务状态
            for (TeachingDailyTask task : taskList) {
                if (task.getTaskType() == 1 && task.getStatus() == 1) {
                    signInTask.put("completed", true);
                } else if (task.getTaskType() == 2 && task.getStatus() == 1) {
                    commentTask.put("completed", true);
                } else if (task.getTaskType() == 3 && task.getStatus() == 1) {
                    likeTask.put("completed", true);
                }
            }
            
            resultList.add(signInTask);
            resultList.add(commentTask);
            resultList.add(likeTask);
            
            result.setSuccess(true);
            result.setResult(resultList);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("获取每日任务失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional
    public Result<Integer> completeSignIn(String userId) {
        Result<Integer> result = new Result<>();
        
        try {
            // 获取今天的日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String today = dateFormat.format(new Date());
            Date taskDate = dateFormat.parse(today);
            
            // 检查今日是否已签到
            QueryWrapper<TeachingDailyTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("task_type", 1); // 1表示签到任务
            queryWrapper.eq("task_date", taskDate);
            TeachingDailyTask existTask = this.getOne(queryWrapper);
            
            if (existTask != null && existTask.getStatus() == 1) {
                result.setSuccess(true);
                result.setResult(0); // 已签到，不再奖励金币
                result.setMessage("今日已签到");
                return result;
            }
            
            // 创建或更新签到记录
            if (existTask == null) {
                existTask = new TeachingDailyTask();
                existTask.setUserId(userId);
                existTask.setTaskType(1);
                existTask.setStatus(1);
                existTask.setTaskDate(taskDate);
                existTask.setCompleteTime(new Date());
                this.save(existTask);
            } else {
                existTask.setStatus(1);
                existTask.setCompleteTime(new Date());
                this.updateById(existTask);
            }
            
            // 从配置获取签到任务奖励金币数量
            int coinReward = this.getConfigCoinValue("daily_task_signin_coin", 10);
            
            // 增加金币奖励
            studentCoinService.addUserCoin(userId, coinReward, 4, "完成每日签到任务", existTask.getId());
            
            result.setSuccess(true);
            result.setResult(coinReward); // 返回获得的金币数量
            result.setMessage("签到成功，获得" + coinReward + "金币");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResult(0);
            result.setMessage("签到失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional
    public Result<Integer> handleCommentTask(String userId, String workId) {
        Result<Integer> result = new Result<>();
        
        try {
            // 获取今天的日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String today = dateFormat.format(new Date());
            Date taskDate = dateFormat.parse(today);
            
            // 检查今日是否已完成评论任务
            QueryWrapper<TeachingDailyTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("task_type", 2); // 2表示评论任务
            queryWrapper.eq("task_date", taskDate);
            TeachingDailyTask existTask = this.getOne(queryWrapper);
            
            if (existTask != null && existTask.getStatus() == 1) {
                result.setSuccess(true);
                result.setResult(0); // 已完成，不再奖励金币
                result.setMessage("今日已完成评论任务");
                return result;
            }
            
            // 创建或更新任务记录
            if (existTask == null) {
                existTask = new TeachingDailyTask();
                existTask.setUserId(userId);
                existTask.setTaskType(2);
                existTask.setStatus(1);
                existTask.setTaskDate(taskDate);
                existTask.setCompleteTime(new Date());
                existTask.setRelatedId(workId);
                this.save(existTask);
            } else {
                existTask.setStatus(1);
                existTask.setCompleteTime(new Date());
                existTask.setRelatedId(workId);
                this.updateById(existTask);
            }
            
            // 从配置获取评论任务奖励金币数量
            int coinReward = this.getConfigCoinValue("daily_task_comment_coin", 10);
            
            // 增加金币奖励
            studentCoinService.addUserCoin(userId, coinReward, 4, "完成评论作品任务", existTask.getId());
            
            result.setSuccess(true);
            result.setResult(coinReward); // 返回获得的金币数量
            result.setMessage("评论任务完成，获得" + coinReward + "金币");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResult(0);
            result.setMessage("评论任务处理失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    @Transactional
    public Result<Integer> handleLikeTask(String userId, String workId) {
        Result<Integer> result = new Result<>();
        
        try {
            // 获取今天的日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String today = dateFormat.format(new Date());
            Date taskDate = dateFormat.parse(today);
            
            // 检查今日是否已完成点赞任务
            QueryWrapper<TeachingDailyTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("task_type", 3); // 3表示点赞任务
            queryWrapper.eq("task_date", taskDate);
            TeachingDailyTask existTask = this.getOne(queryWrapper);
            
            if (existTask != null && existTask.getStatus() == 1) {
                result.setSuccess(true);
                result.setResult(0); // 已完成，不再奖励金币
                result.setMessage("今日已完成点赞任务");
                return result;
            }
            
            // 创建或更新任务记录
            if (existTask == null) {
                existTask = new TeachingDailyTask();
                existTask.setUserId(userId);
                existTask.setTaskType(3);
                existTask.setStatus(1);
                existTask.setTaskDate(taskDate);
                existTask.setCompleteTime(new Date());
                existTask.setRelatedId(workId);
                this.save(existTask);
            } else {
                existTask.setStatus(1);
                existTask.setCompleteTime(new Date());
                existTask.setRelatedId(workId);
                this.updateById(existTask);
            }
            
            // 从配置获取点赞任务奖励金币数量
            int coinReward = this.getConfigCoinValue("daily_task_like_coin", 10);
            
            // 增加金币奖励
            studentCoinService.addUserCoin(userId, coinReward, 4, "完成点赞作品任务", existTask.getId());
            
            result.setSuccess(true);
            result.setResult(coinReward); // 返回获得的金币数量
            result.setMessage("点赞任务完成，获得" + coinReward + "金币");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResult(0);
            result.setMessage("点赞任务处理失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 从配置中获取金币数量，如果配置不存在则使用默认值
     * 
     * @param configKey 配置项键名
     * @param defaultValue 默认值
     * @return 金币数量
     */
    private int getConfigCoinValue(String configKey, int defaultValue) {
        String value = sysConfigService.getConfigItem(configKey);
        if (value != null && !value.isEmpty()) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
} 