package org.jeecg.modules.teaching.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 课程通知处理定时任务
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Slf4j
@Component
public class CourseNotificationJob implements Job {

    @Autowired
    private ITeachingCourseNotificationService teachingCourseNotificationService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("=====开始执行课程通知处理定时任务，时间：{}=====", DateUtils.now());
        try {
            // 处理普通通知
            int notificationCount = teachingCourseNotificationService.processNotifications();
            log.info("处理普通通知 {} 条", notificationCount);
            
            // 处理课程提醒通知
            int reminderCount = teachingCourseNotificationService.processReminderNotifications();
            log.info("处理课程提醒通知 {} 条", reminderCount);
            
            log.info("=====课程通知处理定时任务执行完毕，共处理 {} 条通知=====", notificationCount + reminderCount);
        } catch (Exception e) {
            log.error("课程通知处理定时任务执行异常", e);
        }
    }
} 