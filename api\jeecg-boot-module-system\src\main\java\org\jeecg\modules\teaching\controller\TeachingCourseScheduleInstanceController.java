package org.jeecg.modules.teaching.controller;

import java.util.Date;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.teaching.constant.RoleConstant;
import org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 单次课程实例
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
@Api(tags="单次课程实例")
@RestController
@RequestMapping("/teaching/courseScheduleInstance")
@Slf4j
public class TeachingCourseScheduleInstanceController {
    @Autowired
    private ITeachingCourseScheduleInstanceService teachingCourseScheduleInstanceService;

    @Autowired
    private ISysUserService sysUserService;
    
    /**
     * 分页列表查询
     *
     * @param teachingCourseScheduleInstance
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "单次课程实例-分页列表查询")
    @ApiOperation(value="单次课程实例-分页列表查询", notes="单次课程实例-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingCourseScheduleInstance teachingCourseScheduleInstance,
                               @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                               @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                               HttpServletRequest req) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);
        
        if(user == null) {
            return Result.error("未获取到登录用户");
        }

        QueryWrapper<TeachingCourseScheduleInstance> queryWrapper = QueryGenerator.initQueryWrapper(teachingCourseScheduleInstance, req.getParameterMap());
        
        // 应用权限过滤
        Set<String> roleSet = sysUserService.getUserRolesSet(username);
        // 教师只能查看自己的课程排期
        if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
            queryWrapper.eq("teacher_id", user.getId());
        } else if (roleSet.contains(RoleConstant.STUDENT)) {
            // 学生只能查看与自己所在班级相关的课程排期
            // 这部分需要从父类继承过来，保持一致
            queryWrapper.eq("1", "2"); // 这里暂时使用一个不可能成立的条件
        }
        
        Page<TeachingCourseScheduleInstance> page = new Page<TeachingCourseScheduleInstance>(pageNo, pageSize);
        IPage<TeachingCourseScheduleInstance> pageList = teachingCourseScheduleInstanceService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 添加
     *
     * @param teachingCourseScheduleInstance
     * @return
     */
    @AutoLog(value = "单次课程实例-添加")
    @ApiOperation(value="单次课程实例-添加", notes="单次课程实例-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TeachingCourseScheduleInstance teachingCourseScheduleInstance, 
                      HttpServletRequest req,
                      @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户信息
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        if(user == null) {
            return Result.error("未获取到登录用户");
        }

        // 检查权限
        Set<String> roleSet = sysUserService.getUserRolesSet(username);
        
        // 仅管理员可以添加单次课程实例
        if (!roleSet.contains(RoleConstant.ADMIN)) {
            return Result.error("您没有权限执行此操作");
        }
        
        // 设置创建信息
        teachingCourseScheduleInstance.setCreateBy(user.getUsername());
        teachingCourseScheduleInstance.setCreateTime(new Date());
        
        return teachingCourseScheduleInstanceService.addOrUpdateInstance(teachingCourseScheduleInstance, sendNotification);
    }
    
    /**
     * 编辑
     *
     * @param teachingCourseScheduleInstance
     * @return
     */
    @AutoLog(value = "单次课程实例-编辑")
    @ApiOperation(value="单次课程实例-编辑", notes="单次课程实例-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody TeachingCourseScheduleInstance teachingCourseScheduleInstance, 
                       HttpServletRequest req,
                       @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户信息
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        if(user == null) {
            return Result.error("未获取到登录用户");
        }

        // 检查权限
        Set<String> roleSet = sysUserService.getUserRolesSet(username);
        
        // 仅管理员可以编辑单次课程实例
        if (!roleSet.contains(RoleConstant.ADMIN)) {
            return Result.error("您没有权限执行此操作");
        }
        
        // 设置更新信息
        teachingCourseScheduleInstance.setUpdateBy(user.getUsername());
        teachingCourseScheduleInstance.setUpdateTime(new Date());
        
        return teachingCourseScheduleInstanceService.addOrUpdateInstance(teachingCourseScheduleInstance, sendNotification);
    }
    
    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "单次课程实例-通过id删除")
    @ApiOperation(value="单次课程实例-通过id删除", notes="单次课程实例-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id, 
                         HttpServletRequest req,
                         @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户信息
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        if(user == null) {
            return Result.error("未获取到登录用户");
        }

        // 检查权限
        Set<String> roleSet = sysUserService.getUserRolesSet(username);
        
        // 仅管理员可以删除单次课程实例
        if (!roleSet.contains(RoleConstant.ADMIN)) {
            return Result.error("您没有权限执行此操作");
        }
        
        return teachingCourseScheduleInstanceService.deleteInstance(id, sendNotification);
    }
    
    /**
     * 根据父课程ID和实例日期获取单次课程实例
     *
     * @param parentId
     * @param instanceDate
     * @return
     */
    @AutoLog(value = "单次课程实例-根据父课程和日期获取")
    @ApiOperation(value="单次课程实例-根据父课程和日期获取", notes="单次课程实例-根据父课程和日期获取")
    @GetMapping(value = "/getByParentAndDate")
    public Result<?> getByParentAndDate(
                                     @RequestParam(name="parentId",required=true) String parentId,
                                     @RequestParam(name="instanceDate",required=true) String instanceDate,
                                     HttpServletRequest req) {
        // 获取当前用户信息
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        if(user == null) {
            return Result.error("未获取到登录用户");
        }
        
        // 根据父课程ID和实例日期查询实例
        TeachingCourseScheduleInstance instance = teachingCourseScheduleInstanceService.getInstanceByParentIdAndDate(parentId, instanceDate);
        return Result.ok(instance);
    }
} 