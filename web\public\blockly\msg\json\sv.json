{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Eldaria", "<PERSON><PERSON><PERSON>", "Sabelöga", "<PERSON><PERSON><PERSON>", "Technic2", "WikiPhoenix", "아라"]}, "VARIABLES_DEFAULT_NAME": "föremål", "UNNAMED_KEY": "<PERSON>m<PERSON><PERSON><PERSON><PERSON>", "TODAY": "<PERSON><PERSON>", "DUPLICATE_BLOCK": "Du<PERSON><PERSON><PERSON>", "ADD_COMMENT": "Lägg till kommentar", "REMOVE_COMMENT": "<PERSON><PERSON><PERSON> kommentar", "DUPLICATE_COMMENT": "<PERSON><PERSON><PERSON><PERSON> kommentar", "EXTERNAL_INPUTS": "Externa inmatningar", "INLINE_INPUTS": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_BLOCK": "Radera block", "DELETE_X_BLOCKS": "Radera %1 block", "DELETE_ALL_BLOCKS": "Radera alla %1 block?", "CLEAN_UP": "Städa upp block", "COLLAPSE_BLOCK": "Fäll ihop block", "COLLAPSE_ALL": "Fäll ihop block", "EXPAND_BLOCK": "Fäll ut block", "EXPAND_ALL": "Fäll ut block", "DISABLE_BLOCK": "Inaktivera block", "ENABLE_BLOCK": "Aktivera block", "HELP": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UNDO": "Å<PERSON><PERSON>", "REDO": "<PERSON><PERSON><PERSON> om", "CHANGE_VALUE_TITLE": "Ändra värde:", "RENAME_VARIABLE": "Byt namn på variabel...", "RENAME_VARIABLE_TITLE": "Byt namn på alla'%1'-variabler till:", "NEW_VARIABLE": "Skapa variabel...", "NEW_STRING_VARIABLE": "Skapa strängvariabel...", "NEW_NUMBER_VARIABLE": "<PERSON><PERSON><PERSON> siffervaria<PERSON>...", "NEW_COLOUR_VARIABLE": "Skapa färgvariabel...", "NEW_VARIABLE_TYPE_TITLE": "Ny variabeltyp:", "NEW_VARIABLE_TITLE": "Nytt variabelnamn:", "VARIABLE_ALREADY_EXISTS": "En variabel med namnet \"%1\" finns redan.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "En variabel med namnet \"%1\" finns redan för en annan typ: \"%2\".", "DELETE_VARIABLE_CONFIRMATION": "Radera %1 användningar av variabeln \"%2\"?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Kan inte radera variabeln '%1' eftersom den är en del av definition för funktionen '%2'", "DELETE_VARIABLE": "Radera variabeln \"%1\"", "COLOUR_PICKER_HELPURL": "https://sv.wikipedia.org/wiki/<PERSON><PERSON>rg", "COLOUR_PICKER_TOOLTIP": "Välj en färg från paletten.", "COLOUR_RANDOM_TITLE": "s<PERSON><PERSON><PERSON><PERSON>", "COLOUR_RANDOM_TOOLTIP": "Slumpa fram en färg.", "COLOUR_RGB_HELPURL": "https://www.december.com/html/spec/colorpercompact.html", "COLOUR_RGB_TITLE": "färg med", "COLOUR_RGB_RED": "<PERSON><PERSON><PERSON>", "COLOUR_RGB_GREEN": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_RGB_BLUE": "blå", "COLOUR_RGB_TOOLTIP": "Skapa en färg med det angivna mängden röd, gr<PERSON><PERSON> och blå. Alla värden måste vara mellan 0 och 100.", "COLOUR_BLEND_HELPURL": "https://meyerweb.com/eric/tools/color-blend/#:::rgbp", "COLOUR_BLEND_TITLE": "blanda", "COLOUR_BLEND_COLOUR1": "färg 1", "COLOUR_BLEND_COLOUR2": "färg 2", "COLOUR_BLEND_RATIO": "förhållande", "COLOUR_BLEND_TOOLTIP": "Blandar ihop två färger med ett bestämt förhållande (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "upprepa %1 gånger", "CONTROLS_REPEAT_INPUT_DO": "utför", "CONTROLS_REPEAT_TOOLTIP": "Utför n<PERSON>gra kommandon flera gånger.", "CONTROLS_WHILEUNTIL_HELPURL": "https://github.com/google/blockly/wiki/Loops#repeat", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "upprepa så länge", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "upprepa tills", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Medan ett värde är sant, utför några kommandon.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Medan ett värde är falskt, utför några kommandon.", "CONTROLS_FOR_HELPURL": "https://github.com/google/blockly/wiki/Loops#count-with", "CONTROLS_FOR_TOOLTIP": "<PERSON><PERSON>t variabeln \"%1\" anta värden från starttalet till sluttalet, ber<PERSON>knat med det angivna intervallet, och utför de angivna blocken.", "CONTROLS_FOR_TITLE": "räkna med %1 från %2 till %3 med %4", "CONTROLS_FOREACH_HELPURL": "https://github.com/google/blockly/wiki/Loops#for-each", "CONTROLS_FOREACH_TITLE": "för varje föremål %1 i listan %2", "CONTROLS_FOREACH_TOOLTIP": "<PERSON><PERSON>r varje objekt i en lista, ange variabeln '%1' till objektet, och utför sedan några kommandon.", "CONTROLS_FLOW_STATEMENTS_HELPURL": "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "bryt ut ur loop", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "fortsätta med nästa upprepning av loop", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Bryt ut ur den innehållande upprepningen.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Hoppa över resten av denna loop och fortsätt med nästa loop.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Varning: Detta block kan endast användas i en loop.", "CONTROLS_IF_HELPURL": "https://github.com/google/blockly/wiki/IfElse", "CONTROLS_IF_TOOLTIP_1": "Om ett värde är sant, utför några kommandon.", "CONTROLS_IF_TOOLTIP_2": "Om värdet är sant, utför det första kommandoblocket. Utför annars det andra kommandoblocket.", "CONTROLS_IF_TOOLTIP_3": "Om det första värdet är sant, utför det första kommandoblocket. <PERSON><PERSON>, om det andra värdet är sant, utför det andra kommandoblocket.", "CONTROLS_IF_TOOLTIP_4": "Om det första värdet är sant, utför det första kommandoblocket. <PERSON><PERSON>, om det andra värdet är sant, utför det andra kommandoblocket. Om ingen av värdena är sanna, utför det sista kommandoblocket.", "CONTROLS_IF_MSG_IF": "om", "CONTROLS_IF_MSG_ELSEIF": "annars om", "CONTROLS_IF_MSG_ELSE": "annars", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON> till, ta bort eller ändra ordningen för sektioner för att omkonfigurera blocket \"om\".", "CONTROLS_IF_ELSEIF_TOOLTIP": "Lägg till ett villkor blocket \"om\".", "CONTROLS_IF_ELSE_TOOLTIP": "<PERSON><PERSON><PERSON> till ett sista villkor som täcker alla alternativ som är kvar för \"if\"-blocket.", "IOS_OK": "OK", "IOS_CANCEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IOS_ERROR": "<PERSON><PERSON>", "IOS_PROCEDURES_INPUTS": "INMATNINGAR", "IOS_PROCEDURES_ADD_INPUT": "+ <PERSON><PERSON>gg till inmatning", "IOS_PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON><PERSON> kommandon", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "<PERSON>na funktion har dubblettinmatningar.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON><PERSON> till variabel", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON> till", "IOS_VARIABLES_RENAME_BUTTON": "Döp om", "IOS_VARIABLES_DELETE_BUTTON": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_VARIABLE_NAME": "Variabelnamn", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Du kan inte använda ett tomt variabelnamn.", "LOGIC_COMPARE_HELPURL": "https://sv.wikipedia.org/wiki/Olikhet", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON> till<PERSON> sant om båda värdena är lika med varandra.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON> till<PERSON> sant om båda värdena inte är lika med varandra.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON>er till<PERSON><PERSON> sant om det första värdet är mindre än det andra.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON>er till<PERSON><PERSON> sant om det första värdet är mindre än eller lika med det andra.", "LOGIC_COMPARE_TOOLTIP_GT": "<PERSON>er till<PERSON><PERSON> sant om det första värdet är större än det andra.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON>er till<PERSON><PERSON> sant om det första värdet är större än eller lika med det andra.", "LOGIC_OPERATION_HELPURL": "https://github.com/google/blockly/wiki/Logic#logical-operations", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON> till<PERSON> sant om båda värdena är sanna.", "LOGIC_OPERATION_AND": "och", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON><PERSON> till<PERSON> sant om minst ett av värdena är sant.", "LOGIC_OPERATION_OR": "eller", "LOGIC_NEGATE_HELPURL": "https://github.com/google/blockly/wiki/Logic#not", "LOGIC_NEGATE_TITLE": "inte %1", "LOGIC_NEGATE_TOOLTIP": "Ger tillbaka sant om inmatningen är falsk. Ger tillbaka falskt och inmatningen är sann.", "LOGIC_BOOLEAN_TRUE": "sant", "LOGIC_BOOLEAN_FALSE": "falskt", "LOGIC_BOOLEAN_TOOLTIP": "<PERSON><PERSON>r antingen sant eller falskt.", "LOGIC_NULL_HELPURL": "https://sv.wikipedia.org/wiki/Null", "LOGIC_NULL": "null", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON><PERSON> null.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "om sant", "LOGIC_TERNARY_IF_FALSE": "om falskt", "LOGIC_TERNARY_TOOLTIP": "Kontrollera villkoret i \"test\". Om villkoret är sant, ge tillbaka \"om sant\"-v<PERSON>rde<PERSON>; annars ge tillbaka \"om falskt\"-värdet.", "MATH_NUMBER_HELPURL": "https://sv.wikipedia.org/wiki/Tal", "MATH_NUMBER_TOOLTIP": "Ett tal.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "arcsin", "MATH_TRIG_ACOS": "arccos", "MATH_TRIG_ATAN": "arctan", "MATH_ARITHMETIC_HELPURL": "https://sv.wikipedia.org/wiki/Aritmetik", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON><PERSON><PERSON> summan av de två talen.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "<PERSON><PERSON><PERSON> differensen mellan de två talen.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Returnerar produkten av de två talen.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Returnerar kvoten av de två talen.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Ger till<PERSON><PERSON> det första talet upphöjt till det andra talet.", "MATH_SINGLE_HELPURL": "https://sv.wikipedia.org/wiki/Kvadratrot", "MATH_SINGLE_OP_ROOT": "kvadratrot", "MATH_SINGLE_TOOLTIP_ROOT": "Returnerar k<PERSON>dratroten av ett tal.", "MATH_SINGLE_OP_ABSOLUTE": "absolut", "MATH_SINGLE_TOOLTIP_ABS": "Returnerar absolutvärdet av ett tal.", "MATH_SINGLE_TOOLTIP_NEG": "Returnerar negationen av ett tal.", "MATH_SINGLE_TOOLTIP_LN": "Returnera den naturliga logaritmen av ett tal.", "MATH_SINGLE_TOOLTIP_LOG10": "Returnerar logaritmen för bas 10 av ett tal.", "MATH_SINGLE_TOOLTIP_EXP": "Ger tillbaka e upphöjt i ett tal.", "MATH_SINGLE_TOOLTIP_POW10": "Ger tillbaka 10 upphöjt i ett tal.", "MATH_TRIG_HELPURL": "https://sv.wikipedia.org/wiki/Trigonometrisk_funktion", "MATH_TRIG_TOOLTIP_SIN": "<PERSON>er tillbaka sinus för en grad (inte radian).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON> <PERSON><PERSON><PERSON> cosinus för en grad (inte radian).", "MATH_TRIG_TOOLTIP_TAN": "<PERSON>er tillbaka tangens för en grad (inte radian).", "MATH_TRIG_TOOLTIP_ASIN": "<PERSON><PERSON> <PERSON><PERSON><PERSON> arcus sinus (arcsin) för ett tal.", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON> <PERSON><PERSON><PERSON> arcus cosinus (arccos) för ett tal.", "MATH_TRIG_TOOLTIP_ATAN": "Ger <PERSON><PERSON>ka arcus tangens (arctan) av ett tal.", "MATH_CONSTANT_HELPURL": "https://sv.wikipedia.org/wiki/Matematisk_konstant", "MATH_CONSTANT_TOOLTIP": "Returnerar en av de vanliga konstanterna: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…) eller ∞ (<PERSON><PERSON><PERSON><PERSON><PERSON>).", "MATH_IS_EVEN": "är jämnt", "MATH_IS_ODD": "är ojämnt", "MATH_IS_PRIME": "är ett primtal", "MATH_IS_WHOLE": "är helt", "MATH_IS_POSITIVE": "är positivt", "MATH_IS_NEGATIVE": "är negativt", "MATH_IS_DIVISIBLE_BY": "är delbart med", "MATH_IS_TOOLTIP": "Kontrollera om ett tal är jämnt, ojämnt, helt, positivt, negativt eller det är delbart med ett bestämt tal. Returnerar med sant eller falskt.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "ändra %1 med %2", "MATH_CHANGE_TOOLTIP": "<PERSON><PERSON><PERSON> till ett tal till variabeln '%1'.", "MATH_ROUND_HELPURL": "https://sv.wikipedia.org/wiki/Avrundning", "MATH_ROUND_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt eller ned<PERSON>t.", "MATH_ROUND_OPERATOR_ROUND": "<PERSON><PERSON><PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "<PERSON><PERSON><PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDDOWN": "<PERSON><PERSON><PERSON><PERSON>", "MATH_ONLIST_OPERATOR_SUM": "summan av listan", "MATH_ONLIST_TOOLTIP_SUM": "Ger <PERSON><PERSON>ka summan av alla talen i listan.", "MATH_ONLIST_OPERATOR_MIN": "minsta talet i listan", "MATH_ONLIST_TOOLTIP_MIN": "<PERSON>er till<PERSON>ka det minsta talet i listan.", "MATH_ONLIST_OPERATOR_MAX": "högsta talet i listan", "MATH_ONLIST_TOOLTIP_MAX": "Ger tillbaka det största talet i listan.", "MATH_ONLIST_OPERATOR_AVERAGE": "medelvärdet av listan", "MATH_ONLIST_TOOLTIP_AVERAGE": "Ger tillbaka medelvärdet (aritmetiskt) av de numeriska värdena i listan.", "MATH_ONLIST_OPERATOR_MEDIAN": "<PERSON>en av listan", "MATH_ONLIST_TOOLTIP_MEDIAN": "<PERSON><PERSON><PERSON> medianen av talen i listan.", "MATH_ONLIST_OPERATOR_MODE": "typvärdet i listan", "MATH_ONLIST_TOOLTIP_MODE": "Ger <PERSON><PERSON><PERSON> en lista med de(t) vanligaste objekte(t/n) i listan.", "MATH_ONLIST_OPERATOR_STD_DEV": "standardavvikelsen i listan", "MATH_ONLIST_TOOLTIP_STD_DEV": "Ger tillbaka standardavvikelsen i listan.", "MATH_ONLIST_OPERATOR_RANDOM": "slumpmässigt objekt i listan", "MATH_ONLIST_TOOLTIP_RANDOM": "Returnerar ett slumpmässigt element från listan.", "MATH_MODULO_HELPURL": "https://sv.wikipedia.org/wiki/Modulär_aritmetik", "MATH_MODULO_TITLE": "resten av %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Returnerar kvoten från divisionen av de två talen.", "MATH_CONSTRAIN_TITLE": "begränsa %1 till mellan %2 och %3", "MATH_CONSTRAIN_TOOLTIP": "Begräns<PERSON> ett tal till att mellan de angivna gränsvärden (inkluderande).", "MATH_RANDOM_INT_HELPURL": "https://sv.wikipedia.org/wiki/Slumptalsgenerator", "MATH_RANDOM_INT_TITLE": "slumpartat heltal från %1 till %2", "MATH_RANDOM_INT_TOOLTIP": "<PERSON><PERSON> <PERSON> ett slumpat heltal mellan två värden, inkluderande.", "MATH_RANDOM_FLOAT_HELPURL": "https://sv.wikipedia.org/wiki/Slumptalsgenerator", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "slumpat decimaltal", "MATH_RANDOM_FLOAT_TOOLTIP": "<PERSON>er <PERSON><PERSON> ett slumpat decimaltal mellan 0.0 (inkluderat) och 1.0 (exkluderat).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 av X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "<PERSON><PERSON><PERSON> arc<PERSON> av punkt (X, Y) i grader från -180 till 180.", "TEXT_TEXT_HELPURL": "https://sv.wikipedia.org/wiki/Str%C3%A4ng_%28data%29", "TEXT_TEXT_TOOLTIP": "<PERSON> b<PERSON>, ord eller textrad.", "TEXT_JOIN_TITLE_CREATEWITH": "skapa text med", "TEXT_JOIN_TOOLTIP": "Skapa en textbit genom att sammanfoga ett valfritt antal föremål.", "TEXT_CREATE_JOIN_TITLE_JOIN": "sammanfoga", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON> till, ta bort eller ändra ordningen för sektioner för att omkonfigurera detta textblock.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "<PERSON><PERSON><PERSON> till ett föremål till texten.", "TEXT_APPEND_TITLE": "för att %1 lägga till text %2", "TEXT_APPEND_TOOLTIP": "Lägg till lite text till variabeln '%1'.", "TEXT_LENGTH_TITLE": "längden på %1", "TEXT_LENGTH_TOOLTIP": "<PERSON>er <PERSON><PERSON><PERSON> antalet bokst<PERSON>ver (inklusive mellanslag) i den angivna texten.", "TEXT_ISEMPTY_TITLE": "%1 är tom", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON>r sant om den angivna texten är tom.", "TEXT_INDEXOF_TOOLTIP": "Ger tillbaka indexet för den första/sista förekomsten av första texten i den andra texten.  Ger tillbaka %1 om texten inte hittas.", "TEXT_INDEXOF_TITLE": "i texten %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "hitta första förekomsten av texten", "TEXT_INDEXOF_OPERATOR_LAST": "hitta sista förekomsten av texten", "TEXT_CHARAT_TITLE": "i texten %1 %2", "TEXT_CHARAT_FROM_START": "hämta bokstaven #", "TEXT_CHARAT_FROM_END": "hämta bokstaven # från slutet", "TEXT_CHARAT_FIRST": "hämta första bokstaven", "TEXT_CHARAT_LAST": "hä<PERSON>ta sista bok<PERSON>ven", "TEXT_CHARAT_RANDOM": "hä<PERSON><PERSON> s<PERSON>ad bok<PERSON>v", "TEXT_CHARAT_TOOLTIP": "<PERSON>er <PERSON><PERSON><PERSON> bokstaven på den specificerade positionen.", "TEXT_GET_SUBSTRING_TOOLTIP": "<PERSON><PERSON> <PERSON><PERSON> en viss del av texten.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "i texten", "TEXT_GET_SUBSTRING_START_FROM_START": "få textdel från bokstav #", "TEXT_GET_SUBSTRING_START_FROM_END": "få textdel från bokstav # från slutet", "TEXT_GET_SUBSTRING_START_FIRST": "få textdel från första bokstaven", "TEXT_GET_SUBSTRING_END_FROM_START": "till bokstav #", "TEXT_GET_SUBSTRING_END_FROM_END": "till bokstav # från slutet", "TEXT_GET_SUBSTRING_END_LAST": "till sista bokstaven", "TEXT_CHANGECASE_TOOLTIP": "Returnerar en kopia av texten i ett annat skiftläge.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "till VERSALER", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "till gemener", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "till Versala Initialer", "TEXT_TRIM_TOOLTIP": "Returnerar en kopia av texten med borttagna mellanrum från en eller båda ändar.", "TEXT_TRIM_OPERATOR_BOTH": "ta bort mellanrum från båda sidorna av", "TEXT_TRIM_OPERATOR_LEFT": "ta bort mellanrum från vänstra sidan av", "TEXT_TRIM_OPERATOR_RIGHT": "ta bort mellanrum från högra sidan av", "TEXT_PRINT_TITLE": "skriv %1", "TEXT_PRINT_TOOLTIP": "Skriv den angivna texten, talet eller annat värde.", "TEXT_PROMPT_TYPE_TEXT": "fr<PERSON><PERSON> efter text med meddelande", "TEXT_PROMPT_TYPE_NUMBER": "fr<PERSON><PERSON> efter ett tal med meddelande", "TEXT_PROMPT_TOOLTIP_NUMBER": "Fråga användaren efter ett tal.", "TEXT_PROMPT_TOOLTIP_TEXT": "<PERSON><PERSON>ga användaren efter lite text.", "TEXT_COUNT_MESSAGE0": "räkna %1 i %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "<PERSON><PERSON><PERSON>na hur många gånger en text förekommer inom en annan text.", "TEXT_REPLACE_MESSAGE0": "ersätt %1 med %2 i %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "<PERSON><PERSON><PERSON>t alla förekomster av en text inom en annan text.", "TEXT_REVERSE_MESSAGE0": "vänd på %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "<PERSON><PERSON>nder på teckenordningen i texten.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "skapa tom lista", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON> <PERSON><PERSON> en lista utan någon data, alltså med längden 0", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "Skapa en lista med valfritt antal föremål.", "LISTS_CREATE_WITH_INPUT_WITH": "skapa lista med", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON> till, ta bort eller ändra ordningen på objekten för att göra om det här \"list\"-blocket.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Lägg till ett föremål till listan.", "LISTS_REPEAT_TOOLTIP": "Skapar en lista som innehåller ett valt värde upprepat ett bestämt antalet gånger.", "LISTS_REPEAT_TITLE": "skapa lista med föremålet %1 upprepat %2 gånger", "LISTS_LENGTH_TITLE": "längden på %1", "LISTS_LENGTH_TOOLTIP": "<PERSON><PERSON>r längden på en lista.", "LISTS_ISEMPTY_TITLE": "%1 är tom", "LISTS_ISEMPTY_TOOLTIP": "<PERSON><PERSON>r sant om listan är tom.", "LISTS_INLIST": "i listan", "LISTS_INDEX_OF_FIRST": "hitta första förekomsten av objektet", "LISTS_INDEX_OF_LAST": "hitta sista förekomsten av objektet", "LISTS_INDEX_OF_TOOLTIP": "Ger tillbaka den första/sista förekomsten av objektet i listan. Returnerar %1 om objektet inte hittas.", "LISTS_GET_INDEX_GET": "hä<PERSON><PERSON>", "LISTS_GET_INDEX_GET_REMOVE": "hämta och ta bort", "LISTS_GET_INDEX_REMOVE": "ta bort", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# från slutet", "LISTS_GET_INDEX_FIRST": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_LAST": "sista", "LISTS_GET_INDEX_RANDOM": "slumpad", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 är det första objektet.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 är det sista objektet.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Ger tillbaka objektet på den efterfrågade positionen i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Returnerar det första objektet i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Returnerar det sista objektet i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Returnerar ett slumpmässigt objekt i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Tar bort och återställer objektet på den specificerade positionen i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Tar bort och återställer det första objektet i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Tar bort och återställer det sista objektet i en lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Tar bort och återställer ett slumpmässigt objekt i en lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Tar bort objektet på den specificerade positionen i en lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Tar bort det första objektet i en lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Tar bort det sista objektet i en lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Tar bort en slumpmässig post i en lista.", "LISTS_SET_INDEX_SET": "ange", "LISTS_SET_INDEX_INSERT": "Sätt in vid", "LISTS_SET_INDEX_INPUT_TO": "som", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "<PERSON><PERSON><PERSON> in objektet vid en specificerad position i en lista.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Anger det första objektet i en lista.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Anger det sista elementet i en lista.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "<PERSON><PERSON><PERSON> in ett slumpat objekt i en lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "<PERSON><PERSON><PERSON> in objektet vid en specificerad position i en lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "s<PERSON><PERSON> in objektet i början av en lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Lägg till objektet i slutet av en lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "<PERSON><PERSON><PERSON> in objektet på en slumpad position i en lista.", "LISTS_GET_SUBLIST_START_FROM_START": "få underlista från #", "LISTS_GET_SUBLIST_START_FROM_END": "få underlista från # från slutet", "LISTS_GET_SUBLIST_START_FIRST": "få underlista från fö<PERSON>a", "LISTS_GET_SUBLIST_END_FROM_START": "till #", "LISTS_GET_SUBLIST_END_FROM_END": "till # fr<PERSON>n slutet", "LISTS_GET_SUBLIST_END_LAST": "till sista", "LISTS_GET_SUBLIST_TOOLTIP": "Skapar en kopia av den specificerade delen av en lista.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sortera %1 %2 %3", "LISTS_SORT_TOOLTIP": "Sortera en kopia av en lista.", "LISTS_SORT_ORDER_ASCENDING": "stigande", "LISTS_SORT_ORDER_DESCENDING": "fallande", "LISTS_SORT_TYPE_NUMERIC": "numeriskt", "LISTS_SORT_TYPE_TEXT": "alfabetiskt", "LISTS_SORT_TYPE_IGNORECASE": "alfabetiskt, ignorera skiftläge", "LISTS_SPLIT_HELPURL": "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists", "LISTS_SPLIT_LIST_FROM_TEXT": "skapa lista från text", "LISTS_SPLIT_TEXT_FROM_LIST": "skapa text från lista", "LISTS_SPLIT_WITH_DELIMITER": "med avgränsare", "LISTS_SPLIT_TOOLTIP_SPLIT": "Dela upp text till en textlista och bryt vid varje avgränsare.", "LISTS_SPLIT_TOOLTIP_JOIN": "Sammanfoga en textlista till en text, som separeras av en avgränsare.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "vänd på %1", "LISTS_REVERSE_TOOLTIP": "Vänd på en kopia av en lista.", "VARIABLES_GET_TOOLTIP": "Returnerar värdet av denna variabel.", "VARIABLES_GET_CREATE_SET": "Skapa \"välj %1\"", "VARIABLES_SET": "ange %1 till %2", "VARIABLES_SET_TOOLTIP": "<PERSON><PERSON><PERSON> s<PERSON> att den här variabeln blir lika med inputen.", "VARIABLES_SET_CREATE_GET": "Skapa 'hämta %1'", "PROCEDURES_DEFNORETURN_HELPURL": "https://sv.wikipedia.org/wiki/Funk<PERSON>_(programmering)", "PROCEDURES_DEFNORETURN_TITLE": "f<PERSON>r att", "PROCEDURES_DEFNORETURN_PROCEDURE": "<PERSON><PERSON><PERSON>", "PROCEDURES_BEFORE_PARAMS": "med:", "PROCEDURES_CALL_BEFORE_PARAMS": "med:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Skapar en funktion utan output.", "PROCEDURES_DEFNORETURN_COMMENT": "<PERSON><PERSON><PERSON><PERSON> denna funktion...", "PROCEDURES_DEFRETURN_HELPURL": "https://sv.wikipedia.org/wiki/Funk<PERSON>_(programmering)", "PROCEDURES_DEFRETURN_RETURN": "returnera", "PROCEDURES_DEFRETURN_TOOLTIP": "Skapar en funktion med output.", "PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON><PERSON>", "PROCEDURES_DEF_DUPLICATE_WARNING": "Varning: <PERSON><PERSON>tion har dubbla parametrar.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Kör den användardefinierade funktionen \"%1\".", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Kör den användardefinierade funktionen \"%1\" och använd resultatet av den.", "PROCEDURES_MUTATORCONTAINER_TITLE": "inmatningar", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON> till, ta bort och ändra ordningen för inmatningar till denna funktion.", "PROCEDURES_MUTATORARG_TITLE": "inmatningsnamn:", "PROCEDURES_MUTATORARG_TOOLTIP": "Lägg till en inmatning till funktionen.", "PROCEDURES_HIGHLIGHT_DEF": "Markera funktionsdefinition", "PROCEDURES_CREATE_DO": "Skapa '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Om ett värde är sant returneras ett andra värde.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Varning: Detta block får användas endast i en funktionsdefinition.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "<PERSON><PERSON><PERSON> n<PERSON>...", "WORKSPACE_ARIA_LABEL": "<PERSON><PERSON><PERSON>", "COLLAPSED_WARNINGS_WARNING": "Hopfällda block innehåller varningar.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}