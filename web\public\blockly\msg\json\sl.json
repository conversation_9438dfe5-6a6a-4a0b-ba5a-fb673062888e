{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Dbc334", "Eleassar", "HairyFotr", "<PERSON><PERSON>", "LoveMortuus", "MaxiMouse", "<PERSON><PERSON>"]}, "VARIABLES_DEFAULT_NAME": "element", "UNNAMED_KEY": "ne<PERSON>imenovano", "TODAY": "<PERSON><PERSON>", "DUPLICATE_BLOCK": "Podvoji", "ADD_COMMENT": "<PERSON><PERSON><PERSON> k<PERSON>", "REMOVE_COMMENT": "Odstrani komentar", "DUPLICATE_COMMENT": "Podvoji komentar", "EXTERNAL_INPUTS": "Zunanji vnosi", "INLINE_INPUTS": "Vrstični vnosi", "DELETE_BLOCK": "Izbriši blok", "DELETE_X_BLOCKS": "Izbriši bloke (%1)", "DELETE_ALL_BLOCKS": "Izbrišem vse bloke (%1)?", "CLEAN_UP": "Ponastavi bloke", "COLLAPSE_BLOCK": "Skrči blok", "COLLAPSE_ALL": "Skrči bloke", "EXPAND_BLOCK": "Razširi blok", "EXPAND_ALL": "Razširi bloke", "DISABLE_BLOCK": "Onemogoči blok", "ENABLE_BLOCK": "Omogoči blok", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "REDO": "<PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Spremeni vrednost:", "RENAME_VARIABLE": "Preimenuj spremenljivko ...", "RENAME_VARIABLE_TITLE": "Preimenuj vse spremenljivke »%1« v:", "NEW_VARIABLE": "Ustvari spremenljivko ...", "NEW_STRING_VARIABLE": "Ustvari spremenljivko niza ...", "NEW_NUMBER_VARIABLE": "Ustvari spremenljivko <PERSON> ...", "NEW_COLOUR_VARIABLE": "Ustvari spremenljivko barve ...", "NEW_VARIABLE_TYPE_TITLE": "Vrsta nove spremenljivke:", "NEW_VARIABLE_TITLE": "Ime nove spremenljivke:", "VARIABLE_ALREADY_EXISTS": "Spremenljivka »%1« že obstaja.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Spremenljivka z imenom »%1« za tip »%2« že obstaja.", "DELETE_VARIABLE_CONFIRMATION": "Izbrišem %1 uporab spremenljivke »%2«?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Ni mogoče izbrisati spremenljivke »%1«, ker je uporabljena v definiciji funkcije »%2«.", "DELETE_VARIABLE": "Izbriši spremenljivko »%1«", "COLOUR_PICKER_HELPURL": "https://sl.wikipedia.org/wiki/Barva", "COLOUR_PICKER_TOOLTIP": "Izberite barvo s palete.", "COLOUR_RANDOM_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COLOUR_RANDOM_TOOLTIP": "Izberite naključno barvo.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "<PERSON><PERSON><PERSON><PERSON> bar<PERSON>", "COLOUR_RGB_RED": "rdeča", "COLOUR_RGB_GREEN": "zelena", "COLOUR_RGB_BLUE": "modra", "COLOUR_RGB_TOOLTIP": "Ustvari barvo z določeno količino rdeče, zelene in modre. Vse vrednosti morajo biti med 0 in 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "mešanica", "COLOUR_BLEND_COLOUR1": "barva 1", "COLOUR_BLEND_COLOUR2": "barva 2", "COLOUR_BLEND_RATIO": "razmerje", "COLOUR_BLEND_TOOLTIP": "Zmeša dve barvi v določene razmerju (0,0 – 1,0).", "CONTROLS_REPEAT_HELPURL": "https://sl.wikipedia.org/wiki/Zan<PERSON>_for", "CONTROLS_REPEAT_TITLE": "ponovi %1-krat", "CONTROLS_REPEAT_INPUT_DO": "<PERSON>z<PERSON>i", "CONTROLS_REPEAT_TOOLTIP": "Določeni stavki se izvedejo večkrat.", "CONTROLS_WHILEUNTIL_HELPURL": "https://github.com/google/blockly/wiki/Loops#repeat", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "ponavljaj, dokler", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "pona<PERSON><PERSON><PERSON><PERSON>, dokler ni", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Določeni stavki se izvajajo, dokler je vrednost resnična.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Določeni stavki se izvajajo, dokler je vrednost neresnična.", "CONTROLS_FOR_HELPURL": "https://github.com/google/blockly/wiki/Loops#count-with", "CONTROLS_FOR_TOOLTIP": "Vrednost spremenljivke »%1« se v določenem koraku spreminja od začetnega do končnega števila. Pri tem se izvedejo določeni bloki.", "CONTROLS_FOR_TITLE": "štej s/z %1 od %2 do %3 po %4", "CONTROLS_FOREACH_HELPURL": "https://github.com/google/blockly/wiki/Loops#for-each", "CONTROLS_FOREACH_TITLE": "za vsak element %1 v seznamu %2", "CONTROLS_FOREACH_TOOLTIP": "Za vsak element v seznamu nastavi spremenljivko »%1« na ta element. Pri tem se izvedejo določeni stavki.", "CONTROLS_FLOW_STATEMENTS_HELPURL": "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "prekini zanko", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "nadaljuj z naslednjo ponovitvijo zanke", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Prekine vsebujo<PERSON><PERSON> z<PERSON>.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Preskoči preostanek te zanke in nadaljuje z naslednjo ponovitvijo.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Pozor: Ta blok lahko uporabite znotraj zanke samo enkrat.", "CONTROLS_IF_HELPURL": "https://github.com/google/blockly/wiki/IfElse", "CONTROLS_IF_TOOLTIP_1": "Če je vrednost resnična, izvedi določene stavke.", "CONTROLS_IF_TOOLTIP_2": "Če je vrednost resnična, izvedi prvo skupino stavkov. <PERSON>cer izvedi drugo skupino stavkov.", "CONTROLS_IF_TOOLTIP_3": "Če je prva vrednost resnična, izvedi prvo skupino stavkov. <PERSON><PERSON>, če je resnična druga vrednost, izvedi drugo skupino stavkov.", "CONTROLS_IF_TOOLTIP_4": "Če je prva vrednost resnična, izvedi prvo skupino stavkov. <PERSON><PERSON>, če je resnična druga vrednost, izvedi drugo skupino stavkov. Če ni resnična nobena od vrednosti, izvedi zadnjo skupino stavkov.", "CONTROLS_IF_MSG_IF": "če", "CONTROLS_IF_MSG_ELSEIF": "sicer če", "CONTROLS_IF_MSG_ELSE": "sicer", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, odstranite ali spremenite vrstni red odsekov za ponovno nastavitev bloka »če«.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Dodajte bloku »če« pogoj.", "CONTROLS_IF_ELSE_TOOLTIP": "Dodajte bloku »če« končni pogoj.", "IOS_OK": "V redu", "IOS_CANCEL": "Prekliči", "IOS_ERROR": "Napaka", "IOS_PROCEDURES_INPUTS": "VNOSI", "IOS_PROCEDURES_ADD_INPUT": "+ <PERSON><PERSON><PERSON> vnos", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Do<PERSON>li stavke", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Ta funkcija ima podvojene vnose.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON>j spremenljivko", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "Izbriši", "IOS_VARIABLES_VARIABLE_NAME": "<PERSON><PERSON>", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Ne moreš uporabiti spremenljivke brez imena.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON>, če sta v<PERSON>a enaka.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON>, če vnosa nista enaka.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON><PERSON>, če je prvi vnos manjši od drug<PERSON>a.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON><PERSON>, če je prvi vnos manjši ali enak drug<PERSON>u.", "LOGIC_COMPARE_TOOLTIP_GT": "<PERSON><PERSON>, če je prvi vnos večji od drugega.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON><PERSON>, če je prvi vnos večji ali enak drugemu.", "LOGIC_OPERATION_HELPURL": "https://github.com/google/blockly/wiki/Logic#logical-operations", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON>, če sta oba vnosa resnična.", "LOGIC_OPERATION_AND": "in", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON><PERSON>, če je vsaj eden od vnosov resničen.", "LOGIC_OPERATION_OR": "ali", "LOGIC_NEGATE_HELPURL": "https://github.com/google/blockly/wiki/Logic#not", "LOGIC_NEGATE_TITLE": "ne %1", "LOGIC_NEGATE_TOOLTIP": "<PERSON><PERSON> re<PERSON>, če je vnos neresničen. <PERSON><PERSON> ne<PERSON>, če je vnos resničen.", "LOGIC_BOOLEAN_HELPURL": "https://github.com/google/blockly/wiki/Logic#values", "LOGIC_BOOLEAN_TRUE": "resnično", "LOGIC_BOOLEAN_FALSE": "ne<PERSON><PERSON>č<PERSON>", "LOGIC_BOOLEAN_TOOLTIP": "<PERSON><PERSON> re<PERSON>nič<PERSON> ali neresnično.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "prazno", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON> p<PERSON>.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "če resnično", "LOGIC_TERNARY_IF_FALSE": "če neresnično", "LOGIC_TERNARY_TOOLTIP": "Preveri pogoj v »testu«. Če je pogoj resničen, potem vrne vrednost »če resnično«; sicer vrne vrednost »če neresnično«.", "MATH_NUMBER_HELPURL": "https://sl.wikipedia.org/wiki/%C5%A0tevilo", "MATH_NUMBER_TOOLTIP": "Število.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "asin", "MATH_TRIG_ACOS": "acos", "MATH_TRIG_ATAN": "atan", "MATH_ARITHMETIC_HELPURL": "https://sl.wikipedia.org/wiki/Aritmetika", "MATH_ARITHMETIC_TOOLTIP_ADD": "Vrne vsoto dveh števil.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Vrne razliko dveh števil.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Vrne zmnožek dveh števil.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Vrne količnik dveh števil.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Vrne prvo število na potenco drugega števila.", "MATH_SINGLE_HELPURL": "https://sl.wikipedia.org/wiki/Kvadratni_koren", "MATH_SINGLE_OP_ROOT": "kvadratni koren", "MATH_SINGLE_TOOLTIP_ROOT": "Vrne k<PERSON>dratni koren <PERSON>.", "MATH_SINGLE_OP_ABSOLUTE": "absolutno", "MATH_SINGLE_TOOLTIP_ABS": "Vrne absolutno vrednost števila.", "MATH_SINGLE_TOOLTIP_NEG": "<PERSON><PERSON> nega<PERSON>.", "MATH_SINGLE_TOOLTIP_LN": "Vrne naravni logaritem števila.", "MATH_SINGLE_TOOLTIP_LOG10": "Vrne desetiški logaritem števila.", "MATH_SINGLE_TOOLTIP_EXP": "Vrne e na potenco števila.", "MATH_SINGLE_TOOLTIP_POW10": "Vrne 10 na potenco števila.", "MATH_TRIG_HELPURL": "https://sl.wikipedia.org/wiki/Trigonometrična_funkcija", "MATH_TRIG_TOOLTIP_SIN": "Vrne sinus kota v stopinjah (ne radianih).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON> kosinus kota v stopinjah (ne radianih).", "MATH_TRIG_TOOLTIP_TAN": "Vrne tangens kota v stopinjah (ne radianih).", "MATH_TRIG_TOOLTIP_ASIN": "Vrne arkus sinus števila.", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON> arkus k<PERSON>.", "MATH_TRIG_TOOLTIP_ATAN": "Vrne arkus tangens števila.", "MATH_CONSTANT_HELPURL": "https://sl.wikipedia.org/wiki/Matematična_konstanta", "MATH_CONSTANT_TOOLTIP": "<PERSON><PERSON> eno izmed pogostih konstant: π (3,141…), e (2,718…), φ (1,618…), sqrt(2) (1,414…), sqrt(½) (0,707 ...) ali ∞ (<PERSON><PERSON><PERSON><PERSON><PERSON>).", "MATH_IS_EVEN": "je sodo", "MATH_IS_ODD": "je liho", "MATH_IS_PRIME": "je p<PERSON><PERSON><PERSON><PERSON><PERSON>", "MATH_IS_WHOLE": "je celo", "MATH_IS_POSITIVE": "je pozitivno", "MATH_IS_NEGATIVE": "je negativno", "MATH_IS_DIVISIBLE_BY": "je deljivo s/z", "MATH_IS_TOOLTIP": "<PERSON><PERSON><PERSON>, ali je šte<PERSON>o sodo, liho, p<PERSON><PERSON><PERSON><PERSON><PERSON>, celo, poz<PERSON><PERSON><PERSON>, negativno ali če je deljivo z določenim številom. Vrne resnično ali neresnično.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Increment_and_decrement_operators", "MATH_CHANGE_TITLE": "spremeni %1 za %2", "MATH_CHANGE_TOOLTIP": "Prišteje število k spremenljivki »%1«.", "MATH_ROUND_HELPURL": "https://sl.wikipedia.org/wiki/Zaokro%C5%BEanje", "MATH_ROUND_TOOLTIP": "Zaokroži število navzgor ali navzdol.", "MATH_ROUND_OPERATOR_ROUND": "zaokroži", "MATH_ROUND_OPERATOR_ROUNDUP": "zaokroži navzgor", "MATH_ROUND_OPERATOR_ROUNDDOWN": "zaokroži navzdol", "MATH_ONLIST_OPERATOR_SUM": "vsota seznama", "MATH_ONLIST_TOOLTIP_SUM": "Vrne vsoto vseh števil v seznamu.", "MATH_ONLIST_OPERATOR_MIN": "minimum seznama", "MATH_ONLIST_TOOLTIP_MIN": "Vrne najmanjše število v seznamu.", "MATH_ONLIST_OPERATOR_MAX": "maksimum seznama", "MATH_ONLIST_TOOLTIP_MAX": "Vrne največje število v seznamu.", "MATH_ONLIST_OPERATOR_AVERAGE": "povprečje seznama", "MATH_ONLIST_TOOLTIP_AVERAGE": "Vrne povprečje (aritmetično sredino) števil v seznamu.", "MATH_ONLIST_OPERATOR_MEDIAN": "<PERSON>a seznama", "MATH_ONLIST_TOOLTIP_MEDIAN": "Vrne mediano število v seznamu.", "MATH_ONLIST_OPERATOR_MODE": "modus seznama", "MATH_ONLIST_TOOLTIP_MODE": "Vrne seznam najpogostejšega(ih) elementa(-ov) v seznamu.", "MATH_ONLIST_OPERATOR_STD_DEV": "standardni odmik seznama", "MATH_ONLIST_TOOLTIP_STD_DEV": "Vrne standardni odmik elementov v seznamu.", "MATH_ONLIST_OPERATOR_RANDOM": "naključni element seznama", "MATH_ONLIST_TOOLTIP_RANDOM": "Vrne naključno število izmed števil v seznamu.", "MATH_MODULO_HELPURL": "https://sl.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "MATH_MODULO_TITLE": "ostanek pri %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Vrne ostanek pri deljenju dveh števil.", "MATH_CONSTRAIN_HELPURL": "https://en.wikipedia.org/wiki/Clamping_%28graphics%29", "MATH_CONSTRAIN_TITLE": "omeji %1 na najmanj %2 in največ %3", "MATH_CONSTRAIN_TOOLTIP": "<PERSON><PERSON><PERSON>, da bo med <PERSON> (vključenima) mejama.", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "naključno število med %1 in %2", "MATH_RANDOM_INT_TOOLTIP": "Vrne naključno število med dvema določenima mejama, vključno z mejama.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "naključni ulomek", "MATH_RANDOM_FLOAT_TOOLTIP": "Vrne naključni ulomek med (vključno) 0,0 in 1,0 (izkl<PERSON><PERSON><PERSON>).", "MATH_ATAN2_HELPURL": "https://sl.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 koordinat X: %1 in Y: %2", "MATH_ATAN2_TOOLTIP": "Vrne arkus tangens točke (X, Y) v stopinjah med −180 in 180.", "TEXT_TEXT_HELPURL": "https://sl.wikipedia.org/wiki/Niz", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON>, beseda ali vrstica besedila.", "TEXT_JOIN_HELPURL": "https://github.com/google/blockly/wiki/Text#text-creation", "TEXT_JOIN_TITLE_CREATEWITH": "ustvari besedilo iz", "TEXT_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>, da zdr<PERSON>ži poljub<PERSON> števil<PERSON>.", "TEXT_CREATE_JOIN_TITLE_JOIN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON>, odstrani ali spremeni vrstni red odsekov za ponovno nastavitev tega bloka besedila.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Doda element k besedilu.", "TEXT_APPEND_HELPURL": "https://github.com/google/blockly/wiki/Text#text-modification", "TEXT_APPEND_TITLE": "k %1 dodaj besedilo %2", "TEXT_APPEND_TOOLTIP": "Doda besedilo k spremenljivki »%1«.", "TEXT_LENGTH_HELPURL": "https://github.com/google/blockly/wiki/Text#text-modification", "TEXT_LENGTH_TITLE": "dolžina %1", "TEXT_LENGTH_TOOLTIP": "Vrne število znakov (vključno s presledki) v določenem besedilu.", "TEXT_ISEMPTY_HELPURL": "https://github.com/google/blockly/wiki/Text#checking-for-empty-text", "TEXT_ISEMPTY_TITLE": "%1 je prazno", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON>, če je dolo<PERSON>eno besedilo p<PERSON>.", "TEXT_INDEXOF_HELPURL": "https://github.com/google/blockly/wiki/Text#finding-text", "TEXT_INDEXOF_TOOLTIP": "Vrne mesto (indeks) prve/zadnje pojavitve drugega besedila v prvem besedilu. Če besedila ne najde, vrne %1.", "TEXT_INDEXOF_TITLE": "v besedilu %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "najdi prvo pojavitev besedila", "TEXT_INDEXOF_OPERATOR_LAST": "naj<PERSON> z<PERSON><PERSON><PERSON> p<PERSON> besedila", "TEXT_CHARAT_HELPURL": "https://github.com/google/blockly/wiki/Text#extracting-text", "TEXT_CHARAT_TITLE": "v besedilu %1 %2", "TEXT_CHARAT_FROM_START": "vrni črko št.", "TEXT_CHARAT_FROM_END": "vrni črko št. od konca", "TEXT_CHARAT_FIRST": "vrni prvo črko", "TEXT_CHARAT_LAST": "v<PERSON><PERSON> zadnjo č<PERSON>o", "TEXT_CHARAT_RANDOM": "vrni naključno črko", "TEXT_CHARAT_TOOLTIP": "Vrne črko na določenem mestu.", "TEXT_GET_SUBSTRING_TOOLTIP": "Vrne dolo<PERSON>en del besedila.", "TEXT_GET_SUBSTRING_HELPURL": "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "iz besedila", "TEXT_GET_SUBSTRING_START_FROM_START": "vrni podniz od črke št.", "TEXT_GET_SUBSTRING_START_FROM_END": "vrni podniz od črke št. od konca", "TEXT_GET_SUBSTRING_START_FIRST": "vrni podniz od prve črke", "TEXT_GET_SUBSTRING_END_FROM_START": "do črke št.", "TEXT_GET_SUBSTRING_END_FROM_END": "do črke št. od konca", "TEXT_GET_SUBSTRING_END_LAST": "do zadnje črke", "TEXT_CHANGECASE_HELPURL": "https://github.com/google/blockly/wiki/Text#adjusting-text-case", "TEXT_CHANGECASE_TOOLTIP": "Vrne kopijo besedila v drugi obliki.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "v VELIKE ČRKE", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "v male črke", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "v Velike Začetnice", "TEXT_TRIM_HELPURL": "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces", "TEXT_TRIM_TOOLTIP": "Vrne kopijo besedila z odstranjenimi presledki z ene ali obeh strani.", "TEXT_TRIM_OPERATOR_BOTH": "odstrani presledke z obeh strani", "TEXT_TRIM_OPERATOR_LEFT": "odstrani presledke z leve strani", "TEXT_TRIM_OPERATOR_RIGHT": "odstrani presledke z desne strani", "TEXT_PRINT_HELPURL": "https://github.com/google/blockly/wiki/Text#printing-text", "TEXT_PRINT_TITLE": "izpiši %1", "TEXT_PRINT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ali drugo vrednost.", "TEXT_PROMPT_HELPURL": "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user", "TEXT_PROMPT_TYPE_TEXT": "vprašaj za besedilo s sporočilom", "TEXT_PROMPT_TYPE_NUMBER": "vprašaj za številko s sporočilom", "TEXT_PROMPT_TOOLTIP_NUMBER": "Vpraša uporabnika za vnos številke.", "TEXT_PROMPT_TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> upora<PERSON>a za vnos besedila.", "TEXT_COUNT_MESSAGE0": "preštej %1 v %2", "TEXT_COUNT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, kolikokrat se neko besedilo pojavi v drugem besedilu.", "TEXT_REPLACE_MESSAGE0": "zamenjaj %1 z %2 v %3", "TEXT_REPLACE_TOOLTIP": "Zamenja vse pojavitve besedila v drugem besedilu.", "TEXT_REVERSE_MESSAGE0": "obrni %1", "TEXT_REVERSE_TOOLTIP": "Obrne vrstni red znakov v besedilu.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "ustvari prazen seznam", "LISTS_CREATE_EMPTY_TOOLTIP": "Vrne seznam dolžine 0, ki ne vsebuje nobenih podatkovnih zapisov.", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "Ustvari seznam s poljubnim številom elementov.", "LISTS_CREATE_WITH_INPUT_WITH": "ustvari seznam iz", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "seznam", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON>, odstrani ali spremeni vrstni red blokov seznama.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Doda element v seznam.", "LISTS_REPEAT_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_REPEAT_TOOLTIP": "Ustvari seznam iz dane vrednosti z določenim številom ponovitev.", "LISTS_REPEAT_TITLE": "ustvari seznam z elementom %1, ki se ponovi %2-krat", "LISTS_LENGTH_HELPURL": "https://github.com/google/blockly/wiki/Lists#length-of", "LISTS_LENGTH_TITLE": "dolžina %1", "LISTS_LENGTH_TOOLTIP": "<PERSON>rne <PERSON>ž<PERSON>.", "LISTS_ISEMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#is-empty", "LISTS_ISEMPTY_TITLE": "%1 je prazen", "LISTS_ISEMPTY_TOOLTIP": "<PERSON><PERSON>, če je seznam prazen.", "LISTS_INLIST": "v seznamu", "LISTS_INDEX_OF_HELPURL": "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list", "LISTS_INDEX_OF_FIRST": "najdi prvo pojavitev elementa", "LISTS_INDEX_OF_LAST": "najdi zadn<PERSON> pojavi<PERSON>v <PERSON>a", "LISTS_INDEX_OF_TOOLTIP": "Vrne mesto (indeks) prve/zadnje pojavitve elementa v seznamu. Če elementa ne najde, vrne %1.", "LISTS_GET_INDEX_GET": "vrni", "LISTS_GET_INDEX_GET_REMOVE": "odstrani in vrni", "LISTS_GET_INDEX_REMOVE": "odstrani", "LISTS_GET_INDEX_FROM_START": "št.", "LISTS_GET_INDEX_FROM_END": "mesto št. od konca", "LISTS_GET_INDEX_FIRST": "prvo mesto", "LISTS_GET_INDEX_LAST": "zad<PERSON>je mesto", "LISTS_GET_INDEX_RANDOM": "naključno mesto", "LISTS_INDEX_FROM_START_TOOLTIP": "Prvi element je št. %1.", "LISTS_INDEX_FROM_END_TOOLTIP": "Zadnji element je št. %1.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Vrne element na določenem mestu v seznamu.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Vrne prvi element seznama.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Vrne zadnji element seznama.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Vrne naključni element seznama.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Odstrani in vrne element na določenem mestu v seznamu.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Odstrani in vrne prvi element seznama.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Odstrani in vrne zadnji element seznama.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Odstrani in vrne naključni element seznama.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Odstrani element na določenem mestu v seznamu.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Odstrani prvi element seznama.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Odstrani zadnji element seznama.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Odstrani naključni element seznama.", "LISTS_SET_INDEX_HELPURL": "https://github.com/google/blockly/wiki/Lists#in-list--set", "LISTS_SET_INDEX_SET": "nastavi na", "LISTS_SET_INDEX_INSERT": "vstavi na", "LISTS_SET_INDEX_INPUT_TO": "element", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Nastavi element na določenem mestu v seznamu.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Nastavi prvi element seznama.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Nastavi zadnji element seznama.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Nastavi naključni element seznama.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Vstavi element na določeno mesto v seznamu.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Vstavi element na začetek seznama.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Doda element na konec seznama.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Vstavi element na naključno mesto v seznamu.", "LISTS_GET_SUBLIST_HELPURL": "https://github.com/google/blockly/wiki/Lists#getting-a-sublist", "LISTS_GET_SUBLIST_START_FROM_START": "ustvari podseznam od mesta št.", "LISTS_GET_SUBLIST_START_FROM_END": "ustvari podseznam od mesta št. od konca", "LISTS_GET_SUBLIST_START_FIRST": "ustvari podseznam od prvega mesta", "LISTS_GET_SUBLIST_END_FROM_START": "do mesta št.", "LISTS_GET_SUBLIST_END_FROM_END": "do mesta št. od konca", "LISTS_GET_SUBLIST_END_LAST": "do zadnjega mesta", "LISTS_GET_SUBLIST_TOOLTIP": "Ustvari kopijo določenega dela seznama.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "uredi %1 %2 %3", "LISTS_SORT_TOOLTIP": "<PERSON><PERSON><PERSON> kopi<PERSON> se<PERSON>.", "LISTS_SORT_ORDER_ASCENDING": "naraščajoče", "LISTS_SORT_ORDER_DESCENDING": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_NUMERIC": "številsko", "LISTS_SORT_TYPE_TEXT": "abecedno", "LISTS_SORT_TYPE_IGNORECASE": "abecedno, prezri velikost črk", "LISTS_SPLIT_HELPURL": "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists", "LISTS_SPLIT_LIST_FROM_TEXT": "ustvari seznam iz besedila", "LISTS_SPLIT_TEXT_FROM_LIST": "ustvari besedilo iz seznama", "LISTS_SPLIT_WITH_DELIMITER": "z ločilom", "LISTS_SPLIT_TOOLTIP_SPLIT": "Razdruži besedilo v seznam besedil s prelomom pri vsakem ločilu.", "LISTS_SPLIT_TOOLTIP_JOIN": "Združi seznam besedil v eno besedilo z ločilom med besedili.", "LISTS_REVERSE_MESSAGE0": "obrni %1", "LISTS_REVERSE_TOOLTIP": "<PERSON><PERSON><PERSON> kop<PERSON>.", "VARIABLES_GET_HELPURL": "https://github.com/google/blockly/wiki/Variables#get", "VARIABLES_GET_TOOLTIP": "Vrne vrednost spremenljivke.", "VARIABLES_GET_CREATE_SET": "Ustvari »nastavi %1«", "VARIABLES_SET_HELPURL": "https://github.com/google/blockly/wiki/Variables#set", "VARIABLES_SET": "nastavi %1 na %2", "VARIABLES_SET_TOOLTIP": "<PERSON><PERSON><PERSON>, da je vrednost spremenljivke enaka vnosu.", "VARIABLES_SET_CREATE_GET": "Ustvari »vrni %1«", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "<PERSON>z<PERSON>i", "PROCEDURES_DEFNORETURN_PROCEDURE": "<PERSON>kaj", "PROCEDURES_BEFORE_PARAMS": "s/z:", "PROCEDURES_CALL_BEFORE_PARAMS": "s/z:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Ustvari funkcijo brez izhoda.", "PROCEDURES_DEFNORETURN_COMMENT": "Opiši funkcijo ...", "PROCEDURES_DEFRETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFRETURN_RETURN": "vrni", "PROCEDURES_DEFRETURN_TOOLTIP": "Ustvari funkcijo z izhodom.", "PROCEDURES_ALLOW_STATEMENTS": "dovoli stavke", "PROCEDURES_DEF_DUPLICATE_WARNING": "Pozor: Ta funkcija ima podvojene parametre.", "PROCEDURES_CALLNORETURN_HELPURL": "https://sl.wikipedia.org/wiki/Subrutina", "PROCEDURES_CALLNORETURN_TOOLTIP": "Izvede uporabniško določeno funkcijo »%1«.", "PROCEDURES_CALLRETURN_HELPURL": "https://sl.wikipedia.org/wiki/Subrutina", "PROCEDURES_CALLRETURN_TOOLTIP": "Izvede uporabniško funkcijo »%1« in uporabi njen izhod.", "PROCEDURES_MUTATORCONTAINER_TITLE": "vnosi", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON>, odstrani ali spremeni vrstni red vnosov za to funkcijo.", "PROCEDURES_MUTATORARG_TITLE": "ime vnosa:", "PROCEDURES_MUTATORARG_TOOLTIP": "Funkciji doda vnos.", "PROCEDURES_HIGHLIGHT_DEF": "Označi blok funkcije", "PROCEDURES_CREATE_DO": "Ustvari »%1«", "PROCEDURES_IFRETURN_TOOLTIP": "Če je vrednost resnična, vrne drugo vrednost.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Pozor: Ta blok lahko uporabite samo v bloku funkcije.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Povej nekaj ...", "WORKSPACE_ARIA_LABEL": "<PERSON><PERSON><PERSON>v delovni prostor", "COLLAPSED_WARNINGS_WARNING": "Skrčeni bloki vsebujejo opozorila.", "DIALOG_OK": "V redu", "DIALOG_CANCEL": "Prekliči"}