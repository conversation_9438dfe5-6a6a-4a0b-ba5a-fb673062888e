package org.jeecg.modules.teaching.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.teaching.dto.SelfTestJudgeHojDTO;
import org.jeecg.modules.teaching.dto.TestJudgeRequestDTO;
import org.jeecg.modules.teaching.dto.TransientJudgeDTO;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.jeecg.modules.teaching.service.ITeachingJudgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.apache.shiro.SecurityUtils;


import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TeachingJudgeServiceImpl implements ITeachingJudgeService {
    
    private final RestTemplate restTemplate;
    
    @Value("${hoj.server.url:http://localhost:10080}")
    private String hojServerUrl;
    
    @Value("${hoj.judge.token:default}")
    private String judgeServerToken;
    
    @Value("${hoj.server.jwt-token:}")
    private String initialHojJwtToken;

    @Value("${hoj.server.username:}")
    private String hojUsername;

    @Value("${hoj.server.password:}")
    private String hojPassword;

    private volatile String hojJwtToken;

    private final IExamQuestionService examQuestionService;

    private final ObjectMapper objectMapper;

    @Autowired
    public TeachingJudgeServiceImpl(@Qualifier("judgeRestTemplate") RestTemplate restTemplate, IExamQuestionService examQuestionService, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.examQuestionService = examQuestionService;
        this.objectMapper = objectMapper;
    }

    @PostConstruct
    public void init() {
        this.hojJwtToken = this.initialHojJwtToken;
    }

    private synchronized void updateHojJwtToken(String newToken) {
        if (newToken != null && !newToken.isEmpty() && !newToken.equals(this.hojJwtToken)) {
            log.info("HOJ JWT token has been refreshed. Updating in-memory token.");
            this.hojJwtToken = newToken;
        }
    }

    private void checkAndRefreshToken(ResponseEntity<?> responseEntity) {
        HttpHeaders headers = responseEntity.getHeaders();
        String newToken = headers.getFirst("Authorization");
        if (newToken != null && !newToken.isEmpty()) {
            updateHojJwtToken(newToken);
        }
    }

    /**
     * 当令牌失效时，强制使用用户名和密码登录HOJ以获取新令牌。
     * 这是一个同步方法，以防止多个线程同时尝试登录。
     */
    private synchronized void forceLoginAndRefreshToken() {
        log.warn("JWT token is likely expired or invalid. Attempting to re-authenticate with HOJ...");
        if (hojUsername == null || hojUsername.isEmpty() || hojPassword == null || hojPassword.isEmpty()) {
            throw new IllegalStateException("HOJ username and password are not configured. Cannot re-authenticate.");
        }

        String loginUrl = hojServerUrl + "/api/login";
        Map<String, String> loginRequest = new java.util.HashMap<>();
        loginRequest.put("username", hojUsername);
        loginRequest.put("password", hojPassword);

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(loginRequest, headers);

            // 使用 postForEntity 进行登录请求
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> responseEntity = restTemplate.postForEntity(loginUrl, requestEntity, Map.class);

            // HOJ的标准做法是将新令牌放在响应头中
            String newToken = responseEntity.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
            if (newToken != null && !newToken.isEmpty()) {
                log.info("Successfully re-authenticated with HOJ. New token acquired from header.");
                this.updateHojJwtToken(newToken);
            } else {
                throw new RuntimeException("Re-authentication failed: HOJ did not return a new token in response header.");
            }
        } catch (Exception e) {
            log.error("Failed to re-authenticate with HOJ.", e);
            throw new RuntimeException("Re-authentication with HOJ failed: " + e.getMessage(), e);
        }
    }

    /**
     * 执行对HOJ的API请求，并增加了自动重登和重试逻辑。
     * @param requestSupplier 一个提供待执行请求的函数
     * @return ResponseEntity 响应实体
     * @param <T> 响应体的类型
     */
    private <T> ResponseEntity<T> executeRequest(Supplier<ResponseEntity<T>> requestSupplier) {
        try {
            // 第一次尝试
            return requestSupplier.get();
        } catch (HttpClientErrorException e) {
            // 如果不是认证/授权相关的错误，直接抛出
            if (e.getStatusCode() != HttpStatus.UNAUTHORIZED && e.getStatusCode() != HttpStatus.FORBIDDEN) {
                throw e;
            }

                log.warn("Request failed with status: {}. Assuming token expired. Attempting re-login and retry.", e.getStatusCode());
            
            try {
                forceLoginAndRefreshToken();
            } catch (Exception loginEx) {
                // 如果重新登录失败，抛出包含具体原因的异常
                throw new RuntimeException("HOJ re-authentication failed. Please check username/password configuration. Original error: " + e.getMessage(), loginEx);
            }
            
                log.info("Retrying the original request with the new token...");
            try {
                // 第二次尝试
                return requestSupplier.get();
            } catch (HttpClientErrorException e2) {
                // 如果第二次尝试仍然失败，很可能是权限问题
                 if (e2.getStatusCode() == HttpStatus.UNAUTHORIZED || e2.getStatusCode() == HttpStatus.FORBIDDEN) {
                    throw new RuntimeException("HOJ request failed after re-authentication. Please check if the user has the required permissions. Final error: " + e2.getMessage(), e2);
            }
                 // 抛出第二次尝试遇到的其他错误
                 throw e2;
            }
        }
    }
    
    /**
     * 【在线自测】功能的后端实现。
     * <p>
     * <b>工作模式</b>: 异步（Asynchronous）。前端发起请求后，将立即获得一个任务key，并需要通过轮询获取最终结果。
     * <p>
     * <b>交互服务</b>: 与【HOJ主后端服务 (DataBackup)】的 {@code /api/teaching/self-test-judge} 接口通信。
     * <p>
     * <b>认证方式</b>: 使用HTTP Header中的JWT，通过 {@link #executeRequest} 方法自动处理。
     * <p>
     * <b>核心流程</b>:
     * <ol>
     *     <li>接收前端传递的代码、语言、自定义输入、时限和内存限制。</li>
     *     <li>获取当前登录用户信息。</li>
     *     <li>构建请求体，调用 {@link #executeRequest} 方法将任务提交给HOJ主后端。</li>
     *     <li>从HOJ主后端的响应中解析出 {@code testJudgeKey}。</li>
     *     <li>将此 {@code testJudgeKey} 包装后立即返回给前端，用于后续的结果轮询。</li>
     * </ol>
     *
     * @param request 包含代码、语言、自定义输入等信息的请求DTO。
     * @return 一个Map对象，其中包含了用于轮询结果的 {@code testJudgeKey}。
     */
    @Override
    public Map<String, Object> testJudge(TestJudgeRequestDTO request) {
        String url = hojServerUrl + "/api/teaching/self-test-judge";

        // 获取当前登录用户信息
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new RuntimeException("用户未登录，无法进行在线自测");
        }

        // 验证必填字段
        if (request.getCode() == null || request.getCode().trim().isEmpty()) {
            throw new RuntimeException("提交的代码不能为空");
        }
        
        if (request.getLanguage() == null || request.getLanguage().trim().isEmpty()) {
            throw new RuntimeException("判题语言不能为空");
        }
        
        if (request.getTestCaseInput() == null) {
            throw new RuntimeException("测试输入不能为空");
        }
        
        // 验证预期输出
        if (request.getExpectedOutput() == null) {
            throw new RuntimeException("预期输出不能为空");
        }
        
        // 确保时间限制和内存限制有值
        if (request.getTimeLimit() == null) {
            // 设置默认时间限制为1000ms
            request.setTimeLimit(1000);
            log.warn("在线自测请求中未提供时间限制，使用默认值1000ms");
        }
        
        if (request.getMemoryLimit() == null) {
            // 设置默认内存限制为256MB
            request.setMemoryLimit(256);
            log.warn("在线自测请求中未提供内存限制，使用默认值256MB");
        }
        
        // 如果堆栈限制为空，设置默认值
        if (request.getStackLimit() == null) {
            // 设置默认堆栈限制为128MB
            request.setStackLimit(128);
            log.warn("在线自测请求中未提供堆栈限制，使用默认值128MB");
        }

        // 构建发送给HOJ的请求体 (使用强类型DTO)
        SelfTestJudgeHojDTO hojRequest = new SelfTestJudgeHojDTO();
        hojRequest.setCode(request.getCode());
        hojRequest.setLanguage(request.getLanguage());
        hojRequest.setTestCaseInput(request.getTestCaseInput());
        hojRequest.setExpectedOutput(request.getExpectedOutput());
        hojRequest.setTimeLimit(request.getTimeLimit());
        hojRequest.setMemoryLimit(request.getMemoryLimit());
        hojRequest.setStackLimit(request.getStackLimit());
        hojRequest.setUid(Objects.toString(loginUser.getId(), ""));
        hojRequest.setUsername(Objects.toString(loginUser.getUsername(), ""));
        hojRequest.setRealname(Objects.toString(loginUser.getRealname(), ""));

        try {
            log.info("发送在线自测任务提交请求 -> URL: [{}], 请求体: [{}]", url, hojRequest);
            
            // 使用带JWT认证和重试逻辑的executeRequest方法
            //ResponseEntity<Map> responseEntity = executeRequest(() -> {
            @SuppressWarnings("rawtypes")
            Supplier<ResponseEntity<Map>> requestSupplier = () -> {
            HttpHeaders headers = new HttpHeaders();
            log.info("我真的服了，这里应该会执行吧？");
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
                if (this.hojJwtToken != null && !this.hojJwtToken.isEmpty()) {
                    headers.set("Authorization", this.hojJwtToken);
                }
                HttpEntity<SelfTestJudgeHojDTO> requestEntity = new HttpEntity<>(hojRequest, headers);
                // 终极解决方案：绕过依赖注入，手动创建实例
                RestTemplate tempRestTemplate = new RestTemplate();
                return tempRestTemplate.exchange(url, HttpMethod.POST, requestEntity, Map.class);
            };
            log.info("我真的服了，这里都不执行吗？");
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> responseEntity = executeRequest(requestSupplier);
            // 检查并刷新可能由响应头返回的新token
            checkAndRefreshToken(responseEntity);
            log.info("做完菜了");
            // 从DataBackup的响应中提取真正的返回数据
            @SuppressWarnings("unchecked")
            Map<String, Object> body = (Map<String, Object>) responseEntity.getBody();

            // 根据HOJ实际返回的格式进行适配
            if (body != null && "success".equals(body.get("msg"))) {
                Object data = body.get("data");
                if (data == null) {
                    throw new RuntimeException("HOJ响应成功，但未返回判题任务key (data is null)");
                }
                String testJudgeKey = (String) data;
                log.info("在线自测任务提交成功，获取到 testJudgeKey: {}", testJudgeKey);

                // 将testJudgeKey包装在Map中返回给前端
                Map<String, Object> clientResponse = new java.util.HashMap<>();
                clientResponse.put("testJudgeKey", testJudgeKey);

                // 只返回核心数据，框架会自动包装成Result对象
                return clientResponse;

            } else {
                log.error("HOJ返回了非预期的响应体: {}", body);
                String errorMessage = body != null ? (String) body.get("msg") : "Unknown error from HOJ";
                throw new RuntimeException("提交在线自测失败: " + errorMessage);
            }

        } catch (Exception e) {
            log.error("调用在线自测服务失败", e);
            throw new RuntimeException("调用在线自测服务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 【提交评测】功能的后端实现。
     * <p>
     * <b>工作模式</b>: 异步（Asynchronous）。此方法仅负责提交判题任务，并立即返回一个任务ID。
     * <p>
     * <b>交互服务</b>: 与【HOJ主后端服务】（例如: :8866端口）的 {@code /api/admin/problem/submit-transient-judge} 接口通信。
     * <p>
     * <b>认证方式</b>: 使用HTTP Header中的JWT (Authorization)。
     * <p>
     * <b>核心流程</b>:
     * <ol>
     *     <li>根据前端传来的题目ID (pid)，从本系统数据库查询题目的完整信息（如时限、内存限制、官方测试用例）。</li>
     *     <li>将代码和完整的题目信息打包，构建请求。</li>
     *     <li>使用 {@link #executeRequest} 方法（内置重登和重试逻辑）将请求发送给HOJ主后端。</li>
     *     <li>HOJ主后端将任务加入判题队列，并返回一个唯一的 submissionId。</li>
     *     <li>将此 submissionId 返回给前端，用于后续的结果查询。</li>
     * </ol>
     *
     * @param request 包含代码、语言、题目ID等信息的请求DTO。
     * @return 一个Map对象，其中包含了用于轮询结果的 {@code submissionId}。
     */
    @Override
    public Map<String, Object> submitTestJudge(TestJudgeRequestDTO request) {
        if (request.getPid() == null || request.getPid().trim().isEmpty()) {
            throw new RuntimeException("提交失败：题目ID不能为空！");
        }
        ExamQuestion question = examQuestionService.getById(request.getPid());
        if (question == null) {
            throw new RuntimeException("提交失败：未找到ID为 " + request.getPid() + " 的题目！");
        }
        String contentJson = question.getContent();
        if (contentJson == null || contentJson.trim().isEmpty()) {
            throw new RuntimeException("提交失败：题目内容为空，无法获取判题参数！");
        }
        Map<String, Object> contentMap;
        try {
            contentMap = objectMapper.readValue(contentJson, new TypeReference<Map<String, Object>>() {
            });
        } catch (IOException e) {
            log.error("解析题目内容JSON失败, questionId: {}", question.getId(), e);
            throw new RuntimeException("提交失败：题目内容格式错误！", e);
        }

        Integer timeLimit = (Integer) contentMap.get("time_limit");
        Integer memoryLimitMb = (Integer) contentMap.get("memory_limit");
        Integer stackLimit = (Integer) contentMap.get("stack_limit");
        @SuppressWarnings("unchecked")
        List<Map<String, String>> sampleCases = (List<Map<String, String>>) contentMap.get("sample_cases");

        if (timeLimit == null || memoryLimitMb == null || sampleCases == null || sampleCases.isEmpty()) {
            throw new RuntimeException("提交失败：题目内容缺少必要的判题参数（时间限制、内存限制或测试用例）！");
        }

        List<TransientJudgeDTO.TestCase> transientTestCases = sampleCases.stream().map(tc -> {
            TransientJudgeDTO.TestCase transientCase = new TransientJudgeDTO.TestCase();
            transientCase.setInput(tc.get("input"));
            transientCase.setOutput(tc.get("output"));
            return transientCase;
        }).collect(Collectors.toList());

        TransientJudgeDTO hojRequest = new TransientJudgeDTO();
        hojRequest.setCode(request.getCode());
        hojRequest.setLanguage(request.getLanguage());
        hojRequest.setTimeLimit(timeLimit);
        // HOJ期望内存限制为MB单位，与题目解析后的单位一致
        hojRequest.setMemoryLimit(memoryLimitMb);
        hojRequest.setStackLimit(stackLimit != null ? stackLimit : 128);
        hojRequest.setJudgeMode("default");
        hojRequest.setTestCases(transientTestCases);

        try {
            String url = hojServerUrl + "/api/admin/problem/submit-transient-judge";
            log.info("向HOJ代理API发送请求: {}, 请求体: {}", url, hojRequest);
            
            // 将实际的请求和重试逻辑包装到executeRequest方法中
            @SuppressWarnings("rawtypes")
            Supplier<ResponseEntity<Map>> requestSupplier = () -> {
            HttpHeaders headers = new HttpHeaders();
                headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
                if (this.hojJwtToken != null && !this.hojJwtToken.isEmpty()) {
                    headers.set("Authorization", this.hojJwtToken);
            }
                HttpEntity<TransientJudgeDTO> requestEntity = new HttpEntity<>(hojRequest, headers);
                return restTemplate.exchange(url, HttpMethod.POST, requestEntity, Map.class);
            };

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> responseEntity = executeRequest(requestSupplier);
            
            // 即使重试成功，也检查是否有更新的令牌（滑动窗口机制）
            checkAndRefreshToken(responseEntity);

            // 检查HTTP状态码是否为成功状态 (2xx)
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> response = (Map<String, Object>) responseEntity.getBody();
                log.info("收到HOJ代理API的成功响应: {}", response);
            
            if (response != null) {
                    Map<String, Object> result = new java.util.HashMap<>();
                    // HOJ成功时，data字段直接就是submissionId
                    Object submissionId = response.get("data");
                    if (submissionId != null) {
                        result.put("submissionId", submissionId);
                        return result;
                    } else {
                         // 兜底逻辑，如果data为空，但http状态码是成功的，也告知前端任务已提交
                        log.warn("HOJ响应成功，但未返回submissionId，响应体: {}", response);
                        result.put("submissionId", null);
                        result.put("message", "即时判题任务提交成功！");
                        return result;
                    }
                } else {
                     // 兜底逻辑
                    log.warn("HOJ响应成功，但响应体为空");
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("message", "即时判题任务提交成功！");
                    return result;
                }
            } else {
                // 如果HTTP状态码不是2xx，则认为是失败
                log.error("调用HOJ代理API失败，HTTP状态码: {}, 响应体: {}", responseEntity.getStatusCode(), responseEntity.getBody());
                String errorMsg = "HTTP状态码: " + responseEntity.getStatusCode();
                if (responseEntity.hasBody() && responseEntity.getBody() != null) {
                    errorMsg = String.valueOf(responseEntity.getBody().get("msg"));
                    }
                throw new RuntimeException("提交到HOJ失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("调用HOJ代理API失败", e);
            throw new RuntimeException("调用HOJ代理API失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取【提交评测】异步任务的判题结果。
     * <p>
     * <b>工作模式</b>: 异步轮询的实际执行者。前端会使用 {@link #submitTestJudge} 返回的 submissionId 反复调用此方法。
     * <p>
     * <b>交互服务</b>: 与【HOJ主后端服务】的 {@code /api/get-submission-detail} 接口通信。
     * <p>
     * <b>认证方式</b>: 使用HTTP Header中的JWT (Authorization)。
     * <p>
     * <b>核心流程</b>:
     * <ol>
     *     <li>接收前端传递的 submissionId。</li>
     *     <li>使用 {@link #executeRequest} 方法向HOJ主后端查询指定ID的判题状态。</li>
     *     <li>如果判题未完成，HOJ返回等待状态；如果已完成，则返回完整结果。</li>
     *     <li>调用 {@link #translateHojStatus} 转换状态码。</li>
     *     <li>将查询到的结果返回给前端。</li>
     * </ol>
     *
     * @param submissionId 需要查询结果的判题任务ID。
     * @return 一个Map对象，包含了详细的判题结果（状态、耗时、内存、输出等）。
     */
    @Override
    public Map<String, Object> getTestJudgeResult(String submissionId) {
        try {
            if (submissionId == null || submissionId.trim().isEmpty() || submissionId.equals("[object Object]")) {
                log.error("获取判题结果失败：提交ID为空或格式错误, submissionId: {}", submissionId);
                throw new IllegalArgumentException("无效的提交ID: " + submissionId);
            }

            String url = hojServerUrl + "/api/get-submission-detail?submitId=" + submissionId;
            log.info("请求判题结果: {}", url);
            
            // 将实际的请求和重试逻辑包装到executeRequest方法中
            @SuppressWarnings("rawtypes")
            Supplier<ResponseEntity<Map>> requestSupplier = () -> {
            HttpHeaders headers = new HttpHeaders();
                if (this.hojJwtToken != null && !this.hojJwtToken.isEmpty()) {
                    headers.set("Authorization", this.hojJwtToken);
            }
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
                return restTemplate.exchange(url, HttpMethod.GET, requestEntity, Map.class);
            };

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> responseEntity = executeRequest(requestSupplier);

            checkAndRefreshToken(responseEntity);
            log.info("responseEntity数据{}",responseEntity);

            log.info("收到判题结果响应状态码: {}", responseEntity.getStatusCode());
            
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> response = (Map<String, Object>) responseEntity.getBody();
                if (response.get("data") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) response.get("data");
                    if (data.get("submission") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> submission = (Map<String, Object>) data.get("submission");
                        translateHojStatus(submission);
                        return submission;
                    }
                }
                // 如果响应格式不符合预期，直接返回原始数据
                return response;
            } else {
                log.error("从HOJ获取判题结果失败，HTTP状态码: {}, 响应体: {}", responseEntity.getStatusCode(), responseEntity.getBody());
                throw new RuntimeException("获取判题结果失败: " + responseEntity.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取判题结果异常", e);
            throw new RuntimeException("获取判题结果异常: " + e.getMessage(), e);
        }
            }
    
    /**
     * 获取在线自测的判题结果
     * @param testJudgeKey 判题任务key
     * @return 判题结果
     */
    @Override
    public Map<String, Object> getSelfTestJudgeResult(String testJudgeKey) {
        String url = hojServerUrl + "/api/get-test-judge-result?testJudgeKey=" + testJudgeKey;
        log.info("查询【在线自测】的判题结果 -> URL: [{}]", url);
        try {
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> responseEntity = executeRequest(() -> {
                HttpHeaders headers = new HttpHeaders();
                if (this.hojJwtToken != null && !this.hojJwtToken.isEmpty()) {
                    headers.set("Authorization", this.hojJwtToken);
                }
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
                return restTemplate.exchange(url, HttpMethod.GET, requestEntity, Map.class);
            });

            checkAndRefreshToken(responseEntity);
            @SuppressWarnings("unchecked")
            Map<String, Object> body = (Map<String, Object>) responseEntity.getBody();
            log.info("body数据：{}",body);
            // 检查HOJ的响应，并将其转换为我们系统统一的格式
            if (body != null && "success".equals(body.get("msg"))) {
                Object data = body.get("data");
                if (data == null) {
                    throw new RuntimeException("HOJ响应成功，但未返回判题结果 (data is null)");
                }
                // 只返回核心数据，框架会自动包装成Result对象
                log.info("data数据：{}",data);
                @SuppressWarnings("unchecked")
                Map<String, Object> resultData = (Map<String, Object>) data;
                return resultData;
            } else {
                // 如果HOJ响应不成功，也返回我们标准的失败格式
                String errorMessage = body != null ? (String) body.get("msg") : "Unknown error from HOJ";
                throw new RuntimeException("获取在线自测结果失败: " + errorMessage);
            }
        } catch (Exception e) {
            log.error("获取在线自测结果失败", e);
            throw new RuntimeException("获取在线自测结果失败: " + e.getMessage(), e);
        }
    }
            
    /**
     * 将HOJ返回的数字状态码转换为人类可读的文本描述。
     * 这是一个私有辅助方法，用于统一处理前端展示的状态文本。
     *
     * @param submission 从HOJ获取的判题结果Map，其中必须包含一个名为 "status" 的Integer类型字段。
     */
    private void translateHojStatus(Map<String, Object> submission) {
        if (submission == null || !submission.containsKey("status")) {
            return;
        }

        try {
            Integer status = (Integer) submission.get("status");
            String statusStr = "Unknown";
            switch (status) {
                case -10: statusStr = "Not Submitted"; break; // 提交失败
                case -4: statusStr = "Cancelled"; break; // 已取消
                case -3: statusStr = "Presentation Error"; break; // 格式错误
                case -2: statusStr = "Compile Error"; break; // 编译失败
                case -1: statusStr = "Wrong Answer"; break; // 答案错误
                case 0: statusStr = "Accepted"; break; // 完全正确
                case 1: statusStr = "Time Limit Exceeded"; break; // 时间超限
                case 2: statusStr = "Memory Limit Exceeded"; break; // 内存超限
                case 3: statusStr = "Runtime Error"; break; // 运行错误
                case 4: statusStr = "System Error"; break; // 系统错误
                case 5: statusStr = "Pending"; break; // 等待判题
                case 6: statusStr = "Compiling"; break; // 编译中
                case 7: statusStr = "Judging"; break; // 正在判题
                case 8: statusStr = "Partial Accepted"; break; // 部分正确
                case 9: statusStr = "Submitting"; break; // 提交中
                case 10: statusStr = "Submitted Failed"; break; // 提交失败
                // 默认处理未知状态
                default:
                    statusStr = "Unknown Status"; // 未知状态
                    break;
            }
            submission.put("status", statusStr);
        } catch (Exception e) {
            log.error("翻译HOJ状态码时出错", e);
            // 出错时，保留原始status
        }
    }
} 