package org.jeecg.modules.teaching.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.teaching.entity.TeachingCourseNotification;

import java.util.Date;
import java.util.List;

/**
 * @Description: 课程通知
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
public interface TeachingCourseNotificationMapper extends BaseMapper<TeachingCourseNotification> {
    
    /**
     * 查询待发送的课程提醒通知
     * @param currentTime 当前时间
     * @param notificationType 通知类型（REMIND）
     * @return 待发送的通知列表
     */
    List<TeachingCourseNotification> selectPendingReminders(@Param("currentTime") Date currentTime, @Param("notificationType") String notificationType);
} 