# 数据库设计规范

## 表命名规范

### 命名约定
- **系统表**: `sys_*` (如: `sys_user`, `sys_role`, `sys_permission`)
- **教学表**: `teaching_*` (如: `teaching_course`, `teaching_class`)
- **考试表**: `exam_*` (如: `exam_question`, `exam_paper`, `exam_record`)
- **HOJ集成表**: `hoj_*` (如: `hoj_submission`, `hoj_problem`)

### 表名规则
- 使用小写字母和下划线
- 表名应该是复数形式或描述性名词
- 避免使用保留字
- 表名长度不超过30个字符

## 字段设计规范

### 基础字段
每个业务表都应包含以下基础字段：
```sql
CREATE TABLE `teaching_example` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-正常,1-已删除)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```

### 字段命名规范
- 使用小写字母和下划线
- 字段名应该具有描述性
- 布尔类型字段使用`is_*`前缀
- 时间字段使用`*_time`后缀
- 状态字段使用`status`或`*_status`

### 数据类型规范
```sql
-- 主键
id varchar(36) NOT NULL COMMENT '主键ID'

-- 字符串类型
title varchar(255) NOT NULL COMMENT '标题'
content text COMMENT '内容'
description varchar(500) COMMENT '描述'

-- 数值类型
sort_no int DEFAULT '0' COMMENT '排序号'
score decimal(10,2) DEFAULT '0.00' COMMENT '分数'
count int DEFAULT '0' COMMENT '数量'

-- 时间类型
create_time datetime DEFAULT NULL COMMENT '创建时间'
start_time datetime DEFAULT NULL COMMENT '开始时间'

-- 状态类型
status tinyint(1) DEFAULT '1' COMMENT '状态(0-禁用,1-启用)'
del_flag tinyint(1) DEFAULT '0' COMMENT '删除标志(0-正常,1-已删除)'

-- 枚举类型
question_type varchar(20) DEFAULT 'SINGLE_CHOICE' COMMENT '题目类型'
```

## 核心业务表设计

### 1. 考试题目表 (exam_question)
```sql
CREATE TABLE `exam_question` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '题目标题',
  `question_type` varchar(20) NOT NULL COMMENT '题目类型(SINGLE_CHOICE-单选,MULTIPLE_CHOICE-多选,FILL_BLANK-填空,ESSAY-简答,PROGRAMMING-编程)',
  `subject` varchar(50) NOT NULL COMMENT '科目',
  `level` varchar(20) NOT NULL COMMENT '级别',
  `difficulty` varchar(20) NOT NULL COMMENT '难度(EASY-简单,MEDIUM-中等,HARD-困难)',
  `content` text NOT NULL COMMENT '题目内容',
  `answer` text COMMENT '参考答案',
  `analysis` text COMMENT '答案解析',
  `score` decimal(5,2) DEFAULT '1.00' COMMENT '分值',
  `author` varchar(50) COMMENT '出题人',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_subject_level` (`subject`, `level`),
  KEY `idx_question_type` (`question_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考试题目表';
```

### 2. 试卷表 (exam_paper)
```sql
CREATE TABLE `exam_paper` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `paper_name` varchar(255) NOT NULL COMMENT '试卷名称',
  `subject` varchar(50) NOT NULL COMMENT '科目',
  `level` varchar(20) NOT NULL COMMENT '级别',
  `year` int DEFAULT NULL COMMENT '年份',
  `paper_type` varchar(20) DEFAULT 'PRACTICE' COMMENT '试卷类型(PRACTICE-练习,EXAM-考试,MOCK-模拟)',
  `total_score` decimal(8,2) DEFAULT '100.00' COMMENT '总分',
  `duration` int DEFAULT '120' COMMENT '考试时长(分钟)',
  `question_count` int DEFAULT '0' COMMENT '题目数量',
  `description` text COMMENT '试卷描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_subject_level_year` (`subject`, `level`, `year`),
  KEY `idx_paper_type` (`paper_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='试卷表';
```

### 3. 试卷题目关联表 (exam_paper_question)
```sql
CREATE TABLE `exam_paper_question` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `paper_id` varchar(36) NOT NULL COMMENT '试卷ID',
  `question_id` varchar(36) NOT NULL COMMENT '题目ID',
  `sort_no` int DEFAULT '0' COMMENT '题目序号',
  `score` decimal(5,2) DEFAULT '1.00' COMMENT '题目分值',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paper_question` (`paper_id`, `question_id`),
  KEY `idx_paper_id` (`paper_id`),
  KEY `idx_question_id` (`question_id`),
  CONSTRAINT `fk_paper_question_paper` FOREIGN KEY (`paper_id`) REFERENCES `exam_paper` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_paper_question_question` FOREIGN KEY (`question_id`) REFERENCES `exam_question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='试卷题目关联表';
```

### 4. 考试记录表 (exam_record)
```sql
CREATE TABLE `exam_record` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `paper_id` varchar(36) NOT NULL COMMENT '试卷ID',
  `exam_type` varchar(20) DEFAULT 'PRACTICE' COMMENT '考试类型(PRACTICE-练习,FORMAL-正式考试)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int DEFAULT '0' COMMENT '用时(秒)',
  `total_score` decimal(8,2) DEFAULT '0.00' COMMENT '总分',
  `user_score` decimal(8,2) DEFAULT '0.00' COMMENT '得分',
  `correct_count` int DEFAULT '0' COMMENT '正确题数',
  `wrong_count` int DEFAULT '0' COMMENT '错误题数',
  `status` varchar(20) DEFAULT 'IN_PROGRESS' COMMENT '状态(IN_PROGRESS-进行中,COMPLETED-已完成,TIMEOUT-超时)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_paper_id` (`paper_id`),
  KEY `idx_exam_type` (`exam_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考试记录表';
```

### 5. 答题记录表 (exam_answer_record)
```sql
CREATE TABLE `exam_answer_record` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `record_id` varchar(36) NOT NULL COMMENT '考试记录ID',
  `question_id` varchar(36) NOT NULL COMMENT '题目ID',
  `user_answer` text COMMENT '用户答案',
  `correct_answer` text COMMENT '正确答案',
  `is_correct` tinyint(1) DEFAULT '0' COMMENT '是否正确(0-错误,1-正确)',
  `score` decimal(5,2) DEFAULT '0.00' COMMENT '得分',
  `answer_time` datetime DEFAULT NULL COMMENT '答题时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_question` (`record_id`, `question_id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_question_id` (`question_id`),
  CONSTRAINT `fk_answer_record_record` FOREIGN KEY (`record_id`) REFERENCES `exam_record` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_answer_record_question` FOREIGN KEY (`question_id`) REFERENCES `exam_question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='答题记录表';
```

### 6. 错题记录表 (exam_mistake)
```sql
CREATE TABLE `exam_mistake` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `question_id` varchar(36) NOT NULL COMMENT '题目ID',
  `last_answer` text COMMENT '最近一次错误答案',
  `mistake_count` int DEFAULT '1' COMMENT '错误次数',
  `last_mistake_time` datetime DEFAULT NULL COMMENT '最近一次答错时间',
  `is_mastered` tinyint(1) DEFAULT '0' COMMENT '是否已掌握(0-未掌握,1-已掌握)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_question` (`user_id`, `question_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_is_mastered` (`is_mastered`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错题记录表';
```

## 索引设计规范

### 索引类型
1. **主键索引**: 每个表必须有主键
2. **唯一索引**: 保证数据唯一性
3. **普通索引**: 提高查询性能
4. **复合索引**: 多字段组合查询

### 索引命名规范
- 主键: `PRIMARY`
- 唯一索引: `uk_字段名` 或 `uk_表名_字段名`
- 普通索引: `idx_字段名` 或 `idx_表名_字段名`
- 外键: `fk_表名_关联表名`

### 索引设计原则
1. 经常用于WHERE条件的字段建索引
2. 经常用于ORDER BY的字段建索引
3. 经常用于JOIN的字段建索引
4. 避免在小表上建索引
5. 避免在频繁更新的字段上建索引

## 外键约束规范

### 外键命名
```sql
CONSTRAINT `fk_子表_父表` FOREIGN KEY (`外键字段`) 
REFERENCES `父表` (`主键字段`) ON DELETE CASCADE
```

### 级联操作
- `ON DELETE CASCADE`: 删除父记录时同时删除子记录
- `ON DELETE SET NULL`: 删除父记录时将子记录外键设为NULL
- `ON UPDATE CASCADE`: 更新父记录主键时同时更新子记录外键

## 性能优化规范

### 表设计优化
1. 合理选择数据类型，避免过度设计
2. 适当使用冗余字段减少JOIN操作
3. 大表考虑分区或分表
4. 合理设置字段默认值

### 查询优化
1. 避免SELECT *，只查询需要的字段
2. 合理使用LIMIT限制结果集
3. 避免在WHERE子句中使用函数
4. 合理使用子查询和JOIN

### 维护规范
1. 定期分析表结构和索引使用情况
2. 定期清理无用数据
3. 监控慢查询日志
4. 定期备份重要数据
