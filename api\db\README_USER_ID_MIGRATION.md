# 用户ID字段迁移说明

## 问题背景

系统中的`exam_collection`（收藏题目表）和`exam_mistake`（错题记录表）两个表存在数据问题：`user_id`字段存储的是用户名（如"admin"），而非用户的实际ID。这导致：

1. 与系统其他表的关联性不一致，其他表通常使用`sys_user.id`而非`sys_user.username`作为外键
2. 如果用户修改用户名，可能导致历史收藏和错题记录无法关联到正确的用户
3. 存在用户名重复的风险，可能导致数据混乱

## 修复方案

我们提供了一个SQL迁移脚本`migrate_user_ids.sql`，用于将这两个表中的用户名替换为实际的用户ID。同时，我们修改了相关控制器代码，确保今后所有新增的记录都使用正确的用户ID。

### 修复步骤

1. **备份数据**
   - 脚本首先创建两个表的备份，以防数据迁移出现问题
   - 备份表名为`exam_collection_backup`和`exam_mistake_backup`

2. **数据迁移**
   - 通过SQL更新语句，将`user_id`字段从用户名替换为对应的用户ID
   - 使用子查询从`sys_user`表中查找用户名对应的用户ID

3. **验证结果**
   - 脚本最后包含两个查询语句，用于检查是否有未能成功更新的记录
   - 这些记录可能是因为用户名在`sys_user`表中不存在

### 执行迁移

请按照以下步骤执行迁移：

1. 在执行迁移前，**务必先备份整个数据库**
2. 使用数据库管理工具（如MySQL Workbench、Navicat等）执行`migrate_user_ids.sql`脚本
3. 检查脚本执行后的输出，确认是否有未能成功更新的记录
4. 如有未成功更新的记录，请手动处理或与管理员联系

### 代码修改

同时，我们已经修改了后端控制器代码：

1. `ExamCollectionController`和`WrongRecordsController`现在继承自`JeecgController`
2. 使用`getCurrentUser().getId()`方法直接获取当前登录用户的ID
3. 移除了通过用户名查询用户ID的逻辑，避免额外的数据库查询
4. 这确保了系统对旧数据和新数据都能正确处理

## 注意事项

1. 此迁移仅影响`exam_collection`和`exam_mistake`两个表
2. 迁移完成后，系统将使用用户ID而非用户名来关联用户和收藏/错题记录
3. 如果迁移过程中出现问题，可以使用备份表恢复数据
4. 前端代码不需要修改，因为前端只是调用API，不直接处理这些字段

## 性能和安全改进

此次修改不仅解决了数据一致性问题，还带来以下好处：

1. **性能提升**：减少了每次操作需要的数据库查询次数
2. **安全性提升**：避免了用户名重复可能带来的数据混乱
3. **代码一致性**：与系统其他模块保持一致的用户标识方式
4. **维护性提升**：简化了代码，减少了潜在的错误点 