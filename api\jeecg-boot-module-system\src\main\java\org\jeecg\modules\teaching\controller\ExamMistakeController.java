package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.teaching.entity.ExamMistake;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.service.IExamMistakeService;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.jeecg.modules.teaching.vo.ExamMistakeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 错题记录
 * @Author: jeecg-boot
 * @Date:   2023-06-18
 * @Version: V1.0
 */
@Api(tags="错题记录")
@RestController
@RequestMapping("/teaching/examSystem/examMistake")
@Slf4j
public class ExamMistakeController extends JeecgController<ExamMistake, IExamMistakeService> {

    @Autowired
    private IExamMistakeService examMistakeService;
    
    @Autowired
    private IExamQuestionService examQuestionService;

    /**
     * 查询错题记录列表
     * @param subject 科目 (Scratch, Python, C++)
     * @param level 级别 (Scratch:1-4, Python/C++:1-8)
     * @param questionType 题目类型 (1:单选题, 2:判断题, 3:编程题)
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @param request HTTP请求
     * @return 分页结果
     */
    @AutoLog(value = "错题记录-列表查询")
    @ApiOperation(value="错题记录-列表查询", notes="查询用户的错题记录")
    @GetMapping(value = "/list")
    public Result<?> queryList(
            @ApiParam(value = "科目", required = false) @RequestParam(required = false) String subject,
            @ApiParam(value = "级别", required = false) @RequestParam(required = false) String level,
            @ApiParam(value = "题目类型", required = false) @RequestParam(required = false) Integer questionType,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页数量", required = false) @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "排序字段", required = false) @RequestParam(required = false) String sortField,
            @ApiParam(value = "排序方向", required = false) @RequestParam(required = false) String sortOrder,
            HttpServletRequest request) {
        
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        log.info("错题记录查询 - 用户ID: {}, 排序字段: {}, 排序方向: {}", userId, sortField, sortOrder);

        // 先获取用户的所有错题
        List<ExamMistake> mistakes = examMistakeService.getUserMistakes(userId);
        
        // 如果错题为空，直接返回空列表
        if (mistakes == null || mistakes.isEmpty()) {
            return Result.ok(new Page<ExamMistakeVO>(pageNo, pageSize));
        }
        
        // 构建VO列表
        List<ExamMistakeVO> mistakeVOs = new ArrayList<>();
        
        for (ExamMistake mistake : mistakes) {
            // 获取题目信息
            ExamQuestion question = examQuestionService.getById(mistake.getQuestionId());
            if (question == null) {
                // 题目已被删除，跳过
                continue;
            }
            
            // 过滤条件：科目、级别、题目类型
            if ((subject != null && !subject.equals(question.getSubject())) ||
                (level != null && !level.equals(question.getLevel())) ||
                (questionType != null && !questionType.equals(question.getQuestionType()))) {
                continue;
            }
            
            // 创建VO
            ExamMistakeVO vo = new ExamMistakeVO();
            vo.setId(mistake.getId());
            vo.setUserId(mistake.getUserId());
            vo.setQuestionId(mistake.getQuestionId());
            vo.setLastAnswer(mistake.getLastAnswer());
            vo.setMistakeCount(mistake.getMistakeCount());
            vo.setLastMistakeTime(mistake.getLastMistakeTime());
            
            // 题目信息
            vo.setQuestionTitle(question.getTitle());
            vo.setQuestionType(question.getQuestionType());
            vo.setSubject(question.getSubject());
            vo.setLevel(question.getLevel());
            vo.setDifficulty(question.getDifficulty());

            // 处理content字段
            String contentStr = question.getContent();
            // 验证是否为有效JSON格式（可选步骤，仅记录日志）
            if (contentStr != null && !contentStr.isEmpty()) {
                try {
                    // 尝试解析以验证JSON有效性，但不改变原始数据
                    com.alibaba.fastjson.JSON.parseObject(contentStr);
                } catch (Exception e) {
                    log.warn("题目内容不是有效的JSON格式: questionId={}, error={}", question.getId(), e.getMessage());
                }
            }
            // 设置原始content字符串，保持与数据库一致
            vo.setContent(contentStr);
            
            mistakeVOs.add(vo);
        }

        // 排序处理
        if (sortField != null && sortOrder != null) {
            log.info("应用排序 - 字段: {}, 方向: {}", sortField, sortOrder);
            mistakeVOs.sort((a, b) -> {
                int result = 0;
                try {
                    switch (sortField) {
                        case "mistakeCount":
                            result = Integer.compare(a.getMistakeCount(), b.getMistakeCount());
                            break;
                        case "lastMistakeTime":
                            if (a.getLastMistakeTime() != null && b.getLastMistakeTime() != null) {
                                result = a.getLastMistakeTime().compareTo(b.getLastMistakeTime());
                            } else if (a.getLastMistakeTime() != null) {
                                result = 1;
                            } else if (b.getLastMistakeTime() != null) {
                                result = -1;
                            }
                            break;
                        case "questionTitle":
                            if (a.getQuestionTitle() != null && b.getQuestionTitle() != null) {
                                result = a.getQuestionTitle().compareTo(b.getQuestionTitle());
                            }
                            break;
                        case "subject":
                            if (a.getSubject() != null && b.getSubject() != null) {
                                result = a.getSubject().compareTo(b.getSubject());
                            }
                            break;
                        case "questionType":
                            result = Integer.compare(a.getQuestionType(), b.getQuestionType());
                            break;
                        default:
                            result = 0;
                    }

                    // 如果是降序，反转结果
                    if ("desc".equals(sortOrder)) {
                        result = -result;
                    }
                } catch (Exception e) {
                    log.warn("排序过程中出现异常: {}", e.getMessage());
                    result = 0;
                }
                return result;
            });
        }

        // 手动分页
        int total = mistakeVOs.size();
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        
        if (fromIndex > total) {
            // 页码超出范围，返回空列表
            return Result.ok(new Page<ExamMistakeVO>(pageNo, pageSize, total));
        }
        
        List<ExamMistakeVO> pageData = mistakeVOs.subList(fromIndex, toIndex);
        
        // 构建分页对象
        Page<ExamMistakeVO> page = new Page<>(pageNo, pageSize, total);
        page.setRecords(pageData);
        
        return Result.ok(page);
    }
    
    /**
     * 添加错题记录
     * @param examMistake 错题记录
     * @param request HTTP请求
     * @return 操作结果
     */
    @AutoLog(value = "错题记录-添加")
    @ApiOperation(value="错题记录-添加", notes="添加错题记录")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ExamMistake examMistake, HttpServletRequest request) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        // 设置用户ID
        examMistake.setUserId(userId);
        
        // 判断题答案统一转换为T/F格式
        if (examMistake.getLastAnswer() != null) {
            if ("true".equals(examMistake.getLastAnswer())) {
                examMistake.setLastAnswer("T");
            } else if ("false".equals(examMistake.getLastAnswer())) {
                examMistake.setLastAnswer("F");
            }
        }
        
        // 查询是否已存在该题目的错题记录
        QueryWrapper<ExamMistake> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("question_id", examMistake.getQuestionId());
        ExamMistake existMistake = examMistakeService.getOne(queryWrapper);
        
        if (existMistake != null) {
            // 更新已有记录
            existMistake.setLastAnswer(examMistake.getLastAnswer());
            existMistake.setMistakeCount(existMistake.getMistakeCount() + 1);
            existMistake.setLastMistakeTime(new Date());
            examMistakeService.updateById(existMistake);
            return Result.ok("更新错题记录成功");
        } else {
            // 新增记录
            examMistake.setMistakeCount(1);
            examMistake.setLastMistakeTime(new Date());
            examMistakeService.save(examMistake);
            return Result.ok("添加错题记录成功");
        }
    }
    
    /**
     * 删除错题记录
     * @param id 错题记录ID
     * @param request HTTP请求
     * @return 操作结果
     */
    @AutoLog(value = "错题记录-删除")
    @ApiOperation(value="错题记录-删除", notes="删除错题记录")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id", required=true) String id, HttpServletRequest request) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        // 验证记录所属
        ExamMistake mistake = examMistakeService.getById(id);
        if (mistake == null) {
            return Result.error("记录不存在");
        }
        
        if (!mistake.getUserId().equals(userId)) {
            return Result.error("无权删除此记录");
        }
        
        // 删除记录
        examMistakeService.removeById(id);
        return Result.ok("删除成功");
    }
    
    /**
     * 批量删除错题记录
     * @param ids 错题记录ID，多个ID用逗号分隔
     * @param request HTTP请求
     * @return 操作结果
     */
    @AutoLog(value = "错题记录-批量删除")
    @ApiOperation(value="错题记录-批量删除", notes="批量删除错题记录")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids", required=true) String ids, HttpServletRequest request) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        // 解析ID列表
        List<String> idList = Arrays.asList(ids.split(","));
        
        // 验证所有记录都属于当前用户
        QueryWrapper<ExamMistake> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        queryWrapper.eq("user_id", userId);
        List<ExamMistake> mistakes = examMistakeService.list(queryWrapper);
        
        if (mistakes.size() != idList.size()) {
            return Result.error("部分记录不存在或无权删除");
        }
        
        // 批量删除
        examMistakeService.removeByIds(idList);
        return Result.ok("批量删除成功");
    }

    /**
     * 导出错题（仅包含题目内容，不含解析、错误次数等）
     * @param subject 科目
     * @param level 级别
     * @param questionType 题目类型
     * @param ids 指定错题记录ID列表，逗号分隔
     * @param request HTTP请求
     * @param response HTTP响应
     */
    @AutoLog(value = "错题-导出")
    @ApiOperation(value="错题-导出", notes="导出错题为TXT格式，只包含题目内容")
    @GetMapping(value = "/export")
    public void exportWrongRecords(
            @ApiParam(value = "科目") @RequestParam(required = false) String subject,
            @ApiParam(value = "级别") @RequestParam(required = false) String level,
            @ApiParam(value = "题目类型") @RequestParam(required = false) Integer questionType,
            @ApiParam(value = "指定错题ID列表") @RequestParam(required = false) String ids,
            HttpServletRequest request,
            HttpServletResponse response) {
        
        try {
            // 获取当前登录用户ID
            String userId = getCurrentUser().getId();
            
            // 获取符合条件的错题记录
            List<ExamMistake> mistakes;
            
            if (ids != null && !ids.trim().isEmpty()) {
                // 按指定ID列表筛选
                List<String> idList = Arrays.asList(ids.split(","));
                
                // 验证所有记录都属于当前用户
                QueryWrapper<ExamMistake> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("id", idList);
                queryWrapper.eq("user_id", userId);
                mistakes = examMistakeService.list(queryWrapper);
                
                // 检查是否有权限访问所有记录
                if (mistakes.size() != idList.size()) {
                    writeEmptyContent(response, "部分记录不存在或无权限访问");
                    return;
                }
            } else {
                // 获取用户的所有错题
                mistakes = examMistakeService.getUserMistakes(userId);
            }
            
            // 如果错题为空，返回空文件
            if (mistakes == null || mistakes.isEmpty()) {
                writeEmptyContent(response, "没有找到符合条件的错题记录");
                return;
            }
            
            // 获取错题对应的题目ID
            List<String> questionIds = mistakes.stream()
                    .map(ExamMistake::getQuestionId)
                    .collect(Collectors.toList());
            
            // 查询题目信息
            QueryWrapper<ExamQuestion> questionQueryWrapper = new QueryWrapper<>();
            questionQueryWrapper.in("id", questionIds);
            
            // 应用过滤条件
            if (subject != null && !subject.trim().isEmpty()) {
                questionQueryWrapper.eq("subject", subject);
            }
            if (level != null && !level.trim().isEmpty()) {
                questionQueryWrapper.eq("level", level);
            }
            if (questionType != null) {
                questionQueryWrapper.eq("question_type", questionType);
            }
            
            List<ExamQuestion> questions = examQuestionService.list(questionQueryWrapper);
            
            // 如果没有匹配的题目，返回空文件
            if (questions.isEmpty()) {
                writeEmptyContent(response, "没有找到符合筛选条件的题目");
                return;
            }
            
            // 过滤掉不符合条件的错题
            List<String> filteredQuestionIds = questions.stream()
                    .map(ExamQuestion::getId)
                    .collect(Collectors.toList());
            
            mistakes = mistakes.stream()
                    .filter(mistake -> filteredQuestionIds.contains(mistake.getQuestionId()))
                    .collect(Collectors.toList());
            
            // 如果过滤后没有错题，返回空文件
            if (mistakes.isEmpty()) {
                writeEmptyContent(response, "没有找到符合筛选条件的错题记录");
                return;
            }
            
            // 设置响应头
            String fileName = "错题导出_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".txt";
            response.setContentType("text/plain;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + 
                    new String(fileName.getBytes(), "ISO8859-1"));
            
            // 构建导出内容
            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
                // 写入元数据
                writer.write("【错题导出】\n");
                writer.write("导出时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n");
                writer.write("题目数量: " + questions.size() + "\n");
                writer.write("\n");
                
                // 按题目类型分组
                Map<Integer, List<ExamQuestion>> questionsByType = questions.stream()
                        .collect(Collectors.groupingBy(ExamQuestion::getQuestionType));
                
                // 写入单选题
                if (questionsByType.containsKey(1)) {
                    writer.write("【单选题】\n\n");
                    writeQuestions(writer, questionsByType.get(1));
                }
                
                // 写入判断题
                if (questionsByType.containsKey(2)) {
                    writer.write("\n【判断题】\n\n");
                    writeQuestions(writer, questionsByType.get(2));
                }
                
                // 写入编程题
                if (questionsByType.containsKey(3)) {
                    writer.write("\n【编程题】\n\n");
                    writeQuestions(writer, questionsByType.get(3));
                }
                
                writer.flush();
            }
        } catch (Exception e) {
            log.error("导出错题失败", e);
            try {
                // 在发生异常时，返回错误信息
                writeEmptyContent(response, "导出失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误信息失败", ex);
            }
        }
    }

    /**
     * 写入题目内容（只包含题目，不包含解析和答案）
     */
    private void writeQuestions(BufferedWriter writer, List<ExamQuestion> questions) throws IOException {
        int index = 1;
        for (ExamQuestion question : questions) {
            // 写入题目标题
            writer.write(index + ". " + question.getTitle() + "\n");
            
            // 解析content字段中的内容
            JSONObject content = null;
            try {
                if (StringUtils.isNotBlank(question.getContent())) {
                    content = JSON.parseObject(question.getContent());
                }
            } catch (Exception e) {
                log.warn("解析题目内容失败: {}", e.getMessage());
            }
            
            // 写入题目类型特定内容
            switch (question.getQuestionType()) {
                case 1: // 单选题
                    if (content != null) {
                        // 只导出选项，不导出答案
                        try {
                            Object optionsObj = content.get("options");
                            if (optionsObj instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<String> options = (List<String>) optionsObj;
                                for (int i = 0; i < options.size(); i++) {
                                    writer.write(String.format("%c. %s\n", 'A' + i, options.get(i)));
                                }
                            } else if (optionsObj instanceof String) {
                                writer.write(optionsObj.toString() + "\n");
                            }
                        } catch (Exception e) {
                            log.warn("解析单选题选项失败: {}", e.getMessage());
                        }
                    }
                    break;
                    
                case 2: // 判断题
                    // 判断题不需要额外内容，因为只有两个选项：正确/错误
                    writer.write("A. 正确\n");
                    writer.write("B. 错误\n");
                    break;
                    
                case 3: // 编程题
                    if (content != null) {
                        // 处理描述
                        String description = content.getString("description");
                        if (StringUtils.isNotBlank(description)) {
                            writer.write("描述:\n" + description + "\n");
                        }
                        
                        // 处理输入格式
                        String input_format = content.getString("input_format");
                        if (StringUtils.isNotBlank(input_format)) {
                            writer.write("输入格式:\n" + input_format + "\n");
                        }
                        
                        // 处理输出格式
                        String output_format = content.getString("output_format");
                        if (StringUtils.isNotBlank(output_format)) {
                            writer.write("输出格式:\n" + output_format + "\n");
                        }
                        
                        // 处理样例案例，这是一个数组而非单个字符串
                        try {
                            Object sampleCasesObj = content.get("sample_cases");
                            if (sampleCasesObj instanceof JSONObject || sampleCasesObj instanceof List) {
                                List<Object> sampleCases = JSON.parseArray(JSON.toJSONString(sampleCasesObj));
                                if (sampleCases != null && !sampleCases.isEmpty()) {
                                    writer.write("样例:\n");
                                    for (int i = 0; i < sampleCases.size(); i++) {
                                        JSONObject sampleCase = (JSONObject) sampleCases.get(i);
                                        String input = sampleCase.getString("input");
                                        String output = sampleCase.getString("output");
                                        
                                        writer.write("样例 " + (i + 1) + ":\n");
                                        if (StringUtils.isNotBlank(input)) {
                                            writer.write("输入:\n" + input + "\n");
                                        }
                                        if (StringUtils.isNotBlank(output)) {
                                            writer.write("输出:\n" + output + "\n");
                                        }
                                        writer.write("\n");
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析样例案例失败: {}", e.getMessage());
                        }
                        
                        // 处理提示信息
                        String hint = content.getString("hint");
                        if (StringUtils.isNotBlank(hint)) {
                            writer.write("提示:\n" + hint + "\n");
                        }
                    }
                    break;
            }
            
            // 题目间分隔
            writer.write("\n");
            index++;
        }
    }
    
    /**
     * 写入空内容或错误信息
     */
    private void writeEmptyContent(HttpServletResponse response, String message) throws IOException {
        response.setContentType("text/plain;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=empty_wrong_records.txt");
        
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
            writer.write("【错题导出】\n\n");
            writer.write(message + "\n");
            writer.flush();
        }
    }
} 