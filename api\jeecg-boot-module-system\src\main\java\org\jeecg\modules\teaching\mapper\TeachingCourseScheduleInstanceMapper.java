package org.jeecg.modules.teaching.mapper;


import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 单次课程实例
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
public interface TeachingCourseScheduleInstanceMapper extends BaseMapper<TeachingCourseScheduleInstance> {
    
    /**
     * 根据父课程ID和实例日期查询实例
     * @param parentId 父课程ID
     * @param instanceDate 实例日期
     * @return 单次课程实例
     */
    TeachingCourseScheduleInstance selectByParentIdAndDate(@Param("parentId") String parentId, @Param("instanceDate") String instanceDate);
} 