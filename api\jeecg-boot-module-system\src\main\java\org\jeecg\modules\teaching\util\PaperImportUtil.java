package org.jeecg.modules.teaching.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.entity.ExamQuestion;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 试卷导入工具类
 * 用于解析文本文件中的试卷
 */
@Slf4j
public class PaperImportUtil {
    
    /**
     * 导入结果，包含试卷信息和题目列表
     */
    @Data
    public static class ImportResult {
        private ExamPaper paperInfo;
        private List<ExamQuestion> questions;
        
        public ImportResult(ExamPaper paperInfo, List<ExamQuestion> questions) {
            this.paperInfo = paperInfo;
            this.questions = questions;
        }
    }
    
    /**
     * 解析试卷文本内容
     * @param content 文本内容
     * @return 导入结果
     */
    public static ImportResult parsePaperText(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        
        try (BufferedReader reader = new BufferedReader(new StringReader(content))) {
            // 解析试卷元数据
            ExamPaper paperInfo = new ExamPaper();
            String line;
            StringBuilder metadataText = new StringBuilder();
            
            // 读取元数据部分（直到遇到题型区块的起始标记）
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }
                
                if (line.startsWith("【一、") || line.startsWith("【二、") || line.startsWith("【三、")) {
                    break; // 遇到题型区块的起始标记，结束元数据的读取
                }
                
                metadataText.append(line).append("\n");
            }
            
            // 解析试卷元数据
            parseMetadata(metadataText.toString(), paperInfo);
            // 重新读取文件，解析题目
            reader.close();

            // 将试卷的元数据传递给题目解析方法
            List<ExamQuestion> questions = QuestionImportUtil.parseQuestionText(
                content,
                paperInfo.getSubject(),
                paperInfo.getLevel(),
                paperInfo.getDifficulty()
            );

            return new ImportResult(paperInfo, questions);
            
        } catch (IOException e) {
            log.error("解析试卷文本内容失败", e);
            return null;
        }
    }
    
    /**
     * 解析试卷元数据
     * @param metadataText 元数据文本
     * @param paperInfo 试卷信息对象
     */
    private static void parseMetadata(String metadataText, ExamPaper paperInfo) {
        // 提取试卷标题
        Pattern titlePattern = Pattern.compile("【试卷标题】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher titleMatcher = titlePattern.matcher(metadataText);
        if (titleMatcher.find()) {
            paperInfo.setTitle(titleMatcher.group(1).trim());
        }
        
        // 提取所属科目
        Pattern subjectPattern = Pattern.compile("【所属科目】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher subjectMatcher = subjectPattern.matcher(metadataText);
        if (subjectMatcher.find()) {
            paperInfo.setSubject(subjectMatcher.group(1).trim());
        }
        
        // 提取所属级别
        Pattern levelPattern = Pattern.compile("【所属级别】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher levelMatcher = levelPattern.matcher(metadataText);
        if (levelMatcher.find()) {
            String levelText = levelMatcher.group(1).trim();
            // 标准化级别格式
            paperInfo.setLevel(standardizeLevel(levelText));
        }
        
        // 提取难度
        Pattern difficultyPattern = Pattern.compile("【难度】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher difficultyMatcher = difficultyPattern.matcher(metadataText);
        if (difficultyMatcher.find()) {
            String difficultyText = difficultyMatcher.group(1).trim();
            int difficulty = 1; // 默认简单
            if ("中等".equals(difficultyText)) {
                difficulty = 2;
            } else if ("困难".equals(difficultyText)) {
                difficulty = 3;
            }
            paperInfo.setDifficulty(difficulty);
        } else {
            paperInfo.setDifficulty(1); // 默认简单
        }
        
        // 提取类型
        Pattern typePattern = Pattern.compile("【类型】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher typeMatcher = typePattern.matcher(metadataText);
        if (typeMatcher.find()) {
            paperInfo.setType(typeMatcher.group(1).trim());
        }
        
        // 提取年份
        Pattern yearPattern = Pattern.compile("【年份】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher yearMatcher = yearPattern.matcher(metadataText);
        if (yearMatcher.find()) {
            String yearText = yearMatcher.group(1).trim();
            try {
                paperInfo.setYear(Integer.parseInt(yearText));
            } catch (NumberFormatException e) {
                log.warn("解析年份失败: " + yearText);
                paperInfo.setYear(null);
            }
        }
        
        // 不在这里设置考试时长，由ExamPaperServiceImpl统一处理
        // 确保级别信息正确解析即可
    }
    

    
    /**
     * 将各种格式的级别标准化为"X级"格式
     * @param level 原始级别文本，如"6"、"六"、"6级"、"六级"等
     * @return 标准化的级别文本，如"六级"
     */
    private static String standardizeLevel(String level) {
        if (level == null || level.isEmpty()) {
            return level;
        }
        
        // 如果已经是"X级"格式，检查是否需要转换数字部分
        if (level.endsWith("级")) {
            String prefix = level.substring(0, level.length() - 1);
            // 如果前缀是数字，转换为中文
            try {
                int num = Integer.parseInt(prefix);
                return numberToChinese(num) + "级";
            } catch (NumberFormatException e) {
                // 前缀不是数字，可能已经是中文
                return level;
            }
        }
        
        // 如果是纯数字，转换为"X级"格式
        try {
            int num = Integer.parseInt(level);
            return numberToChinese(num) + "级";
        } catch (NumberFormatException e) {
            // 不是纯数字，检查是否是单个中文数字
            switch (level) {
                case "一": return "一级";
                case "二": return "二级";
                case "三": return "三级";
                case "四": return "四级";
                case "五": return "五级";
                case "六": return "六级";
                case "七": return "七级";
                case "八": return "八级";
                case "九": return "九级";
                default: return level; // 无法识别，返回原始值
            }
        }
    }
    
    /**
     * 将数字转换为中文数字
     * @param num 数字
     * @return 中文数字
     */
    private static String numberToChinese(int num) {
        if (num <= 0 || num > 9) {
            return String.valueOf(num); // 超出范围，返回原始数字
        }
        
        switch (num) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "七";
            case 8: return "八";
            case 9: return "九";
            default: return String.valueOf(num);
        }
    }
} 