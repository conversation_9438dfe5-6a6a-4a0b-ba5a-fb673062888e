# 教学系统课程排期功能更新说明

## 更新内容

本次更新在课程排期功能中添加了删除单次重复课程的功能，主要包括以下内容：

1. 数据库结构变更：
   - 在 `teaching_course_schedule` 表中添加 `deleted_instances` 列，用于存储被删除的单次课程日期标识

2. API接口新增：
   - `/teaching/coursesScheduling/getDeletedInstances` (GET): 获取所有已删除的单次课程记录
   - `/teaching/coursesScheduling/deleteSingleInstance` (POST): 删除单次课程记录

## 部署步骤

1. 执行数据库变更脚本：
   ```sql
   -- 路径：api/jeecg-boot-module-system/src/main/resources/db/alter_teaching_course_schedule.sql
   ```

2. 重新编译并部署后端服务

## 前端调用说明

1. 获取已删除的单次课程记录：
   ```javascript
   getAction('/teaching/coursesScheduling/getDeletedInstances', {}).then(res => {
     if (res.success) {
       // res.result 格式为: {parentId: [date1, date2, ...]}
       const deletedInstances = res.result;
     }
   });
   ```

2. 删除单次课程记录：
   ```javascript
   postAction('/teaching/coursesScheduling/deleteSingleInstance', {
     parentId: "父课程ID",
     instanceDate: "2023-06-01" // 日期格式为yyyy-MM-dd
   }).then(res => {
     if (res.success) {
       // 删除成功
     }
   });
   ``` 