package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingStudentCoinRecord;

/**
 * 学生金币记录服务接口
 */
public interface ITeachingStudentCoinRecordService extends IService<TeachingStudentCoinRecord> {

    /**
     * 获取用户金币记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 金币记录
     */
    Result<?> getUserCoinRecords(String userId, Integer pageNo, Integer pageSize);

    /**
     * 获取用户兑换记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 兑换记录
     */
    Result<?> getUserExchangeRecords(String userId, Integer pageNo, Integer pageSize);
} 