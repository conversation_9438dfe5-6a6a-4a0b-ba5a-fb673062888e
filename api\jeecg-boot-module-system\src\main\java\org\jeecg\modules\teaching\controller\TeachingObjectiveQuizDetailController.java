package org.jeecg.modules.teaching.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuizDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客观题答题详情 Controller
 */
@Slf4j
@Api(tags = "客观题答题详情")
@RestController
@RequestMapping("/teaching/objectiveQuizDetail")
public class TeachingObjectiveQuizDetailController {
    
    @Autowired
    private ITeachingObjectiveQuizDetailService detailService;
    
    /**
     * 根据记录ID查询答题详情
     */
    @AutoLog(value = "客观题答题详情-根据记录ID查询")
    @ApiOperation(value = "客观题答题详情-根据记录ID查询", notes = "根据记录ID查询答题详情")
    @GetMapping(value = "/listByRecordId")
    public Result<?> listByRecordId(@RequestParam(name = "recordId", required = true) String recordId) {
        try {
            // 使用按题目创建时间排序的查询方法
            List<TeachingObjectiveQuizDetail> detailList = detailService.listByRecordIdOrderByQuestionCreateTime(recordId);
            
            return Result.ok(detailList);
        } catch (Exception e) {
            log.error("查询答题详情失败", e);
            return Result.error("查询失败，" + e.getMessage());
        }
    }
} 