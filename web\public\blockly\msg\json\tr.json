{"@metadata": {"authors": ["Alpkant", "<PERSON><PERSON><PERSON>", "BaRaN6161 TURK", "<PERSON><PERSON><PERSON>", "By erdo can", "Grkn gll", "<PERSON><PERSON><PERSON><PERSON>", "HakanIST", "<PERSON><PERSON><PERSON>", "Imabadplayer", "<PERSON>", "Kumkumuk", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>cAang", "<PERSON><PERSON>", "MuratTheTurkish", "<PERSON><PERSON>", "Uncitoyen", "Uğurkent", "Watermelon juice", "WikiBronze", "아라"]}, "VARIABLES_DEFAULT_NAME": "öge", "UNNAMED_KEY": "<PERSON><PERSON><PERSON>", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON>", "ADD_COMMENT": "<PERSON><PERSON>", "REMOVE_COMMENT": "<PERSON><PERSON><PERSON>", "DUPLICATE_COMMENT": "<PERSON><PERSON><PERSON>", "EXTERNAL_INPUTS": "<PERSON><PERSON>", "INLINE_INPUTS": "<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON>", "DELETE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_X_BLOCKS": "%1 Bloğunu Sil", "DELETE_ALL_BLOCKS": "Tüm %1 blokları silinsin mi?", "CLEAN_UP": "Blokları Temizle", "COLLAPSE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "COLLAPSE_ALL": "Blokları Daralt", "EXPAND_BLOCK": "Bloğu Genişlet", "EXPAND_ALL": "Blokları Genişlet", "DISABLE_BLOCK": "Bloğu Devre Dışı Bırak", "ENABLE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "HELP": "Yardım", "UNDO": "<PERSON><PERSON> al", "REDO": "<PERSON><PERSON>", "CHANGE_VALUE_TITLE": "<PERSON><PERSON><PERSON>:", "RENAME_VARIABLE": "Değişkeni yeniden adlandır...", "RENAME_VARIABLE_TITLE": "Tüm '%1' değişkenini yeniden adlandır:", "NEW_VARIABLE": "Değişken oluştur...", "NEW_STRING_VARIABLE": "<PERSON><PERSON>keni oluştur...", "NEW_NUMBER_VARIABLE": "Sayı değişkeni oluştur...", "NEW_COLOUR_VARIABLE": "Renk değişkeni oluştur...", "NEW_VARIABLE_TYPE_TITLE": "<PERSON><PERSON> tipi:", "NEW_VARIABLE_TITLE": "<PERSON><PERSON> ismi:", "VARIABLE_ALREADY_EXISTS": "'%1' is<PERSON><PERSON> adı zaten var.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "'%1' isimli değ<PERSON>ken '%2' tipli başka bir değişkende tanımlı.", "DELETE_VARIABLE_CONFIRMATION": "'%2' değişkeninin %1 kullanımını silmek istiyor musunuz?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "'%1' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, '%2' fonksiyonunun tanımının bir parçası olduğu için si<PERSON>z", "DELETE_VARIABLE": "'%1' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> silmek istiyor musunuz?", "COLOUR_PICKER_HELPURL": "https://tr.wikipedia.org/wiki/Renk", "COLOUR_PICKER_TOOLTIP": "<PERSON>letten bir renk seç.", "COLOUR_RANDOM_TITLE": "rast<PERSON>e renk", "COLOUR_RANDOM_TOOLTIP": "Rast<PERSON>e bir renk seç.", "COLOUR_RGB_HELPURL": "https://www.december.com/html/spec/colorpercompact.html", "COLOUR_RGB_TITLE": "<PERSON><PERSON>", "COLOUR_RGB_RED": "kırmızı", "COLOUR_RGB_GREEN": "<PERSON><PERSON><PERSON>", "COLOUR_RGB_BLUE": "mavi", "COLOUR_RGB_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yeşil ve mavinin belirli miktarıyla bir renk oluştur.  Tüm değerler 0 ile 100 arasında olmalıdır.", "COLOUR_BLEND_HELPURL": "https://meyerweb.com/eric/tools/color-blend/#:::rgbp", "COLOUR_BLEND_TITLE": "karıştır", "COLOUR_BLEND_COLOUR1": "1. renk", "COLOUR_BLEND_COLOUR2": "2. renk", "COLOUR_BLEND_RATIO": "oran", "COLOUR_BLEND_TOOLTIP": "Verilen bir orana (0.0 - 1.0) bağlı olarak iki rengi karıştırır.", "CONTROLS_REPEAT_HELPURL": "https://tr.wikipedia.org/wiki/<PERSON>_d<PERSON><PERSON><PERSON><PERSON>", "CONTROLS_REPEAT_TITLE": "%1 kez tekrarla", "CONTROLS_REPEAT_INPUT_DO": "yap", "CONTROLS_REPEAT_TOOLTIP": "Bazı işlemleri birkaç kez yap.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "tekrar ederken", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "kadar tekrarla", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Bir değer doğru olduğunda bazı beyanlarda bulun.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Bir değer yanlış olduğunda bazı beyanlarda bulun.", "CONTROLS_FOR_TOOLTIP": "Başlangıç sayısından bitiş sayısına kadar belirtilen aralık ve belirtilen engeller ile devam eden değerler alan '%1' değişkeni oluştur.", "CONTROLS_FOR_TITLE": "<PERSON><PERSON><PERSON> %1 %2 den %3 ye, her adımda %4 değ<PERSON>ş<PERSON>", "CONTROLS_FOREACH_TITLE": "her öğe için %1 listede %2", "CONTROLS_FOREACH_TOOLTIP": "<PERSON><PERSON> <PERSON><PERSON><PERSON> her öğe için  '%1' değiş<PERSON>ini maddeye atayın  ve bundan sonra bazı açıklamalar yapın.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "döngüden çık", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "döng<PERSON><PERSON><PERSON>n sonraki adımından devam et", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "İçeren döngüden çık.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "<PERSON>u dö<PERSON>ü<PERSON>ün geri kalanını atlayın ve sonraki adım ile devam edin.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Uyarı: Bu blok yalnızca bir döngü içinde kullanılabilir.", "CONTROLS_IF_TOOLTIP_1": "<PERSON><PERSON><PERSON> true, yani g<PERSON> ise ardından gelen işlemi yerine getir.", "CONTROLS_IF_TOOLTIP_2": "<PERSON><PERSON><PERSON> true, yani ger<PERSON> ise ilk bloktaki işlemleri yerine getir. <PERSON><PERSON><PERSON> halde ikinci bloktaki işlemleri yerine getir.", "CONTROLS_IF_TOOLTIP_3": "Eğer ilk değişken true, yani koşul gerçekleşmiş ise ilk blok içerisindeki işlemi gerçekleştir. Eğer ikinci değişken true ise, ikinci bloktaki işlemi gerçekleştir.", "CONTROLS_IF_TOOLTIP_4": "<PERSON>ğer ilk değer true, yani o<PERSON> ise, ilk bloktaki işlemi gerçekleştir. İlk değer true değil ama ikinci değer true ise, ikinci bloktaki işlemi gerçekleştir. <PERSON><PERSON><PERSON> değerlerin hiçbiri true değil ise son bloktaki işlemi gerçekleştir.", "CONTROLS_IF_MSG_IF": "<PERSON><PERSON><PERSON>", "CONTROLS_IF_MSG_ELSEIF": "<PERSON><PERSON><PERSON><PERSON>", "CONTROLS_IF_MSG_ELSE": "<PERSON><PERSON><PERSON><PERSON>", "CONTROLS_IF_IF_TOOLTIP": "If b<PERSON><PERSON><PERSON> ekle, kaldır veya yeniden düzenleme yap.", "CONTROLS_IF_ELSEIF_TOOLTIP": "If bloğuna bir koşul ekleyin.", "CONTROLS_IF_ELSE_TOOLTIP": "If bloğuna kalan durumları \"yakalayan\" bir son ekle.", "IOS_OK": "<PERSON><PERSON>", "IOS_CANCEL": "İptal", "IOS_ERROR": "<PERSON><PERSON>", "IOS_PROCEDURES_INPUTS": "GİRİŞLER", "IOS_PROCEDURES_ADD_INPUT": "+ <PERSON><PERSON><PERSON>", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Açıklamalara izin ver", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Bu fonksiyonda mükerrer girdi tanımlı.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON>ğ<PERSON><PERSON><PERSON>", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON>ı<PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "Sil", "IOS_VARIABLES_VARIABLE_NAME": "Değişken adı", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Değişken adı kısmı boş bırakılamaz.", "LOGIC_COMPARE_HELPURL": "https://tr.wikipedia.org/wiki/Eşitsizlikler", "LOGIC_COMPARE_TOOLTIP_EQ": "Her iki giriş de birbirine eşitse true değerini dö<PERSON>.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Her iki giriş de birbirine eşit değilse true değ<PERSON>ni dö<PERSON>.", "LOGIC_COMPARE_TOOLTIP_LT": "İlk giriş ikinci girişten küçükse true değerini döndürün.", "LOGIC_COMPARE_TOOLTIP_LTE": "İlk giriş ikinci girişten küçük veya ona eşitse true değerini döndürün.", "LOGIC_COMPARE_TOOLTIP_GT": "İlk giriş ikinci girişten büyükse true değerini döndürün.", "LOGIC_COMPARE_TOOLTIP_GTE": "İlk giriş ikinci girişten büyük veya ona eşitse true değerini döndürün.", "LOGIC_OPERATION_TOOLTIP_AND": "Her iki giri<PERSON> de doğru<PERSON>a true döndür.", "LOGIC_OPERATION_AND": "ve", "LOGIC_OPERATION_TOOLTIP_OR": "Girişlerden en az biri doğru olduğunda true değerini döndürün.", "LOGIC_OPERATION_OR": "veya", "LOGIC_NEGATE_TITLE": "%1 değil", "LOGIC_NEGATE_TOOLTIP": "<PERSON><PERSON><PERSON> yanlışsa true değerini döndürür. <PERSON><PERSON><PERSON> doğ<PERSON>ysa false değerini döndürür.", "LOGIC_BOOLEAN_TRUE": "true", "LOGIC_BOOLEAN_FALSE": "false", "LOGIC_BOOLEAN_TOOLTIP": "True veya false de<PERSON><PERSON><PERSON>.", "LOGIC_NULL_HELPURL": "https://tr.wikipedia.org/wiki/Sıfırlanabilir_tip", "LOGIC_NULL": "boş", "LOGIC_NULL_TOOLTIP": "Boş değerini döndürür.", "LOGIC_TERNARY_HELPURL": "https://tr.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "deneme", "LOGIC_TERNARY_IF_TRUE": "if true", "LOGIC_TERNARY_IF_FALSE": "if false", "LOGIC_TERNARY_TOOLTIP": "'test' du<PERSON><PERSON><PERSON> kontrol edin. Koşul true olursa, 'if true' de<PERSON><PERSON><PERSON> döndürür; aksi tak<PERSON> 'if false' de<PERSON><PERSON><PERSON> döndürür.", "MATH_NUMBER_HELPURL": "https://tr.wikipedia.org/wiki/Sayı", "MATH_NUMBER_TOOLTIP": "<PERSON><PERSON>.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "tire", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "x", "MATH_POWER_SYMBOL": "üst alma", "MATH_TRIG_SIN": "<PERSON><PERSON><PERSON>", "MATH_TRIG_COS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MATH_TRIG_TAN": "tan<PERSON><PERSON>", "MATH_TRIG_ASIN": "<PERSON><PERSON><PERSON><PERSON>", "MATH_TRIG_ACOS": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MATH_TRIG_ATAN": "<PERSON><PERSON><PERSON><PERSON>", "MATH_ARITHMETIC_HELPURL": "https://tr.wikipedia.org/wiki/Aritmetik", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON>ki sayının top<PERSON>ını döndürün.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "<PERSON><PERSON> sayının farkını döndürün.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "İki sayının çarpımını döndürün.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "<PERSON><PERSON> sayının bö<PERSON>ü<PERSON><PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON>.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Yükseltilen ilk sayıyı ikinci sayının gücüne döndürün.", "MATH_SINGLE_HELPURL": "https://tr.wikipedia.org/wiki/Karek<PERSON>k", "MATH_SINGLE_OP_ROOT": "kare k<PERSON>k", "MATH_SINGLE_TOOLTIP_ROOT": "<PERSON><PERSON> sayının karekökünü döndürür.", "MATH_SINGLE_OP_ABSOLUTE": "kesin", "MATH_SINGLE_TOOLTIP_ABS": "<PERSON><PERSON> sayının mutlak değerini döndürür.", "MATH_SINGLE_TOOLTIP_NEG": "<PERSON><PERSON> sayının red<PERSON> döndür.", "MATH_SINGLE_TOOLTIP_LN": "Bir sayının doğal logaritmasını döndür.", "MATH_SINGLE_TOOLTIP_LOG10": "Bir sayının 10 logaritmasını geri döndür.", "MATH_SINGLE_TOOLTIP_EXP": "<PERSON><PERSON>yi bir sayı<PERSON><PERSON>n g<PERSON><PERSON> d<PERSON>.", "MATH_SINGLE_TOOLTIP_POW10": "10'u sayının gü<PERSON><PERSON> d<PERSON>.", "MATH_TRIG_HELPURL": "https://tr.wikipedia.org/wiki/Trigonometrik_fonksiyonlar", "MATH_TRIG_TOOLTIP_SIN": "<PERSON><PERSON><PERSON><PERSON> bir der<PERSON><PERSON> (r<PERSON><PERSON>).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON> derec<PERSON>n k<PERSON>ü<PERSON> (r<PERSON><PERSON>).", "MATH_TRIG_TOOLTIP_TAN": "<PERSON>ir derecenin tan<PERSON>ı<PERSON> (r<PERSON>an <PERSON>).", "MATH_TRIG_TOOLTIP_ASIN": "Bir sayının ark sinüsünü döndürün.", "MATH_TRIG_TOOLTIP_ACOS": "Bir sayının arkosinini döndürün.", "MATH_TRIG_TOOLTIP_ATAN": "Bir sayının arktanjantını döndürün.", "MATH_CONSTANT_HELPURL": "https://tr.wikipedia.org/wiki/Matematiks<PERSON>_sabit", "MATH_CONSTANT_TOOLTIP": "Or<PERSON>k sabitlerden birini döndür: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (<PERSON><PERSON><PERSON>).", "MATH_IS_EVEN": "çift", "MATH_IS_ODD": "tek", "MATH_IS_PRIME": "asal", "MATH_IS_WHOLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MATH_IS_POSITIVE": "pozitif", "MATH_IS_NEGATIVE": "negatif", "MATH_IS_DIVISIBLE_BY": "bölünebilir", "MATH_IS_TOOLTIP": "<PERSON>ir sayının çift, tek, asal, b<PERSON><PERSON><PERSON><PERSON>, pozitif, negatif veya belirli bir sayıya bölünebilir olup olmadığını kontrol edin. True veya false değerini döndürür.", "MATH_CHANGE_HELPURL": "https://tr.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_deyimi", "MATH_CHANGE_TITLE": "%1 %2 g<PERSON>re deği<PERSON>tir", "MATH_CHANGE_TOOLTIP": "'%1' de<PERSON><PERSON><PERSON><PERSON><PERSON> bir sayı ekle.", "MATH_ROUND_HELPURL": "https://tr.wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>", "MATH_ROUND_TOOLTIP": "Bir sayıyı yukarı veya aşağı yuvarlayın.", "MATH_ROUND_OPERATOR_ROUND": "yu<PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "yukarı yuvarla", "MATH_ROUND_OPERATOR_ROUNDDOWN": "aşağ<PERSON> yuvarla", "MATH_ONLIST_HELPURL": "", "MATH_ONLIST_OPERATOR_SUM": "<PERSON><PERSON>", "MATH_ONLIST_TOOLTIP_SUM": "<PERSON>eki tüm sayıların toplamını döndürün.", "MATH_ONLIST_OPERATOR_MIN": "listenin en küçüğü", "MATH_ONLIST_TOOLTIP_MIN": "Listedeki en küçük sayıyı döndür.", "MATH_ONLIST_OPERATOR_MAX": "maksimum liste", "MATH_ONLIST_TOOLTIP_MAX": "Listedeki en büyük sayıyı döndürün.", "MATH_ONLIST_OPERATOR_AVERAGE": "liste ortalaması", "MATH_ONLIST_TOOLTIP_AVERAGE": "Listedeki sayı<PERSON> değerlerin ortalamasını (aritmetik ortalama) döndürün.", "MATH_ONLIST_OPERATOR_MEDIAN": "listenin medyanı", "MATH_ONLIST_TOOLTIP_MEDIAN": "Listeden ortanca numarayı döndürün.", "MATH_ONLIST_OPERATOR_MODE": "liste modları", "MATH_ONLIST_TOOLTIP_MODE": "Listedeki en yaygın öğenin bir listesini döndürür.", "MATH_ONLIST_OPERATOR_STD_DEV": "listenin standart sapması", "MATH_ONLIST_TOOLTIP_STD_DEV": "Listenin standart sapmasın<PERSON> dö<PERSON><PERSON>.", "MATH_ONLIST_OPERATOR_RANDOM": "<PERSON>in r<PERSON><PERSON>", "MATH_ONLIST_TOOLTIP_RANDOM": "<PERSON>en rastgele bir öğ<PERSON>.", "MATH_MODULO_HELPURL": "https://tr.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i", "MATH_MODULO_TITLE": "%1 ÷ %2 geri kalan kısım", "MATH_MODULO_TOOLTIP": "Kalanı iki sayıyı bölmekten döndürün.", "MATH_CONSTRAIN_TITLE": "%1 en düşük %2 en yüksek %3 ile sınırla", "MATH_CONSTRAIN_TOOLTIP": "Bir sayıyı belirtilen sınırlar arasında (dahil) ile sınırlandırın.", "MATH_RANDOM_INT_HELPURL": "https://tr.wikipedia.org/wiki/Rast<PERSON><PERSON>_<PERSON><PERSON>_üretimi", "MATH_RANDOM_INT_TITLE": "%1 ile %2 rastgele tam sayı üretin", "MATH_RANDOM_INT_TOOLTIP": "Belirtilen iki sınır arasında rastgele bir tamsayı dö<PERSON>ü<PERSON>ün.", "MATH_RANDOM_FLOAT_HELPURL": "https://tr.wikipedia.org/wiki/Rast<PERSON><PERSON>_<PERSON><PERSON>_üretimi", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "r<PERSON><PERSON>e kesir", "MATH_RANDOM_FLOAT_TOOLTIP": "0.0 (da<PERSON>) ve 1.0 (<PERSON><PERSON><PERSON>) arasında rastgele bir kesir <PERSON>.", "MATH_ATAN2_HELPURL": "https://tr.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2, X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "-180'den 180'e derece cinsinden nokta (X, Y) arktanjantını döndürün.", "TEXT_TEXT_HELPURL": "https://tr.wikipedia.org/wiki/Dize_(bilgisayar_bilimi)", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON> harf, kelime veya metin satırı.", "TEXT_JOIN_TITLE_CREATEWITH": "ile metin o<PERSON>", "TEXT_JOIN_TOOLTIP": "İstediğiniz sayıda öğeyi birleştirerek bir metin parçası oluşturun.", "TEXT_CREATE_JOIN_TITLE_JOIN": "katıl", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON>u metin bloğunu yeniden yapılandırmak için b<PERSON><PERSON><PERSON><PERSON>, kaldırın veya yeniden sıralayın.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "<PERSON><PERSON> bir ö<PERSON>.", "TEXT_APPEND_TITLE": "%1 için %2 met<PERSON>i e<PERSON>.", "TEXT_APPEND_TOOLTIP": "'%1' de<PERSON><PERSON><PERSON><PERSON><PERSON> bir metin e<PERSON>in.", "TEXT_LENGTH_TITLE": "%1 uzunluğu", "TEXT_LENGTH_TOOLTIP": "<PERSON>ğlana<PERSON> met<PERSON><PERSON> (boşluklar dahil) sayısını döndürür.", "TEXT_ISEMPTY_TITLE": "%1 boş", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> metin bo<PERSON><PERSON> true değ<PERSON>ni döndürür.", "TEXT_INDEXOF_TOOLTIP": "İkinci metindeki ilk metnin ilk/son o<PERSON><PERSON><PERSON><PERSON><PERSON> dizinini döndürür. Metin bulunmazsa %1 değerini döndürür.", "TEXT_INDEXOF_TITLE": "%1 metni içinde %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "metnin ilk oluşumunu bul", "TEXT_INDEXOF_OPERATOR_LAST": "metnin son tekrarlaması<PERSON><PERSON> bul", "TEXT_CHARAT_TITLE": "%1 içinde %2", "TEXT_CHARAT_FROM_START": "# harfini al", "TEXT_CHARAT_FROM_END": "# sona harfleri al", "TEXT_CHARAT_FIRST": "ilk harfini al", "TEXT_CHARAT_LAST": "son harfi al", "TEXT_CHARAT_RANDOM": "rast<PERSON>e harf al", "TEXT_CHARAT_TAIL": "", "TEXT_CHARAT_TOOLTIP": "Belirtilen konumdaki harfi dö<PERSON>ürür.", "TEXT_GET_SUBSTRING_TOOLTIP": "<PERSON><PERSON> belirli bir b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON><PERSON>.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "metinde", "TEXT_GET_SUBSTRING_START_FROM_START": "# harfinden alt dize al", "TEXT_GET_SUBSTRING_START_FROM_END": "# harfinden alt dize al", "TEXT_GET_SUBSTRING_START_FIRST": "ilk harfinden alt dize al", "TEXT_GET_SUBSTRING_END_FROM_START": "# harfe", "TEXT_GET_SUBSTRING_END_FROM_END": "en başından # harfi", "TEXT_GET_SUBSTRING_END_LAST": "son harfe", "TEXT_GET_SUBSTRING_TAIL": "", "TEXT_CHANGECASE_TOOLTIP": "Metnin bir kopyasını farklı bir durumda döndürün.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "ÜST DURUMA", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "küçük harfe", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "Başlık Vakasına", "TEXT_TRIM_TOOLTIP": "Bir veya her iki uçtan boş<PERSON>lar kaldırılmış olarak metnin bir kopyasını döndürün.", "TEXT_TRIM_OPERATOR_BOTH": "her iki tara<PERSON>ından da kırpın", "TEXT_TRIM_OPERATOR_LEFT": "sol tarafındaki boşlukları kırpın", "TEXT_TRIM_OPERATOR_RIGHT": "sağ tarafındaki boşlukları kırp", "TEXT_PRINT_TITLE": "%1 yaz", "TEXT_PRINT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> met<PERSON>, say<PERSON><PERSON><PERSON> veya başka bir değeri yazdırın.", "TEXT_PROMPT_TYPE_TEXT": "mesaj i<PERSON><PERSON><PERSON> metin istemi", "TEXT_PROMPT_TYPE_NUMBER": "mesaj i<PERSON><PERSON><PERSON> numara istemi", "TEXT_PROMPT_TOOLTIP_NUMBER": "Bir numara için kull<PERSON>ı<PERSON>ı sor.", "TEXT_PROMPT_TOOLTIP_TEXT": "Bazı metinler için kullanı<PERSON>ı sor.", "TEXT_COUNT_MESSAGE0": "%1 içinde %2 say.", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Bazı metnin başka bir metnin içinde kaç kez oluştuğunu sayın.", "TEXT_REPLACE_MESSAGE0": "%1 yerine %3 içindeki %2 ile değiştir", "TEXT_REPLACE_TOOLTIP": "Bazı metnin tüm tekrarlarını başka bir metnin içinde değiştirin.", "TEXT_REVERSE_MESSAGE0": "%1 ters çevirin", "TEXT_REVERSE_TOOLTIP": "Metindeki karakterlerin sırasını tersine çevirir.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "boş liste oluştur", "LISTS_CREATE_EMPTY_TOOLTIP": "Veri kaydı içermeyen 0 uzunluğunda bir liste döndürür", "LISTS_CREATE_WITH_TOOLTIP": "İstediğiniz sayıda öğe içeren bir liste oluşturun.", "LISTS_CREATE_WITH_INPUT_WITH": "ile liste oluştur", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "liste", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "Bu liste bloğunu yeniden yapılandırmak için b<PERSON><PERSON><PERSON><PERSON>, kaldırın veya yeniden sıralayın.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Listeye bir öğe e<PERSON>.", "LISTS_REPEAT_TOOLTIP": "Belirtilen sayıda tekrarlanan belirli bir değerden oluşan bir liste oluşturur.", "LISTS_REPEAT_TITLE": "%1 tekrarlanan %2 öğeyle liste oluştur", "LISTS_LENGTH_TITLE": "%1 uzunluğu", "LISTS_LENGTH_TOOLTIP": "<PERSON><PERSON> <PERSON><PERSON> dö<PERSON>ü<PERSON>.", "LISTS_ISEMPTY_TITLE": "%1 boş", "LISTS_ISEMPTY_TOOLTIP": "Liste boşsa true değerini döndürür.", "LISTS_INLIST": "listede", "LISTS_INDEX_OF_FIRST": "öğenin ilk oluşumunu bul", "LISTS_INDEX_OF_LAST": "<PERSON><PERSON><PERSON><PERSON> son tekrarlamasını bul", "LISTS_INDEX_OF_TOOLTIP": "Listedeki öğenin ilk/son o<PERSON><PERSON><PERSON><PERSON><PERSON> dizinini döndürür. Öğe bulunmazsa %1 değerini döndürür.", "LISTS_GET_INDEX_GET": "al", "LISTS_GET_INDEX_GET_REMOVE": "al ve kaldır", "LISTS_GET_INDEX_REMOVE": "kaldır", "LISTS_GET_INDEX_FROM_START": "# Kare", "LISTS_GET_INDEX_FROM_END": "# sonundan", "LISTS_GET_INDEX_FIRST": "ilk", "LISTS_GET_INDEX_LAST": "son", "LISTS_GET_INDEX_RANDOM": "r<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_TAIL": "", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 ilk öğedir.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 son ö<PERSON><PERSON><PERSON>.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Listede belirtilen konumda bulunan öğeyi döndürür.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Listedeki ilk öğeyi döndürür.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "<PERSON><PERSON><PERSON> son <PERSON><PERSON><PERSON><PERSON>.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "<PERSON>eki r<PERSON>gele bir öğeyi döndürür.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Öğeyi bir listede belirtilen konumda kaldırır ve döndürür.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Listedeki ilk öğeyi kaldırır ve döndürür.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "<PERSON><PERSON><PERSON> son ö<PERSON><PERSON><PERSON> kaldırır ve döndürür.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "<PERSON>eki rastgele bir öğeyi kaldırır ve döndürür.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Listede belirtilen konumda bulunan öğeyi kaldırır.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Listedeki ilk öğeyi kaldırır.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "<PERSON><PERSON><PERSON> son <PERSON><PERSON><PERSON><PERSON> kaldı<PERSON>.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "<PERSON>eki r<PERSON>gele bir öğeyi kaldırır.", "LISTS_SET_INDEX_SET": "<PERSON><PERSON><PERSON>", "LISTS_SET_INDEX_INSERT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_SET_INDEX_INPUT_TO": "olar<PERSON>", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Öğeyi bir listede belirtilen konuma ayarlar.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Listedeki ilk öğeyi ayarlar.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "<PERSON><PERSON><PERSON> son <PERSON><PERSON><PERSON><PERSON>.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "<PERSON>e rastgele bir ö<PERSON>.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Öğeyi bir listede belirtilen konuma ekler.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "<PERSON><PERSON><PERSON><PERSON>in ba<PERSON><PERSON><PERSON> e<PERSON>.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "<PERSON><PERSON><PERSON><PERSON> listenin sonuna e<PERSON>.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Öğeyi bir listeye rastgele ekler.", "LISTS_GET_SUBLIST_START_FROM_START": "# listesinden alt liste al", "LISTS_GET_SUBLIST_START_FROM_END": "# listesinden alt listeyi al", "LISTS_GET_SUBLIST_START_FIRST": "ilk önce alt listeyi al", "LISTS_GET_SUBLIST_END_FROM_START": "#", "LISTS_GET_SUBLIST_END_FROM_END": "sonuna kadar #", "LISTS_GET_SUBLIST_END_LAST": "sona", "LISTS_GET_SUBLIST_TAIL": "", "LISTS_GET_SUBLIST_TOOLTIP": "<PERSON>in belirtilen bölümünün bir kopyasını oluşturur.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sıra %1 %2 %3", "LISTS_SORT_TOOLTIP": "Listenin bir kopyasını sıralayın.", "LISTS_SORT_ORDER_ASCENDING": "artan", "LISTS_SORT_ORDER_DESCENDING": "<PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_NUMERIC": "<PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_TEXT": "alfabetik", "LISTS_SORT_TYPE_IGNORECASE": "alfabetik, görmezden gelin", "LISTS_SPLIT_LIST_FROM_TEXT": "metinden liste yap", "LISTS_SPLIT_TEXT_FROM_LIST": "<PERSON>en metin yap", "LISTS_SPLIT_WITH_DELIMITER": "sınırlayıcı ile", "LISTS_SPLIT_TOOLTIP_SPLIT": "<PERSON><PERSON>, her bir sınırlayıcıyı kırarak bir metin listesine b<PERSON>.", "LISTS_SPLIT_TOOLTIP_JOIN": "<PERSON>in listesini bir sınırlayıcı ile ayrılmış tek bir metinde birleştirin.", "LISTS_REVERSE_MESSAGE0": "%1 ters çevirin", "LISTS_REVERSE_TOOLTIP": "Listenin bir kopyasını ters çevirin.", "ORDINAL_NUMBER_SUFFIX": "", "VARIABLES_GET_TOOLTIP": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> değ<PERSON> döndürür.", "VARIABLES_GET_CREATE_SET": "'set %1' oluştur", "VARIABLES_SET": "%1 %2 ayarla", "VARIABLES_SET_TOOLTIP": "Bu değişkeni girişe eşit olacak şekilde <PERSON>.", "VARIABLES_SET_CREATE_GET": "'get %1' oluştur", "PROCEDURES_DEFNORETURN_HELPURL": "https://tr.wikipedia.org/wiki/Altyordam", "PROCEDURES_DEFNORETURN_TITLE": "-", "PROCEDURES_DEFNORETURN_PROCEDURE": "bir <PERSON><PERSON> yap", "PROCEDURES_BEFORE_PARAMS": "ile:", "PROCEDURES_CALL_BEFORE_PARAMS": "ile:", "PROCEDURES_DEFNORETURN_DO": "", "PROCEDURES_DEFNORETURN_TOOLTIP": "Çıkışı olmayan bir işlev oluşturur.", "PROCEDURES_DEFNORETURN_COMMENT": "Bu işlevi açıklayın...", "PROCEDURES_DEFRETURN_HELPURL": "https://tr.wikipedia.org/wiki/Altyordam", "PROCEDURES_DEFRETURN_RETURN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_DEFRETURN_TOOLTIP": "Çıkışa sahip bir işlev oluşturur.", "PROCEDURES_ALLOW_STATEMENTS": "ifadelere izin ver", "PROCEDURES_DEF_DUPLICATE_WARNING": "Uyarı: <PERSON><PERSON> <PERSON><PERSON><PERSON> yinelenen parametrelere sahiptir.", "PROCEDURES_CALLNORETURN_HELPURL": "https://tr.wikipedia.org/wiki/Alt_program", "PROCEDURES_CALLNORETURN_TOOLTIP": "Kullanıcı tanımlı '%1' işlevini çalıştırın.", "PROCEDURES_CALLRETURN_HELPURL": "https://tr.wikipedia.org/wiki/Alt_program", "PROCEDURES_CALLRETURN_TOOLTIP": "Kullanıcı tanımlı '%1' işlevini çalıştırın ve çıkışını kullanın.", "PROCEDURES_MUTATORCONTAINER_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON> <PERSON>ş<PERSON><PERSON> g<PERSON>, kaldırın veya yeniden sıralayın.", "PROCEDURES_MUTATORARG_TITLE": "giri<PERSON> adı:", "PROCEDURES_MUTATORARG_TOOLTIP": "İşleve bir giri<PERSON>.", "PROCEDURES_HIGHLIGHT_DEF": "Vurgulama işlevi tanımı", "PROCEDURES_CREATE_DO": "'%1' oluştur", "PROCEDURES_IFRETURN_TOOLTIP": "<PERSON><PERSON> değer true ise, ikinci bir de<PERSON> dö<PERSON>.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Uyarı: Bu blok yalnızca bir işlev tanımı içinde kullanılabilir.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "<PERSON><PERSON> söyle...", "WORKSPACE_ARIA_LABEL": "Blockly <PERSON> Alanı", "COLLAPSED_WARNINGS_WARNING": "Daraltılmış bloklar uyarı içerir.", "DIALOG_OK": "<PERSON><PERSON>", "DIALOG_CANCEL": "İptal"}