import{r as s,m as xo,n as G,_ as E,o as zt,p as Wt,D as Ut,M as Eo,s as Io,I as Yt,v as K,H as Zt,w as qt,y as Jt,T as Fo,x as Po,$,z as Mo,A as To,B as Go,R as Vo,X as Kt,C as No,E as Do,G as ko,J as Q,O as yt,K as fe,N as $t,P as Oo,Q as xt,S as _o,a as p,d as ct,U as Et,j as R,F as Fe,V as M,W as Xt,Y as Qt,Z as en,a0 as tn,a1 as jo,a2 as nn,a3 as Lo,a4 as Ho,a5 as we,e as dt,a6 as on,a7 as rn,a8 as Bo,a9 as an,aa as gt,ab as ln,ac as zo,ad as Wo,ae as Uo,h as ft,k as sn,af as Yo,ag as un,ah as Zo,ai as cn,aj as de,ak as X,al as pt,am as oe,an as qe,ao as N,ap as se,aq as Pe,u as It,ar as qo,as as Jo,at as Ko,au as ht,av as Xo,aw as Qo,ax as dn,ay as er,az as tr,aA as nr,f as or,L as rr,t as ar,aB as ir,l as lr}from"./index-f45fa1bb.js";const sr=s.forwardRef((e,t)=>{var n;const{container:o=globalThis==null||(n=globalThis.document)===null||n===void 0?void 0:n.body,...r}=e;return o?xo.createPortal(s.createElement(G.div,E({},r,{ref:t})),o):null});function ur({container:e,accept:t,walk:n,enabled:o=!0}){let r=s.useRef(t),a=s.useRef(n);s.useEffect(()=>{r.current=t,a.current=n},[t,n]),zt(()=>{if(!e||!o)return;let i=Wt(e);if(!i)return;let l=r.current,u=a.current,d=Object.assign(g=>l(g),{acceptNode:l}),c=i.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,d,!1);for(;c.nextNode();)u(c.currentNode)},[e,o,r,a])}var cr=(e=>(e[e.RegisterOption=0]="RegisterOption",e[e.UnregisterOption=1]="UnregisterOption",e))(cr||{});let dr={[0](e,t){let n=[...e.options,{id:t.id,element:t.element,propsRef:t.propsRef}];return{...e,options:_o(n,o=>o.element.current)}},[1](e,t){let n=e.options.slice(),o=e.options.findIndex(r=>r.id===t.id);return o===-1?e:(n.splice(o,1),{...e,options:n})}},mt=s.createContext(null);mt.displayName="RadioGroupDataContext";function gn(e){let t=s.useContext(mt);if(t===null){let n=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,gn),n}return t}let vt=s.createContext(null);vt.displayName="RadioGroupActionsContext";function fn(e){let t=s.useContext(vt);if(t===null){let n=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,fn),n}return t}function gr(e,t){return ko(t.type,dr,e,t)}let fr="div";function pr(e,t){let n=Yt(),{id:o=`headlessui-radiogroup-${n}`,value:r,defaultValue:a,form:i,name:l,onChange:u,by:d=(I,_)=>I===_,disabled:c=!1,...g}=e,h=K(typeof d=="string"?(I,_)=>{let D=d;return(I==null?void 0:I[D])===(_==null?void 0:_[D])}:d),[m,v]=s.useReducer(gr,{options:[]}),f=m.options,[A,C]=Zt(),[w,S]=qt(),b=s.useRef(null),x=Jt(b,t),[P,F]=Fo(r,u,a),T=s.useMemo(()=>f.find(I=>!I.propsRef.current.disabled),[f]),k=s.useMemo(()=>f.some(I=>h(I.propsRef.current.value,P)),[f,P]),O=K(I=>{var _;if(c||h(I,P))return!1;let D=(_=f.find(J=>h(J.propsRef.current.value,I)))==null?void 0:_.propsRef.current;return D!=null&&D.disabled?!1:(F==null||F(I),!0)});ur({container:b.current,accept(I){return I.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:I.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(I){I.setAttribute("role","none")}});let re=K(I=>{let _=b.current;if(!_)return;let D=Wt(_),J=f.filter(V=>V.propsRef.current.disabled===!1).map(V=>V.element.current);switch(I.key){case Q.Enter:Oo(I.currentTarget);break;case Q.ArrowLeft:case Q.ArrowUp:if(I.preventDefault(),I.stopPropagation(),yt(J,fe.Previous|fe.WrapAround)===$t.Success){let V=f.find(ie=>ie.element.current===(D==null?void 0:D.activeElement));V&&O(V.propsRef.current.value)}break;case Q.ArrowRight:case Q.ArrowDown:if(I.preventDefault(),I.stopPropagation(),yt(J,fe.Next|fe.WrapAround)===$t.Success){let V=f.find(ie=>ie.element.current===(D==null?void 0:D.activeElement));V&&O(V.propsRef.current.value)}break;case Q.Space:{I.preventDefault(),I.stopPropagation();let V=f.find(ie=>ie.element.current===(D==null?void 0:D.activeElement));V&&O(V.propsRef.current.value)}break}}),B=K(I=>(v({type:0,...I}),()=>v({type:1,id:I.id}))),ae=s.useMemo(()=>({value:P,firstOption:T,containsCheckedOption:k,disabled:c,compare:h,...m}),[P,T,k,c,h,m]),Ve=s.useMemo(()=>({registerOption:B,change:O}),[B,O]),W={ref:x,id:o,role:"radiogroup","aria-labelledby":A,"aria-describedby":w,onKeyDown:re},yo=s.useMemo(()=>({value:P}),[P]),ge=s.useRef(null),$o=Po();return s.useEffect(()=>{ge.current&&a!==void 0&&$o.addEventListener(ge.current,"reset",()=>{O(a)})},[ge,O]),$.createElement(S,{name:"RadioGroup.Description"},$.createElement(C,{name:"RadioGroup.Label"},$.createElement(vt.Provider,{value:Ve},$.createElement(mt.Provider,{value:ae},l!=null&&P!=null&&Mo({[l]:P}).map(([I,_],D)=>$.createElement(To,{features:Go.Hidden,ref:D===0?J=>{var V;ge.current=(V=J==null?void 0:J.closest("form"))!=null?V:null}:void 0,...Vo({key:I,as:"input",type:"radio",checked:_!=null,hidden:!0,readOnly:!0,form:i,name:I,value:_})})),Kt({ourProps:W,theirProps:g,slot:yo,defaultTag:fr,name:"RadioGroup"})))))}var hr=(e=>(e[e.Empty=1]="Empty",e[e.Active=2]="Active",e))(hr||{});let mr="div";function vr(e,t){var n;let o=Yt(),{id:r=`headlessui-radiogroup-option-${o}`,value:a,disabled:i=!1,...l}=e,u=s.useRef(null),d=Jt(u,t),[c,g]=Zt(),[h,m]=qt(),{addFlag:v,removeFlag:f,hasFlag:A}=No(1),C=Do({value:a,disabled:i}),w=gn("RadioGroup.Option"),S=fn("RadioGroup.Option");zt(()=>S.registerOption({id:r,element:u,propsRef:C}),[r,S,u,e]);let b=K(B=>{var ae;if(xt(B.currentTarget))return B.preventDefault();S.change(a)&&(v(2),(ae=u.current)==null||ae.focus())}),x=K(B=>{if(xt(B.currentTarget))return B.preventDefault();v(2)}),P=K(()=>f(2)),F=((n=w.firstOption)==null?void 0:n.id)===r,T=w.disabled||i,k=w.compare(w.value,a),O={ref:d,id:r,role:"radio","aria-checked":k?"true":"false","aria-labelledby":c,"aria-describedby":h,"aria-disabled":T?!0:void 0,tabIndex:(()=>T?-1:k||!w.containsCheckedOption&&F?0:-1)(),onClick:T?void 0:b,onFocus:T?void 0:x,onBlur:T?void 0:P},re=s.useMemo(()=>({checked:k,disabled:T,active:A(2)}),[k,T,A]);return $.createElement(m,{name:"RadioGroup.Description"},$.createElement(g,{name:"RadioGroup.Label"},Kt({ourProps:O,theirProps:l,slot:re,defaultTag:mr,name:"RadioGroup.Option"})))}let Ar=Ut(pr),Cr=Ut(vr),Ee=Object.assign(Ar,{Option:Cr,Label:Eo,Description:Io});var br=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ee=new WeakMap,pe=new WeakMap,he={},Ne=0,pn=function(e){return e&&(e.host||pn(e.parentNode))},wr=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=pn(n);return o&&e.contains(o)?o:null}).filter(function(n){return!!n})},Sr=function(e,t,n,o){var r=wr(t,Array.isArray(e)?e:[e]);he[n]||(he[n]=new WeakMap);var a=he[n],i=[],l=new Set,u=new Set(r),d=function(g){!g||l.has(g)||(l.add(g),d(g.parentNode))};r.forEach(d);var c=function(g){!g||u.has(g)||Array.prototype.forEach.call(g.children,function(h){if(l.has(h))c(h);else{var m=h.getAttribute(o),v=m!==null&&m!=="false",f=(ee.get(h)||0)+1,A=(a.get(h)||0)+1;ee.set(h,f),a.set(h,A),i.push(h),f===1&&v&&pe.set(h,!0),A===1&&h.setAttribute(n,"true"),v||h.setAttribute(o,"true")}})};return c(t),l.clear(),Ne++,function(){i.forEach(function(g){var h=ee.get(g)-1,m=a.get(g)-1;ee.set(g,h),a.set(g,m),h||(pe.has(g)||g.removeAttribute(o),pe.delete(g)),m||g.removeAttribute(n)}),Ne--,Ne||(ee=new WeakMap,ee=new WeakMap,pe=new WeakMap,he={})}},Rr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=t||br(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live]"))),Sr(o,r,n,"aria-hidden")):function(){return null}};globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function yr({tagList:e,currentTag:t,onChangeCurrentTag:n}){const o=s.useCallback(r=>{n(r)},[n]);return p(Ee,{value:t,onChange:o,children:p("div",{className:"flex items-center space-x-4",children:e.map(r=>p(Ee.Option,{value:r,className:({checked:a})=>`cursor-pointer whitespace-nowrap rounded-[3rem] px-4 py-2 ${a?"bg-indigo-400 text-white":"bg-white text-gray-600 dark:bg-gray-800 dark:text-gray-200"} ${!a&&"hover:bg-indigo-100 dark:hover:bg-gray-600"}`,children:p("p",{className:"font-normal ",children:r})},r))})})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function $r(e,t,n){const[o,r]=s.useState(null);return s.useEffect(()=>{n&&!o&&(async()=>{const i=await xr(t,e);r(i)})()},[t,e,n]),o}async function xr(e,t){const n=await ct.chapterRecords.where({dict:e,chapter:t}).toArray(),o=n.length,r=n.reduce((u,{wordNumber:d,correctWordIndexes:c})=>u+(d-c.length),0),a=o>0?Et(r/o,2):0,i=n.reduce((u,{wrongCount:d})=>u+(d??0),0),l=o>0?Et(i/o,2):0;return{exerciseCount:o,avgWrongWordCount:a,avgWrongInputCount:l}}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function hn(e,{threshold:t=0,root:n=null,rootMargin:o="0%",freezeOnceVisible:r=!1}){const[a,i]=s.useState(),l=(a==null?void 0:a.isIntersecting)&&r,u=([d])=>{i(d)};return s.useEffect(()=>{const d=e==null?void 0:e.current;if(!!!window.IntersectionObserver||l||!d)return;const g={threshold:t,root:n,rootMargin:o},h=new IntersectionObserver(u,g);return h.observe(d),()=>h.disconnect()},[e==null?void 0:e.current,JSON.stringify(t),n,o,l]),a}const Er=e=>p("svg",{viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...e,children:p("path",{fill:"currentColor",fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75s-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12m13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094z",clipRule:"evenodd"})}),Ir=Er;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Fr({index:e,checked:t,dictID:n,onChange:o}){const r=s.useRef(null),a=hn(r,{}),i=!!(a!=null&&a.isIntersecting),l=$r(e,n,i);return s.useEffect(()=>{var u,d;if(t&&r.current!==null){const c=r.current,g=(d=(u=c.parentElement)==null?void 0:u.parentElement)==null?void 0:d.parentElement;g==null||g.scroll({top:c.offsetTop-g.offsetTop-300,behavior:"smooth"})}},[t]),R("div",{ref:r,className:"relative flex h-16 w-40 cursor-pointer  flex-col items-start justify-center overflow-hidden rounded-xl bg-slate-100 px-3 py-2 dark:bg-slate-800",onClick:()=>o(e),children:[R("h1",{children:["第 ",e+1," 章"]}),p("p",{className:"pt-[2px] text-xs text-slate-600",children:l?l.exerciseCount>0?`练习 ${l.exerciseCount} 次`:"未练习":"加载中..."}),t&&p(Ir,{className:"absolute -bottom-4 -right-4 h-18 w-18 text-6xl text-green-500 opacity-40 dark:text-green-300"})]})}function Pr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Mr(...e){return t=>e.forEach(n=>Pr(n,t))}var mn=s.forwardRef((e,t)=>{const{children:n,...o}=e,r=s.Children.toArray(n),a=r.find(Gr);if(a){const i=a.props.children,l=r.map(u=>u===a?s.Children.count(i)>1?s.Children.only(null):s.isValidElement(i)?i.props.children:null:u);return p(Je,{...o,ref:t,children:s.isValidElement(i)?s.cloneElement(i,void 0,l):null})}return p(Je,{...o,ref:t,children:n})});mn.displayName="Slot";var Je=s.forwardRef((e,t)=>{const{children:n,...o}=e;if(s.isValidElement(n)){const r=Nr(n);return s.cloneElement(n,{...Vr(o,n.props),ref:t?Mr(t,r):r})}return s.Children.count(n)>1?s.Children.only(null):null});Je.displayName="SlotClone";var Tr=({children:e})=>p(Fe,{children:e});function Gr(e){return s.isValidElement(e)&&e.type===Tr}function Vr(e,t){const n={...t};for(const o in t){const r=e[o],a=t[o];/^on[A-Z]/.test(o)?r&&a?n[o]=(...l)=>{a(...l),r(...l)}:r&&(n[o]=r):o==="style"?n[o]={...r,...a}:o==="className"&&(n[o]=[r,a].filter(Boolean).join(" "))}return{...e,...n}}function Nr(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const Dr=Xt("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",{variants:{variant:{default:"bg-indigo-500 shadow text-white hover:opacity-90  dark:text-opacity-80 focus:outline-none rounded-lg",destructive:"bg-red-500 text-slate-50 hover:bg-red-500/90 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/90",outline:"border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:bg-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-50",secondary:"bg-slate-100 text-slate-900 hover:bg-slate-100/80 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-800/80",ghost:"hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50",link:"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ue=s.forwardRef(({className:e,variant:t,size:n,asChild:o=!1,...r},a)=>p(o?mn:"button",{className:M(Dr({variant:t,size:n,className:e})),ref:a,...r}));ue.displayName="Button";const Ft=e=>p("svg",{viewBox:"0 0 256 256",width:"1.2em",height:"1.2em",...e,children:p("path",{fill:"currentColor",d:"M119.39 172.94a8 8 0 0 1-1.73 8.72l-32 32a8 8 0 0 1-11.32 0l-32-32A8 8 0 0 1 48 168h24V48a8 8 0 0 1 16 0v120h24a8 8 0 0 1 7.39 4.94m94.27-98.6l-32-32a8 8 0 0 0-11.32 0l-32 32A8 8 0 0 0 144 88h24v120a8 8 0 0 0 16 0V88h24a8 8 0 0 0 5.66-13.66"})});globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const kr=e=>[{accessorKey:"word",size:100,header:({column:t})=>R(ue,{variant:"ghost",className:"p-0",onClick:()=>t.toggleSorting(t.getIsSorted()==="asc"),children:["单词",p(Ft,{className:"ml-1.5 h-4 w-4"})]})},{accessorKey:"trans",size:500,header:"释义"},{accessorKey:"errorCount",size:40,header:({column:t})=>R(ue,{variant:"ghost",className:"p-0",onClick:()=>t.toggleSorting(t.getIsSorted()==="asc"),children:["错误次数",p(Ft,{className:"ml-1.5 h-4 w-4"})]}),cell:({row:t})=>R("span",{className:"flex justify-center",children:[t.original.errorCount," "]})},{accessorKey:"errorChar",header:"易错字母",size:100,cell:({row:t})=>p("p",{children:t.getValue("errorChar").map((n,o)=>p("kbd",{className:"flex justify-center",children:n+" "},`${n}-${o}`))})},{accessorKey:"delete",header:"",size:40,cell:({row:t})=>p(Qt,{children:R(en,{children:[p(tn,{children:p(jo,{className:"cursor-pointer",onClick:()=>e(t.original.word)})}),p(nn,{children:p("p",{children:"Delete Records"})})]})})}];function Or(e){return e.map(t=>({word:t.word,trans:t.originData.trans.join("，")??"",errorCount:t.errorCount,errorChar:t.errorChar}))}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const vn=s.forwardRef(({className:e,...t},n)=>p("div",{className:"relative h-full w-full overflow-auto",children:p("table",{ref:n,className:M("w-full caption-bottom text-sm",e),...t})}));vn.displayName="Table";const An=s.forwardRef(({className:e,...t},n)=>p("thead",{ref:n,className:M("[&_tr]:border-b",e),...t}));An.displayName="TableHeader";const Cn=s.forwardRef(({className:e,...t},n)=>p("tbody",{ref:n,className:M("[&_tr:last-child]:border-0",e),...t}));Cn.displayName="TableBody";const _r=s.forwardRef(({className:e,...t},n)=>p("tfoot",{ref:n,className:M("border-t bg-slate-100/50 font-medium dark:bg-slate-800/50 [&>tr]:last:border-b-0",e),...t}));_r.displayName="TableFooter";const Se=s.forwardRef(({className:e,...t},n)=>p("tr",{ref:n,className:M("border-b transition-colors hover:bg-slate-100/50 data-[state=selected]:bg-slate-100 dark:hover:bg-slate-800/50 dark:data-[state=selected]:bg-slate-800",e),...t}));Se.displayName="TableRow";const bn=s.forwardRef(({className:e,...t},n)=>p("th",{ref:n,className:M("h-12 px-4 text-left align-middle font-medium text-slate-500 dark:text-slate-400 [&:has([role=checkbox])]:pr-0",e),...t}));bn.displayName="TableHead";const Ke=s.forwardRef(({className:e,...t},n)=>p("td",{ref:n,className:M("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));Ke.displayName="TableCell";const jr=s.forwardRef(({className:e,...t},n)=>p("caption",{ref:n,className:M("mt-4 text-sm text-slate-500 dark:text-slate-400",e),...t}));jr.displayName="TableCaption";/**
 * table-core
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Z(e,t){return typeof e=="function"?e(t):e}function j(e,t){return n=>{t.setState(o=>({...o,[e]:Z(n,o[e])}))}}function Ie(e){return e instanceof Function}function Lr(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function Hr(e,t){const n=[],o=r=>{r.forEach(a=>{n.push(a);const i=t(a);i!=null&&i.length&&o(i)})};return o(e),n}function y(e,t,n){let o=[],r;return()=>{let a;n.key&&n.debug&&(a=Date.now());const i=e();if(!(i.length!==o.length||i.some((d,c)=>o[c]!==d)))return r;o=i;let u;if(n.key&&n.debug&&(u=Date.now()),r=t(...i),n==null||n.onChange==null||n.onChange(r),n.key&&n.debug&&n!=null&&n.debug()){const d=Math.round((Date.now()-a)*100)/100,g=Math.round((Date.now()-u)*100)/100/16,h=(m,v)=>{for(m=String(m);m.length<v;)m=" "+m;return m}}return r}}function Br(e,t,n,o){var r,a;const l={...e._getDefaultColumnDef(),...t},u=l.accessorKey;let d=(r=(a=l.id)!=null?a:u?u.replace(".","_"):void 0)!=null?r:typeof l.header=="string"?l.header:void 0,c;if(l.accessorFn?c=l.accessorFn:u&&(u.includes(".")?c=h=>{let m=h;for(const f of u.split(".")){var v;m=(v=m)==null?void 0:v[f]}return m}:c=h=>h[l.accessorKey]),!d)throw new Error;let g={id:`${String(d)}`,accessorFn:c,parent:o,depth:n,columnDef:l,columns:[],getFlatColumns:y(()=>[!0],()=>{var h;return[g,...(h=g.columns)==null?void 0:h.flatMap(m=>m.getFlatColumns())]},{key:"column.getFlatColumns",debug:()=>{var h;return(h=e.options.debugAll)!=null?h:e.options.debugColumns}}),getLeafColumns:y(()=>[e._getOrderColumnsFn()],h=>{var m;if((m=g.columns)!=null&&m.length){let v=g.columns.flatMap(f=>f.getLeafColumns());return h(v)}return[g]},{key:"column.getLeafColumns",debug:()=>{var h;return(h=e.options.debugAll)!=null?h:e.options.debugColumns}})};for(const h of e._features)h.createColumn==null||h.createColumn(g,e);return g}function Pt(e,t,n){var o;let a={id:(o=n.id)!=null?o:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const i=[],l=u=>{u.subHeaders&&u.subHeaders.length&&u.subHeaders.map(l),i.push(u)};return l(a),i},getContext:()=>({table:e,header:a,column:t})};return e._features.forEach(i=>{i.createHeader==null||i.createHeader(a,e)}),a}const zr={createTable:e=>{e.getHeaderGroups=y(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o,r)=>{var a,i;const l=(a=o==null?void 0:o.map(g=>n.find(h=>h.id===g)).filter(Boolean))!=null?a:[],u=(i=r==null?void 0:r.map(g=>n.find(h=>h.id===g)).filter(Boolean))!=null?i:[],d=n.filter(g=>!(o!=null&&o.includes(g.id))&&!(r!=null&&r.includes(g.id)));return me(t,[...l,...d,...u],e)},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterHeaderGroups=y(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o,r)=>(n=n.filter(a=>!(o!=null&&o.includes(a.id))&&!(r!=null&&r.includes(a.id))),me(t,n,e,"center")),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftHeaderGroups=y(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,o)=>{var r;const a=(r=o==null?void 0:o.map(i=>n.find(l=>l.id===i)).filter(Boolean))!=null?r:[];return me(t,a,e,"left")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightHeaderGroups=y(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,o)=>{var r;const a=(r=o==null?void 0:o.map(i=>n.find(l=>l.id===i)).filter(Boolean))!=null?r:[];return me(t,a,e,"right")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getFooterGroups=y(()=>[e.getHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftFooterGroups=y(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterFooterGroups=y(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightFooterGroups=y(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getFlatHeaders=y(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftFlatHeaders=y(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterFlatHeaders=y(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightFlatHeaders=y(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterLeafHeaders=y(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftLeafHeaders=y(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightLeafHeaders=y(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var o;return!((o=n.subHeaders)!=null&&o.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeafHeaders=y(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,o)=>{var r,a,i,l,u,d;return[...(r=(a=t[0])==null?void 0:a.headers)!=null?r:[],...(i=(l=n[0])==null?void 0:l.headers)!=null?i:[],...(u=(d=o[0])==null?void 0:d.headers)!=null?u:[]].map(c=>c.getLeafHeaders()).flat()},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}})}};function me(e,t,n,o){var r,a;let i=0;const l=function(h,m){m===void 0&&(m=1),i=Math.max(i,m),h.filter(v=>v.getIsVisible()).forEach(v=>{var f;(f=v.columns)!=null&&f.length&&l(v.columns,m+1)},0)};l(e);let u=[];const d=(h,m)=>{const v={depth:m,id:[o,`${m}`].filter(Boolean).join("_"),headers:[]},f=[];h.forEach(A=>{const C=[...f].reverse()[0],w=A.column.depth===v.depth;let S,b=!1;if(w&&A.column.parent?S=A.column.parent:(S=A.column,b=!0),C&&(C==null?void 0:C.column)===S)C.subHeaders.push(A);else{const x=Pt(n,S,{id:[o,m,S.id,A==null?void 0:A.id].filter(Boolean).join("_"),isPlaceholder:b,placeholderId:b?`${f.filter(P=>P.column===S).length}`:void 0,depth:m,index:f.length});x.subHeaders.push(A),f.push(x)}v.headers.push(A),A.headerGroup=v}),u.push(v),m>0&&d(f,m-1)},c=t.map((h,m)=>Pt(n,h,{depth:i,index:m}));d(c,i-1),u.reverse();const g=h=>h.filter(v=>v.column.getIsVisible()).map(v=>{let f=0,A=0,C=[0];v.subHeaders&&v.subHeaders.length?(C=[],g(v.subHeaders).forEach(S=>{let{colSpan:b,rowSpan:x}=S;f+=b,C.push(x)})):f=1;const w=Math.min(...C);return A=A+w,v.colSpan=f,v.rowSpan=A,{colSpan:f,rowSpan:A}});return g((r=(a=u[0])==null?void 0:a.headers)!=null?r:[]),u}const ve={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},De=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Wr={getDefaultColumnDef:()=>ve,getInitialState:e=>({columnSizing:{},columnSizingInfo:De(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",onColumnSizingChange:j("columnSizing",e),onColumnSizingInfoChange:j("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,o,r;const a=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:ve.minSize,(o=a??e.columnDef.size)!=null?o:ve.size),(r=e.columnDef.maxSize)!=null?r:ve.maxSize)},e.getStart=n=>{const o=n?n==="left"?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns(),r=o.findIndex(a=>a.id===e.id);if(r>0){const a=o[r-1];return a.getStart(n)+a.getSize()}return 0},e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:o,...r}=n;return r})},e.getCanResize=()=>{var n,o;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((o=t.options.enableColumnResizing)!=null?o:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const o=r=>{if(r.subHeaders.length)r.subHeaders.forEach(o);else{var a;n+=(a=r.column.getSize())!=null?a:0}};return o(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=()=>{const n=t.getColumn(e.column.id),o=n==null?void 0:n.getCanResize();return r=>{if(!n||!o||(r.persist==null||r.persist(),ke(r)&&r.touches&&r.touches.length>1))return;const a=e.getSize(),i=e?e.getLeafHeaders().map(f=>[f.column.id,f.column.getSize()]):[[n.id,n.getSize()]],l=ke(r)?Math.round(r.touches[0].clientX):r.clientX,u={},d=(f,A)=>{typeof A=="number"&&(t.setColumnSizingInfo(C=>{var w,S;const b=A-((w=C==null?void 0:C.startOffset)!=null?w:0),x=Math.max(b/((S=C==null?void 0:C.startSize)!=null?S:0),-.999999);return C.columnSizingStart.forEach(P=>{let[F,T]=P;u[F]=Math.round(Math.max(T+T*x,0)*100)/100}),{...C,deltaOffset:b,deltaPercentage:x}}),(t.options.columnResizeMode==="onChange"||f==="end")&&t.setColumnSizing(C=>({...C,...u})))},c=f=>d("move",f),g=f=>{d("end",f),t.setColumnSizingInfo(A=>({...A,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},h={moveHandler:f=>c(f.clientX),upHandler:f=>{document.removeEventListener("mousemove",h.moveHandler),document.removeEventListener("mouseup",h.upHandler),g(f.clientX)}},m={moveHandler:f=>(f.cancelable&&(f.preventDefault(),f.stopPropagation()),c(f.touches[0].clientX),!1),upHandler:f=>{var A;document.removeEventListener("touchmove",m.moveHandler),document.removeEventListener("touchend",m.upHandler),f.cancelable&&(f.preventDefault(),f.stopPropagation()),g((A=f.touches[0])==null?void 0:A.clientX)}},v=Ur()?{passive:!1}:!1;ke(r)?(document.addEventListener("touchmove",m.moveHandler,v),document.addEventListener("touchend",m.upHandler,v)):(document.addEventListener("mousemove",h.moveHandler,v),document.addEventListener("mouseup",h.upHandler,v)),t.setColumnSizingInfo(f=>({...f,startOffset:l,startSize:a,deltaOffset:0,deltaPercentage:0,columnSizingStart:i,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?De():(n=e.initialState.columnSizingInfo)!=null?n:De())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((o,r)=>o+r.getSize(),0))!=null?t:0}}};let Ae=null;function Ur(){if(typeof Ae=="boolean")return Ae;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return Ae=e,Ae}function ke(e){return e.type==="touchstart"}const Yr={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:j("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var o,r;if(!t){e._queue(()=>{t=!0});return}if((o=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?o:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=o=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(o),e.toggleAllRowsExpanded=o=>{o??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=o=>{var r,a;e.setExpanded(o?{}:(r=(a=e.initialState)==null?void 0:a.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(o=>o.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>o=>{o.persist==null||o.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const o=e.getState().expanded;return o===!0||Object.values(o).some(Boolean)},e.getIsAllRowsExpanded=()=>{const o=e.getState().expanded;return typeof o=="boolean"?o===!0:!(!Object.keys(o).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let o=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(a=>{const i=a.split(".");o=Math.max(o,i.length)}),o},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(o=>{var r;const a=o===!0?!0:!!(o!=null&&o[e.id]);let i={};if(o===!0?Object.keys(t.getRowModel().rowsById).forEach(l=>{i[l]=!0}):i=o,n=(r=n)!=null?r:!a,!a&&n)return{...i,[e.id]:!0};if(a&&!n){const{[e.id]:l,...u}=i;return u}return o})},e.getIsExpanded=()=>{var n;const o=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:o===!0||o!=null&&o[e.id])},e.getCanExpand=()=>{var n,o,r;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((o=t.options.enableExpanding)!=null?o:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let n=!0,o=e;for(;n&&o.parentId;)o=t.getRow(o.parentId,!0),n=o.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},wn=(e,t,n)=>{var o;const r=n.toLowerCase();return!!(!((o=e.getValue(t))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(r))};wn.autoRemove=e=>L(e);const Sn=(e,t,n)=>{var o;return!!(!((o=e.getValue(t))==null||(o=o.toString())==null)&&o.includes(n))};Sn.autoRemove=e=>L(e);const Rn=(e,t,n)=>{var o;return((o=e.getValue(t))==null||(o=o.toString())==null?void 0:o.toLowerCase())===(n==null?void 0:n.toLowerCase())};Rn.autoRemove=e=>L(e);const yn=(e,t,n)=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(n)};yn.autoRemove=e=>L(e)||!(e!=null&&e.length);const $n=(e,t,n)=>!n.some(o=>{var r;return!((r=e.getValue(t))!=null&&r.includes(o))});$n.autoRemove=e=>L(e)||!(e!=null&&e.length);const xn=(e,t,n)=>n.some(o=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(o)});xn.autoRemove=e=>L(e)||!(e!=null&&e.length);const En=(e,t,n)=>e.getValue(t)===n;En.autoRemove=e=>L(e);const In=(e,t,n)=>e.getValue(t)==n;In.autoRemove=e=>L(e);const At=(e,t,n)=>{let[o,r]=n;const a=e.getValue(t);return a>=o&&a<=r};At.resolveFilterValue=e=>{let[t,n]=e,o=typeof t!="number"?parseFloat(t):t,r=typeof n!="number"?parseFloat(n):n,a=t===null||Number.isNaN(o)?-1/0:o,i=n===null||Number.isNaN(r)?1/0:r;if(a>i){const l=a;a=i,i=l}return[a,i]};At.autoRemove=e=>L(e)||L(e[0])&&L(e[1]);const U={includesString:wn,includesStringSensitive:Sn,equalsString:Rn,arrIncludes:yn,arrIncludesAll:$n,arrIncludesSome:xn,equals:En,weakEquals:In,inNumberRange:At};function L(e){return e==null||e===""}const Zr={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],globalFilter:void 0,...e}),getDefaultOptions:e=>({onColumnFiltersChange:j("columnFilters",e),onGlobalFilterChange:j("globalFilter",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100,globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const o=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof o=="string"||typeof o=="number"}}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],o=n==null?void 0:n.getValue(e.id);return typeof o=="string"?U.includesString:typeof o=="number"?U.inNumberRange:typeof o=="boolean"||o!==null&&typeof o=="object"?U.equals:Array.isArray(o)?U.arrIncludes:U.weakEquals},e.getFilterFn=()=>{var n,o;return Ie(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(o=t.options.filterFns)==null?void 0:o[e.columnDef.filterFn])!=null?n:U[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,o,r;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((o=t.options.enableColumnFilters)!=null?o:!0)&&((r=t.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getCanGlobalFilter=()=>{var n,o,r,a;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((o=t.options.enableGlobalFilter)!=null?o:!0)&&((r=t.options.enableFilters)!=null?r:!0)&&((a=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?a:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(o=>o.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,o;return(n=(o=t.getState().columnFilters)==null?void 0:o.findIndex(r=>r.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(o=>{const r=e.getFilterFn(),a=o==null?void 0:o.find(c=>c.id===e.id),i=Z(n,a?a.value:void 0);if(Mt(r,i,e)){var l;return(l=o==null?void 0:o.filter(c=>c.id!==e.id))!=null?l:[]}const u={id:e.id,value:i};if(a){var d;return(d=o==null?void 0:o.map(c=>c.id===e.id?u:c))!=null?d:[]}return o!=null&&o.length?[...o,u]:[u]})},e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.getGlobalAutoFilterFn=()=>U.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:o}=e.options;return Ie(o)?o:o==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[o])!=null?t:U[o]},e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),o=r=>{var a;return(a=Z(t,r))==null?void 0:a.filter(i=>{const l=n.find(u=>u.id===i.id);if(l){const u=l.getFilterFn();if(Mt(u,i.value,l))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(o)},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)},e.resetColumnFilters=t=>{var n,o;e.setColumnFilters(t?[]:(n=(o=e.initialState)==null?void 0:o.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel()),e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}};function Mt(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const qr=(e,t,n)=>n.reduce((o,r)=>{const a=r.getValue(e);return o+(typeof a=="number"?a:0)},0),Jr=(e,t,n)=>{let o;return n.forEach(r=>{const a=r.getValue(e);a!=null&&(o>a||o===void 0&&a>=a)&&(o=a)}),o},Kr=(e,t,n)=>{let o;return n.forEach(r=>{const a=r.getValue(e);a!=null&&(o<a||o===void 0&&a>=a)&&(o=a)}),o},Xr=(e,t,n)=>{let o,r;return n.forEach(a=>{const i=a.getValue(e);i!=null&&(o===void 0?i>=i&&(o=r=i):(o>i&&(o=i),r<i&&(r=i)))}),[o,r]},Qr=(e,t)=>{let n=0,o=0;if(t.forEach(r=>{let a=r.getValue(e);a!=null&&(a=+a)>=a&&(++n,o+=a)}),n)return o/n},ea=(e,t)=>{if(!t.length)return;const n=t.map(a=>a.getValue(e));if(!Lr(n))return;if(n.length===1)return n[0];const o=Math.floor(n.length/2),r=n.sort((a,i)=>a-i);return n.length%2!==0?r[o]:(r[o-1]+r[o])/2},ta=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),na=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,oa=(e,t)=>t.length,Oe={sum:qr,min:Jr,max:Kr,extent:Xr,mean:Qr,median:ea,unique:ta,uniqueCount:na,count:oa},ra={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:j("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(o=>o!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,o,r,a;return(n=(o=(r=(a=e.columnDef.enableGrouping)!=null?a:!0)!=null?r:t.options.enableGrouping)!=null?o:!0)!=null?n:!!e.accessorFn},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],o=n==null?void 0:n.getValue(e.id);if(typeof o=="number")return Oe.sum;if(Object.prototype.toString.call(o)==="[object Date]")return Oe.extent},e.getAggregationFn=()=>{var n,o;if(!e)throw new Error;return Ie(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(o=t.options.aggregationFns)==null?void 0:o[e.columnDef.aggregationFn])!=null?n:Oe[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,o;e.setGrouping(t?[]:(n=(o=e.initialState)==null?void 0:o.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const o=t.getColumn(n);return o!=null&&o.columnDef.getGroupingValue?(e._groupingValuesCache[n]=o.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,o)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=n.subRows)!=null&&r.length)}}};function aa(e,t,n){if(!(t!=null&&t.length)||!n)return e;const o=e.filter(a=>!t.includes(a.id));return n==="remove"?o:[...t.map(a=>e.find(i=>i.id===a)).filter(Boolean),...o]}const ia={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:j("columnOrder",e)}),createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=y(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,o)=>r=>{let a=[];if(!(t!=null&&t.length))a=r;else{const i=[...t],l=[...r];for(;l.length&&i.length;){const u=i.shift(),d=l.findIndex(c=>c.id===u);d>-1&&a.push(l.splice(d,1)[0])}a=[...a,...l]}return aa(a,n,o)},{key:!1})}},Xe=0,Qe=10,_e=()=>({pageIndex:Xe,pageSize:Qe}),la={getInitialState:e=>({...e,pagination:{..._e(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:j("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var o,r;if(!t){e._queue(()=>{t=!0});return}if((o=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?o:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=o=>{const r=a=>Z(o,a);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=o=>{var r;e.setPagination(o?_e():(r=e.initialState.pagination)!=null?r:_e())},e.setPageIndex=o=>{e.setPagination(r=>{let a=Z(o,r.pageIndex);const i=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return a=Math.max(0,Math.min(a,i)),{...r,pageIndex:a}})},e.resetPageIndex=o=>{var r,a;e.setPageIndex(o?Xe:(r=(a=e.initialState)==null||(a=a.pagination)==null?void 0:a.pageIndex)!=null?r:Xe)},e.resetPageSize=o=>{var r,a;e.setPageSize(o?Qe:(r=(a=e.initialState)==null||(a=a.pagination)==null?void 0:a.pageSize)!=null?r:Qe)},e.setPageSize=o=>{e.setPagination(r=>{const a=Math.max(1,Z(o,r.pageSize)),i=r.pageSize*r.pageIndex,l=Math.floor(i/a);return{...r,pageIndex:l,pageSize:a}})},e.setPageCount=o=>e.setPagination(r=>{var a;let i=Z(o,(a=e.options.pageCount)!=null?a:-1);return typeof i=="number"&&(i=Math.max(-1,i)),{...r,pageCount:i}}),e.getPageOptions=y(()=>[e.getPageCount()],o=>{let r=[];return o&&o>0&&(r=[...new Array(o)].fill(null).map((a,i)=>i)),r},{key:!1,debug:()=>{var o;return(o=e.options.debugAll)!=null?o:e.options.debugTable}}),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:o}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:o<r-1},e.previousPage=()=>e.setPageIndex(o=>o-1),e.nextPage=()=>e.setPageIndex(o=>o+1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var o;return(o=e.options.pageCount)!=null?o:Math.ceil(e.getPrePaginationRowModel().rows.length/e.getState().pagination.pageSize)}}},je=()=>({left:[],right:[]}),Le=()=>({top:[],bottom:[]}),sa={getInitialState:e=>({columnPinning:je(),rowPinning:Le(),...e}),getDefaultOptions:e=>({onColumnPinningChange:j("columnPinning",e),onRowPinningChange:j("rowPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const o=e.getLeafColumns().map(r=>r.id).filter(Boolean);t.setColumnPinning(r=>{var a,i;if(n==="right"){var l,u;return{left:((l=r==null?void 0:r.left)!=null?l:[]).filter(g=>!(o!=null&&o.includes(g))),right:[...((u=r==null?void 0:r.right)!=null?u:[]).filter(g=>!(o!=null&&o.includes(g))),...o]}}if(n==="left"){var d,c;return{left:[...((d=r==null?void 0:r.left)!=null?d:[]).filter(g=>!(o!=null&&o.includes(g))),...o],right:((c=r==null?void 0:r.right)!=null?c:[]).filter(g=>!(o!=null&&o.includes(g)))}}return{left:((a=r==null?void 0:r.left)!=null?a:[]).filter(g=>!(o!=null&&o.includes(g))),right:((i=r==null?void 0:r.right)!=null?i:[]).filter(g=>!(o!=null&&o.includes(g)))}})},e.getCanPin=()=>e.getLeafColumns().some(o=>{var r,a,i;return((r=o.columnDef.enablePinning)!=null?r:!0)&&((a=(i=t.options.enableColumnPinning)!=null?i:t.options.enablePinning)!=null?a:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(l=>l.id),{left:o,right:r}=t.getState().columnPinning,a=n.some(l=>o==null?void 0:o.includes(l)),i=n.some(l=>r==null?void 0:r.includes(l));return a?"left":i?"right":!1},e.getPinnedIndex=()=>{var n,o;const r=e.getIsPinned();return r?(n=(o=t.getState().columnPinning)==null||(o=o[r])==null?void 0:o.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.pin=(n,o,r)=>{const a=o?e.getLeafRows().map(u=>{let{id:d}=u;return d}):[],i=r?e.getParentRows().map(u=>{let{id:d}=u;return d}):[],l=new Set([...i,e.id,...a]);t.setRowPinning(u=>{var d,c;if(n==="bottom"){var g,h;return{top:((g=u==null?void 0:u.top)!=null?g:[]).filter(f=>!(l!=null&&l.has(f))),bottom:[...((h=u==null?void 0:u.bottom)!=null?h:[]).filter(f=>!(l!=null&&l.has(f))),...Array.from(l)]}}if(n==="top"){var m,v;return{top:[...((m=u==null?void 0:u.top)!=null?m:[]).filter(f=>!(l!=null&&l.has(f))),...Array.from(l)],bottom:((v=u==null?void 0:u.bottom)!=null?v:[]).filter(f=>!(l!=null&&l.has(f)))}}return{top:((d=u==null?void 0:u.top)!=null?d:[]).filter(f=>!(l!=null&&l.has(f))),bottom:((c=u==null?void 0:u.bottom)!=null?c:[]).filter(f=>!(l!=null&&l.has(f)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:o,enablePinning:r}=t.options;return typeof o=="function"?o(e):(n=o??r)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:o,bottom:r}=t.getState().rowPinning,a=n.some(l=>o==null?void 0:o.includes(l)),i=n.some(l=>r==null?void 0:r.includes(l));return a?"top":i?"bottom":!1},e.getPinnedIndex=()=>{var n,o;const r=e.getIsPinned();if(!r)return-1;const a=(n=t._getPinnedRows(r))==null?void 0:n.map(i=>{let{id:l}=i;return l});return(o=a==null?void 0:a.indexOf(e.id))!=null?o:-1},e.getCenterVisibleCells=y(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,o,r)=>{const a=[...o??[],...r??[]];return n.filter(i=>!a.includes(i.column.id))},{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),e.getLeftVisibleCells=y(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,,],(n,o)=>(o??[]).map(a=>n.find(i=>i.column.id===a)).filter(Boolean).map(a=>({...a,position:"left"})),{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),e.getRightVisibleCells=y(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,o)=>(o??[]).map(a=>n.find(i=>i.column.id===a)).filter(Boolean).map(a=>({...a,position:"right"})),{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,o;return e.setColumnPinning(t?je():(n=(o=e.initialState)==null?void 0:o.columnPinning)!=null?n:je())},e.getIsSomeColumnsPinned=t=>{var n;const o=e.getState().columnPinning;if(!t){var r,a;return!!((r=o.left)!=null&&r.length||(a=o.right)!=null&&a.length)}return!!((n=o[t])!=null&&n.length)},e.getLeftLeafColumns=y(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(o=>t.find(r=>r.id===o)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),e.getRightLeafColumns=y(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(o=>t.find(r=>r.id===o)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),e.getCenterLeafColumns=y(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,o)=>{const r=[...n??[],...o??[]];return t.filter(a=>!r.includes(a.id))},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,o;return e.setRowPinning(t?Le():(n=(o=e.initialState)==null?void 0:o.rowPinning)!=null?n:Le())},e.getIsSomeRowsPinned=t=>{var n;const o=e.getState().rowPinning;if(!t){var r,a;return!!((r=o.top)!=null&&r.length||(a=o.bottom)!=null&&a.length)}return!!((n=o[t])!=null&&n.length)},e._getPinnedRows=t=>y(()=>[e.getRowModel().rows,e.getState().rowPinning[t]],(n,o)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(o??[]).map(i=>{const l=e.getRow(i,!0);return l.getIsAllParentsExpanded()?l:null}):(o??[]).map(i=>n.find(l=>l.id===i))).filter(Boolean).map(i=>({...i,position:t}))},{key:!1,debug:()=>{var n;return(n=e.options.debugAll)!=null?n:e.options.debugRows}})(),e.getTopRows=()=>e._getPinnedRows("top"),e.getBottomRows=()=>e._getPinnedRows("bottom"),e.getCenterRows=y(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,o)=>{const r=new Set([...n??[],...o??[]]);return t.filter(a=>!r.has(a.id))},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugRows}})}},ua={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:j("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const o={...n},r=e.getPreGroupedRowModel().flatRows;return t?r.forEach(a=>{a.getCanSelect()&&(o[a.id]=!0)}):r.forEach(a=>{delete o[a.id]}),o})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const o=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),r={...n};return e.getRowModel().rows.forEach(a=>{et(r,a.id,o,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=y(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?He(e,n):{rows:[],flatRows:[],rowsById:{}},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),e.getFilteredSelectedRowModel=y(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?He(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getFilteredSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),e.getGroupedSelectedRowModel=y(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?He(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getGroupedSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let o=!!(t.length&&Object.keys(n).length);return o&&t.some(r=>r.getCanSelect()&&!n[r.id])&&(o=!1),o},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:n}=e.getState();let o=!!t.length;return o&&t.some(r=>!n[r.id])&&(o=!1),o},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,o)=>{const r=e.getIsSelected();t.setRowSelection(a=>{var i;if(n=typeof n<"u"?n:!r,e.getCanSelect()&&r===n)return a;const l={...a};return et(l,e.id,n,(i=o==null?void 0:o.selectChildren)!=null?i:!0,t),l})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return Ct(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return tt(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return tt(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return o=>{var r;n&&e.toggleSelected((r=o.target)==null?void 0:r.checked)}}}},et=(e,t,n,o,r)=>{var a;const i=r.getRow(t);n?(i.getCanMultiSelect()||Object.keys(e).forEach(l=>delete e[l]),i.getCanSelect()&&(e[t]=!0)):delete e[t],o&&(a=i.subRows)!=null&&a.length&&i.getCanSelectSubRows()&&i.subRows.forEach(l=>et(e,l.id,n,o,r))};function He(e,t){const n=e.getState().rowSelection,o=[],r={},a=function(i,l){return i.map(u=>{var d;const c=Ct(u,n);if(c&&(o.push(u),r[u.id]=u),(d=u.subRows)!=null&&d.length&&(u={...u,subRows:a(u.subRows)}),c)return u}).filter(Boolean)};return{rows:a(t.rows),flatRows:o,rowsById:r}}function Ct(e,t){var n;return(n=t[e.id])!=null?n:!1}function tt(e,t,n){var o;if(!((o=e.subRows)!=null&&o.length))return!1;let r=!0,a=!1;return e.subRows.forEach(i=>{if(!(a&&!r)&&(i.getCanSelect()&&(Ct(i,t)?a=!0:r=!1),i.subRows&&i.subRows.length)){const l=tt(i,t);l==="all"?a=!0:(l==="some"&&(a=!0),r=!1)}}),r?"all":a?"some":!1}const nt=/([0-9]+)/gm,ca=(e,t,n)=>Fn(q(e.getValue(n)).toLowerCase(),q(t.getValue(n)).toLowerCase()),da=(e,t,n)=>Fn(q(e.getValue(n)),q(t.getValue(n))),ga=(e,t,n)=>bt(q(e.getValue(n)).toLowerCase(),q(t.getValue(n)).toLowerCase()),fa=(e,t,n)=>bt(q(e.getValue(n)),q(t.getValue(n))),pa=(e,t,n)=>{const o=e.getValue(n),r=t.getValue(n);return o>r?1:o<r?-1:0},ha=(e,t,n)=>bt(e.getValue(n),t.getValue(n));function bt(e,t){return e===t?0:e>t?1:-1}function q(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Fn(e,t){const n=e.split(nt).filter(Boolean),o=t.split(nt).filter(Boolean);for(;n.length&&o.length;){const r=n.shift(),a=o.shift(),i=parseInt(r,10),l=parseInt(a,10),u=[i,l].sort();if(isNaN(u[0])){if(r>a)return 1;if(a>r)return-1;continue}if(isNaN(u[1]))return isNaN(i)?-1:1;if(i>l)return 1;if(l>i)return-1}return n.length-o.length}const le={alphanumeric:ca,alphanumericCaseSensitive:da,text:ga,textCaseSensitive:fa,datetime:pa,basic:ha},ma={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:j("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let o=!1;for(const r of n){const a=r==null?void 0:r.getValue(e.id);if(Object.prototype.toString.call(a)==="[object Date]")return le.datetime;if(typeof a=="string"&&(o=!0,a.split(nt).length>1))return le.alphanumeric}return o?le.text:le.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,o;if(!e)throw new Error;return Ie(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(o=t.options.sortingFns)==null?void 0:o[e.columnDef.sortingFn])!=null?n:le[e.columnDef.sortingFn]},e.toggleSorting=(n,o)=>{const r=e.getNextSortingOrder(),a=typeof n<"u"&&n!==null;t.setSorting(i=>{const l=i==null?void 0:i.find(m=>m.id===e.id),u=i==null?void 0:i.findIndex(m=>m.id===e.id);let d=[],c,g=a?n:r==="desc";if(i!=null&&i.length&&e.getCanMultiSort()&&o?l?c="toggle":c="add":i!=null&&i.length&&u!==i.length-1?c="replace":l?c="toggle":c="replace",c==="toggle"&&(a||r||(c="remove")),c==="add"){var h;d=[...i,{id:e.id,desc:g}],d.splice(0,d.length-((h=t.options.maxMultiSortColCount)!=null?h:Number.MAX_SAFE_INTEGER))}else c==="toggle"?d=i.map(m=>m.id===e.id?{...m,desc:g}:m):c==="remove"?d=i.filter(m=>m.id!==e.id):d=[{id:e.id,desc:g}];return d})},e.getFirstSortDir=()=>{var n,o;return((n=(o=e.columnDef.sortDescFirst)!=null?o:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var o,r;const a=e.getFirstSortDir(),i=e.getIsSorted();return i?i!==a&&((o=t.options.enableSortingRemoval)==null||o)&&(!(n&&(r=t.options.enableMultiRemove)!=null)||r)?!1:i==="desc"?"asc":"desc":a},e.getCanSort=()=>{var n,o;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((o=t.options.enableSorting)!=null?o:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,o;return(n=(o=e.columnDef.enableMultiSort)!=null?o:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const o=(n=t.getState().sorting)==null?void 0:n.find(r=>r.id===e.id);return o?o.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,o;return(n=(o=t.getState().sorting)==null?void 0:o.findIndex(r=>r.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(o=>o.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return o=>{n&&(o.persist==null||o.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(o):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,o;e.setSorting(t?[]:(n=(o=e.initialState)==null?void 0:o.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},va={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:j("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(o=>({...o,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,o;return(n=(o=t.getState().columnVisibility)==null?void 0:o[e.id])!=null?n:!0},e.getCanHide=()=>{var n,o;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((o=t.options.enableHiding)!=null?o:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=y(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(o=>o.column.getIsVisible()),{key:"row._getAllVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),e.getVisibleCells=y(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,o,r)=>[...n,...o,...r],{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})},createTable:e=>{const t=(n,o)=>y(()=>[o(),o().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(a=>a.getIsVisible==null?void 0:a.getIsVisible()),{key:n,debug:()=>{var r;return(r=e.options.debugAll)!=null?r:e.options.debugColumns}});e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var o;e.setColumnVisibility(n?{}:(o=e.initialState.columnVisibility)!=null?o:{})},e.toggleAllColumnsVisible=n=>{var o;n=(o=n)!=null?o:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,a)=>({...r,[a.id]:n||!(a.getCanHide!=null&&a.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var o;e.toggleAllColumnsVisible((o=n.target)==null?void 0:o.checked)}}},Tt=[zr,va,ia,sa,Zr,ma,ra,Yr,la,ua,Wr];function Aa(e){var t;e.debugAll||e.debugTable;let n={_features:Tt};const o=n._features.reduce((c,g)=>Object.assign(c,g.getDefaultOptions==null?void 0:g.getDefaultOptions(n)),{}),r=c=>n.options.mergeOptions?n.options.mergeOptions(o,c):{...o,...c};let i={...{},...(t=e.initialState)!=null?t:{}};n._features.forEach(c=>{var g;i=(g=c.getInitialState==null?void 0:c.getInitialState(i))!=null?g:i});const l=[];let u=!1;const d={_features:Tt,options:{...o,...e},initialState:i,_queue:c=>{l.push(c),u||(u=!0,Promise.resolve().then(()=>{for(;l.length;)l.shift()();u=!1}).catch(g=>setTimeout(()=>{throw g})))},reset:()=>{n.setState(n.initialState)},setOptions:c=>{const g=Z(c,n.options);n.options=r(g)},getState:()=>n.options.state,setState:c=>{n.options.onStateChange==null||n.options.onStateChange(c)},_getRowId:(c,g,h)=>{var m;return(m=n.options.getRowId==null?void 0:n.options.getRowId(c,g,h))!=null?m:`${h?[h.id,g].join("."):g}`},getCoreRowModel:()=>(n._getCoreRowModel||(n._getCoreRowModel=n.options.getCoreRowModel(n)),n._getCoreRowModel()),getRowModel:()=>n.getPaginationRowModel(),getRow:(c,g)=>{const h=(g?n.getCoreRowModel():n.getRowModel()).rowsById[c];if(!h)throw new Error;return h},_getDefaultColumnDef:y(()=>[n.options.defaultColumn],c=>{var g;return c=(g=c)!=null?g:{},{header:h=>{const m=h.header.column.columnDef;return m.accessorKey?m.accessorKey:m.accessorFn?m.id:null},cell:h=>{var m,v;return(m=(v=h.renderValue())==null||v.toString==null?void 0:v.toString())!=null?m:null},...n._features.reduce((h,m)=>Object.assign(h,m.getDefaultColumnDef==null?void 0:m.getDefaultColumnDef()),{}),...c}},{debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns},key:!1}),_getColumnDefs:()=>n.options.columns,getAllColumns:y(()=>[n._getColumnDefs()],c=>{const g=function(h,m,v){return v===void 0&&(v=0),h.map(f=>{const A=Br(n,f,v,m),C=f;return A.columns=C.columns?g(C.columns,A,v+1):[],A})};return g(c)},{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),getAllFlatColumns:y(()=>[n.getAllColumns()],c=>c.flatMap(g=>g.getFlatColumns()),{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),_getAllFlatColumnsById:y(()=>[n.getAllFlatColumns()],c=>c.reduce((g,h)=>(g[h.id]=h,g),{}),{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),getAllLeafColumns:y(()=>[n.getAllColumns(),n._getOrderColumnsFn()],(c,g)=>{let h=c.flatMap(m=>m.getLeafColumns());return g(h)},{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),getColumn:c=>n._getAllFlatColumnsById()[c]};Object.assign(n,d);for(let c=0;c<n._features.length;c++){const g=n._features[c];g==null||g.createTable==null||g.createTable(n)}return n}function Ca(e,t,n,o){const r=()=>{var i;return(i=a.getValue())!=null?i:e.options.renderFallbackValue},a={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(o),renderValue:r,getContext:y(()=>[e,n,t,a],(i,l,u,d)=>({table:i,column:l,row:u,cell:d,getValue:d.getValue,renderValue:d.renderValue}),{key:!1,debug:()=>e.options.debugAll})};return e._features.forEach(i=>{i.createCell==null||i.createCell(a,n,t,e)},{}),a}const ba=(e,t,n,o,r,a,i)=>{let l={id:t,index:o,original:n,depth:r,parentId:i,_valuesCache:{},_uniqueValuesCache:{},getValue:u=>{if(l._valuesCache.hasOwnProperty(u))return l._valuesCache[u];const d=e.getColumn(u);if(d!=null&&d.accessorFn)return l._valuesCache[u]=d.accessorFn(l.original,o),l._valuesCache[u]},getUniqueValues:u=>{if(l._uniqueValuesCache.hasOwnProperty(u))return l._uniqueValuesCache[u];const d=e.getColumn(u);if(d!=null&&d.accessorFn)return d.columnDef.getUniqueValues?(l._uniqueValuesCache[u]=d.columnDef.getUniqueValues(l.original,o),l._uniqueValuesCache[u]):(l._uniqueValuesCache[u]=[l.getValue(u)],l._uniqueValuesCache[u])},renderValue:u=>{var d;return(d=l.getValue(u))!=null?d:e.options.renderFallbackValue},subRows:a??[],getLeafRows:()=>Hr(l.subRows,u=>u.subRows),getParentRow:()=>l.parentId?e.getRow(l.parentId,!0):void 0,getParentRows:()=>{let u=[],d=l;for(;;){const c=d.getParentRow();if(!c)break;u.push(c),d=c}return u.reverse()},getAllCells:y(()=>[e.getAllLeafColumns()],u=>u.map(d=>Ca(e,l,d,d.id)),{key:!1,debug:()=>{var u;return(u=e.options.debugAll)!=null?u:e.options.debugRows}}),_getAllCellsByColumnId:y(()=>[l.getAllCells()],u=>u.reduce((d,c)=>(d[c.column.id]=c,d),{}),{key:"row.getAllCellsByColumnId",debug:()=>{var u;return(u=e.options.debugAll)!=null?u:e.options.debugRows}})};for(let u=0;u<e._features.length;u++){const d=e._features[u];d==null||d.createRow==null||d.createRow(l,e)}return l};function wa(){return e=>y(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},o=function(r,a,i){a===void 0&&(a=0);const l=[];for(let d=0;d<r.length;d++){const c=ba(e,e._getRowId(r[d],d,i),r[d],d,a,void 0,i==null?void 0:i.id);if(n.flatRows.push(c),n.rowsById[c.id]=c,l.push(c),e.options.getSubRows){var u;c.originalSubRows=e.options.getSubRows(r[d],d),(u=c.originalSubRows)!=null&&u.length&&(c.subRows=o(c.originalSubRows,a+1,c))}}return l};return n.rows=o(t),n},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}function Sa(){return e=>y(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(t!=null&&t.length))return n;const o=e.getState().sorting,r=[],a=o.filter(u=>{var d;return(d=e.getColumn(u.id))==null?void 0:d.getCanSort()}),i={};a.forEach(u=>{const d=e.getColumn(u.id);d&&(i[u.id]={sortUndefined:d.columnDef.sortUndefined,invertSorting:d.columnDef.invertSorting,sortingFn:d.getSortingFn()})});const l=u=>{const d=u.map(c=>({...c}));return d.sort((c,g)=>{for(let m=0;m<a.length;m+=1){var h;const v=a[m],f=i[v.id],A=(h=v==null?void 0:v.desc)!=null?h:!1;let C=0;if(f.sortUndefined){const w=c.getValue(v.id),S=g.getValue(v.id),b=w===void 0,x=S===void 0;(b||x)&&(C=b&&x?0:b?f.sortUndefined:-f.sortUndefined)}if(C===0&&(C=f.sortingFn(c,g,v.id)),C!==0)return A&&(C*=-1),f.invertSorting&&(C*=-1),C}return c.index-g.index}),d.forEach(c=>{var g;r.push(c),(g=c.subRows)!=null&&g.length&&(c.subRows=l(c.subRows))}),d};return{rows:l(n.rows),flatRows:r,rowsById:n.rowsById}},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}/**
 * react-table
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Gt(e,t){return e?Ra(e)?s.createElement(e,t):e:null}function Ra(e){return ya(e)||typeof e=="function"||$a(e)}function ya(e){return typeof e=="function"&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}function $a(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function xa(e){const t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=s.useState(()=>({current:Aa(t)})),[o,r]=s.useState(()=>n.current.initialState);return n.current.setOptions(a=>({...a,...e,state:{...o,...e.state},onStateChange:i=>{r(i),e.onStateChange==null||e.onStateChange(i)}})),n.current}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Ea({data:e,isLoading:t,error:n,onDelete:o}){var u;const[r,a]=s.useState([]),i=s.useMemo(()=>kr(o),[o]),l=xa({data:e,columns:i,getCoreRowModel:wa(),onSortingChange:a,getSortedRowModel:Sa(),state:{sorting:r},autoResetPageIndex:!0});return p("div",{className:"h-full w-full rounded-md border p-1",children:R(vn,{className:"h-full w-full",children:[p(An,{className:"sticky top-0 bg-white dark:bg-slate-900",children:l.getHeaderGroups().map(d=>p(Se,{children:d.headers.map(c=>p(bn,{colSpan:c.colSpan,style:{width:c.getSize()},children:c.isPlaceholder?null:Gt(c.column.columnDef.header,c.getContext())},c.id))},d.id))}),p(Cn,{className:"w-full",children:(u=l.getRowModel().rows)!=null&&u.length?l.getRowModel().rows.map(d=>p(Se,{"data-state":d.getIsSelected()&&"selected",children:d.getVisibleCells().map(c=>p(Ke,{style:{width:c.column.getSize()},children:Gt(c.column.columnDef.cell,c.getContext())},c.id))},d.id)):p(Se,{children:p(Ke,{colSpan:l.getAllColumns().length,className:"h-[22rem] text-center",children:t?p(Lo,{}):n?"好像遇到错误啦！尝试刷新下":"暂无数据, 快去练习吧！"})})})]})})}const Ia=e=>p("svg",{viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...e,children:p("path",{fill:"currentColor",d:"M22 14h-1c0-3.87-3.13-7-7-7h-1V5.73A2 2 0 1 0 10 4c0 .74.4 1.39 1 1.73V7h-1c-3.87 0-7 3.13-7 7H2c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1h1v1a2 2 0 0 0 2 2h14c1.11 0 2-.89 2-2v-1h1c.55 0 1-.45 1-1v-3c0-.55-.45-1-1-1M7.5 18A2.5 2.5 0 0 1 5 15.5c0-.82.4-1.54 1-2l3.83 2.88C9.5 17.32 8.57 18 7.5 18m9 0c-1.07 0-2-.68-2.33-1.62L18 13.5c.6.46 1 1.18 1 2a2.5 2.5 0 0 1-2.5 2.5"})});globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Fa({errorData:e,dict:t}){const n=Ho(t.id),o=we(an),r=we(gt),a=dt(),i=we(ln),l=async()=>{r(t.id),i(-1);const d=await zo(t.id,e);o({isReviewMode:!0,reviewRecord:d}),a("/")},u=()=>{r(t.id),i(-1),o({isReviewMode:!0,reviewRecord:n}),a("/")};return R("div",{className:"flex h-full flex-col items-center justify-around px-60",children:[R("div",{children:[p(Ia,{fontSize:30,className:"text-indigo-300 "}),p("blockquote",{children:R("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300",children:["我们将使用您在该词典的历史练习数据、错误次数、练习时间来智能生成练习列表",p("br",{}),"目前该生成方式还处于实验阶段，我们会逐步完善该生成方式"]})})]}),R("div",{className:"flex w-full flex-col items-center",children:[n&&R(Fe,{children:[R("div",{className:" ml-10 flex w-full items-center py-0",children:[p(on,{value:n.index+1,max:n.words.length,className:"mr-4 h-2 w-full rounded-full border  border-indigo-400 bg-white",children:p(rn,{className:"h-full rounded-full bg-indigo-400 pl-0",style:{width:`calc(${(n.index+1)/n.words.length*100}% )`}})}),R("span",{className:"p-0 text-xs",children:[n.index+1,"/",n.words.length]})]}),p("div",{className:"mt-1 text-sm font-normal text-gray-500",children:`( 创建于 ${Bo(n.createTime)} )`})]}),!n&&R("div",{children:["当前词典错词数: ",e.length]}),R("div",{className:"mt-6 flex gap-10",children:[n&&p(ue,{size:"sm",onClick:u,children:"继续当前进度"}),R(ue,{size:"sm",onClick:l,children:["开始",n&&"新的","复习"]})]})]})]})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Pa(e,t){const{data:n,error:o,isLoading:r}=Wo(e==null?void 0:e.url,Uo),[a,i]=s.useState([]);return s.useEffect(()=>{n&&ct.wordRecords.where("wrongCount").above(0).filter(l=>l.dict===e.id).toArray().then(l=>{const u=[];l.forEach(c=>{let g=u.find(h=>h.word===c.word);g||(g={word:c.word,records:[]},u.push(g)),g.records.push(c)});const d=[];u.forEach(c=>{const g={};c.records.forEach(v=>{for(const f in v.mistakes){const A=v.mistakes[f];A.length>0&&(g[f]=(g[f]??0)+A.length)}});const h=n.find(v=>v.name===c.word);if(!h)return;const m={word:c.word,originData:h,errorCount:c.records.reduce((v,f)=>(v+=f.wrongCount,v),0),errorLetters:g,errorChar:Object.entries(g).sort((v,f)=>f[1]-v[1]).map(([v])=>c.word[Number(v)]),latestErrorTime:c.records.reduce((v,f)=>(v=Math.max(v,f.timeStamp),v),0)};d.push(m)}),i(d)})},[e.id,n,t]),{errorWordData:a,isLoading:r,error:o}}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const Pn=s.forwardRef(({className:e,children:t,...n},o)=>R(ft,{ref:o,className:M("relative overflow-hidden",e),...n,children:[p(sn,{className:"h-full w-full rounded-[inherit]",children:t}),p(Mn,{}),p(Yo,{})]}));Pn.displayName=ft.displayName;const Mn=s.forwardRef(({className:e,orientation:t="vertical",...n},o)=>p(un,{ref:o,orientation:t,className:M("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...n,children:p(Zo,{className:"relative flex-1 rounded-full bg-slate-200 dark:bg-slate-800"})}));Mn.displayName=un.displayName;const ot=s.forwardRef((e,t)=>{const{children:n,...o}=e,r=s.Children.toArray(n),a=r.find(Ta);if(a){const i=a.props.children,l=r.map(u=>u===a?s.Children.count(i)>1?s.Children.only(null):s.isValidElement(i)?i.props.children:null:u);return s.createElement(rt,E({},o,{ref:t}),s.isValidElement(i)?s.cloneElement(i,void 0,l):null)}return s.createElement(rt,E({},o,{ref:t}),n)});ot.displayName="Slot";const rt=s.forwardRef((e,t)=>{const{children:n,...o}=e;return s.isValidElement(n)?s.cloneElement(n,{...Ga(o,n.props),ref:t?cn(t,n.ref):n.ref}):s.Children.count(n)>1?s.Children.only(null):null});rt.displayName="SlotClone";const Ma=({children:e})=>s.createElement(s.Fragment,null,e);function Ta(e){return s.isValidElement(e)&&e.type===Ma}function Ga(e,t){const n={...t};for(const o in t){const r=e[o],a=t[o];/^on[A-Z]/.test(o)?r&&a?n[o]=(...l)=>{a(...l),r(...l)}:r&&(n[o]=r):o==="style"?n[o]={...r,...a}:o==="className"&&(n[o]=[r,a].filter(Boolean).join(" "))}return{...e,...n}}function Va(e){const t=e+"CollectionProvider",[n,o]=de(t),[r,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=m=>{const{scope:v,children:f}=m,A=$.useRef(null),C=$.useRef(new Map).current;return $.createElement(r,{scope:v,itemMap:C,collectionRef:A},f)},l=e+"CollectionSlot",u=$.forwardRef((m,v)=>{const{scope:f,children:A}=m,C=a(l,f),w=X(v,C.collectionRef);return $.createElement(ot,{ref:w},A)}),d=e+"CollectionItemSlot",c="data-radix-collection-item",g=$.forwardRef((m,v)=>{const{scope:f,children:A,...C}=m,w=$.useRef(null),S=X(v,w),b=a(d,f);return $.useEffect(()=>(b.itemMap.set(w,{ref:w,...C}),()=>void b.itemMap.delete(w))),$.createElement(ot,{[c]:"",ref:S},A)});function h(m){const v=a(e+"CollectionConsumer",m);return $.useCallback(()=>{const A=v.collectionRef.current;if(!A)return[];const C=Array.from(A.querySelectorAll(`[${c}]`));return Array.from(v.itemMap.values()).sort((b,x)=>C.indexOf(b.ref.current)-C.indexOf(x.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:i,Slot:u,ItemSlot:g},h,o]}const Be="rovingFocusGroup.onEntryFocus",Na={bubbles:!1,cancelable:!0},wt="RovingFocusGroup",[at,Tn,Da]=Va(wt),[ka,Me]=de(wt,[Da]),[Oa,_a]=ka(wt),ja=s.forwardRef((e,t)=>s.createElement(at.Provider,{scope:e.__scopeRovingFocusGroup},s.createElement(at.Slot,{scope:e.__scopeRovingFocusGroup},s.createElement(La,E({},e,{ref:t}))))),La=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:u,onEntryFocus:d,...c}=e,g=s.useRef(null),h=X(t,g),m=pt(a),[v=null,f]=oe({prop:i,defaultProp:l,onChange:u}),[A,C]=s.useState(!1),w=qe(d),S=Tn(n),b=s.useRef(!1),[x,P]=s.useState(0);return s.useEffect(()=>{const F=g.current;if(F)return F.addEventListener(Be,w),()=>F.removeEventListener(Be,w)},[w]),s.createElement(Oa,{scope:n,orientation:o,dir:m,loop:r,currentTabStopId:v,onItemFocus:s.useCallback(F=>f(F),[f]),onItemShiftTab:s.useCallback(()=>C(!0),[]),onFocusableItemAdd:s.useCallback(()=>P(F=>F+1),[]),onFocusableItemRemove:s.useCallback(()=>P(F=>F-1),[])},s.createElement(G.div,E({tabIndex:A||x===0?-1:0,"data-orientation":o},c,{ref:h,style:{outline:"none",...e.style},onMouseDown:N(e.onMouseDown,()=>{b.current=!0}),onFocus:N(e.onFocus,F=>{const T=!b.current;if(F.target===F.currentTarget&&T&&!A){const k=new CustomEvent(Be,Na);if(F.currentTarget.dispatchEvent(k),!k.defaultPrevented){const O=S().filter(W=>W.focusable),re=O.find(W=>W.active),B=O.find(W=>W.id===v),Ve=[re,B,...O].filter(Boolean).map(W=>W.ref.current);Gn(Ve)}}b.current=!1}),onBlur:N(e.onBlur,()=>C(!1))})))}),Ha="RovingFocusGroupItem",Ba=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:a,...i}=e,l=se(),u=a||l,d=_a(Ha,n),c=d.currentTabStopId===u,g=Tn(n),{onFocusableItemAdd:h,onFocusableItemRemove:m}=d;return s.useEffect(()=>{if(o)return h(),()=>m()},[o,h,m]),s.createElement(at.ItemSlot,{scope:n,id:u,focusable:o,active:r},s.createElement(G.span,E({tabIndex:c?0:-1,"data-orientation":d.orientation},i,{ref:t,onMouseDown:N(e.onMouseDown,v=>{o?d.onItemFocus(u):v.preventDefault()}),onFocus:N(e.onFocus,()=>d.onItemFocus(u)),onKeyDown:N(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){d.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const f=Ua(v,d.orientation,d.dir);if(f!==void 0){v.preventDefault();let C=g().filter(w=>w.focusable).map(w=>w.ref.current);if(f==="last")C.reverse();else if(f==="prev"||f==="next"){f==="prev"&&C.reverse();const w=C.indexOf(v.currentTarget);C=d.loop?Ya(C,w+1):C.slice(w+1)}setTimeout(()=>Gn(C))}})})))}),za={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Wa(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Ua(e,t,n){const o=Wa(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return za[o]}function Gn(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Ya(e,t){return e.map((n,o)=>e[(t+o)%e.length])}const Vn=ja,Nn=Ba,Dn="Tabs",[Za,us]=de(Dn,[Me]),kn=Me(),[qa,St]=Za(Dn),Ja=s.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,onValueChange:r,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:u="automatic",...d}=e,c=pt(l),[g,h]=oe({prop:o,onChange:r,defaultProp:a});return s.createElement(qa,{scope:n,baseId:se(),value:g,onValueChange:h,orientation:i,dir:c,activationMode:u},s.createElement(G.div,E({dir:c,"data-orientation":i},d,{ref:t})))}),Ka="TabsList",Xa=s.forwardRef((e,t)=>{const{__scopeTabs:n,loop:o=!0,...r}=e,a=St(Ka,n),i=kn(n);return s.createElement(Vn,E({asChild:!0},i,{orientation:a.orientation,dir:a.dir,loop:o}),s.createElement(G.div,E({role:"tablist","aria-orientation":a.orientation},r,{ref:t})))}),Qa="TabsTrigger",ei=s.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,disabled:r=!1,...a}=e,i=St(Qa,n),l=kn(n),u=On(i.baseId,o),d=_n(i.baseId,o),c=o===i.value;return s.createElement(Nn,E({asChild:!0},l,{focusable:!r,active:c}),s.createElement(G.button,E({type:"button",role:"tab","aria-selected":c,"aria-controls":d,"data-state":c?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:u},a,{ref:t,onMouseDown:N(e.onMouseDown,g=>{!r&&g.button===0&&g.ctrlKey===!1?i.onValueChange(o):g.preventDefault()}),onKeyDown:N(e.onKeyDown,g=>{[" ","Enter"].includes(g.key)&&i.onValueChange(o)}),onFocus:N(e.onFocus,()=>{const g=i.activationMode!=="manual";!c&&!r&&g&&i.onValueChange(o)})})))}),ti="TabsContent",ni=s.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,forceMount:r,children:a,...i}=e,l=St(ti,n),u=On(l.baseId,o),d=_n(l.baseId,o),c=o===l.value,g=s.useRef(c);return s.useEffect(()=>{const h=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(h)},[]),s.createElement(Pe,{present:r||c},({present:h})=>s.createElement(G.div,E({"data-state":c?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":u,hidden:!h,id:d,tabIndex:0},i,{ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0}}),h&&a))});function On(e,t){return`${e}-trigger-${t}`}function _n(e,t){return`${e}-content-${t}`}const oi=Ja,jn=Xa,Ln=ei,Hn=ni;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const ri=oi,ai=s.forwardRef(({className:e,...t},n)=>p(jn,{ref:n,className:M("inline-flex h-10 items-center justify-center rounded-md bg-slate-100 p-1 text-slate-500 dark:bg-slate-800 dark:text-slate-400",e),...t}));ai.displayName=jn.displayName;const ii=s.forwardRef(({className:e,...t},n)=>p(Ln,{ref:n,className:M("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-slate-950 data-[state=active]:shadow-sm dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300 dark:data-[state=active]:bg-slate-950 dark:data-[state=active]:text-slate-50",e),...t}));ii.displayName=Ln.displayName;const Re=s.forwardRef(({className:e,...t},n)=>p(Hn,{ref:n,className:M("mt-2 focus:outline-none",e),...t}));Re.displayName=Hn.displayName;const Bn=s.forwardRef((e,t)=>{const{pressed:n,defaultPressed:o=!1,onPressedChange:r,...a}=e,[i=!1,l]=oe({prop:n,onChange:r,defaultProp:o});return s.createElement(G.button,E({type:"button","aria-pressed":i,"data-state":i?"on":"off","data-disabled":e.disabled?"":void 0},a,{ref:t,onClick:N(e.onClick,()=>{e.disabled||l(!i)})}))}),zn=Bn;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const Wn=Xt("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors hover:bg-slate-100 hover:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-slate-100 data-[state=on]:text-slate-900 dark:ring-offset-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-400 dark:focus-visible:ring-slate-300 dark:data-[state=on]:bg-slate-800 dark:data-[state=on]:text-slate-50",{variants:{variant:{default:"bg-transparent",outline:"border border-slate-200 bg-transparent hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:hover:bg-slate-800 dark:hover:text-slate-50"},size:{default:"h-10 px-3",sm:"h-9 px-2.5",lg:"h-11 px-5"}},defaultVariants:{variant:"default",size:"default"}}),li=s.forwardRef(({className:e,variant:t,size:n,...o},r)=>p(zn,{ref:r,className:M(Wn({variant:t,size:n,className:e})),...o}));li.displayName=zn.displayName;const Te="ToggleGroup",[Un,cs]=de(Te,[Me]),Yn=Me(),si=$.forwardRef((e,t)=>{const{type:n,...o}=e;if(n==="single"){const r=o;return $.createElement(ui,E({},r,{ref:t}))}if(n==="multiple"){const r=o;return $.createElement(ci,E({},r,{ref:t}))}throw new Error(`Missing prop \`type\` expected on \`${Te}\``)}),[Zn,qn]=Un(Te),ui=$.forwardRef((e,t)=>{const{value:n,defaultValue:o,onValueChange:r=()=>{},...a}=e,[i,l]=oe({prop:n,defaultProp:o,onChange:r});return $.createElement(Zn,{scope:e.__scopeToggleGroup,type:"single",value:i?[i]:[],onItemActivate:l,onItemDeactivate:$.useCallback(()=>l(""),[l])},$.createElement(Jn,E({},a,{ref:t})))}),ci=$.forwardRef((e,t)=>{const{value:n,defaultValue:o,onValueChange:r=()=>{},...a}=e,[i=[],l]=oe({prop:n,defaultProp:o,onChange:r}),u=$.useCallback(c=>l((g=[])=>[...g,c]),[l]),d=$.useCallback(c=>l((g=[])=>g.filter(h=>h!==c)),[l]);return $.createElement(Zn,{scope:e.__scopeToggleGroup,type:"multiple",value:i,onItemActivate:u,onItemDeactivate:d},$.createElement(Jn,E({},a,{ref:t})))}),[di,gi]=Un(Te),Jn=$.forwardRef((e,t)=>{const{__scopeToggleGroup:n,disabled:o=!1,rovingFocus:r=!0,orientation:a,dir:i,loop:l=!0,...u}=e,d=Yn(n),c=pt(i),g={role:"group",dir:c,...u};return $.createElement(di,{scope:n,rovingFocus:r,disabled:o},r?$.createElement(Vn,E({asChild:!0},d,{orientation:a,dir:c,loop:l}),$.createElement(G.div,E({},g,{ref:t}))):$.createElement(G.div,E({},g,{ref:t})))}),it="ToggleGroupItem",fi=$.forwardRef((e,t)=>{const n=qn(it,e.__scopeToggleGroup),o=gi(it,e.__scopeToggleGroup),r=Yn(e.__scopeToggleGroup),a=n.value.includes(e.value),i=o.disabled||e.disabled,l={...e,pressed:a,disabled:i},u=$.useRef(null);return o.rovingFocus?$.createElement(Nn,E({asChild:!0},r,{focusable:!i,active:a,ref:u}),$.createElement(Vt,E({},l,{ref:t}))):$.createElement(Vt,E({},l,{ref:t}))}),Vt=$.forwardRef((e,t)=>{const{__scopeToggleGroup:n,value:o,...r}=e,a=qn(it,n),i={role:"radio","aria-checked":e.pressed,"aria-pressed":void 0},l=a.type==="single"?i:void 0;return $.createElement(Bn,E({},l,r,{ref:t,onPressedChange:u=>{u?a.onItemActivate(o):a.onItemDeactivate(o)}}))}),Kn=si,Xn=fi;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const Qn=s.createContext({size:"default",variant:"default"}),eo=s.forwardRef(({className:e,variant:t,size:n,children:o,...r},a)=>p(Kn,{ref:a,className:M("flex items-center justify-center gap-1",e),...r,children:p(Qn.Provider,{value:{variant:t,size:n},children:o})}));eo.displayName=Kn.displayName;const ye=s.forwardRef(({className:e,children:t,variant:n,size:o,...r},a)=>{const i=s.useContext(Qn);return p(Xn,{ref:a,className:M(Wn({variant:i.variant||n,size:i.size||o}),"focus:outline-none",e),...r,children:t})});ye.displayName=Xn.displayName;const pi=e=>p("svg",{viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...e,children:p("path",{fill:"currentColor",d:"M4 6H2v14c0 1.1.9 2 2 2h14v-2H4zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m-3 2v5l-1-.75L15 9V4zm3 12H8V4h5v9l3-2.25L19 13V4h1z"})}),hi=pi,mi=e=>p("svg",{viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...e,children:p("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13V5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v13c0 1-.6 3-3 3m0 0H6c-1 0-3-.6-3-3v-2h12v2c0 2.4 2 3 3 3M9 7h8m-8 4h4"})}),vi=mi,Ai=e=>p("svg",{viewBox:"0 0 16 16",width:"1.2em",height:"1.2em",...e,children:p("path",{fill:"currentColor",fillRule:"evenodd",d:"M9 2.5a1 1 0 1 1-2 0a1 1 0 0 1 2 0m1.45-.5a2.5 2.5 0 0 0-4.9 0H3a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1zM8 5H5.5V3.5h-2v11h9v-11h-2V5zM5 7.75A.75.75 0 0 1 5.75 7h4.5a.75.75 0 0 1 0 1.5h-4.5A.75.75 0 0 1 5 7.75m.75 1.75a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5z",clipRule:"evenodd"})}),Ci=Ai;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function bi({dictionary:e}){const[t,n]=It(ln),[o,r]=It(gt),[a,i]=s.useState("chapters"),l=we(an),u=dt(),{deleteWordRecord:d}=qo(),[c,g]=s.useState(!1),h=s.useMemo(()=>e.id===o?t:0,[t,o,e.id]),{errorWordData:m,isLoading:v,error:f}=Pa(e,c),A=s.useMemo(()=>Or(m),[m]),C=s.useCallback(async b=>{await d(b,e.id),g(x=>!x)},[d,e.id]),w=s.useCallback(b=>{r(e.id),n(b),l(x=>({...x,isReviewMode:!1})),u("/")},[e.id,u,n,r,l]),S=s.useCallback(b=>{b!==a&&i(b)},[a]);return R("div",{className:"flex flex-col rounded-[4rem] px-4 py-3 pl-5 text-gray-800 dark:text-gray-300",children:[R("div",{className:"text relative flex h-40 flex-col gap-2",children:[p("h3",{className:"text-2xl font-semibold",children:e.name}),R("p",{className:"mt-1",children:[e.chapterCount," 章节"]}),R("p",{children:["共 ",e.length," 词"]}),p("p",{children:e.description}),p("div",{className:"absolute bottom-5 right-4",children:R(eo,{type:"single",value:a,onValueChange:S,children:[R(ye,{value:"chapters",disabled:a==="chapters",className:`${a==="chapters"?"text-primary-foreground bg-primary":""} disabled:opacity-100`,children:[p(vi,{className:"mr-1.5 text-gray-500"}),"章节选择"]}),m.length>0&&R(Fe,{children:[R(ye,{value:"errors",disabled:a==="errors",className:`${a==="errors"?"text-primary-foreground bg-primary":""} disabled:opacity-100`,children:[p(hi,{className:"mr-1.5 text-gray-500"}),"查看错题"]}),R(ye,{value:"review",disabled:a==="review",className:`${a==="review"?"text-primary-foreground bg-primary":""} disabled:opacity-100`,children:[p(Ci,{className:"mr-1.5 text-gray-500"}),"错题回顾"]})]})]})})]}),p("div",{className:"flex pl-0",children:R(ri,{value:a,className:"h-[30rem] w-full ",children:[p(Re,{value:"chapters",className:"h-full ",children:p(Pn,{className:"h-[30rem] ",children:p("div",{className:"flex w-full flex-wrap gap-3",children:Jo(0,e.chapterCount,1).map(b=>p(Fr,{index:b,checked:h===b,dictID:e.id,onChange:w},`${e.id}-${b}`))})})}),p(Re,{value:"errors",className:"h-full",children:p(Ea,{data:A,isLoading:v,error:f,onDelete:C})}),p(Re,{value:"review",className:"h-full",children:p(Fa,{errorData:m,dict:e})})]})})]})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function wi(e,t){const[n,o]=s.useState(null);return s.useEffect(()=>{t&&!n&&(async()=>{const a=await Si(e);o(a)})()},[e,t]),n}async function Si(e){return{exercisedChapterCount:(await ct.chapterRecords.where({dict:e}).toArray()).map(({chapter:a})=>a).filter(a=>a!==null).filter((a,i,l)=>l.indexOf(a)===i).length}}const Ri=""+new URL("book-cover-c875e761.png",import.meta.url).href,ze="focusScope.autoFocusOnMount",We="focusScope.autoFocusOnUnmount",Nt={bubbles:!1,cancelable:!0},yi=s.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:a,...i}=e,[l,u]=s.useState(null),d=qe(r),c=qe(a),g=s.useRef(null),h=X(t,f=>u(f)),m=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(o){let f=function(S){if(m.paused||!l)return;const b=S.target;l.contains(b)?g.current=b:Y(g.current,{select:!0})},A=function(S){if(m.paused||!l)return;const b=S.relatedTarget;b!==null&&(l.contains(b)||Y(g.current,{select:!0}))},C=function(S){if(document.activeElement===document.body)for(const x of S)x.removedNodes.length>0&&Y(l)};document.addEventListener("focusin",f),document.addEventListener("focusout",A);const w=new MutationObserver(C);return l&&w.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",f),document.removeEventListener("focusout",A),w.disconnect()}}},[o,l,m.paused]),s.useEffect(()=>{if(l){kt.add(m);const f=document.activeElement;if(!l.contains(f)){const C=new CustomEvent(ze,Nt);l.addEventListener(ze,d),l.dispatchEvent(C),C.defaultPrevented||($i(Pi(to(l)),{select:!0}),document.activeElement===f&&Y(l))}return()=>{l.removeEventListener(ze,d),setTimeout(()=>{const C=new CustomEvent(We,Nt);l.addEventListener(We,c),l.dispatchEvent(C),C.defaultPrevented||Y(f??document.body,{select:!0}),l.removeEventListener(We,c),kt.remove(m)},0)}}},[l,d,c,m]);const v=s.useCallback(f=>{if(!n&&!o||m.paused)return;const A=f.key==="Tab"&&!f.altKey&&!f.ctrlKey&&!f.metaKey,C=document.activeElement;if(A&&C){const w=f.currentTarget,[S,b]=xi(w);S&&b?!f.shiftKey&&C===b?(f.preventDefault(),n&&Y(S,{select:!0})):f.shiftKey&&C===S&&(f.preventDefault(),n&&Y(b,{select:!0})):C===w&&f.preventDefault()}},[n,o,m.paused]);return s.createElement(G.div,E({tabIndex:-1},i,{ref:h,onKeyDown:v}))});function $i(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(Y(o,{select:t}),document.activeElement!==n)return}function xi(e){const t=to(e),n=Dt(t,e),o=Dt(t.reverse(),e);return[n,o]}function to(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Dt(e,t){for(const n of e)if(!Ei(n,{upTo:t}))return n}function Ei(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ii(e){return e instanceof HTMLInputElement&&"select"in e}function Y(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Ii(e)&&t&&e.select()}}const kt=Fi();function Fi(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Ot(e,t),e.unshift(t)},remove(t){var n;e=Ot(e,t),(n=e[0])===null||n===void 0||n.resume()}}}function Ot(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function Pi(e){return e.filter(t=>t.tagName!=="A")}let Ue=0;function Mi(){s.useEffect(()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=n[0])!==null&&e!==void 0?e:_t()),document.body.insertAdjacentElement("beforeend",(t=n[1])!==null&&t!==void 0?t:_t()),Ue++,()=>{Ue===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),Ue--}},[])}function _t(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var z=function(){return z=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},z.apply(this,arguments)};function no(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function Ti(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,a;o<r;o++)(a||!(o in t))&&(a||(a=Array.prototype.slice.call(t,0,o)),a[o]=t[o]);return e.concat(a||Array.prototype.slice.call(t))}var $e="right-scroll-bar-position",xe="width-before-scroll-bar",Gi="with-scroll-bars-hidden",Vi="--removed-body-scroll-bar-size";function Ni(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Di(e,t){var n=s.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}function ki(e,t){return Di(t||null,function(n){return e.forEach(function(o){return Ni(o,n)})})}function Oi(e){return e}function _i(e,t){t===void 0&&(t=Oi);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var i=t(a,o);return n.push(i),function(){n=n.filter(function(l){return l!==i})}},assignSyncMedium:function(a){for(o=!0;n.length;){var i=n;n=[],i.forEach(a)}n={push:function(l){return a(l)},filter:function(){return n}}},assignMedium:function(a){o=!0;var i=[];if(n.length){var l=n;n=[],l.forEach(a),i=n}var u=function(){var c=i;i=[],c.forEach(a)},d=function(){return Promise.resolve().then(u)};d(),n={push:function(c){i.push(c),d()},filter:function(c){return i=i.filter(c),n}}}};return r}function ji(e){e===void 0&&(e={});var t=_i(null);return t.options=z({async:!0,ssr:!1},e),t}var oo=function(e){var t=e.sideCar,n=no(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return s.createElement(o,z({},n))};oo.isSideCarExport=!0;function Li(e,t){return e.useMedium(t),oo}var ro=ji(),Ye=function(){},Ge=s.forwardRef(function(e,t){var n=s.useRef(null),o=s.useState({onScrollCapture:Ye,onWheelCapture:Ye,onTouchMoveCapture:Ye}),r=o[0],a=o[1],i=e.forwardProps,l=e.children,u=e.className,d=e.removeScrollBar,c=e.enabled,g=e.shards,h=e.sideCar,m=e.noIsolation,v=e.inert,f=e.allowPinchZoom,A=e.as,C=A===void 0?"div":A,w=no(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),S=h,b=ki([n,t]),x=z(z({},w),r);return s.createElement(s.Fragment,null,c&&s.createElement(S,{sideCar:ro,removeScrollBar:d,shards:g,noIsolation:m,inert:v,setCallbacks:a,allowPinchZoom:!!f,lockRef:n}),i?s.cloneElement(s.Children.only(l),z(z({},x),{ref:b})):s.createElement(C,z({},x,{className:u,ref:b}),l))});Ge.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ge.classNames={fullWidth:xe,zeroRight:$e};var jt,Hi=function(){if(jt)return jt;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Bi(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Hi();return t&&e.setAttribute("nonce",t),e}function zi(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Wi(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Ui=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Bi())&&(zi(t,n),Wi(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Yi=function(){var e=Ui();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ao=function(){var e=Yi(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},Zi={left:0,top:0,right:0,gap:0},Ze=function(e){return parseInt(e||"",10)||0},qi=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[Ze(n),Ze(o),Ze(r)]},Ji=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Zi;var t=qi(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Ki=ao(),Xi=function(e,t,n,o){var r=e.left,a=e.top,i=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Gi,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(l,"px ").concat(o,`;
  }
  body {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat($e,` {
    right: `).concat(l,"px ").concat(o,`;
  }
  
  .`).concat(xe,` {
    margin-right: `).concat(l,"px ").concat(o,`;
  }
  
  .`).concat($e," .").concat($e,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(xe," .").concat(xe,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body {
    `).concat(Vi,": ").concat(l,`px;
  }
`)},Qi=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o,a=s.useMemo(function(){return Ji(r)},[r]);return s.createElement(Ki,{styles:Xi(a,!t,r,n?"":"!important")})},lt=!1;if(typeof window<"u")try{var Ce=Object.defineProperty({},"passive",{get:function(){return lt=!0,!0}});window.addEventListener("test",Ce,Ce),window.removeEventListener("test",Ce,Ce)}catch{lt=!1}var te=lt?{passive:!1}:!1,el=function(e){return e.tagName==="TEXTAREA"},io=function(e,t){var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!el(e)&&n[t]==="visible")},tl=function(e){return io(e,"overflowY")},nl=function(e){return io(e,"overflowX")},Lt=function(e,t){var n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var o=lo(e,n);if(o){var r=so(e,n),a=r[1],i=r[2];if(a>i)return!0}n=n.parentNode}while(n&&n!==document.body);return!1},ol=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},rl=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},lo=function(e,t){return e==="v"?tl(t):nl(t)},so=function(e,t){return e==="v"?ol(t):rl(t)},al=function(e,t){return e==="h"&&t==="rtl"?-1:1},il=function(e,t,n,o,r){var a=al(e,window.getComputedStyle(t).direction),i=a*o,l=n.target,u=t.contains(l),d=!1,c=i>0,g=0,h=0;do{var m=so(e,l),v=m[0],f=m[1],A=m[2],C=f-A-a*v;(v||C)&&lo(e,l)&&(g+=C,h+=v),l=l.parentNode}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return(c&&(r&&g===0||!r&&i>g)||!c&&(r&&h===0||!r&&-i>h))&&(d=!0),d},be=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ht=function(e){return[e.deltaX,e.deltaY]},Bt=function(e){return e&&"current"in e?e.current:e},ll=function(e,t){return e[0]===t[0]&&e[1]===t[1]},sl=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ul=0,ne=[];function cl(e){var t=s.useRef([]),n=s.useRef([0,0]),o=s.useRef(),r=s.useState(ul++)[0],a=s.useState(function(){return ao()})[0],i=s.useRef(e);s.useEffect(function(){i.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var f=Ti([e.lockRef.current],(e.shards||[]).map(Bt),!0).filter(Boolean);return f.forEach(function(A){return A.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),f.forEach(function(A){return A.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(f,A){if("touches"in f&&f.touches.length===2)return!i.current.allowPinchZoom;var C=be(f),w=n.current,S="deltaX"in f?f.deltaX:w[0]-C[0],b="deltaY"in f?f.deltaY:w[1]-C[1],x,P=f.target,F=Math.abs(S)>Math.abs(b)?"h":"v";if("touches"in f&&F==="h"&&P.type==="range")return!1;var T=Lt(F,P);if(!T)return!0;if(T?x=F:(x=F==="v"?"h":"v",T=Lt(F,P)),!T)return!1;if(!o.current&&"changedTouches"in f&&(S||b)&&(o.current=x),!x)return!0;var k=o.current||x;return il(k,A,f,k==="h"?S:b,!0)},[]),u=s.useCallback(function(f){var A=f;if(!(!ne.length||ne[ne.length-1]!==a)){var C="deltaY"in A?Ht(A):be(A),w=t.current.filter(function(x){return x.name===A.type&&x.target===A.target&&ll(x.delta,C)})[0];if(w&&w.should){A.cancelable&&A.preventDefault();return}if(!w){var S=(i.current.shards||[]).map(Bt).filter(Boolean).filter(function(x){return x.contains(A.target)}),b=S.length>0?l(A,S[0]):!i.current.noIsolation;b&&A.cancelable&&A.preventDefault()}}},[]),d=s.useCallback(function(f,A,C,w){var S={name:f,delta:A,target:C,should:w};t.current.push(S),setTimeout(function(){t.current=t.current.filter(function(b){return b!==S})},1)},[]),c=s.useCallback(function(f){n.current=be(f),o.current=void 0},[]),g=s.useCallback(function(f){d(f.type,Ht(f),f.target,l(f,e.lockRef.current))},[]),h=s.useCallback(function(f){d(f.type,be(f),f.target,l(f,e.lockRef.current))},[]);s.useEffect(function(){return ne.push(a),e.setCallbacks({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:h}),document.addEventListener("wheel",u,te),document.addEventListener("touchmove",u,te),document.addEventListener("touchstart",c,te),function(){ne=ne.filter(function(f){return f!==a}),document.removeEventListener("wheel",u,te),document.removeEventListener("touchmove",u,te),document.removeEventListener("touchstart",c,te)}},[]);var m=e.removeScrollBar,v=e.inert;return s.createElement(s.Fragment,null,v?s.createElement(a,{styles:sl(r)}):null,m?s.createElement(Qi,{gapMode:"margin"}):null)}const dl=Li(ro,cl);var uo=s.forwardRef(function(e,t){return s.createElement(Ge,z({},e,{ref:t,sideCar:dl}))});uo.classNames=Ge.classNames;const gl=uo,co=s.forwardRef((e,t)=>{const{children:n,...o}=e,r=s.Children.toArray(n),a=r.find(pl);if(a){const i=a.props.children,l=r.map(u=>u===a?s.Children.count(i)>1?s.Children.only(null):s.isValidElement(i)?i.props.children:null:u);return s.createElement(st,E({},o,{ref:t}),s.isValidElement(i)?s.cloneElement(i,void 0,l):null)}return s.createElement(st,E({},o,{ref:t}),n)});co.displayName="Slot";const st=s.forwardRef((e,t)=>{const{children:n,...o}=e;return s.isValidElement(n)?s.cloneElement(n,{...hl(o,n.props),ref:t?cn(t,n.ref):n.ref}):s.Children.count(n)>1?s.Children.only(null):null});st.displayName="SlotClone";const fl=({children:e})=>s.createElement(s.Fragment,null,e);function pl(e){return s.isValidElement(e)&&e.type===fl}function hl(e,t){const n={...t};for(const o in t){const r=e[o],a=t[o];/^on[A-Z]/.test(o)?r&&a?n[o]=(...l)=>{a(...l),r(...l)}:r&&(n[o]=r):o==="style"?n[o]={...r,...a}:o==="className"&&(n[o]=[r,a].filter(Boolean).join(" "))}return{...e,...n}}const go="Dialog",[fo,ds]=de(go),[ml,H]=fo(go),vl=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:a,modal:i=!0}=e,l=s.useRef(null),u=s.useRef(null),[d=!1,c]=oe({prop:o,defaultProp:r,onChange:a});return s.createElement(ml,{scope:t,triggerRef:l,contentRef:u,contentId:se(),titleId:se(),descriptionId:se(),open:d,onOpenChange:c,onOpenToggle:s.useCallback(()=>c(g=>!g),[c]),modal:i},n)},Al="DialogTrigger",Cl=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=H(Al,n),a=X(t,r.triggerRef);return s.createElement(G.button,E({type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":Rt(r.open)},o,{ref:a,onClick:N(e.onClick,r.onOpenToggle)}))}),po="DialogPortal",[bl,ho]=fo(po,{forceMount:void 0}),wl=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,a=H(po,t);return s.createElement(bl,{scope:t,forceMount:n},s.Children.map(o,i=>s.createElement(Pe,{present:n||a.open},s.createElement(sr,{asChild:!0,container:r},i))))},ut="DialogOverlay",Sl=s.forwardRef((e,t)=>{const n=ho(ut,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,a=H(ut,e.__scopeDialog);return a.modal?s.createElement(Pe,{present:o||a.open},s.createElement(Rl,E({},r,{ref:t}))):null}),Rl=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=H(ut,n);return s.createElement(gl,{as:co,allowPinchZoom:!0,shards:[r.contentRef]},s.createElement(G.div,E({"data-state":Rt(r.open)},o,{ref:t,style:{pointerEvents:"auto",...o.style}})))}),ce="DialogContent",yl=s.forwardRef((e,t)=>{const n=ho(ce,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,a=H(ce,e.__scopeDialog);return s.createElement(Pe,{present:o||a.open},a.modal?s.createElement($l,E({},r,{ref:t})):s.createElement(xl,E({},r,{ref:t})))}),$l=s.forwardRef((e,t)=>{const n=H(ce,e.__scopeDialog),o=s.useRef(null),r=X(t,n.contentRef,o);return s.useEffect(()=>{const a=o.current;if(a)return Rr(a)},[]),s.createElement(mo,E({},e,{ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:N(e.onCloseAutoFocus,a=>{var i;a.preventDefault(),(i=n.triggerRef.current)===null||i===void 0||i.focus()}),onPointerDownOutside:N(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,l=i.button===0&&i.ctrlKey===!0;(i.button===2||l)&&a.preventDefault()}),onFocusOutside:N(e.onFocusOutside,a=>a.preventDefault())}))}),xl=s.forwardRef((e,t)=>{const n=H(ce,e.__scopeDialog),o=s.useRef(!1),r=s.useRef(!1);return s.createElement(mo,E({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var i;if((i=e.onCloseAutoFocus)===null||i===void 0||i.call(e,a),!a.defaultPrevented){var l;o.current||(l=n.triggerRef.current)===null||l===void 0||l.focus(),a.preventDefault()}o.current=!1,r.current=!1},onInteractOutside:a=>{var i,l;(i=e.onInteractOutside)===null||i===void 0||i.call(e,a),a.defaultPrevented||(o.current=!0,a.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const u=a.target;((l=n.triggerRef.current)===null||l===void 0?void 0:l.contains(u))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&r.current&&a.preventDefault()}}))}),mo=s.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:a,...i}=e,l=H(ce,n),u=s.useRef(null),d=X(t,u);return Mi(),s.createElement(s.Fragment,null,s.createElement(yi,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:a},s.createElement(Ko,E({role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":Rt(l.open)},i,{ref:d,onDismiss:()=>l.onOpenChange(!1)}))),!1)}),El="DialogTitle",Il=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=H(El,n);return s.createElement(G.h2,E({id:r.titleId},o,{ref:t}))}),Fl="DialogDescription",Pl=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=H(Fl,n);return s.createElement(G.p,E({id:r.descriptionId},o,{ref:t}))}),Ml="DialogClose",Tl=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=H(Ml,n);return s.createElement(G.button,E({type:"button"},o,{ref:t,onClick:N(e.onClick,()=>r.onOpenChange(!1))}))});function Rt(e){return e?"open":"closed"}const Gl=vl,Vl=Cl,Nl=wl,vo=Sl,Ao=yl,Co=Il,bo=Pl,Dl=Tl;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var kl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),_l=(e,t)=>{const n=s.forwardRef(({color:o="currentColor",size:r=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:l="",children:u,...d},c)=>s.createElement("svg",{ref:c,...kl,width:r,height:r,stroke:o,strokeWidth:i?Number(a)*24/Number(r):a,className:["lucide",`lucide-${Ol(e)}`,l].join(" "),...d},[...t.map(([g,h])=>s.createElement(g,h)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jl=_l("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const Ll=Gl,Hl=Vl,Bl=Nl,wo=s.forwardRef(({className:e,...t},n)=>p(vo,{ref:n,className:M("fixed inset-0 z-50 bg-white/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 dark:bg-slate-950/80",e),...t}));wo.displayName=vo.displayName;const So=s.forwardRef(({className:e,children:t,...n},o)=>R(Bl,{children:[p(wo,{}),R(Ao,{ref:o,className:M("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-slate-200 bg-white p-6 shadow-lg duration-200 focus:outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] dark:border-slate-800 dark:bg-slate-950 sm:rounded-lg",e),...n,children:[t,R(Dl,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-slate-100 data-[state=open]:text-slate-500 dark:ring-offset-slate-950 dark:focus:ring-slate-300 dark:data-[state=open]:bg-slate-800 dark:data-[state=open]:text-slate-400",children:[p(jl,{className:"h-4 w-4"}),p("span",{className:"sr-only",children:"Close"})]})]})]}));So.displayName=Ao.displayName;const zl=s.forwardRef(({className:e,...t},n)=>p(Co,{ref:n,className:M("text-lg font-semibold leading-none tracking-tight",e),...t}));zl.displayName=Co.displayName;const Wl=s.forwardRef(({className:e,...t},n)=>p(bo,{ref:n,className:M("text-sm text-slate-500 dark:text-slate-400",e),...t}));Wl.displayName=bo.displayName;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Ul({dictionary:e}){const t=ht(gt),n=s.useRef(null),o=hn(n,{}),r=!!(o!=null&&o.isIntersecting),a=wi(e.id,r),i=s.useMemo(()=>Xo(e.length),[e.length]),l=t===e.id,u=s.useMemo(()=>a?Math.ceil(a.exercisedChapterCount/i*100):0,[a,i]);return R(Ll,{children:[p(Hl,{asChild:!0,children:p("div",{ref:n,className:`group flex  h-36 w-80 cursor-pointer items-center justify-center overflow-hidden rounded-lg p-4 text-left shadow-lg focus:outline-none ${l?"bg-indigo-400":"bg-zinc-50 hover:bg-white dark:bg-gray-800 dark:hover:bg-gray-700"}`,role:"button",children:R("div",{className:"relative ml-1 mt-2 flex h-full w-full flex-col items-start justify-start",children:[p("h1",{className:`mb-1.5 text-xl font-normal  ${l?"text-white":"text-gray-800 group-hover:text-indigo-400 dark:text-gray-200"}`,children:e.name}),p(Qt,{children:R(en,{delayDuration:400,children:[p(tn,{asChild:!0,children:p("p",{className:`mb-1 max-w-full truncate ${l?"text-white":"textdelayDuration-gray-600 dark:text-gray-200"} whitespace-nowrap`,children:e.description})}),p(nn,{children:p("p",{children:`${e.description}`})})]})}),R("p",{className:`mb-0.5 font-bold  ${l?"text-white":"text-gray-600 dark:text-gray-200"}`,children:[e.length," 词"]}),R("div",{className:" flex w-full items-center pt-2",children:[u>0&&p(on,{value:u,max:100,className:`mr-4 h-2 w-full rounded-full border  bg-white ${l?"border-indigo-600":"border-indigo-400"}`,children:p(rn,{className:`h-full rounded-full pl-0 ${l?"bg-indigo-600":"bg-indigo-400"}`,style:{width:`calc(${u}% )`}})}),p("img",{src:Ri,className:`absolute right-3 top-3 w-16 ${l?"opacity-50":"opacity-20"}`})]})]})})}),p(So,{className:"w-[60rem] max-w-none !rounded-[20px]",children:p(bi,{dictionary:e})})]})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Yl({groupedDictsByTag:e}){const t=s.useMemo(()=>Object.keys(e),[e]),[n,o]=s.useState(t[0]),r=ht(dn),a=s.useCallback(i=>{o(i)},[]);return s.useEffect(()=>{const i=Qo(t,r.tags);i.length>0&&o(i[0])},[r.tags,t]),R("div",{children:[p(yr,{tagList:t,currentTag:n,onChangeCurrentTag:a}),p("div",{className:"mt-8 grid gap-x-5 gap-y-10 px-1 pb-4 sm:grid-cols-1 md:grid-cols-2 dic3:grid-cols-3 dic4:grid-cols-4",children:e[n].map(i=>p(Ul,{dictionary:i},i.id))})]})}const Zl=e=>p("svg",{viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...e,children:R("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,children:[p("path",{d:"M19 4v16H7a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z"}),p("path",{d:"M19 16H7a2 2 0 0 0-2 2M9 8h6"})]})}),ql=Zl;globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function Jl(){const[e,t]=s.useState(!1),n=s.useCallback(()=>{t(!0)},[]),o=s.useCallback(()=>{t(!1)},[]);return R(Fe,{children:[e&&R(er,{openState:e,title:"申请词典",icon:ql,buttonClassName:"bg-indigo-500 hover:bg-indigo-400",iconClassName:"text-indigo-500 bg-indigo-100 dark:text-indigo-300 dark:bg-indigo-500",onClose:o,children:[R("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:["如果您有相关的编程基础，可以参考",p("a",{href:"https://github.com/Kaiyiwing/qwerty-learner/blob/master/docs/toBuildDict.md",className:"px-2 text-blue-500",target:"_blank",rel:"noreferrer",children:"导入词典"}),"，给项目贡献新的词典。",p("br",{}),p("br",{}),"如果您没有相关的编程基础，可以将您的字典需求发送邮件到"," ",p("a",{href:"mailto:<EMAIL>",className:"px-2 text-blue-500","aria-label":"发送邮件到 <EMAIL>",children:"<EMAIL>"}),"，或者在网页底部添加我们的用户社群进行反馈。"]}),p("br",{})]}),p("button",{className:"cursor-pointer pr-6 text-sm text-indigo-500",onClick:n,children:"没有找到想要的词典？"})]})}const Kl="data:image/png;base64,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",Xl="data:image/png;base64,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",Ql="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAARGVYSWZNTQAqAAAACAABh2kABAAAAAEAAAAaAAAAAAADoAEAAwAAAAEAAQAAoAIABAAAAAEAAAAmoAMABAAAAAEAAAAmAAAAAITyIFsAAAGbaVRYdFhNTDpjb20uYWRvYmUueG1wAAAAAAA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJYTVAgQ29yZSA2LjAuMCI+CiAgIDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+CiAgICAgIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiCiAgICAgICAgICAgIHhtbG5zOmV4aWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20vZXhpZi8xLjAvIj4KICAgICAgICAgPGV4aWY6UGl4ZWxYRGltZW5zaW9uPjM4PC9leGlmOlBpeGVsWERpbWVuc2lvbj4KICAgICAgICAgPGV4aWY6UGl4ZWxZRGltZW5zaW9uPjM4PC9leGlmOlBpeGVsWURpbWVuc2lvbj4KICAgICAgPC9yZGY6RGVzY3JpcHRpb24+CiAgIDwvcmRmOlJERj4KPC94OnhtcG1ldGE+Cl+r3MQAAAskSURBVFjD7VdnVFTXFj4KyTN5McbETiKCQRGwUKSI6IiKYCGAomJQsDxrYi8BDA5Bg+gzT8WCoqBiwQGkl2GAGRi6CMwMAzggHSVKwBYjcpnvnTuATyMm+ZMfby3PWt/cWefec/a3y9lnb0LejXfj3fg7Bvpwudy+zs48NRYcDledw+Wq83g8NRag70BIn7+XAhXAChIKhepcrlCd0P9/dS0lry6k6PUlz9lZjQWAP9PgpRVYEuyG+AMSBw+eGbB5b6CO18FLU+3c/GyIztZ54eFRM7MqZCZCieRzKu8vK8BqoNK8S3tq/m4X/JEVQkJC+sWt3aErio523PPjJZ/V2wOjV24LLFr+zanGZZsCnuna+MJt43HwhbkolkmRdj0cEWs2tYWZ2MjCXdYGx4VcmtuLH9An4LOJrleWrZ5WA/T7M+KsFbb4h45NiYx2TN3m5ZOyaVds+qbdlSInt/YKgQhbDsbC3PEUbFzPYNK8n6iXd+PfxyNQ3dikvFMq7RTs9lBeo1Kjxluh4OIV3K2qQlqqoJFVrovOK14784NvyTVzW8Tt968SFxQIMjMzg8LD4/yNHff72H590G/DnsAT7ltPxrp+E1Dksv5408KtZ9uL0rJRvmID5A6rULpgBSR9J6E2WcQ477raMdL8YAcxOMCQz/Z1pmQUdz568lgpj41TRhGiDKcQBZ1XlsmknbV3G15Iy27jp2NB5ZTGm8QCgkNEeXFxiKTzWf5HUNvQgPqmRuTklmCzVyjIOF8Yzj+CGUuOw3D2YYxwOgGZILdTbjG7o2QChaU9IyEjlTXJIrjtvU6ttAcua87iTmMLnj1qQ8Ge7xFD98709UOlvBSVtTXgCzLhuTeQ0TD2pN+7KEZxOD3eejXOrcSxfBHk2VkveIQwqavWv7hTUdHxoK21o7G+oYN3I73D0t6/g5DtjLqud6fOogClVJADueUclBjbQWrlABnRQR0/A/orzuFwQAKeU9V/ratDzjBzZJDRqMoU4+eW+8jJu4Vt245QMisp1jMaJp4YbfwvBRk1qhdiH9mKCfkakfHpTKWkGAk6Jkggw3A7Oxf329rw+PFDNDbcBS9KDAu7wyAa+yFNyUWFlR0kRraQTXNAKdFFTXwaUvMq0dn+Ag8SkpFNrSTd442We/dQ23wfRwJ4lMwaCmfWShRuDCFbMERv1dssNlvcdwS74GvmVHAUamlAFnhzEc9uHHwR93++jyftz/Giox3NzS0Ii7+JCtFNlFvYQDZ5LuTWCyGh37bdvAV2PC4sQf1Zui4zB08fPUZrywOUFkqQliiEMCUDSYnpuMJLREBIDLPOKxjvGa1T9MTYa8SmL1grJuO/7dZgKha7+6OwuBTl13ngU4E50+ahNl2EtgcteP7bM5Xwp/UNkBJDyK3sUTbdkf7/klqJjye3K3E/JhHNkbGoPX0e0k07kDvTAVm2XyHTwQlpTs5IdV+JDA8PFAUFMeWCNISev0iJjXoz+I957xMnRyciLJLPnAiJxmbvc1jndR5iWTXuKRQoOXAY5f85iV+o0N/a2/H812doKyyGrL8pyqZSYmbzUGYxH2UTbKhLx9B4G4QSKqOQIo9oQzxwPFLpfDwZgWjSD/RkIoxNGTSeU+jz3BRrhTuX26/nFnlJzIsQcbTVDIg9PZnCwECU3oiCPD4REqp5dVY26oSZqIlJQGXgeVR4+KDMdR1khjaQT7brIvUqpjug3NoJco4DSqbMw02T2cieOB1CvSlIHjMZcdqTEK05Hjc0DZCga8akTbDCycF6ilE96eJVYt5DtMRXyWBWEyaCvoulSKZIo0inyGDTCEUuxU3qsmINc8iM50Bu+gox07kos/qKklrYRWzqAhTrc5A/YBLERJNabAjiuqyECKKG8L6aSNA2ZtIMOTitb6bgdJ/K14kN1xbztCYgRseYidMzRbK+OQT6FkjXt4R40nTkmMxEvtkcFFrYodhiLiSW81FK8dJipqwrF6BUcyqNtcHUlR+o4k/C+QrFi1eiYPla5LquQv72HchYvQZ8e0fEjjZgiTJJlEsAGargdJ/K14h5UldepnM3yCAmZrgBksZOhsBgCoQTp0FsxEGOsTXyTWbhpgEHtz43QxHRh+R9Gvislcy6SMn1Z6Jq4y7cDb2O1ux8PCy4hRZ6KpvzC/FLUxOq6u/iRqQAyQnpSKM5U5yQAklyClN++QpCV7i/PJWvETu2e7c49TsP8GfZMnE0OFmTJ1IIul2ZScHmpIKBRpAuckfFru9RudMbcmNbFTHWfVLyPh5SEj3jxZNf6akMVrk/z24RKjKyEJmUBTJ0M5W9BCYLvOC69QRzKjQJp05fUHDdOW+eSme37eKQ0GiIkoXMzWQBShOSUBEbh9IwHioionAnPgn3iiVokVegWZSFR8VSPKuuhWygGcosF6jShZxo4W5aFvJk9XjY+lhFjmEY3C0sQr7berAuk50LQnGRBJs9TlJycynW0fS0kc2fCo67e+9XEiFLQSZsY+a6H8TybSexad8F8MUlaH3yBE+VSrRUVaPQ3hVly1bjaV0DWhXVkL1nRK8le5rLWGJjUJ+cAaNVIbCYfxwxiYVo+eURGEqw7eFDSMIiVIEvWLgY8oJ8BJyPVOVNXcu9GGf2lgT7xVhHsYbehu4EuxQurj9CwBfjXnMzWlpbURUTr3Jp5X4/PGx+gBMReShJyobCej49nXYoVV1J2qhPyYSbVzjIF/vpXlxwFh0HLzIDdQ1NaHv6GIqiYsTZOOA63Usi4CMqWUzlbaXfr1Bwu/MYW5K/YjGOmAxbhy07jjFJSSLU0cu3hVYFDVV3UODBVcVaU0IiahruY83Oi1SoD+Rp+aiYagspjTOZ6hLXVl3iS/Zcwz8N/WBufxT9TFiCO2A25wAiaODXNTaiuvoO+D77cYHuKY+JZoS0giGfuvZusZ0794nTUjNQWVnJ1N9rootrUExji81lmVrGaC6RQFRQAWLoSwV9B/2lJ9myB+WWtqpLnK0upJQYrcewZPc1EIMflWNnHlHqzPDvnDjLjyFjPBi2kli15nBHZlY+U9NQz2RdvNRJLcdIr1zF1Ruxil7rseCLF8Slt8tRVl7OFMTGI9lpmSoeJEePorbyDk5c4lNC32LYZB8YTDuAvvOPQkqJlU2dwxJTUot1UmJMPT+jY+meax1k3A9KXWs/jJzCxWAjD4ww3IGxplvZIKdYjtOnQ1FIS+y8qGiGzQChW3eWCbur59eIHT90KDPl1BkkLl7+nNZjnekurkpFVpZSRM1st5ytnTYo9Tm+Sm2rA50axr7MAPtjHbLUvA6ppQ2TN4leOTTpCmnw54SGYd7aQyBDVuCLiW7tg8YsfjBAy778Qw1bsfpQ61StcXMEHwyfRi+R/rKFDm4tQZcvI+ZMEHwIaXL/XR5T/Wwk/XP96VywIc32585DJMrG3sOX2Nqpk5DNSj2rvRhENdcw9cJwQw8MtfFBwuVIXBs/GUe0JsH3y4n3PAZqZXm4rz47cdaSbwgxsh6uaT7uo6Gjh9Dt33uzzeCp0ccwCnNPvwP7fL/zuMwl5MM3iLkQMmGtk9MPHt97i1Zt92wixKadbg5NAxcM11uGD7UW45PRi54N/NKx6RMte8mneg4J367ZcGjd5yNdnQZoGul//PGnf9b2dZ22t3davbWOv58YaDBcZ9xMW0eLfp8ZWZNPjGf0H2Jq8Y8hBqPJgJED6fu+b2t6e9o9mrLVuqAi0uctoP2psxq3q9ntvZ91JkSNQ4j6X2h4e7rPvuyGXMJRZ9f+7a3+/7RhNe3Rulft34134/96/BfE1lOTQlAOnwAAAABJRU5ErkJggg==",es="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABYSURBVFhH7daxDQAgCAVRcEUXdyO0cIHrfnEvJFJeQWFP1Zs8679xDKMMowyjDKMMowyjDKMMo3r2jvzB9pwTGvb8PYrHTxlGGUYZRhlGGUYZRhlGhYZVXf2mDIUBFahTAAAAAElFTkSuQmCC",ts="data:image/png;base64,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",ns="data:image/png;base64,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";globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const os=[{id:"en",name:"英语",flag:Ql},{id:"ja",name:"日语",flag:ts},{id:"de",name:"德语",flag:Xl},{id:"kk",name:"哈萨克语",flag:ns},{id:"id",name:"印尼语",flag:es},{id:"code",name:"Code",flag:Kl}];function rs(){const{state:e,setState:t}=s.useContext(Ro),n=s.useCallback(o=>{t(r=>{r.currentLanguageTab=o})},[t]);return p(Ee,{value:e.currentLanguageTab,onChange:n,children:p("div",{className:"flex items-center space-x-4",children:os.map(o=>p(Ee.Option,{value:o.id,className:"cursor-pointer",children:({checked:r})=>R("div",{className:`flex items-center border-b-2 px-2 pb-1 ${r?"border-indigo-500":"border-transparent"}`,children:[p("img",{src:o.flag,className:"mr-1.5 h-7 w-7"}),p("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-200",children:o.name})]})},o.id))})})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};function as(e,t){return e.reduce((n,o)=>{const r=t(o);return Object.prototype.hasOwnProperty.call(n,r)?n[r].push(o):n[r]=[o],n},{})}function is(e){return e.reduce((t,n)=>(n.tags.forEach(o=>{Object.prototype.hasOwnProperty.call(t,o)?t[o].push(n):t[o]=[n]}),t),{})}globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};const ls={currentLanguageTab:"en"},Ro=s.createContext(null);function gs(){const[e,t]=tr(ls),n=dt(),o=ht(dn),{groupedByCategoryAndTag:r}=s.useMemo(()=>{const i=nr.filter(d=>d.languageCategory===e.currentLanguageTab);return{groupedByCategoryAndTag:Object.entries(as(i,d=>d.category)).map(([d,c])=>[d,is(c)])}},[e.currentLanguageTab]),a=s.useCallback(()=>{n("/")},[n]);return or("enter,esc",a,{preventDefault:!0}),s.useEffect(()=>{o&&t(i=>{i.currentLanguageTab=o.languageCategory})},[o,t]),p(rr,{children:p(Ro.Provider,{value:{state:e,setState:t},children:R("div",{className:"relative mb-auto mt-auto flex w-full flex-1 flex-col overflow-y-auto pl-20",children:[p(ar,{className:"absolute right-20 top-10 mr-2 h-7 w-7 cursor-pointer text-gray-400",onClick:a}),p("div",{className:"mt-20 flex w-full flex-1 flex-col items-center justify-center overflow-y-auto",children:R("div",{className:"flex h-full flex-col overflow-y-auto",children:[R("div",{className:"flex h-20 w-full items-center justify-between pb-6",children:[p(rs,{}),p(Jl,{})]}),R(ft,{className:"flex-1 overflow-y-auto",children:[R(sn,{className:"h-full w-full ",children:[p("div",{className:"mr-4 flex flex-1 flex-col items-start justify-start gap-14 overflow-y-auto",children:r.map(([i,l])=>p(Yl,{groupedDictsByTag:l},i))}),R("div",{className:"flex items-center justify-center pb-10 pt-[20rem] text-gray-500",children:[p(ir,{className:"mr-1 h-5 w-5"}),p("p",{className:"mr-5 w-10/12 text-xs",children:"本项目的词典数据来自多个开源项目以及社区贡献者的无偿提供。我们深感感激并尊重每一位贡献者的知识产权。 这些数据仅供个人学习和研究使用，严禁用于任何商业目的。如果你是数据的版权所有者，并且认为我们的使用方式侵犯了你的权利，请通过网站底部的电子邮件与我们联系。一旦收到有效的版权投诉，我们将在最短的时间内删除相关内容或寻求必要的许可。 同时，我们也鼓励所有使用这些数据的人尊重版权所有者的权利，并且在使用这些数据时遵守所有相关的法律和规定。 请注意，虽然我们尽力确保所有数据的合法性和准确性，但我们不能对任何数据的准确性、完整性、合法性或可靠性做出任何保证。使用这些数据的风险完全由用户自己承担。"})]})]}),p(lr,{className:"flex touch-none select-none bg-transparent ",orientation:"vertical"})]})]})})]})})})}export{Ro as GalleryContext,gs as default};
