package org.jeecg.modules.teaching.controller;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.common.controller.BaseController;
import org.jeecg.modules.teaching.entity.TeachingStudentCoinRecord;

import org.jeecg.modules.teaching.service.ITeachingStudentCoinRecordService;
import org.jeecg.modules.teaching.service.ITeachingStudentCoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * 学生金币控制器
 */
@Slf4j
@RestController
@RequestMapping("/teaching/coin")
@Api(tags = "学生金币控制器")
public class TeachingStudentCoinController extends BaseController {

    @Autowired
    private ITeachingStudentCoinService studentCoinService;
    
    @Autowired
    private ITeachingStudentCoinRecordService studentCoinRecordService;

    /**
     * 获取用户金币数
     */
    @GetMapping("/getUserCoin")
    public Result<?> getUserCoin() {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        return studentCoinService.getUserCoinCount(user.getId());
    }
    
    /**
     * 获取金币记录
     */
    @GetMapping("/getCoinRecords")
    public Result<?> getCoinRecords(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        return studentCoinRecordService.getUserCoinRecords(user.getId(), pageNo, pageSize);
    }
    
    /**
     * 获取兑换记录
     */
    @GetMapping("/getExchangeRecords")
    public Result<?> getExchangeRecords(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        return studentCoinRecordService.getUserExchangeRecords(user.getId(), pageNo, pageSize);
    }
    
    /**
     * 兑换礼品
     */
    @PostMapping("/exchangeGift")
    public Result<?> exchangeGift(@RequestBody Map<String, Object> params) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        
        String giftId = params.get("giftId").toString();
        String giftName = params.get("giftName").toString();
        Integer coinCount = Integer.parseInt(params.get("coinCount").toString());
        String description = "兑换礼品：" + giftName;
        
        return studentCoinService.consumeUserCoin(user.getId(), coinCount, description, giftId, giftName);
    }
    
    /**
     * 游戏消费金币
     */
    @AutoLog(value = "游戏消费金币")
    @ApiOperation(value = "游戏消费金币", notes = "游戏消费金币")
    @PostMapping("/consumeGameCoin")
    public Result<?> consumeGameCoin(@RequestBody Map<String, Object> params) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String gameTitle = params.get("gameTitle") != null ? params.get("gameTitle").toString() : "";
            Integer coinCount = params.get("coinCount") != null ? Integer.parseInt(params.get("coinCount").toString()) : 0;
            
            if (gameTitle.isEmpty() || coinCount <= 0) {
                return Result.error("参数错误");
            }
            
            String description = "游戏消费：" + gameTitle + "（10分钟）";
            // 游戏消费使用游戏名称作为relatedId，不创建礼品兑换记录
            return studentCoinService.consumeUserCoin(user.getId(), coinCount, description, "game_" + gameTitle, null);
        } catch (Exception e) {
            log.error("游戏消费金币失败", e);
            return Result.error("游戏消费金币失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户最近一个月的金币记录
     * 
     * @return
     */
    @AutoLog(value = "获取用户最近一个月金币记录")
    @ApiOperation(value = "获取用户最近一个月金币记录", notes = "获取用户最近一个月金币记录")
    @GetMapping(value = "/getRecentCoinRecords")
    public Result<?> getRecentCoinRecords() {
        // 获取当前登录用户ID
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = user.getId();
        
        // 计算一个月前的日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        Date oneMonthAgo = calendar.getTime();
        
        // 查询记录
        QueryWrapper<TeachingStudentCoinRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.ge("create_time", oneMonthAgo);
        queryWrapper.orderByDesc("create_time"); // 按创建时间倒序
        
        List<TeachingStudentCoinRecord> records = studentCoinRecordService.list(queryWrapper);
        
        return Result.ok(records);
    }
} 