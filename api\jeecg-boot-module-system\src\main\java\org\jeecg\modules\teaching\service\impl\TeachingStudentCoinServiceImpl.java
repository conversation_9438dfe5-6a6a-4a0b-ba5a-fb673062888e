package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingGiftExchange;
import org.jeecg.modules.teaching.entity.TeachingStudentCoin;
import org.jeecg.modules.teaching.entity.TeachingStudentCoinRecord;
import org.jeecg.modules.teaching.mapper.TeachingGiftExchangeMapper;
import org.jeecg.modules.teaching.mapper.TeachingStudentCoinMapper;
import org.jeecg.modules.teaching.mapper.TeachingStudentCoinRecordMapper;
import org.jeecg.modules.teaching.service.ITeachingStudentCoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 学生金币服务实现类
 */
@Service
public class TeachingStudentCoinServiceImpl extends ServiceImpl<TeachingStudentCoinMapper, TeachingStudentCoin> implements ITeachingStudentCoinService {

    @Autowired
    private TeachingStudentCoinRecordMapper coinRecordMapper;
    
    @Autowired
    private TeachingGiftExchangeMapper giftExchangeMapper;

    @Override
    public Result<Integer> getUserCoinCount(String userId) {
        Result<Integer> result = new Result<>();
        QueryWrapper<TeachingStudentCoin> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        TeachingStudentCoin studentCoin = this.getOne(queryWrapper);
        if (studentCoin == null) {
            // 如果没有记录，创建一条初始记录
            studentCoin = new TeachingStudentCoin();
            studentCoin.setUserId(userId);
            studentCoin.setCoinCount(0);
            studentCoin.setCreateTime(new Date());
            studentCoin.setUpdateTime(new Date());
            this.save(studentCoin);
            result.setResult(0);
        } else {
            result.setResult(studentCoin.getCoinCount());
        }
        return result;
    }

    @Override
    @Transactional
    public Result<Integer> addUserCoin(String userId, Integer coinCount, Integer source, String description, String relatedId) {
        Result<Integer> result = new Result<>();
        try {
            // 查询或创建用户金币记录
            QueryWrapper<TeachingStudentCoin> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            TeachingStudentCoin studentCoin = this.getOne(queryWrapper);
            if (studentCoin == null) {
                studentCoin = new TeachingStudentCoin();
                studentCoin.setUserId(userId);
                studentCoin.setCoinCount(coinCount);
                studentCoin.setCreateTime(new Date());
                studentCoin.setUpdateTime(new Date());
                this.save(studentCoin);
            } else {
                studentCoin.setCoinCount(studentCoin.getCoinCount() + coinCount);
                studentCoin.setUpdateTime(new Date());
                this.updateById(studentCoin);
            }

            // 创建金币增加记录
            TeachingStudentCoinRecord record = new TeachingStudentCoinRecord();
            record.setUserId(userId);
            record.setOperationType(1); // 1表示获取金币
            record.setCoinCount(coinCount);
            record.setDescription(description);
            record.setSource(source);
            record.setRelatedId(relatedId);
            record.setCreateTime(new Date());
            coinRecordMapper.insert(record);

            // 添加金币增加信息
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("coinCount", studentCoin.getCoinCount());
            resultMap.put("coinAdded", coinCount);
            resultMap.put("description", description);
            
            result.setSuccess(true);
            result.setResult(studentCoin.getCoinCount());
            result.setMessage("金币增加成功：" + description);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("金币增加失败：" + e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public Result<Integer> consumeUserCoin(String userId, Integer coinCount, String description, String giftId, String giftName) {
        Result<Integer> result = new Result<>();
        try {
            // 查询用户金币记录
            QueryWrapper<TeachingStudentCoin> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            TeachingStudentCoin studentCoin = this.getOne(queryWrapper);
            if (studentCoin == null || studentCoin.getCoinCount() < coinCount) {
                result.setSuccess(false);
                result.setMessage("金币不足，无法完成操作");
                return result;
            }

            // 更新用户金币
            studentCoin.setCoinCount(studentCoin.getCoinCount() - coinCount);
            studentCoin.setUpdateTime(new Date());
            this.updateById(studentCoin);

            // 创建金币消费记录
            TeachingStudentCoinRecord record = new TeachingStudentCoinRecord();
            record.setUserId(userId);
            record.setOperationType(2); // 2表示消费金币
            record.setCoinCount(coinCount);
            record.setDescription(description);
            
            // 判断是否为游戏消费
            boolean isGameConsumption = giftId != null && giftId.startsWith("game_");
            if (isGameConsumption) {
                record.setSource(6); // 6表示游戏消费
            } else {
            record.setSource(5); // 5表示礼物兑换
            }
            
            record.setRelatedId(giftId);
            record.setCreateTime(new Date());
            coinRecordMapper.insert(record);

            // 如果是礼品兑换，则创建礼品兑换记录
            if (!isGameConsumption && giftName != null) {
            TeachingGiftExchange giftExchange = new TeachingGiftExchange();
            giftExchange.setUserId(userId);
            giftExchange.setGiftId(giftId);
            giftExchange.setGiftName(giftName);
            giftExchange.setCoinCount(coinCount);
            giftExchange.setExchangeTime(new Date());
            giftExchange.setStatus(0); // 待领取
            giftExchangeMapper.insert(giftExchange);
                
                result.setMessage("礼物兑换成功");
            } else {
                result.setMessage("金币消费成功");
            }

            result.setResult(studentCoin.getCoinCount());
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("操作失败：" + e.getMessage());
        }
        return result;
    }
} 