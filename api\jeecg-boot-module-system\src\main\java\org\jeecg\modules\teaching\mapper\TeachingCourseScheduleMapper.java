package org.jeecg.modules.teaching.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.vo.CourseScheduleVO;

import java.util.List;

/**
 * @Description: 课程排期
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface TeachingCourseScheduleMapper extends BaseMapper<TeachingCourseSchedule> {
    /**
     * 获取课程排期列表
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 课程排期列表
     */
    List<CourseScheduleVO> getScheduleList(Page<CourseScheduleVO> page, @Param("ew") QueryWrapper<TeachingCourseSchedule> queryWrapper);
    
    /**
     * 检查时间冲突
     * @param schedule 排期信息
     * @return 冲突数量
     */
    Integer checkConflict(@Param("schedule") TeachingCourseSchedule schedule);
    
    /**
     * 检查班级冲突
     * @param schedule 排期信息
     * @return 冲突数量
     */
    Integer checkClassConflict(@Param("schedule") TeachingCourseSchedule schedule);
    
    /**
     * 检查教师冲突
     * @param schedule 排期信息
     * @return 冲突数量
     */
    Integer checkTeacherConflict(@Param("schedule") TeachingCourseSchedule schedule);
    
    /**
     * 检查教室冲突
     * @param schedule 排期信息
     * @return 冲突数量
     */
    Integer checkRoomConflict(@Param("schedule") TeachingCourseSchedule schedule);
} 