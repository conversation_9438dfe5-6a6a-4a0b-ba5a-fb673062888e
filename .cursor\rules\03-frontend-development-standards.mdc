---
alwaysApply: true
---
# 前端开发规范

## 项目结构规范

### 目录结构
```
web/src/
├── api/                    # API接口定义
│   ├── index.js           # 通用API
│   ├── teaching.js        # 教学模块API
│   └── examSystem.js      # 考试系统API
├── components/            # 公共组件
│   ├── jeecg/            # Jeecg框架组件
│   └── teaching/         # 教学业务组件
├── views/                # 页面视图
│   ├── examSystem/       # 考试系统页面
│   ├── teaching/         # 教学管理页面
│   └── system/           # 系统管理页面
├── utils/                # 工具函数
├── store/                # Vuex状态管理
└── router/               # 路由配置
```

## Vue组件规范

### 组件命名
- **页面组件**: PascalCase (如: `ProblemManage.vue`)
- **业务组件**: PascalCase (如: `QuestionForm.vue`)
- **基础组件**: 以Base开头 (如: `BaseTable.vue`)

### 组件结构模板
```vue
<template>
  <div class="problem-manage">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="题目标题">
              <a-input placeholder="请输入题目标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('题目')">导出</a-button>
      <a-button type="primary" icon="import" @click="handleImportExcel">导入</a-button>
    </div>

    <!-- 表格区域 -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">
        
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        
        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </template>
      </a-table>
    </div>

    <!-- 表单弹窗 -->
    <problem-modal ref="modalForm" @ok="modalFormOk"></problem-modal>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ProblemModal from './modules/ProblemModal'

export default {
  name: 'ProblemManage',
  mixins: [JeecgListMixin],
  components: {
    ProblemModal
  },
  data() {
    return {
      description: '题库管理页面',
      // 表格列定义
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '题目标题',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '题目类型',
          align: 'center',
          dataIndex: 'questionType'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/teaching/examQuestion/list',
        delete: '/teaching/examQuestion/delete',
        deleteBatch: '/teaching/examQuestion/deleteBatch',
        exportXlsUrl: '/teaching/examQuestion/exportXls',
        importExcelUrl: '/teaching/examQuestion/importExcel'
      }
    }
  },
  methods: {
    // 自定义方法
    handleCustomAction() {
      // 业务逻辑
    }
  }
}
</script>

<style scoped>
.problem-manage {
  padding: 24px;
}
</style>
```

## API接口规范

### API文件结构
```javascript
// api/examSystem.js
import { axios } from '@/utils/request'

const api = {
  // 题库管理
  question: {
    list: '/teaching/examQuestion/list',
    add: '/teaching/examQuestion/add',
    edit: '/teaching/examQuestion/edit',
    delete: '/teaching/examQuestion/delete',
    deleteBatch: '/teaching/examQuestion/deleteBatch',
    exportXls: '/teaching/examQuestion/exportXls',
    importExcel: '/teaching/examQuestion/importExcel',
    autoFormat: '/teaching/examQuestion/autoFormatTemplate'
  }
}

// 题库管理API
export const getQuestionList = (params) => {
  return axios({
    url: api.question.list,
    method: 'get',
    params
  })
}

export const addQuestion = (data) => {
  return axios({
    url: api.question.add,
    method: 'post',
    data
  })
}

export const autoFormatTemplate = (formData) => {
  return axios({
    url: api.question.autoFormat,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export default api
```

### 请求响应处理
```javascript
// 标准请求处理
async handleSubmit() {
  try {
    this.loading = true
    const res = await addQuestion(this.form)
    if (res.success) {
      this.$message.success('操作成功')
      this.handleCancel()
      this.$emit('ok')
    } else {
      this.$message.warning(res.message || '操作失败')
    }
  } catch (error) {
    console.error('操作失败:', error)
    this.$message.error('操作失败，请重试')
  } finally {
    this.loading = false
  }
}
```

## 表单组件规范

### Ant Design Vue下拉框(a-select)规范

#### 🚨 重要：下拉框placeholder不显示问题
**现象**: 下拉框初始状态不显示placeholder提示信息，需要点击清除按钮(×)后才显示
**原因**: 下拉框绑定的值为空字符串`''`而不是`undefined`或`null`

**问题示例**:
```javascript
// ❌ 错误：空字符串会被认为是有效值，不显示placeholder
data() {
  return {
    queryParam: {
      subject: '',      // 空字符串
      difficulty: null  // null值
    }
  }
}
```

**解决方案**:
```javascript
// ✅ 正确：使用undefined确保placeholder正确显示
data() {
  return {
    queryParam: {
      subject: undefined,    // 使用undefined
      difficulty: undefined  // 使用undefined
    }
  }
}
```

**完整的下拉框配置**:
```vue
<template>
  <a-select
    v-model="queryParam.subject"
    placeholder="请选择科目"
    allowClear
  >
    <a-select-option value="Scratch">Scratch</a-select-option>
    <a-select-option value="Python">Python</a-select-option>
    <a-select-option value="C++">C++</a-select-option>
  </a-select>
</template>
```

**关键要点**:
- 初始值必须使用`undefined`而不是空字符串`''`
- 必须添加`allowClear`属性以提供清除功能
- placeholder应该提供清晰的使用示例
- 重置表单时也要将值设为`undefined`

### 表单验证
```javascript
data() {
  return {
    form: {},
    validatorRules: {
      title: [
        { required: true, message: '请输入题目标题!' },
        { min: 2, max: 100, message: '标题长度在2到100个字符之间!' }
      ],
      questionType: [
        { required: true, message: '请选择题目类型!' }
      ]
    }
  }
}
```

### 表单提交
```javascript
methods: {
  handleOk() {
    const that = this
    // 触发表单验证
    this.$refs.form.validate(valid => {
      if (valid) {
        that.confirmLoading = true
        let httpurl = ''
        let method = ''
        if (!this.model.id) {
          httpurl = this.url.add
          method = 'post'
        } else {
          httpurl = this.url.edit
          method = 'put'
        }
        httpAction(httpurl, this.model, method).then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
          that.confirmLoading = false
        })
      }
    })
  }
}
```

## 状态管理规范

### Vuex模块结构
```javascript
// store/modules/examSystem.js
const state = {
  questionList: [],
  currentQuestion: null,
  loading: false
}

const mutations = {
  SET_QUESTION_LIST(state, list) {
    state.questionList = list
  },
  SET_CURRENT_QUESTION(state, question) {
    state.currentQuestion = question
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

const actions = {
  async fetchQuestionList({ commit }, params) {
    commit('SET_LOADING', true)
    try {
      const res = await getQuestionList(params)
      if (res.success) {
        commit('SET_QUESTION_LIST', res.result.records)
      }
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

## 样式规范

### CSS命名规范
- 使用BEM命名法
- 组件样式使用scoped
- 公共样式放在全局样式文件中

### 样式示例
```vue
<style scoped>
.problem-manage {
  padding: 24px;
}

.problem-manage__header {
  margin-bottom: 16px;
}

.problem-manage__table {
  background: #fff;
}

.problem-manage__form {
  padding: 24px;
}

.problem-manage__form--loading {
  opacity: 0.6;
}
</style>
```

## 性能优化规范

### 组件懒加载
```javascript
// 路由懒加载
const ProblemManage = () => import('@/views/examSystem/ProblemManage')

// 组件懒加载
components: {
  ProblemModal: () => import('./modules/ProblemModal')
}
```

### 列表优化
```javascript
// 使用虚拟滚动处理大量数据
// 合理使用分页
// 避免在模板中使用复杂计算
```

## JavaScript语法兼容性规范

### 🚨 禁用的ES2020+语法特性

#### 可选链操作符 (Optional Chaining)
**禁止使用**: `?.` 可选链操作符在当前项目的Babel配置中不被支持

**问题示例**:
```javascript
// ❌ 错误：使用可选链操作符会导致编译失败
this.selectedFile?.name || '未知'
error.response?.status === 401
```

**解决方案**:
```javascript
// ✅ 正确：使用传统的逻辑与操作符
(this.selectedFile && this.selectedFile.name) || '未知'
error.response && error.response.status === 401

// ✅ 正确：使用条件判断
if (this.selectedFile) {
  return this.selectedFile.name
}
return '未知'
```

#### 空值合并操作符 (Nullish Coalescing)
**禁止使用**: `??` 空值合并操作符同样不被支持

**问题示例**:
```javascript
// ❌ 错误：使用空值合并操作符
const value = data.field ?? 'default'
```

**解决方案**:
```javascript
// ✅ 正确：使用传统的逻辑或操作符
const value = data.field !== null && data.field !== undefined ? data.field : 'default'
// 或者
const value = data.field || 'default' // 注意：这会将falsy值也替换为默认值
```

**关键要点**:
- 项目使用较老的Babel配置，不支持ES2020+的新语法特性
- 必须使用ES2015-ES2019的语法特性
- 在编写代码时优先考虑浏览器兼容性
- 如需使用新语法，需要先升级Babel配置

## 错误处理规范

### 全局错误处理
```javascript
// main.js
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  // 上报错误信息
}

// 请求拦截器错误处理
axios.interceptors.response.use(
  response => response,
  error => {
    // ✅ 正确：使用传统语法检查响应状态
    if (error.response && error.response.status === 401) {
      // 跳转登录页
    }
    return Promise.reject(error)
  }
)
```
