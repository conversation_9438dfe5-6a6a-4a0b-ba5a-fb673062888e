package org.jeecg.modules.teaching.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.dto.TestJudgeRequestDTO;
import org.jeecg.modules.teaching.service.ITeachingJudgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 判题控制器
 */
@Api(tags = "判题服务")
@RestController
@RequestMapping("/teaching/judge")
@Slf4j
public class TeachingJudgeController {
    
    @Autowired
    private ITeachingJudgeService teachingJudgeService;
    
    /**
     * 测试判题
     */
    @ApiOperation(value = "测试判题", notes = "用于代码编辑器的测试运行功能")
    @PostMapping("/test")
    public Result<?> testJudge(@RequestBody TestJudgeRequestDTO request) {
        log.info("收到测试判题请求: {}", request);
        try {
            Map<String, Object> result = teachingJudgeService.testJudge(request);
            log.info("测试判题成功，返回结果: {}", result);
            return Result.ok((Object) result);
        } catch (Exception e) {
            log.error("测试判题失败", e);
            return Result.error("测试判题失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交判题
     */
    @ApiOperation(value = "提交判题", notes = "用于题目的正式提交")
    @PostMapping("/submit")
    public Result<?> submitTestJudge(@RequestBody TestJudgeRequestDTO request) {
        log.info("收到提交判题请求: {}", request);
        try {
            Map<String, Object> result = teachingJudgeService.submitTestJudge(request);
            log.info("提交判题成功，返回结果: {}", result);
            return Result.ok((Object) result);
        } catch (Exception e) {
            log.error("提交判题失败", e);
            return Result.error("提交判题失败: " + e.getMessage());
        }
    }

    /**
     * 获取判题结果
     */
    @ApiOperation(value = "获取判题结果", notes = "根据提交ID获取判题结果")
    @GetMapping("/result/{submissionId}")
    public Result<?> getTestJudgeResult(@PathVariable String submissionId) {
        log.info("收到获取判题结果请求，提交ID: {}", submissionId);
        try {
            Map<String, Object> result = teachingJudgeService.getTestJudgeResult(submissionId);
            // 不打印完整结果，可能包含大量代码和输出
            log.info("获取判题结果成功，状态: {}", result.get("status"));
            return Result.ok((Object) result);
        } catch (Exception e) {
            log.error("获取判题结果失败", e);
            return Result.error("获取判题结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取在线自测的判题结果
     */
    @ApiOperation(value = "获取在线自测的判题结果", notes = "根据测试判题Key获取结果")
    @GetMapping("/self-test-result")
    public Result<?> getSelfTestResult(@RequestParam String testJudgeKey) {
        log.info("收到获取在线自测结果请求，testJudgeKey: {}", testJudgeKey);
        try {
            Map<String, Object> result = teachingJudgeService.getSelfTestJudgeResult(testJudgeKey);
            log.info("获取在线自测结果成功，结果: {}", result);
            if (result != null) {
                log.info("获取在线自测结果成功，状态: {}", result.get("status"));
            } else {
                log.info("获取在线自测结果为空");
            }
            return Result.ok((Object) result);
        } catch (Exception e) {
            log.error("获取在线自测结果失败", e);
            return Result.error("获取在线自测结果失败: " + e.getMessage());
        }
    }
} 
 