---
alwaysApply: true
---
# 全栈开发一致性规范

## 后端调试代码测试流程

### 自动查看后端终端规则
**应用场景**：当AI助手添加了后端调试代码（如log.info、log.warn、log.debug等）后，用户进行测试时

**执行规则**：
1. **自动主动查看**：AI助手必须自动查看活跃后端终端，无需用户提醒
2. **查看时机**：用户提到"已测试"、"测试完成"、"测试结果"等关键词时
3. **分析内容**：重点关注新添加的调试日志输出，分析测试结果
4. **反馈用户**：基于终端日志分析问题并提供解决方案

### 调试代码添加规范
- 添加调试代码时要告知用户将会查看终端日志
- 调试日志要包含关键信息（参数值、执行结果、错误信息等）
- 使用合适的日志级别（INFO用于关键流程，WARN用于异常情况，DEBUG用于详细调试）

### 示例场景
```java
// 添加调试代码
log.info("精确重复检测 - 题目信息: title=[{}], subject=[{}], level=[{}]",
    question.getTitle(), question.getSubject(), question.getLevel());
```

**用户测试后 → AI自动查看终端 → 分析日志 → 提供解决方案**

# 全栈一致性开发规范

## 概述

**全栈一致性开发**（Full-Stack Consistency Development）是一种确保前后端逻辑完全同步的开发模式，要求在修改任一端代码时，必须验证另一端的处理逻辑是否保持一致。

## 核心原则

### 1. 双向验证原则
- **修改前端 → 检查后端**：前端逻辑变更后，必须验证后端接收和处理逻辑
- **修改后端 → 检查前端**：后端逻辑变更后，必须验证前端调用和处理逻辑

### 2. 端到端一致性
- 数据流向的完整性验证
- 参数传递的准确性检查
- 错误处理的统一性确认
- 用户体验的连贯性保证

## 开发工作流程

### 阶段一：需求分析
1. **明确数据流向**：前端 → 后端 → 数据库 → 后端 → 前端
2. **定义接口契约**：参数格式、返回值结构、错误码定义
3. **确定验证点**：关键的数据传递和处理节点

### 阶段二：开发实施

#### 前端修改后的检查清单
- [ ] **参数传递检查**
  - 参数名称是否与后端接口一致
  - 参数类型是否匹配（String, Boolean, Integer等）
  - 必填/可选参数是否正确标识
  
- [ ] **请求格式检查**
  - HTTP方法是否正确（GET/POST/PUT/DELETE）
  - Content-Type是否匹配
  - FormData/JSON格式是否符合后端期望
  
- [ ] **响应处理检查**
  - 成功响应的数据结构解析
  - 错误响应的处理逻辑
  - 状态码的正确处理

#### 后端修改后的检查清单
- [ ] **接口定义检查**
  - @RequestParam注解的参数名和类型
  - @RequestBody的数据结构定义
  - 返回值的Result包装格式
  
- [ ] **业务逻辑检查**
  - 参数验证逻辑是否完整
  - 业务处理流程是否正确
  - 异常处理是否覆盖所有场景
  
- [ ] **响应格式检查**
  - 成功时的数据结构
  - 失败时的错误信息格式
  - 状态码的设置

### 阶段三：一致性验证

#### 数据流验证
```
前端发送 → 网络传输 → 后端接收 → 业务处理 → 数据存储
     ↓                                              ↑
前端接收 ← 网络传输 ← 后端响应 ← 结果封装 ← 数据查询
```

#### 关键验证点
1. **参数映射验证**
   ```javascript
   // 前端发送
   formData.append('allowReference', allowReference)
   
   // 后端接收
   @RequestParam(value = "allowReference") Boolean allowReference
   ```

2. **数据类型验证**
   ```javascript
   // 前端：Boolean类型
   this.confirmImport(true)
   
   // 后端：Boolean类型
   Boolean allowReference
   ```

3. **业务逻辑验证**
   ```javascript
   // 前端逻辑
   if (allowReference) {
     // 完整模式处理
   } else {
     // 新题模式处理
   }
   
   // 后端逻辑
   if (allowReference) {
     // 完整模式：引用重复题目
   } else {
     // 新题模式：跳过重复题目
   }
   ```

## 实施标准

### 1. 代码审查要求
- 每次提交必须包含前后端一致性检查说明
- Code Review时必须验证端到端逻辑
- 测试用例必须覆盖完整的数据流

### 2. 文档要求
- API文档必须与实际代码保持同步
- 前端组件文档必须说明后端依赖
- 数据流图必须反映真实的处理逻辑

### 3. 测试要求
- 单元测试：验证单个组件的逻辑正确性
- 集成测试：验证前后端接口的协作
- 端到端测试：验证完整用户流程

## 常见问题和解决方案

### 问题1：参数名不一致
```javascript
// ❌ 错误示例
// 前端发送：allowRef
// 后端接收：allowReference

// ✅ 正确示例
// 前端发送：allowReference
// 后端接收：allowReference
```

### 问题2：数据类型不匹配
```javascript
// ❌ 错误示例
// 前端发送：字符串 "true"
// 后端期望：布尔值 true

// ✅ 正确示例
// 前端发送：布尔值 true
// 后端接收：Boolean类型
```

### 问题3：错误处理不一致
```javascript
// ❌ 错误示例
// 后端返回：{ success: false, message: "错误" }
// 前端期望：{ error: true, msg: "错误" }

// ✅ 正确示例
// 后端返回：{ success: false, message: "错误" }
// 前端处理：if (!res.success) { this.$message.error(res.message) }
```

## 工具和技术

### 1. 开发工具
- **API文档工具**：Swagger/OpenAPI自动生成文档
- **类型检查**：TypeScript确保类型一致性
- **接口测试**：Postman/Insomnia验证API

### 2. 自动化验证
- **CI/CD流水线**：自动运行端到端测试
- **代码生成**：根据后端接口自动生成前端调用代码
- **契约测试**：Pact等工具验证接口契约

## 最佳实践

### 1. 开发顺序
1. **接口设计优先**：先定义清晰的API契约
2. **后端实现**：按照契约实现业务逻辑
3. **前端对接**：严格按照契约调用接口
4. **端到端测试**：验证完整流程

### 2. 沟通协作
- **接口变更通知**：任何接口修改必须通知相关开发者
- **版本管理**：API版本化管理，避免破坏性变更
- **文档同步**：代码变更时同步更新文档

### 3. 质量保证
- **代码审查**：重点关注前后端一致性
- **自动化测试**：覆盖关键的数据流路径
- **监控告警**：生产环境的接口调用监控

## 总结

全栈一致性开发不仅是技术要求，更是团队协作的基础。通过严格执行这一规范，可以：

- **减少Bug**：避免前后端不一致导致的问题
- **提高效率**：减少调试和修复时间
- **改善体验**：确保用户操作的连贯性
- **便于维护**：代码逻辑清晰，易于理解和修改

**记住：每一次修改都是一次全栈验证的机会！**
