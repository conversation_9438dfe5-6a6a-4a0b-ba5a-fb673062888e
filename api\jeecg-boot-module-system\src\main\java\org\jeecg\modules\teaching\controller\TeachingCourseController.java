package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysFileService;
import org.jeecg.modules.teaching.entity.TeachingCourse;
import org.jeecg.modules.teaching.service.ITeachingCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 课程
 * @Author: jeecg-boot
 * @Date:   2020-04-14
 * @Version: V1.0
 */
@Api(tags="课程")
@RestController
@RequestMapping("/teaching/teachingCourse")
@Slf4j
public class TeachingCourseController extends JeecgController<TeachingCourse, ITeachingCourseService> {
	@Autowired
	private ITeachingCourseService teachingCourseService;
	@Autowired
	private ISysFileService sysFileService;
	@Autowired
	private ISysDepartService sysDepartService;

	/**
	 * 获取我的课程
	 * @return
	 */
	 @GetMapping("/mineCourse")
	 public Result<List<TeachingCourse>> mineCourse(){
		 String mineUserId = getCurrentUser().getId();
		 return teachingCourseService.mineCourse(mineUserId);
	 }

	/**
	 * 获取首页展示的课程
	 * @return
	 */
	@GetMapping("getHomeCourse")
	public Result<?> getHomeCourse(
			@RequestParam(required = false) String courseName,
			@RequestParam(required = false) Integer courseType,
			@RequestParam(required = false) Integer courseCategory,
			@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
			@RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
		QueryWrapper<TeachingCourse> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda()
				.eq(TeachingCourse::getShowHome, 1)
				.like(StringUtils.isNotBlank(courseName), TeachingCourse::getCourseName, courseName)
				.eq(courseCategory!=null, TeachingCourse::getCourseCategory, courseCategory)
				.eq(courseType!=null, TeachingCourse::getCourseType, courseType);
		Page<TeachingCourse> page = new Page<TeachingCourse>(pageNo, pageSize);
		IPage<TeachingCourse> pageList = teachingCourseService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 * 分页列表查询
	 *
	 * @param teachingCourse
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "课程-分页列表查询")
	@ApiOperation(value="课程-分页列表查询", notes="课程-分页列表查询")
	@GetMapping(value = "/list")
	@PermissionData
	public Result<?> queryPageList(TeachingCourse teachingCourse,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		Map<String, String[]> param = req.getParameterMap();
		QueryWrapper<TeachingCourse> queryWrapper = new QueryWrapper<>();
		//部门权限过滤
		if (param.containsKey("departId")){
			List<String> parentDepIds = sysDepartService.getParentDepartIds(param.get("departId")[0]);
			queryWrapper.and(wrapper -> {
				wrapper.or().eq("depart_ids","");
				for (String departId : parentDepIds){wrapper.or().like("depart_ids", departId);}
				// 阶段二：MyBatis-Plus 3.4.x兼容性修复 - 移除return语句
			});
		}
		QueryGenerator.installMplus(queryWrapper, teachingCourse, req.getParameterMap());
		Page<TeachingCourse> page = new Page<TeachingCourse>(pageNo, pageSize);
		IPage<TeachingCourse> pageList = teachingCourseService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param teachingCourse
	 * @return
	 */
	@AutoLog(value = "课程-添加")
	@ApiOperation(value="课程-添加", notes="课程-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody TeachingCourse teachingCourse) {
		teachingCourseService.save(teachingCourse);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param teachingCourse
	 * @return
	 */
	@AutoLog(value = "课程-编辑")
	@ApiOperation(value="课程-编辑", notes="课程-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody TeachingCourse teachingCourse) {
		teachingCourseService.updateById(teachingCourse);
		return Result.ok("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "课程-通过id删除")
	@ApiOperation(value="课程-通过id删除", notes="课程-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		TeachingCourse course = this.teachingCourseService.getById(id);
		if (course != null){
			sysFileService.deleteByKeyWithFile(course.getCourseIcon());
			sysFileService.deleteByKeyWithFile(course.getCourseCover());
			sysFileService.deleteByKeyWithFile(course.getCourseMap());
			teachingCourseService.removeById(id);
		}
		return Result.ok("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "课程-批量删除")
	@ApiOperation(value="课程-批量删除", notes="课程-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		List<String> idList = Arrays.asList(ids.split(","));
		List<TeachingCourse> courseList = this.teachingCourseService.list(new QueryWrapper<TeachingCourse>().in("id", idList));
		for (TeachingCourse course: courseList){
			sysFileService.deleteByKeyWithFile(course.getCourseIcon());
			sysFileService.deleteByKeyWithFile(course.getCourseCover());
			sysFileService.deleteByKeyWithFile(course.getCourseMap());
		}
		this.teachingCourseService.removeByIds(idList);
		return Result.ok("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "课程-通过id查询")
	@ApiOperation(value="课程-通过id查询", notes="课程-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		TeachingCourse teachingCourse = teachingCourseService.getById(id);
		if(teachingCourse==null) {
			return Result.error("未找到对应数据");
		}
		return Result.ok(teachingCourse);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param teachingCourse
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TeachingCourse teachingCourse) {
        return super.exportXls(request, teachingCourse, TeachingCourse.class, "课程");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TeachingCourse.class);
    }

}
