package org.jeecg.config;

import org.jeecg.modules.quartz.service.IQuartzJobService;
import org.jeecg.modules.quartz.entity.QuartzJob;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * 定时任务初始化配置类
 * 用于在系统启动时自动注册特定的定时任务
 */
@Slf4j
@Component
public class SchedulerTaskConfig implements CommandLineRunner {

    @Autowired
    private IQuartzJobService quartzJobService;

    @Override
    public void run(String... args) throws Exception {
        // 注册课程通知定时任务
        registerCourseNotificationJob();
        // 注册课程即将开始提醒定时任务
        registerCourseUpcomingReminderJob();
    }

    /**
     * 注册课程通知处理定时任务
     * 如果任务已存在则不重复注册
     */
    private void registerCourseNotificationJob() {
        // 任务全限定类名
        String jobClassName = "org.jeecg.modules.teaching.job.CourseNotificationJob";
        
        // 检查任务是否已存在
        java.util.List<QuartzJob> existingJobs = quartzJobService.findByJobClassName(jobClassName);
        if (existingJobs != null && !existingJobs.isEmpty()) {
            log.info("课程通知处理定时任务已存在，无需重复注册");
            return;
        }
        
        // 创建定时任务
        QuartzJob quartzJob = new QuartzJob();
        // 设置任务类名
        quartzJob.setJobClassName(jobClassName);
        // 每2分钟执行一次
        quartzJob.setCronExpression("0 */2 * * * ?");
        quartzJob.setParameter("");
        quartzJob.setDescription("定时处理课程通知和课程提醒通知");
        quartzJob.setStatus(CommonConstant.STATUS_NORMAL);
        
        // 保存并调度任务
        try {
            quartzJobService.saveAndScheduleJob(quartzJob);
            log.info("课程通知处理定时任务注册成功");
        } catch (Exception e) {
            log.error("注册课程通知定时任务失败", e);
        }
    }
    
    /**
     * 注册课程即将开始提醒定时任务
     * 检查未来24小时内的课程并创建提醒通知
     */
    private void registerCourseUpcomingReminderJob() {
        // 任务全限定类名
        String jobClassName = "org.jeecg.modules.teaching.job.CourseUpcomingReminderJob";
        
        // 检查任务是否已存在
        java.util.List<QuartzJob> existingJobs = quartzJobService.findByJobClassName(jobClassName);
        if (existingJobs != null && !existingJobs.isEmpty()) {
            log.info("课程即将开始提醒定时任务已存在，无需重复注册");
            return;
        }
        
        // 创建定时任务
        QuartzJob quartzJob = new QuartzJob();
        // 设置任务类名
        quartzJob.setJobClassName(jobClassName);
        // 每6小时执行一次，从原来的30分钟修改为6小时
        quartzJob.setCronExpression("0 0 */6 * * ?");
        quartzJob.setParameter("");
        quartzJob.setDescription("检查未来24小时内的课程并创建提醒通知");
        quartzJob.setStatus(CommonConstant.STATUS_NORMAL);
        
        // 保存并调度任务
        try {
            quartzJobService.saveAndScheduleJob(quartzJob);
            log.info("课程即将开始提醒定时任务注册成功");
        } catch (Exception e) {
            log.error("注册课程即将开始提醒定时任务失败", e);
        }
    }
} 