package org.jeecg.modules.teaching.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.teaching.entity.ExamCollection;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.service.IExamCollectionService;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Objects;
import java.util.Collections;

@Api(tags="用户题目收藏")
@RestController
@RequestMapping("/teaching/examSystem/collection")
@Slf4j
public class ExamCollectionController extends JeecgController<ExamCollection, IExamCollectionService> {

    @Autowired
    private IExamCollectionService examCollectionService;
    
    @Autowired
    private IExamQuestionService examQuestionService;

    @AutoLog(value = "收藏列表-分页查询")
    @ApiOperation(value="收藏列表-分页查询", notes="查询当前用户的收藏列表")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExamCollection examCollection,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   @RequestParam(name="questionTitle", required=false) String questionTitle,
                                   @RequestParam(name="startTime", required=false) String startTime,
                                   @RequestParam(name="endTime", required=false) String endTime,
                                   @RequestParam(name="sortField", required=false) String sortField,
                                   @RequestParam(name="sortOrder", required=false) String sortOrder,
                                   HttpServletRequest req) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        log.info("当前查询的用户ID: " + userId);
        
        // 创建查询条件
        QueryWrapper<ExamCollection> queryWrapper = new QueryWrapper<>();
        
        // 只查询当前用户的收藏
        queryWrapper.eq("user_id", userId);
        
        // 如果有题目ID查询条件，添加到查询中
        if (examCollection.getQuestionId() != null && !examCollection.getQuestionId().trim().isEmpty()) {
            queryWrapper.eq("question_id", examCollection.getQuestionId().trim());
        }
        
        // 如果有时间范围查询条件，添加到查询中
        if (startTime != null && !startTime.trim().isEmpty()) {
            queryWrapper.ge("collection_time", startTime);
        }
        if (endTime != null && !endTime.trim().isEmpty()) {
            queryWrapper.le("collection_time", endTime);
        }
        
        // 处理排序
        if (sortField != null && sortOrder != null) {
            log.info("收藏列表排序 - 原始字段: {}, 方向: {}", sortField, sortOrder);

            // 将前端的驼峰命名转换为数据库的下划线命名
            String dbFieldName = convertToDbFieldName(sortField);
            log.info("收藏列表排序 - 转换后字段: {}", dbFieldName);

            if ("asc".equals(sortOrder)) {
                queryWrapper.orderByAsc(dbFieldName);
            } else if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(dbFieldName);
            }
        } else {
            // 默认按收藏时间降序排序
            queryWrapper.orderByDesc("collection_time");
        }
        
        // 执行分页查询
        Page<ExamCollection> page = new Page<>(pageNo, pageSize);
        IPage<ExamCollection> pageList = examCollectionService.page(page, queryWrapper);
        
        // 如果指定了题目标题查询，需要先获取收藏列表再筛选
        if (questionTitle != null && !questionTitle.trim().isEmpty()) {
            // 获取符合条件的收藏记录
            List<ExamCollection> collectionList = pageList.getRecords();
            if (collectionList != null && !collectionList.isEmpty()) {
                // 提取题目ID
                List<String> questionIds = collectionList.stream()
                        .map(ExamCollection::getQuestionId)
                        .filter(Objects::nonNull) // 过滤空值
                        .collect(Collectors.toList());
                
                // 如果没有有效的题目ID，直接返回空结果
                if (questionIds.isEmpty()) {
                    pageList.setRecords(Collections.emptyList());
                    pageList.setTotal(0);
                } else {
                    // 查询题目信息
                    QueryWrapper<ExamQuestion> questionQueryWrapper = new QueryWrapper<>();
                    questionQueryWrapper.in("id", questionIds);
                    // 使用INSTR函数替代LIKE查询，以支持特殊字符和增强模糊匹配能力
                    questionQueryWrapper.apply("INSTR(LOWER(title), LOWER({0})) > 0", questionTitle.trim());
                    List<ExamQuestion> questions = examQuestionService.list(questionQueryWrapper);
                    
                    // 提取符合标题条件的题目ID
                    List<String> filteredQuestionIds = questions.stream()
                            .map(ExamQuestion::getId)
                            .collect(Collectors.toList());
                    
                    // 过滤收藏列表，只保留符合题目标题条件的记录
                    List<ExamCollection> filteredCollections = collectionList.stream()
                            .filter(collection -> filteredQuestionIds.contains(collection.getQuestionId()))
                            .collect(Collectors.toList());
                    
                    // 更新分页结果
                    pageList.setRecords(filteredCollections);
                    pageList.setTotal(filteredCollections.size());
                }
            }
        }
        
        // 查询题目标题并填充到收藏记录中
        List<ExamCollection> records = pageList.getRecords();
        if (records != null && !records.isEmpty()) {
            // 获取所有题目ID
            List<String> questionIds = records.stream()
                    .map(ExamCollection::getQuestionId)
                    .collect(Collectors.toList());
                    
            // 查询题目信息
            QueryWrapper<ExamQuestion> questionQueryWrapper = new QueryWrapper<>();
            questionQueryWrapper.in("id", questionIds);
            List<ExamQuestion> questions = examQuestionService.list(questionQueryWrapper);
            
            // 创建题目ID到标题的映射
            Map<String, String> questionTitleMap = questions.stream()
                    .collect(Collectors.toMap(
                        ExamQuestion::getId,
                        ExamQuestion::getTitle,
                        (t1, t2) -> t1 // 如果有重复的键，保留第一个
                    ));

            // 创建题目ID到题型的映射
            Map<String, Integer> questionTypeMap = questions.stream()
                    .collect(Collectors.toMap(
                        ExamQuestion::getId,
                        ExamQuestion::getQuestionType,
                        (t1, t2) -> t1 // 如果有重复的键，保留第一个
                    ));

            // 为收藏记录设置题目标题和题型
            for (ExamCollection collection : records) {
                String title = questionTitleMap.get(collection.getQuestionId());
                Integer questionType = questionTypeMap.get(collection.getQuestionId());
                collection.setQuestionTitle(title);
                collection.setQuestionType(questionType);
            }
        }
        
        log.info("查询结果: " + pageList);
        return Result.ok(pageList);
    }
    
    @AutoLog(value = "收藏题目-添加")
    @ApiOperation(value="收藏题目-添加", notes="添加一个题目到收藏夹")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ExamCollection examCollection, HttpServletRequest request) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        // 检查是否已收藏
        QueryWrapper<ExamCollection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("question_id", examCollection.getQuestionId());
        if (examCollectionService.count(queryWrapper) > 0) {
            return Result.error("您已收藏过此题，请勿重复操作");
        }
        
        examCollection.setUserId(userId);
        examCollection.setCollectionTime(new Date());
        examCollectionService.save(examCollection);
        
        // 修复：查询并返回刚保存的收藏对象
        ExamCollection savedCollection = examCollectionService.getOne(queryWrapper);
        return Result.ok(savedCollection);
    }

    @AutoLog(value = "收藏题目-删除")
    @ApiOperation(value="收藏题目-删除", notes="从收藏夹移除一个题目")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id", required=true) String id, HttpServletRequest request) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        ExamCollection collection = examCollectionService.getById(id);
        if (collection == null) {
            return Result.error("未找到该收藏记录");
        }
        if (!collection.getUserId().equals(userId)) {
            return Result.error("无权限删除他人的收藏");
        }
        examCollectionService.removeById(id);
        return Result.ok("取消收藏成功！");
    }

    @AutoLog(value = "收藏题目-批量删除")
    @ApiOperation(value="收藏题目-批量删除", notes="批量从收藏夹移除题目")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids, HttpServletRequest req) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        // 校验权限
        QueryWrapper<ExamCollection> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", Arrays.asList(ids.split(",")));
        queryWrapper.eq("user_id", userId);
        if(examCollectionService.count(queryWrapper) != ids.split(",").length){
             return Result.error("参数不识别或无权限操作");
        }
        // 移除this前缀
        examCollectionService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量取消收藏成功！");
    }
    
    @AutoLog(value = "收藏题目-检查状态")
    @ApiOperation(value="收藏题目-检查状态", notes="检查某个题目是否已被当前用户收藏")
    @GetMapping(value = "/check")
    public Result<?> check(@RequestParam(name="questionId", required=true) String questionId, HttpServletRequest request) {
        // 获取当前登录用户ID
        String userId = getCurrentUser().getId();
        
        QueryWrapper<ExamCollection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("question_id", questionId);
        ExamCollection one = examCollectionService.getOne(queryWrapper);
        if (one != null) {
            return Result.ok(one);
        }
        // 返回更符合RESTful风格的响应
        return Result.ok(false);
    }
    
    /**
     * 导出收藏题目
     */
    @AutoLog(value = "收藏题目-导出")
    @ApiOperation(value="收藏题目-导出", notes="导出收藏的题目为TXT格式")
    @GetMapping(value = "/export")
    public void exportCollections(
            @ApiParam(value = "题目ID") @RequestParam(required = false) String questionId,
            @ApiParam(value = "题目标题") @RequestParam(required = false) String questionTitle,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime,
            @ApiParam(value = "指定收藏ID列表") @RequestParam(required = false) String ids,
            HttpServletRequest request,
            HttpServletResponse response) {
        
        try {
            // 获取当前登录用户ID
            String userId = getCurrentUser().getId();
            
            // 构建查询条件
            QueryWrapper<ExamCollection> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            
            // 按题目ID筛选
            if (questionId != null && !questionId.trim().isEmpty()) {
                queryWrapper.eq("question_id", questionId);
            }
            
            // 按时间范围筛选
            if (startTime != null && !startTime.trim().isEmpty()) {
                queryWrapper.ge("collection_time", startTime);
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                queryWrapper.le("collection_time", endTime);
            }
            
            // 按指定ID列表筛选
            if (ids != null && !ids.trim().isEmpty()) {
                queryWrapper.in("id", Arrays.asList(ids.split(",")));
            }
            
            // 按收藏时间排序
            queryWrapper.orderByDesc("collection_time");
            
            // 查询符合条件的收藏记录
            List<ExamCollection> collections = examCollectionService.list(queryWrapper);
            
            // 如果没有收藏记录，返回空文件
            if (collections.isEmpty()) {
                writeEmptyContent(response, "没有找到符合条件的收藏记录");
                return;
            }
            
            // 获取收藏的题目ID列表
            List<String> questionIds = collections.stream()
                    .map(ExamCollection::getQuestionId)
                    .collect(Collectors.toList());
            
            // 查询对应的题目详情
            QueryWrapper<ExamQuestion> questionQueryWrapper = new QueryWrapper<>();
            questionQueryWrapper.in("id", questionIds);
            
            // 添加题目标题筛选条件
            if (questionTitle != null && !questionTitle.trim().isEmpty()) {
                // 使用数据库函数进行模糊匹配，避免特殊字符转义问题
                questionQueryWrapper.apply("INSTR(LOWER(title), LOWER({0})) > 0", questionTitle.trim());
            }
            
            List<ExamQuestion> questions = examQuestionService.list(questionQueryWrapper);
            
            // 如果应用了标题筛选并且没有匹配的题目，返回空文件
            if (questions.isEmpty()) {
                writeEmptyContent(response, "没有找到符合筛选条件的题目");
                return;
            }
            
            // 根据筛选后的题目ID过滤收藏记录
            if (questionTitle != null && !questionTitle.trim().isEmpty()) {
                List<String> filteredQuestionIds = questions.stream()
                        .map(ExamQuestion::getId)
                        .collect(Collectors.toList());
                
                collections = collections.stream()
                        .filter(collection -> filteredQuestionIds.contains(collection.getQuestionId()))
                        .collect(Collectors.toList());
                        
                // 如果过滤后没有收藏记录，返回空文件
                if (collections.isEmpty()) {
                    writeEmptyContent(response, "没有找到符合筛选条件的收藏记录");
                    return;
                }
            }
            
            // 创建题目ID到题目的映射，方便后续使用
            Map<String, ExamQuestion> questionMap = questions.stream()
                    .collect(Collectors.toMap(ExamQuestion::getId, q -> q, (q1, q2) -> q1));
            
            // 设置响应头
            String fileName = "收藏题目导出_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".txt";
            response.setContentType("text/plain;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + 
                    new String(fileName.getBytes(), "ISO8859-1"));
            
            // 构建导出内容
            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
                // 写入元数据
                writer.write("【元数据】\n");
                writer.write("导出时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n");
                writer.write("收藏数量: " + collections.size() + "\n");
                writer.write("\n");
                
                // 按题目类型分组
                Map<Integer, List<ExamQuestion>> questionsByType = questions.stream()
                        .collect(Collectors.groupingBy(ExamQuestion::getQuestionType));
                
                // 写入单选题
                if (questionsByType.containsKey(1)) {
                    writer.write("【单选题】\n\n");
                    writeQuestions(writer, questionsByType.get(1), collections, questionMap);
                }
                
                // 写入判断题
                if (questionsByType.containsKey(2)) {
                    writer.write("\n【判断题】\n\n");
                    writeQuestions(writer, questionsByType.get(2), collections, questionMap);
                }
                
                // 写入编程题
                if (questionsByType.containsKey(3)) {
                    writer.write("\n【编程题】\n\n");
                    writeQuestions(writer, questionsByType.get(3), collections, questionMap);
                }
                
                writer.flush();
            }
        } catch (Exception e) {
            log.error("导出收藏题目失败", e);
            try {
                // 在发生异常时，返回错误信息
                writeEmptyContent(response, "导出失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误信息失败", ex);
            }
        }
    }

    /**
     * 将前端驼峰命名转换为数据库下划线命名
     */
    private String convertToDbFieldName(String camelCaseField) {
        if (camelCaseField == null) {
            return null;
        }

        // 处理特定字段映射
        switch (camelCaseField) {
            case "collectionTime":
                return "collection_time";
            case "userId":
                return "user_id";
            case "questionId":
                return "question_id";
            default:
                // 通用驼峰转下划线
                return camelCaseField.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
        }
    }

    /**
     * 写入题目内容
     */
    private void writeQuestions(BufferedWriter writer, List<ExamQuestion> questions,
                              List<ExamCollection> collections, Map<String, ExamQuestion> questionMap) throws IOException {
        // 按收藏时间排序
        Map<String, Date> questionCollectionTime = collections.stream()
                .collect(Collectors.toMap(
                    ExamCollection::getQuestionId,
                    ExamCollection::getCollectionTime,
                    (t1, t2) -> t1 // 如果有重复，保留第一个时间
                ));
        
        // 按收藏时间排序题目
        List<ExamQuestion> sortedQuestions = new ArrayList<>(questions);
        sortedQuestions.sort((q1, q2) -> {
            Date t1 = questionCollectionTime.get(q1.getId());
            Date t2 = questionCollectionTime.get(q2.getId());
            // 降序排列，最近收藏的排在前面
            if (t1 == null || t2 == null) {
                return 0; // 如果没有收藏时间，保持原顺序
            }
            return t2.compareTo(t1);
        });
        
        // 写入题目内容
        int index = 1;
        for (ExamQuestion question : sortedQuestions) {
            // 写入题目标题
            writer.write(index + ". " + question.getTitle() + "\n");
            
            // 解析content字段中的内容
            JSONObject content = null;
            try {
                if (StringUtils.isNotBlank(question.getContent())) {
                    content = JSON.parseObject(question.getContent());
                }
            } catch (Exception e) {
                log.warn("解析题目内容失败: {}", e.getMessage());
            }
            
            // 写入题目类型特定内容
            switch (question.getQuestionType()) {
                case 1: // 单选题
                    if (content != null) {
                        String options = content.getString("options");
                        String answer = content.getString("answer");
                        
                        if (StringUtils.isNotBlank(options)) {
                            writer.write(options + "\n");
                        }
                        if (StringUtils.isNotBlank(answer)) {
                            writer.write("答案: " + answer + "\n");
                        }
                    }
                    break;
                    
                case 2: // 判断题
                    if (content != null) {
                        String answer = content.getString("answer");
                        if (StringUtils.isNotBlank(answer)) {
                            writer.write("答案: " + ("1".equals(answer) ? "正确" : "错误") + "\n");
                        }
                    }
                    break;
                    
                case 3: // 编程题
                    if (content != null) {
                        // 修复编程题字段名，使其与前端一致
                        String description = content.getString("description");
                        String input_format = content.getString("input_format");
                        String output_format = content.getString("output_format");
                        
                        // 处理描述
                        if (StringUtils.isNotBlank(description)) {
                            writer.write("描述:\n" + description + "\n");
                        }
                        
                        // 处理输入格式
                        if (StringUtils.isNotBlank(input_format)) {
                            writer.write("输入格式:\n" + input_format + "\n");
                        }
                        
                        // 处理输出格式
                        if (StringUtils.isNotBlank(output_format)) {
                            writer.write("输出格式:\n" + output_format + "\n");
                        }
                        
                        // 处理样例案例，这是一个数组而非单个字符串
                        try {
                            Object sampleCasesObj = content.get("sample_cases");
                            if (sampleCasesObj instanceof JSONObject || sampleCasesObj instanceof List) {
                                List<Object> sampleCases = JSON.parseArray(JSON.toJSONString(sampleCasesObj));
                                if (sampleCases != null && !sampleCases.isEmpty()) {
                                    writer.write("样例:\n");
                                    for (int i = 0; i < sampleCases.size(); i++) {
                                        JSONObject sampleCase = (JSONObject) sampleCases.get(i);
                                        String input = sampleCase.getString("input");
                                        String output = sampleCase.getString("output");
                                        
                                        writer.write("样例 " + (i + 1) + ":\n");
                                        if (StringUtils.isNotBlank(input)) {
                                            writer.write("输入:\n" + input + "\n");
                                        }
                                        if (StringUtils.isNotBlank(output)) {
                                            writer.write("输出:\n" + output + "\n");
                                        }
                                        writer.write("\n");
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析样例案例失败: {}", e.getMessage());
                        }
                        
                        // 处理提示信息
                        String hint = content.getString("hint");
                        if (StringUtils.isNotBlank(hint)) {
                            writer.write("提示:\n" + hint + "\n");
                        }
                        
                        // 处理答案 - 防御性编程，为后期添加答案字段预留接口
                        try {
                            String answer = content.getString("answer");
                            if (StringUtils.isNotBlank(answer)) {
                                writer.write("参考答案:\n" + answer + "\n");
                            }
                        } catch (Exception e) {
                            // 当前编程题可能没有答案字段，忽略异常
                            log.debug("编程题暂无参考答案字段或解析失败");
                        }
                    }
                    break;
            }
            
            // 写入题目分析和注释
            if (content != null) {
                String analysis = content.getString("analysis");
                if (StringUtils.isNotBlank(analysis)) {
                    writer.write("题目分析:\n" + analysis + "\n");
                }
            }
            
            // 写入收藏时间
            Date collectionTime = questionCollectionTime.get(question.getId());
            if (collectionTime != null) {
                writer.write("收藏时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(collectionTime) + "\n");
            }
            
            // 题目间分隔
            writer.write("\n");
            index++;
        }
    }
    
    /**
     * 写入空内容或错误信息
     */
    private void writeEmptyContent(HttpServletResponse response, String message) throws IOException {
        response.setContentType("text/plain;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=empty_collection.txt");
        
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
            writer.write("【收藏题目导出】\n\n");
            writer.write(message + "\n");
            writer.flush();
        }
    }
} 