package org.jeecg.modules.teaching.service;


import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.ExamRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 考试记录
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
public interface IExamRecordService extends IService<ExamRecord> {

    /**
     * 提交试卷（直接创建考试记录）
     * @param userId 用户ID
     * @param paperId 试卷ID
     * @param startTime 考试开始时间
     * @param answers 用户答案
     * @return 考试结果
     */
    Result<?> submitExam(String userId, String paperId, Date startTime, Map<String, Object> answers);
    
    /**
     * 获取试卷详情（不包含答案和解析）
     * @param paperId 试卷ID
     * @return 试卷信息
     */
    Result<?> getPaperDetail(String paperId);

    /**
     * 获取考试记录的完整试卷预览信息
     * @param recordId 考试记录ID
     * @return 包含题目内容、用户答案、正确答案等完整信息的试卷预览数据
     */
    Result<?> getExamRecordPaperPreview(String recordId);
}