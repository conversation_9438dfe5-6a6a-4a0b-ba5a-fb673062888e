package org.jeecg.modules.teaching.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程排期Excel导入VO
 */
@Data
public class CourseScheduleExcelVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**课程标题*/
    @Excel(name = "课程标题", width = 15)
    private String scheduleTitle;
    
    /**班级名称*/
    @Excel(name = "班级名称", width = 15)
    private String deptName;
    
    /**课程名称*/
    @Excel(name = "课程名称", width = 15)
    private String courseName;
    
    /**教师姓名*/
    @Excel(name = "教师姓名", width = 15)
    private String teacherName;
    
    /**教室名称*/
    @Excel(name = "教室名称", width = 15)
    private String roomName;
    
    /**开始时间*/
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /**结束时间*/
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    /**重复类型*/
    @Excel(name = "重复类型", width = 15, replace = {"不重复_0", "每天_1", "每周_2"})
    private Integer repeatType;
    
    /**重复结束日期*/
    @Excel(name = "重复结束日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date repeatEndDate;
    
    /**重复星期*/
    @Excel(name = "重复星期", width = 15, dicCode = "周一,周二,周三,周四,周五,周六,周日")
    private String weekdays;
    
    /**备注*/
    @Excel(name = "备注", width = 15)
    private String description;
} 