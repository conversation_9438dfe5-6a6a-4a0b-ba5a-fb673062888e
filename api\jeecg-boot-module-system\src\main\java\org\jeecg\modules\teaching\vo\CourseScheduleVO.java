package org.jeecg.modules.teaching.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;

/**
 * @Description: 课程排期VO
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseScheduleVO extends TeachingCourseSchedule {
    /**班级名称*/
    private String deptName;
    
    /**课程名称*/
    private String courseName;
    
    /**教师名称*/
    private String teacherName;
    
    /**教室名称*/
    private String roomName;
} 