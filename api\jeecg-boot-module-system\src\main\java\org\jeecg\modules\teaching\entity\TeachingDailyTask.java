package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 每日任务记录实体类
 */
@Data
@TableName("teaching_daily_task")
public class TeachingDailyTask implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    /** 用户ID */
    private String userId;
    
    /** 任务类型（1:每日签到, 2:评论作品, 3:点赞作品） */
    private Integer taskType;
    
    /** 完成状态（0:未完成, 1:已完成） */
    private Integer status;
    
    /** 任务日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date taskDate;
    
    /** 完成时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    
    /** 关联ID（如评论ID、点赞作品ID等） */
    private String relatedId;
} 