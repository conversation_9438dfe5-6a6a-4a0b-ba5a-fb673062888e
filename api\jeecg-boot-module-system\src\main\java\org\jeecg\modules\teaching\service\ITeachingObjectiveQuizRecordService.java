package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizRecord;
import org.jeecg.modules.teaching.vo.ObjectiveQuizSaveVO;

/**
 * 客观题答题记录 Service
 */
public interface ITeachingObjectiveQuizRecordService extends IService<TeachingObjectiveQuizRecord> {
    
    /**
     * 保存答题记录
     */
    boolean saveQuizRecord(ObjectiveQuizSaveVO quizSaveVO);
} 