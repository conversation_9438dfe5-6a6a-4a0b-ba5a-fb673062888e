package org.jeecg.modules.teaching.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.modules.teaching.entity.TeachingCourseMedia;
import org.jeecg.modules.teaching.entity.TeachingCourseUnit;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class CourseUnitModel extends TeachingCourseUnit {
    String courseName;
    List<String> courseWorkIdList;
    List<String> courseWorkNameList;
    List<TeachingCourseMedia> courseWorks;

}
