{"@metadata": {"authors": ["BHARATHESHA ALASANDEMAJALU", "<PERSON><PERSON><PERSON><PERSON>", "Chidananda Ka<PERSON>a", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, "VARIABLES_DEFAULT_NAME": "ವಸ್ತು", "UNNAMED_KEY": "ಪುದರ್ ಇಜ್ಜಂತಿನವು", "TODAY": "ಇನಿ", "DUPLICATE_BLOCK": "ನಕಲ್", "ADD_COMMENT": "ಟಿಪ್ಪಣಿ ಸೇರ್ಸಲೆ", "REMOVE_COMMENT": "ಟಿಪ್ಪಣಿನ್ ದೆತ್ತ್‌ದ್ ಬುಡ್ಲೆ", "EXTERNAL_INPUTS": "ಪಿದಯಿದ ಪರಿಪು", "INLINE_INPUTS": "ಉಳಸಾಲ್‍ದ ಉಳಪರಿಪು", "DELETE_BLOCK": "ಬ್ಲಾಕ್‍ನ್ ಮಾಜಾವು", "DELETE_X_BLOCKS": "%1 ಬ್ಲಾಕ್‍ಲೆನ್ ಮಾಜಾವು", "DELETE_ALL_BLOCKS": "ಮಾತ %1 ಬ್ಲಾಕ್‍ಲೆನ್ ದೆತ್ತ್‌ದ್ ಬುಡೊಡೆ?", "CLEAN_UP": "ಬ್ಲಾಕ್‍ಲೆನ್ ಸ್ವೊಚ್ಚೊ ಮಲ್ಪುಲೆ", "COLLAPSE_BLOCK": "ಎಲ್ಯೆ ಮಲ್ತ್‌ದ್ ತಡೆಲೆ", "COLLAPSE_ALL": "ಮಾತಾ ತಡೆಕ್ಲೆನ ಮಾಹಿತಿನ್ ಎಲ್ಯ ಮಲ್ಪು", "EXPAND_BLOCK": "ಬ್ಲಾಕ್‍ದ ಮಾಹಿತಿನ್ ಪರಡಾವು", "EXPAND_ALL": "ಮಾತಾ ತಡೆಕ್ಲೆನ ಮಾಹಿತಿನ್ ಪರಡಾವು", "DISABLE_BLOCK": "ಬ್ಲಾಕ್‍ನ್ ದೆತ್ತ್‌ಪಾಡ್", "ENABLE_BLOCK": "ತಡೆನ್ ಸಕ್ರಿಯೊ ಮಲ್ಪು", "HELP": "ಸಹಾಯೊ", "UNDO": "ದುಂಬುದಲೆಕೊ", "REDO": "ಕುಡ ಮಲ್ಪು", "CHANGE_VALUE_TITLE": "ಮೌಲ್ಯೊನು ಬದಲ್ ಮಲ್ಪು", "RENAME_VARIABLE": "ವ್ಯತ್ಯಯೊಗು ಕುಡೊರ ಪುದರ್ ದೀಲೆ", "RENAME_VARIABLE_TITLE": "ಮಾತಾ '%1' ವ್ಯತ್ಯಯೊಲೆನ ಪುದರ್‌ನ್ ನೆಕ್ಕ್ ಬದಲ್ ಮಲ್ಪುಲೆ:", "NEW_VARIABLE": "ವ್ಯತ್ಯಯೊನು ಉಂಡು ಮಲ್ಪುಲೆ", "NEW_VARIABLE_TITLE": "ಪೊಸ ವ್ಯತ್ಯಯೊದ ಪುದರ್:", "VARIABLE_ALREADY_EXISTS": "'%1' ಪನ್ಪಿ ಪುದರ್‌ದ ವ್ಯತ್ಯಯೊ ದುಂಬೆ ಅಸ್ತಿತ್ವೊಡು ಉಂಡು.", "DELETE_VARIABLE_CONFIRMATION": "'%2' ವ್ಯತ್ಯಯೊದ %1 ಉಪಯೋಗೊಲೆನ್ ಮಾಜಾವೊಡೆ?", "DELETE_VARIABLE": "'%1' ವ್ಯತ್ಯಯೊನು ಮಾಜಾಲೆ", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/ಬಣ್ಣೊ", "COLOUR_PICKER_TOOLTIP": "ಬಣ್ಣೊ ಪಟೊಡ್ದು ಒಂಜಿ ಬಣ್ಣೊನು ಆಯ್ಕೆ ಮಲ್ಪುಲೆ.", "COLOUR_RANDOM_TITLE": "ಒವ್ವೇ ಒಂಜಿ ಬಣ್ಣೊ", "COLOUR_RANDOM_TOOLTIP": "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಬಣ್ಣೊನು ಆಯ್ಕೆ ಮಲ್ಪುಲೆ", "COLOUR_RGB_TITLE": "ಬಣ್ಣೊದ", "COLOUR_RGB_RED": "ಕೆಂಪು", "COLOUR_RGB_GREEN": "ಪಚ್ಚೆ", "COLOUR_RGB_BLUE": "ನೀಲಿ", "COLOUR_RGB_TOOLTIP": "ತೊಜಪಾಯಿನ ಪ್ರಮಾಣೊದ ಕೆಂಪು, ಪಚ್ಚೆ ಬೊಕ್ಕ ನೀಲಿ ಬಣ್ಣೊಡ್ದು ಒಂಜಿ ಬಣ್ಣೊನು ಉಂಡು ಮಲ್ಪುಲೆ. ಮಾತಾ ಮೌಲ್ಯೊಲು 0 ಬುಕ್ಕೊ 100 ತ ನಡುಟೆ ಇಪ್ಪೊಡು.", "COLOUR_BLEND_TITLE": "ಬೆರಕ್ಕೆ ಮಲ್ಪು", "COLOUR_BLEND_COLOUR1": "ಬಣ್ಣೊ ೧(ಒಂಜಿ)", "COLOUR_BLEND_COLOUR2": "ಬಣ್ಣೊ ೨(ರಡ್ಡ್)", "COLOUR_BLEND_RATIO": "ಅನುಪಾತೊ", "COLOUR_BLEND_TOOLTIP": "ಕೊರಿನ ಅನುಪಾತೊಡು (0.0- 1.0) ರಡ್ಡ್ ಬಣ್ಣೊಲೆನ್ ಬೆರಕೆ ಮಲ್ಪುಂಡು.", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": " %1 ಸರ್ತಿ ಕೂಡೊರ ಮಲ್ಪು", "CONTROLS_REPEAT_INPUT_DO": "ಮಲ್ಪುಲೆ", "CONTROLS_REPEAT_TOOLTIP": "ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಸ್ತ್ ಸರ್ತಿ ಮಲ್ಪು", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "ಉಂದು ನಿಜ ಆಂಡ ಕುಡೊರ ಮಲ್ಪು:", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "ಉಂದು ನಿಜ ಆಪಿಲೆಕೊ ಕುಡೊರ ಮಲ್ಪು:", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "ಮೌಲ್ಯ ನಿಜ ಆದಿತ್ತ್ಂಡ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪು", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "ಮೌಲ್ಯ ತಪ್ಪು ಆದಿತ್ತ್ಂಡ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪು", "CONTROLS_FOR_TOOLTIP": "ನಿರ್ದಿಸ್ಟೊ ಮದ್ಯಂತರೊದ ಮೂಲಕೊ ದೆತೊಂದು '%1' ವ್ಯತ್ಯಯೊಡ್ ಸುರುತ್ತ ಅಂಕೆಡ್ದ್ ಕಡೆತ್ತ ಅಂಕೆ ಮುಟ್ಟದ ಮೌಲ್ಯೊನು ದೆತ್ತೊನಾವ್ ಬೊಕ್ಕ ನಿಗಂಟ್ ಮಲ್ತಿನ ತಡೆಕ್ಲೆನ್ ಮಲ್ಪು", "CONTROLS_FOR_TITLE": "%2 ಡ್ದ್ %3 ಮುಟ %4 ಸರ್ತಿ %1 ದ ಒಟ್ಟುಗು ಗೆನ್ಪು", "CONTROLS_FOREACH_TITLE": "%2 ಪಟ್ಟಿಡ್ ಪ್ರತಿ ಒಂಜಿ ವಿಸಯ %1 ಗ್", "CONTROLS_FOREACH_TOOLTIP": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಪ್ರತಿ ವಿಸಯೊಗು, '%1' ವ್ಯತ್ಯಾಯೊನು ವಿಸಯೊಗು ಜೋಡಾಲೆ, ಬೊಕ್ಕ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪುಲೆ.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "ಲೂಪ್ ಕಡಿಯುನಿ", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "ಬೊಕ್ಕದ ಲೂಪ್ ಪುನರಾವರ್ತನೆದೊಟ್ಟುಗು ದುಂಬರಿಲೆ", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "ಇತ್ತಿನ ಲೂಪ್‍ಡ್ದ್ ಪದಿಯಿ ಬಲೆ", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "ಈ ಲೂಪುನು ಅರ್ದೊಡೆ ಬುಡುದ್ ಬೊಕ್ಕ ನನತ್ತ ಪುನರಾವರ್ತನೆಗ್ ದುಂಬರಿಲೆ", "CONTROLS_FLOW_STATEMENTS_WARNING": "ಎಚ್ಚರೊ: ಈ ತಡೆನ್ ಕಾಲಿ ಒಂಜಿ ಲೂಪುದುಲಯಿ ಮಾತ್ರ ಗಳಸೊಲಿ.", "CONTROLS_IF_TOOLTIP_1": "ಮೌಲ್ಯ ನಿಜ ಆದಿತ್ತ್ಂಡ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪು", "CONTROLS_IF_TOOLTIP_2": "ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್‌ಂಡ, ಪಾತೆರೊಲೆನ ಸುರುತ್ತ ತಡೆ ಮಲ್ಪು. ಇಜ್ಜಿಂಡ ಪಾತೆರೊಲೆನ ರಡ್ಡನೆ ತಡೆ ಮಲ್ಪು.", "CONTROLS_IF_TOOLTIP_3": "ಸುರುತ್ತ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್‌ಂಡ, ಪಾತೆರೊಲೆನ ಸುರುತ್ತ ತಡೆ ಮಲ್ಪು. ಇಜ್ಜಿಂಡ, ರಡ್ಡನೆ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್ಂಡ, ಪಾತೆರೊಲೆನ ರಡ್ಡನೆ ತಡೆ ಮಲ್ಪು.", "CONTROLS_IF_TOOLTIP_4": "ಸುರುತ್ತ ಮೌಲ್ಯೊ ನಿಜವಾದಿತ್ತ್‌ಂಡ, ಪಾತೆರೊಲೆನ ಸುರುತ್ತ ತಡೆ ಮಲ್ಪು. ಇಜ್ಜಿಂಡ, ರಡ್ಡನೆದ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್ಂಡ, ಪಾತೆರೊಲೆನ ರಡ್ಡನೆ ತಡೆ ಮಲ್ಪು. ಒಂಜೇಲೆ ಒವ್ವೇ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತಿಜಿಂಡ, ಪಾತೆರೊಲೆನ ಕಡೆತ್ತ ತಡೆ ಮಲ್ಪು.", "CONTROLS_IF_MSG_IF": "ಒಂಜಿ ವೇಲೆ", "CONTROLS_IF_MSG_ELSEIF": "ಅತ್ತಂಡ", "CONTROLS_IF_MSG_ELSE": "ಅತ್ತಂಡ", "CONTROLS_IF_IF_TOOLTIP": "ಸೇರಾವ್, ದೆತ್ತ್‌ ಬುಡು, ಅತ್ತಂಡ ಈ 'ಒಂಜಿ ವೇಲೆ' ತಡೆನ್ ಕುಡ ಸಂರಚಣೆ ಮಲ್ಪೆರೆ ವಿಭಾಗೊಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪುಲೆ.", "CONTROLS_IF_ELSEIF_TOOLTIP": "'ಒಂಜಿ ವೇಲೆ' ತಡೆಕ್ಕ್ ಒಂಜಿ ಶರ್ತನ್ ಸೇರಾವ್", "CONTROLS_IF_ELSE_TOOLTIP": "'ಒಂಜಿ ವೇಲೆ' ತಡೆಕ್ಕ್ ಒಂಜಿ ಕಡೆತ್ತ ಮಾತೆನ್ಲಾ-ಪತ್ತ್ (catch-all) ಶರ್ತನ್ ಸೇರಾವ್", "IOS_OK": "ಸರಿ", "IOS_CANCEL": "ವಜಾ ಮಲ್ಪುಲೆ", "IOS_ERROR": "ದೋಷ", "IOS_PROCEDURES_INPUTS": "ಇನ್‌ಪುಟ್‌ಲು", "IOS_PROCEDURES_ADD_INPUT": "+ ಇನ್‌ಪುಟ್ ಸೇರಾಲೆ", "IOS_PROCEDURES_ALLOW_STATEMENTS": "ಹೇಳಿಕೆಗ್ ಅವಕಾಸೊ", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "ಈ ಕಾರ್ಯೊಡು ಡುಪ್ಲಿಕೇಟ್ ಇನ್‌ಪುಟ್ ಉಂಡು.", "IOS_VARIABLES_ADD_VARIABLE": "+ ವ್ಯತ್ಯಯೊನು ಸೇರಾಲೆ", "IOS_VARIABLES_ADD_BUTTON": "ಸೇರಾಲೆ", "IOS_VARIABLES_RENAME_BUTTON": "ಪೊಸ ಪುದರ್", "IOS_VARIABLES_DELETE_BUTTON": "ಮಾಜಾಲೆ", "IOS_VARIABLES_VARIABLE_NAME": "ವ್ಯತ್ಯಯೊದ ಪುದರ್", "IOS_VARIABLES_EMPTY_NAME_ERROR": "ವ್ಯತ್ಯಯೊದ ಪುದರ್‌ನ್ ಖಾಲಿ ಬುಡಿಯೆರೆ ಆಪುಜಿ", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "ರಡ್ದ್ ಇನ್‌ಪುಟ್‌ಲಾ ಸಮ ಇತ್ತ್ಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_COMPARE_TOOLTIP_NEQ": "ರಡ್ದ್ ಇನ್‌ಪುಟ್‌ಲಾ ಸಮ ಅತ್ತಾಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_COMPARE_TOOLTIP_LT": "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಎಲ್ಯ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_COMPARE_TOOLTIP_LTE": "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಎಲ್ಯ ಅತ್ತಂಡ ಅಯಿಕ್ಕ್ ಸಮ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_COMPARE_TOOLTIP_GT": "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಮಲ್ಲ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_COMPARE_TOOLTIP_GTE": "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಮಲ್ಲ ಅತ್ತಂಡ ಅಯಿಕ್ಕ್ ಸಮ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_OPERATION_TOOLTIP_AND": "ರಡ್ಡ್‌ಲಾ ಇನ್‌ಪುಟ್ ನಿಜ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು", "LOGIC_OPERATION_AND": "ಬುಕ್ಕೊ", "LOGIC_OPERATION_TOOLTIP_OR": "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಇನ್‌ಪುಟ್ ನಿಜ ಆಂಡಲಾ, 'ನಿಜ'ನ್ ಪಿರಕೊರು.", "LOGIC_OPERATION_OR": "ಅತ್ತಂಡ", "LOGIC_NEGATE_TITLE": "%1 ಅತ್ತ್", "LOGIC_NEGATE_TOOLTIP": "ಇನ್‌ಪುಟ್ ಸುಲ್ಲಾದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು. ಇನ್‌ಪುಟ್ ನಿಜ ಆದಿತ್ತ್ಂಡ, 'ಸುಲ್ಲು'ನ್ ಪಿರಕೊರು.", "LOGIC_BOOLEAN_TRUE": "ಸತ್ಯೊ", "LOGIC_BOOLEAN_FALSE": "ಸುಲ್ಲು", "LOGIC_BOOLEAN_TOOLTIP": "ಒಂಜೆ ನಿಜ ಅತ್ತಂಡ ಸುಲ್ಲುನ್ ಪಿರಕೊರು", "LOGIC_NULL": "ಸೊನ್ನೆ", "LOGIC_NULL_TOOLTIP": "ಸೊನ್ನೆನ್ ಪಿರಕೊರ್ಪುಂಡು", "LOGIC_TERNARY_CONDITION": "ಪರೀಕ್ಷೆ", "LOGIC_TERNARY_IF_TRUE": "ಒಂಜಿ ವೇಲೆ ನಿಜ ಆಂಡ", "LOGIC_TERNARY_IF_FALSE": "ಒಂಜಿ ವೇಲೆ ಸುಲ್ಲಾಂಡ", "LOGIC_TERNARY_TOOLTIP": "'ಪರೀಕ್ಷೆ'ಡ್ ಶರ್ತನ್ ಸರಿತೂಲೆ. ಶರ್ತ ನಿಜವಾದಿತ್ತ್ಂಡ, 'ಒಂಜಿ ವೇಲೆ ನಿಜ ಆಂಡ' ಮೌಲ್ಯೊನು ಪಿರಕೊರ್ಪುಂಡು; ಇಜ್ಜಿಂಡ 'ಒಂಜಿ ವೇಲೆ ಸುಲ್ಲಾಂಡ' ಮೌಲ್ಯೊನು ಪಿರಕೊರ್ಪುಂಡು.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/ಸಂಖ್ಯೆ", "MATH_NUMBER_TOOLTIP": "ಅ ನಂಬ್ರೊ.", "MATH_ARITHMETIC_HELPURL": "https://en.wikipedia.org/wiki/ಅಂಕಗಣಿತ", "MATH_ARITHMETIC_TOOLTIP_ADD": "ರಡ್ಡ್ ಸಂಖ್ಯೆದ ಮೊತ್ತನ್ ಪಿರಕೊರು.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "ರಡ್ಡ ಸ್ಂಖ್ಯೆದ ವ್ಯತ್ಯಾಸೊನು ಪಿರಕೊರು.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "ಸಂಖ್ಯೆದ ಗುಣಲಬ್ಧೊನು ಪಿರಕೊರು.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "ಸಂಖ್ಯೆದ ಭಾಗಲಬ್ದೊನು ಪಿರಕೊರು.", "MATH_ARITHMETIC_TOOLTIP_POWER": "ಸುರುತ್ತ ಸಂಖ್ಯೆದ ಘಾತೊನು ರಡ್ಡನೆ ಸಂಖ್ಯೆಗ್ ಏರ್ಪಾದ್ ಪಿರಕೊರು.", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/ವರ್ಗಮೂಲೊ", "MATH_SINGLE_OP_ROOT": "ವರ್ಗಮೂಲೊ", "MATH_SINGLE_TOOLTIP_ROOT": "ಸಂಖ್ಯೆದ ವರ್ಗಮೂಲೊನು ಪಿರಕೊರು.", "MATH_SINGLE_OP_ABSOLUTE": "ಸಂಪೂರ್ನೊ", "MATH_SINGLE_TOOLTIP_ABS": "ಸಂಖ್ಯೆದ ಸರಿಯಾಯಿನ ಮೌಲ್ಯೊನು ಕೊರು", "MATH_SINGLE_TOOLTIP_NEG": "ಸಂಖ್ಯೆದ ನಿಷೇಧೊನು ಪಿರಕೊರು", "MATH_SINGLE_TOOLTIP_LN": "ಸಂಖ್ಯೆದ ಪ್ರಾಕೃತಿಕ ಲಘುಗಣಕನ್ ಪಿರಕೊರು", "MATH_SINGLE_TOOLTIP_LOG10": "ಸಂಖ್ಯೆದ ದಶಮಾನ ಲಘುಗಣಕನ್ ಪಿರಕೊರು", "MATH_SINGLE_TOOLTIP_EXP": "ಒಂಜಿ ಸಂಖ್ಯೆದ ಘಾತೊಗು 'e'ನ್ ಪಿರಕೊರು.", "MATH_SINGLE_TOOLTIP_POW10": "ಒಂಜಿ ಸಂಖ್ಯೆದ ಘಾತೊಗು ೧೦ನ್ ಪಿರಕೊರು", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/ತ್ರಿಕೋನಮಿತಿದ_ಕಾರ್ಯೊಲು", "MATH_TRIG_TOOLTIP_SIN": "ಒಂಜಿ ಡಿಗ್ರಿದ ಸೈನ್ (sine) ಪಿರಕೊರು (ರೇಡಿಯನ್ ಅತ್ತ್).", "MATH_TRIG_TOOLTIP_COS": "ಒಂಜಿ ಡಿಗ್ರಿದ ಕೊಸೈನ್ (cosine) ಪಿರಕೊರು (ರೇಡಿಯನ್ ಅತ್ತ್).", "MATH_TRIG_TOOLTIP_TAN": "ಒಂಜಿ ಡಿಗ್ರಿದ ಟ್ಯಾನ್‌ಜೆಂಟ್ (tangent) ಪಿರಕೊರು (ರೇಡಿಯನ್ ಅತ್ತ್).", "MATH_TRIG_TOOLTIP_ASIN": "ಒಂಜಿ ಸಂಖ್ಯೆದ ಆರ್ಕ್‌ಸೈನ್ ಪಿರಕೊರು.", "MATH_TRIG_TOOLTIP_ACOS": "ಒಂಜಿ ಸಂಖ್ಯೆದ ಆರ್ಕ್‌‌ಕೊಸೈನ್ ಪಿರಕೊರು.", "MATH_TRIG_TOOLTIP_ATAN": "ಒಂಜಿ ಸಂಖ್ಯೆದ ಆರ್ಕ್‌ಟ್ಯಾನ್‌ಜ್ಂಟ್ ಪಿರಕೊರು.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/ಗಣಿತ_ನಿರಂತರ", "MATH_CONSTANT_TOOLTIP": "ಒಂಜಿ ಸಾಮಾನ್ಯ ಸ್ಥಿರಾಂಕೊನು ಪಿರಕೊರು: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (infinity).", "MATH_IS_EVEN": "ಸಮ ಸಂಖ್ಯೆ", "MATH_IS_ODD": "ಬೆಸ ಸಂಖ್ಯೆ", "MATH_IS_PRIME": "ಅವಿಭಾಜ್ಯ ಸಂಖ್ಯೆ", "MATH_IS_WHOLE": "ಪೂರ್ಣ ಸಂಖ್ಯೆ", "MATH_IS_POSITIVE": "ಧನ ಸಂಖ್ಯೆ", "MATH_IS_NEGATIVE": "ಋಣ ಸಂಖ್ಯೆ", "MATH_IS_DIVISIBLE_BY": "ಭಾಗಿಪೊಲಿ", "MATH_IS_TOOLTIP": "ಒಂಜಿ ಸಂಖ್ಯೆ ಸಮನಾ, ಬೆಸನಾ, ಅವಿಭಾಜ್ಯನಾ, ಪೂರ್ಣನಾ, ಧನನಾ, ಋಣನಾ, ಅತ್ತಂಡ ಅವೆನ್ ಬೇತೆ ಒಂಜಿ ನಿರ್ದಿಷ್ಟ ಸಂಖ್ಯೆಡ್ದ್ ಭಾಗಿಪೊಲಿಯಾ ಪಂದ್ ಪರೀಕ್ಷೆ ಮಲ್ಪು. ನಿಜ ಅತ್ತಂಡ ಸುಲ್ಲುನು ಪಿರಕೊರ್ಪುಂಡು.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "%1 ನ್ %2 ಟ್ ಬದಲ್ ಮಲ್ಪು", "MATH_CHANGE_TOOLTIP": "'%1' ವ್ಯತ್ಯಯೊಗು ಒಂಜಿ ಸಂಖ್ಯೆನ್ ಸೇರಾವ್", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/ಪೂರ್ಣಾಂಕೊ", "MATH_ROUND_TOOLTIP": "ಒಂಜಿ ಸಂಖ್ಯೆನ್ ಮಿತ್ತ್‌ಗ್ ಅತ್ತಂಡ ತಿರ್ತ್‌ಗ್ ರೌಂಡ್ ಮಲ್ಪು", "MATH_ROUND_OPERATOR_ROUND": "ರೌಂಡ್", "MATH_ROUND_OPERATOR_ROUNDUP": "ಮಿತ್ತ್‌ಗ್ ರೌಂಡ್", "MATH_ROUND_OPERATOR_ROUNDDOWN": "ತಿರ್ತ್‌ಗ್ ರೌಂಡ್", "MATH_ONLIST_OPERATOR_SUM": "ಪಟ್ಟಿದ ಮೊತ್ತ", "MATH_ONLIST_TOOLTIP_SUM": "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಾತಾ ಸಂಖ್ಯೆಲೆನ ಮೊತ್ತನ್ ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_MIN": "ಪಟ್ಟಿಡ್ ಕಿಞ್ಞವ್", "MATH_ONLIST_TOOLTIP_MIN": "ಪಟ್ಟಿಡುಪ್ಪುನ ಕಿಞ್ಞ ಸಂಕ್ಯೆನ್ ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_MAX": "ಪಟ್ಟಿಡ್ ಮಲ್ಲವ್", "MATH_ONLIST_TOOLTIP_MAX": "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಲ್ಲ ಸಂಖ್ಯೆನ್ ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_AVERAGE": "ಪಟ್ಟಿದ ಸರಾಸರಿ", "MATH_ONLIST_TOOLTIP_AVERAGE": "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಾತಾ ಸಂಖ್ಯೆಲೆನ ಸರಾಸರಿನ್ ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_MEDIAN": "ಪಟ್ಟಿದ ನಡುತ್ತವ್", "MATH_ONLIST_TOOLTIP_MEDIAN": "ಪಟ್ಟಿಡುಪ್ಪುನ ನಡುತ್ತ ಸಂಖ್ಯೆನ್ ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_MODE": "ಪಟ್ಟಿದ ಮಸ್ತ್ ಸಾಮಾನ್ಯ ಮೌಲ್ಯ", "MATH_ONLIST_TOOLTIP_MODE": "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಸ್ತ್ ಸಾಮಾನ್ಯ ವಿಷಯೊನು ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_STD_DEV": "ಪಟ್ಟಿದ ಪ್ರಮಾಣಿತ ವಿಚಲನ", "MATH_ONLIST_TOOLTIP_STD_DEV": "ಪಟ್ಟಿದ ಪ್ರಮಾಣಿತ ವಿಚಲನೊನು ಪಿರಕೊರು", "MATH_ONLIST_OPERATOR_RANDOM": "ಪಟ್ಟಿದ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಷಯ", "MATH_ONLIST_TOOLTIP_RANDOM": "ಪಟ್ಟಿದ ಒವ್ವಾಂಡಲ ಒಂಜಿ ಅಂಶೊನು ಪಿರಕೊರು.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/ಮೋಡ್ಯುಲೊ_ಒಪರೇಶನ್", "MATH_MODULO_TITLE": " %1 ÷ %2 ತ ಶೇಷ", "MATH_MODULO_TOOLTIP": "ರಡ್ಡ್ ಸಂಖ್ಯೆಲೆನ್ ಭಾಗ ಮಲ್ತ್‌ದ್ ಶೇಷೊನು ಪಿರಕೊರು.", "MATH_CONSTRAIN_TITLE": "%2 ಕಮ್ಮಿ %3 ಜಾಸ್ತಿ %1 ನಿರ್ಬಂಧ ಮಲ್ಪು", "MATH_CONSTRAIN_TOOLTIP": "ನಿಗದಿತ ಮಿತಿತ ನಡುಟು ಒಂಜಿ ಸಂಖ್ಯೆನ್ ನಿರ್ಬಂಧ ಮಲ್ಪು", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/ರಾಂಡಮ್_ನಂಬರ್_ಜನರೇಶನ್", "MATH_RANDOM_INT_TITLE": " %1 ಡ್ದ್ %2 ಯಾದೃಚ್ಛಿಕ ಪೂರ್ಣಾಂಕೊ", "MATH_RANDOM_INT_TOOLTIP": "ರಡ್ಡ್ ನಿಗದಿತ ಮಿತಿತ ನಡುತ್ತ ಯಾದೃಚ್ಛಿಕ ಪೂರ್ಣಾಂಕೊನು ಪಿರಕೊರು", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/ರಾಂಡಮ್_ನಂಬರ್_ಜನರೇಶನ್", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಭಿನ್ನರಾಶಿ", "MATH_RANDOM_FLOAT_TOOLTIP": "0.0 (ಸೇರ್‌ದ್) ಬೊಕ್ಕ 1.0 (ಸೇರಂದೆ) ನಡುತ ಒವ್ವಾಂಡಲ ಒಂಜಿ ಭಿನ್ನರಾಶಿನ್ ಪಿರಕೊರು.", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/ಸ್ಟ್ರಿಂಗ್_(ಕಂಪ್ಯೂಟರ್_ಸೈನ್ಸ್)", "TEXT_TEXT_TOOLTIP": "ಒಂಜಿ ಅಕ್ಷರೊ, ಪದೊ ಅತ್ತಂಡ ಪಾಟೊದ ಒಂಜಿ ಸಾಲ್", "TEXT_JOIN_TITLE_CREATEWITH": "ನೆಡ್ದ್ ಪಟ್ಯೊನು ಉಂಡು ಮಲ್ಪು", "TEXT_JOIN_TOOLTIP": "ಏತಾಂಡಲ ವಿಷಯಲೆನ್ ಒಟ್ಟುಗು ಸೇರಾದ್ ಒಂಜಿ ಪಟ್ಯೊದ ತುಂಡುನು ಉಂಡುಮಲ್ಪು.", "TEXT_CREATE_JOIN_TITLE_JOIN": "ಸೇರಾವ್", "TEXT_CREATE_JOIN_TOOLTIP": "ಈ ಪಠ್ಯ ತಡೆನ್ ಕುಡ ಸಂರಚಣೆ ಮಲ್ಪೆರೆ, ಸೇರಾವ್, ದೆತ್ತ್ ಬುಡು, ಅತ್ತಂಡ ವಿಭಾಗೊಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪು.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "ಪಟ್ಯೊಗು ಒಂಜಿ ವಿಷಯೊನು ಸೇರಾವ್", "TEXT_APPEND_TITLE": "ಇಂದೆಕ್ %1 ಪಟ್ಯೊನು ಸೇರವೆ %2", "TEXT_APPEND_TOOLTIP": "%1 ವ್ಯತ್ಯಯೊಗು ಕೆಲವು ಪಟ್ಯೊಲೆನ್ ಸೇರಾವ್", "TEXT_LENGTH_TITLE": "%1 ಉದ್ದೊ", "TEXT_LENGTH_TOOLTIP": "ಕೊರಿನ ಪಟ್ಯೊದ ಅಕ್ಷರೊಲೆನ (ಅಂತರೊಲು ಸೇರ್‌ದ್) ಸಂಖ್ಯೆನ್ ಪಿರಕೊರು.", "TEXT_ISEMPTY_TITLE": "%1 ಖಾಲಿ", "TEXT_ISEMPTY_TOOLTIP": "ಕೊರಿನ ಪಟ್ಯೊ ಖಾಲಿ ಇತ್ತ್ಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು.", "TEXT_INDEXOF_TOOLTIP": "ರಡ್ಡನೆ ಪಟ್ಯೊಡು ಉಪ್ಪುನ ಸುರುತ ಪಟ್ಯೊ ಸುರುಕ್ಕು/ಅಕೇರಿಗ್ ಬತ್ತಿನೆತ್ತ ಸೂಚಿನ್ ಪಿರಕೊರು. ಪಟ್ಯೊ ತಿಕ್ಕಿಜ್ಜಾಂಡ %1 ನ್ ಪಿರಕೊರು.", "TEXT_INDEXOF_TITLE": "ಪಟ್ಯೊಡು %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "ಪಟ್ಯೊ ಸುರುಕ್ಕು ಬತ್ತಿನೇನ್ ನಾಡ್", "TEXT_INDEXOF_OPERATOR_LAST": "ಪಟ್ಯೊ ಅಕೇರಿಗ್ ಬತ್ತಿನೇನ್ ನಾಡ್", "TEXT_CHARAT_TITLE": "%1 %2 ಪದೊಟ್ಟು", "TEXT_CHARAT_FROM_START": "ಅಕ್ಸರೊ #ನ್ ದೆತ್ತೊನು", "TEXT_CHARAT_FROM_END": "ಅಕೇರಿಡ್ದ್ ಅಕ್ಷರೊ #ನ್ ದೆತ್ತೊನು", "TEXT_CHARAT_FIRST": "ಸುರುತ್ತ ಅಕ್ಷರೊನು ದೆತ್ತೊನು", "TEXT_CHARAT_LAST": "ಅಕೇರಿದ ಅಕ್ಷರೊನು ದೆತ್ತೊನು", "TEXT_CHARAT_RANDOM": "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಅಕ್ಷರೊನು ದೆತ್ತೊನು", "TEXT_CHARAT_TOOLTIP": "ಅಕ್ಷರೊನು ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ಪಿರಕೊರ್ಪುಂಡು.", "TEXT_GET_SUBSTRING_TOOLTIP": "ಪಟ್ಯೊದ ಒಂಜಿ ನಿರ್ದಿಷ್ಟ ಬಾಗೊನು ಪಿರಕೊರ್ಪುಂಡು.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ಪಟ್ಯೊಡು", "TEXT_GET_SUBSTRING_START_FROM_START": "ಅಕ್ಷರೊ #ಡ್ದು ಉಪವಾಕ್ಯೊ ದೆತ್ತೊನು", "TEXT_GET_SUBSTRING_START_FROM_END": "ಅಕೇರಿಡ್ದ್ ಅಕ್ಷರೊ #ಡ್ದು ಉಪವಾಕ್ಯೊನು ದೆತ್ತೊನು", "TEXT_GET_SUBSTRING_START_FIRST": "ಸುರುತ್ತ ಅಕ್ಷರೊ #ಡ್ದು ಉಪವಾಕ್ಯೊನು ದೆತ್ತೊನು", "TEXT_GET_SUBSTRING_END_FROM_START": "ಅಕ್ಷರೊ #ಗು", "TEXT_GET_SUBSTRING_END_FROM_END": "ಅಕೇರಿಡ್ದ್ ಅಕ್ಷರೊ #ಗು", "TEXT_GET_SUBSTRING_END_LAST": "ಅಕೇರಿದ ಅಕ್ಷರೊಗು", "TEXT_CHANGECASE_TOOLTIP": "ಪಟ್ಯೊದ ಒಂಜಿ ನಕಲ್‍ನ್ ಬೇತೆ ನಮೂನೆಡ್ (case) ಪಿರಕೊರು.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "ಮಲ್ಲ ಅಕ್ಷರೊಗು", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "ಎಲ್ಯ ಅಕ್ಷರೊಗು", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "ತರೆಬರವುದ ಅಕ್ಷರೊಗು", "TEXT_TRIM_TOOLTIP": "ಒಂಜಿ ಅತ್ತಂಡ ರಡ್ಡ್ ಕೊಡಿಡ್ದ್ ಅಂತರೊಲೆನ್ (space) ದೆತ್ತ್‌ದ್ ಪಟ್ಯೊದ ಪ್ರತಿನ್ ಪಿರಕೊರು.", "TEXT_TRIM_OPERATOR_BOTH": "ರಡ್ಡ್ ಮೆಯಿತ್ತಲ ಅಂತರೊಲೆನ್ (space) ಕತ್ತೆರ್.", "TEXT_TRIM_OPERATOR_LEFT": "ಎಡತ್ತ ಮೆಯಿತ್ತ ಅಂತರೊಲೆನ್ (space) ಕತ್ತೆರ್.", "TEXT_TRIM_OPERATOR_RIGHT": "ಬಲತ್ತ ಮೆಯಿತ್ತ ಅಂತರೊಲೆನ್ (space) ಕತ್ತೆರ್.", "TEXT_PRINT_TITLE": "%1 ಮುದ್ರಣ", "TEXT_PRINT_TOOLTIP": "ನಿರ್ದಿಷ್ಟ ಪಟ್ಯೊ, ಸಂಖ್ಯೆ ಅತ್ತಂಡ ಬೇತೆ ಮೌಲ್ಯೊನು ಮುದ್ರಿಪುಲೆ.", "TEXT_PROMPT_TYPE_TEXT": "ಸಂದೇಶೊದೊಟ್ಟುಗು ಪಟ್ಯೊಗು ಕೇನ್.", "TEXT_PROMPT_TYPE_NUMBER": "ಸಂದೇಶೊದೊಟ್ಟುಗು ಸಂಕ್ಯೆನ್ ಕೇನ್", "TEXT_PROMPT_TOOLTIP_NUMBER": "ಒಂಜಿ ಸಂಖ್ಯೆಗ್ ಸದಸ್ಯೆರೆನ್ ಕೇನ್", "TEXT_PROMPT_TOOLTIP_TEXT": "ಕೆಲವು ಪಟ್ಯೊಗು ಸದಸ್ಯೆರೆನ್ ಕೇನ್.", "LISTS_CREATE_EMPTY_TITLE": "ಕಾಲಿ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ", "LISTS_CREATE_EMPTY_TOOLTIP": "ಒಂಜಿ ಪಟ್ಟಿ, ೦ ಉದ್ದೊದ, ಒವ್ವೇ ಮಾಹಿತಿ ದಾಂತಿನ ದಾಖಲೆ ಪಿರಕೊರು.", "LISTS_CREATE_WITH_TOOLTIP": "ಏತೇ ವಿಸಯೊಲುಪ್ಪುನ ಒಂಜಿ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ", "LISTS_CREATE_WITH_INPUT_WITH": "ಒಟ್ಟುಗು ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "ಪಟ್ಟಿ", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "ಈ ಪಟ್ಟಿ ತಡೆನ್ ಕುಡ ಸಂರಚನೆ ಮಲ್ಪೆರೆ ಸೇರಾಲೆ, ದೆತ್ತ್ ಬುಡುಲೆ, ಅತ್ತಂಡ ವಿಬಾಗೊಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪುಲೆ.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "ಪಟ್ಟಿಗ್ ಒಂಜಿ ವಿಸಯೊನು ಸೇರಾಲೆ.", "LISTS_REPEAT_TOOLTIP": "ಕೊರಿನ ಮೌಲ್ಯೊ ಒಂಜಿ ನಿರ್ದಿಷ್ಟ ಸಂಕ್ಯೆದಾತ್ ಸರ್ತಿ ಪುನರಾವರ್ತನೆ ಆದುಪ್ಪುನ ಒಂಜಿ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ.", "LISTS_REPEAT_TITLE": "%1 ವಿಸಯೊ %2 ಸರ್ತಿ ಪುನರಾವರ್ತನೆ ಆದುಪ್ಪುನ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ.", "LISTS_LENGTH_TITLE": "%1 ಉದ್ದೊ", "LISTS_LENGTH_TOOLTIP": "ಪಟ್ಟಿದ ಉದ್ದೊನು ಪಿರಕೊರು.", "LISTS_ISEMPTY_TITLE": "%1 ಕಾಲಿ", "LISTS_ISEMPTY_TOOLTIP": "ಪಟ್ಯೊ ಖಾಲಿ ಇತ್ತ್ಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು.", "LISTS_INLIST": "ಪಟ್ಟಿಡ್", "LISTS_INDEX_OF_FIRST": "ವಿಸಯೊ ಸುರುಕ್ಕು ಬತ್ತಿನೇನ್ ನಾಡ್", "LISTS_INDEX_OF_LAST": "ವಿಸಯೊ ಅಕೇರಿಗ್ ಬತ್ತಿನೇನ್ ನಾಡ್", "LISTS_INDEX_OF_TOOLTIP": "ಪಟ್ಟಿಡುಪ್ಪುನ ವಿಸಯೊ ಸುರುಕ್ಕು/ಅಕೇರಿಗ್ ಬತ್ತಿನೆತ್ತ ಸೂಚಿನ್ ಪಿರಕೊರ್ಪುಂಡು. ವಿಸಯೊ ತಿಕ್ಕಿಜ್ಜಾಂಡ %1 ನ್ ಪಿರಕೊರ್ಪುಂಡು.", "LISTS_GET_INDEX_GET": "ದೆತೊನು", "LISTS_GET_INDEX_GET_REMOVE": "ದೆತ್ತೊನು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡು", "LISTS_GET_INDEX_REMOVE": "ದೆಪ್ಪುಲೆ", "LISTS_GET_INDEX_FROM_END": "ಅಕೇರಿಡ್ದ್ #", "LISTS_GET_INDEX_FIRST": "ಸುರುತ", "LISTS_GET_INDEX_LAST": "ಕಡೆತ", "LISTS_GET_INDEX_RANDOM": "ಒವ್ವಾಂಡಲ", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 ಸುರುತ ವಿಸಯೊ", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 ಅಕೇರಿತ ವಿಸಯೊ", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.", "LISTS_SET_INDEX_SET": "ಸೆಟ್ ಮಲ್ಪು", "LISTS_SET_INDEX_INSERT": "ಸೇರಾವ್", "LISTS_SET_INDEX_INPUT_TO": "ಲೆಕ", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಸೇರಾವುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "ಒಂಜಿ ಪಟ್ಟಿದ ಸುರುಕ್ಕು ವಿಸಯೊನು ಸೇರಾವುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "ಒಂಜಿ ಪಟ್ಟಿದ ಅಕೇರಿಗ್ ವಿಸಯೊನು ಸೇರಾವುಂಡು.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಓಲಾಂಡಲ ವಿಸಯೊನು ಸೇರಾವುಂಡು.", "LISTS_GET_SUBLIST_START_FROM_START": "# ಡ್ದ್ ಉಪ-ಪಟ್ಟಿನ್ ದೆತ್ತೊನು", "LISTS_GET_SUBLIST_START_FROM_END": "ಅಕೇರಿಡ್ದ್ # ಡ್ದ್ ಉಪ-ಪಟ್ಟಿನ್ ದೆತ್ತೊನು", "LISTS_GET_SUBLIST_START_FIRST": "ಸುರುಡ್ದು ಉಪ-ಪಟ್ಟಿನ್ ದೆತ್ತೊನು", "LISTS_GET_SUBLIST_END_FROM_START": "# ಗ್", "LISTS_GET_SUBLIST_END_FROM_END": "ಅಕೇರಿಡ್ದ್ # ಗ್", "LISTS_GET_SUBLIST_END_LAST": "ಅಕೇರಿಗ್", "LISTS_GET_SUBLIST_TOOLTIP": "ಪಟ್ಯೊದ ನಿರ್ದಿಷ್ಟ ಬಾಗೊದ ಪ್ರತಿನ್ ಉಂಡುಮಲ್ಪುಂಡು.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "%1 %2 %3 ಇಂಗಡಿಪು", "LISTS_SORT_TOOLTIP": "ಒಂಜಿ ಪಟ್ಟಿದ ಒಂಜಿ ಪ್ರತಿನ್ ಇಂಗಡಿಪು", "LISTS_SORT_ORDER_ASCENDING": "ಏರುನು", "LISTS_SORT_ORDER_DESCENDING": "ಜಪ್ಪುನು", "LISTS_SORT_TYPE_NUMERIC": "ಸಂಖ್ಯೆ", "LISTS_SORT_TYPE_TEXT": "ಅಕ್ಷರೊ", "LISTS_SORT_TYPE_IGNORECASE": "ಅಕ್ಷರೊ, ನಮೂನೆ (case) ಅಲಕ್ಷ್ಯೊ ಮಲ್ಪುಲೆ", "LISTS_SPLIT_LIST_FROM_TEXT": "ಪಟ್ಯೊಲೆ ಪಟ್ಟಿನ್ ತಯಾರ್ ಮಲ್ಪುಲೆ", "LISTS_SPLIT_TEXT_FROM_LIST": "ಪಟ್ಟಿದ ಪಟ್ಯೊನು ತಯಾರ್ ಮಲ್ಪುಲೆ", "LISTS_SPLIT_WITH_DELIMITER": "ಮಿತಿಸೂಚಕೊದ ಒಟ್ಟುಗು", "LISTS_SPLIT_TOOLTIP_SPLIT": "ಪಟ್ಯೊಲೆನ್ ಪ್ರತಿ ಮಿತಿಸೂಚಕೊಡು ತುಂಡು ಮಲ್ತ್‌ದ್ ಪಟ್ಯೊಲೆನ ಒಂಜಿ ಪಟ್ಟಿಗ್ ಬಾಗೊ ಮಲ್ಪುಲೆ.", "LISTS_SPLIT_TOOLTIP_JOIN": "ಪಟ್ಯೊಲೆನ ಒಂಜಿ ಪಟ್ಟಿನ್ ಮಿತಿಸೂಚಕೊದ ಮೂಲಕೊ ಬೇತೆ ಮಲ್ತ್‌ದ್ ಒಂಜಿ ಪಟ್ಯೊಗು ಸೇರಾಲೆ.", "VARIABLES_GET_TOOLTIP": "ಈ ವ್ಯತ್ಯಯೊದ ಮೌಲ್ಯೊನು ಪಿರಕೊರು.", "VARIABLES_GET_CREATE_SET": "'ಸೆಟ್ %1' ಉಂಡುಮಲ್ಪುಲೆ", "VARIABLES_SET": "%1 ನ್ %2 ಕ್ಕ್ ಸೆಟ್ ಮಲ್ಪುಲೆ", "VARIABLES_SET_TOOLTIP": "ಈ ವ್ಯತ್ಯಯೊನು ಇನ್‌ಪುಟ್‌ಗ್ ಸಮ ಆಪಿಲೆಕ ಸೆಟ್ ಮಲ್ಪುಂಡು.", "VARIABLES_SET_CREATE_GET": "'ದೆತ್ತೊನು %1' ಉಂಡುಮಲ್ಪುಲೆ", "PROCEDURES_DEFNORETURN_TITLE": "ಇಂದೆಕ್", "PROCEDURES_DEFNORETURN_PROCEDURE": "ಎಂಚಿನಾಂಡಲ ಮಲ್ಪುಲೆ", "PROCEDURES_BEFORE_PARAMS": "ಒಟ್ಟುಗು:", "PROCEDURES_CALL_BEFORE_PARAMS": "ಒಟ್ಟುಗು:", "PROCEDURES_DEFNORETURN_TOOLTIP": "ಔಟ್‌ಪುಟ್ ದಾಂತಿನ ಕಾರ್ಯೊನು ಉಂಡುಮಲ್ಪುಂಡು.", "PROCEDURES_DEFNORETURN_COMMENT": "ಈ ಕಾರ್ಯೊನು ಇವರಿಪುಲೆ...", "PROCEDURES_DEFRETURN_RETURN": "ಪಿರಕೊರು", "PROCEDURES_DEFRETURN_TOOLTIP": "ಔಟ್‌ಪುಟ್ ಇತ್ತಿನ ಕಾರ್ಯೊನು ಉಂಡುಮಲ್ಪುಂಡು.", "PROCEDURES_ALLOW_STATEMENTS": "ಹೇಳಿಕೆಗ್ ಅವಕಾಸೊ", "PROCEDURES_DEF_DUPLICATE_WARNING": "ಎಚ್ಚರಿಕೆ: ಈ ಕಾರ್ಯೊಡು ನಕಲಿ ಮಾನದಂಡೊ ಉಂಡು.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/ವರ್ಗಮೂಲೊ", "PROCEDURES_CALLNORETURN_TOOLTIP": "'%1' ಬಳಕೆದಾರೆರೆ ಕಾರ್ಯೊನು ನಡಪಾಲೆ.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/ವರ್ಗಮೂಲೊ", "PROCEDURES_CALLRETURN_TOOLTIP": " ಬಳಕೆದಾರೆರೆ ಕಾರ್ಯೊ '%1' ನು ನಡಪಾಲೆ ಬುಕ್ಕೊ ಅಯಿತ ಉತ್ಪಾದನೆನ್ ಗಲಸ್‌ಲೆ.", "PROCEDURES_MUTATORCONTAINER_TITLE": "ಉಲಪರಿಪು", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "ಸೇರಾಲೆ, ದೆತ್ತ್‌ ಬುಡುಲೆ, ಅತ್ತಂಡ ಈ ಕಾರ್ಯೊಗು ಉಲಪರಿಪುಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪುಲೆ.", "PROCEDURES_MUTATORARG_TITLE": "ಉಲಪರಿಪುದ ಪುದರ್:", "PROCEDURES_MUTATORARG_TOOLTIP": "ಕಾರ್ಯೊಗು ಒಂಜಿ ಉಲಪರಿಪುನು ಸೇರಲೆ.", "PROCEDURES_HIGHLIGHT_DEF": "ದೆರ್ತ್ ತೋಜುನ ಕಾರ್ಯೊದ ವ್ಯಾಕ್ಯಾನೊ", "PROCEDURES_CREATE_DO": " '%1'ನ್ ಉಂಡುಮಲ್ಪುಲೆ", "PROCEDURES_IFRETURN_TOOLTIP": "ಮೌಲ್ಯೊ ಸತ್ಯೊ ಆಂಡ, ರಡ್ಡನೆ ಮೌಲ್ಯೊನು ಪಿರಕೊರು.", "PROCEDURES_IFRETURN_WARNING": "ಎಚ್ಚರಿಕೆ: ಒಂಜಿ ಕಾರ್ಯ ವ್ಯಾಕ್ಯಾನೊದುಲಯಿ ಮಾತ್ರ ಈ ತಡೆನ್ ಗಲಸೊಲಿ.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "ದಾದಾಂಡಲ ಪನ್ಲೇ...", "DIALOG_OK": "ಅವು", "DIALOG_CANCEL": "ಉಂತಾಲೆ"}