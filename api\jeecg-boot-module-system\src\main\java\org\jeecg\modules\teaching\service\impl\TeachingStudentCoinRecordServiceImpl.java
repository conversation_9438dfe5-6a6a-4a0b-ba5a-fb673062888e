package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingGiftExchange;
import org.jeecg.modules.teaching.entity.TeachingStudentCoinRecord;
import org.jeecg.modules.teaching.mapper.TeachingGiftExchangeMapper;
import org.jeecg.modules.teaching.mapper.TeachingStudentCoinRecordMapper;
import org.jeecg.modules.teaching.service.ITeachingStudentCoinRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 * 学生金币记录服务实现类
 */
@Service
public class TeachingStudentCoinRecordServiceImpl extends ServiceImpl<TeachingStudentCoinRecordMapper, TeachingStudentCoinRecord> implements ITeachingStudentCoinRecordService {

    @Autowired
    private TeachingGiftExchangeMapper giftExchangeMapper;

    @Override
    public Result<?> getUserCoinRecords(String userId, Integer pageNo, Integer pageSize) {
        Result<IPage<TeachingStudentCoinRecord>> result = new Result<>();
        try {
            // 创建分页对象
            Page<TeachingStudentCoinRecord> page = new Page<>(pageNo, pageSize);
            
            // 构建查询条件
            QueryWrapper<TeachingStudentCoinRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.orderByDesc("create_time");
            
            // 执行分页查询
            IPage<TeachingStudentCoinRecord> pageList = this.page(page, queryWrapper);
            
            result.setSuccess(true);
            result.setResult(pageList);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("获取金币记录失败：" + e.getMessage());
        }
        return result;
    }

    @Override
    public Result<?> getUserExchangeRecords(String userId, Integer pageNo, Integer pageSize) {
        Result<IPage<TeachingGiftExchange>> result = new Result<>();
        try {
            // 创建分页对象
            Page<TeachingGiftExchange> page = new Page<>(pageNo, pageSize);
            
            // 构建查询条件
            QueryWrapper<TeachingGiftExchange> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.orderByDesc("exchange_time");
            
            // 执行分页查询
            IPage<TeachingGiftExchange> pageList = giftExchangeMapper.selectPage(page, queryWrapper);
            
            result.setSuccess(true);
            result.setResult(pageList);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("获取兑换记录失败：" + e.getMessage());
        }
        return result;
    }
} 