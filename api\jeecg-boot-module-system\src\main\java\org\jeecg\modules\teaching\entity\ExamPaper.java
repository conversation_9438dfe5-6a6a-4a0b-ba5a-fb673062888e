package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 考试试卷
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@ApiModel(value="exam_paper对象", description="考试试卷")
@Data
@TableName("exam_paper")
public class ExamPaper implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "试卷标题")
    private String title;
    
    @ApiModelProperty(value = "所属科目 (Scratch, Python, C++)")
    private String subject;
    
    @ApiModelProperty(value = "所属级别 (Scratch:1-4, Python/C++:1-8)")
    private String level;
    
    @ApiModelProperty(value = "难度 (1:简单, 2:中等, 3:困难)")
    private Integer difficulty;
    
    @ApiModelProperty(value = "类型 (真题或模拟)")
    private String type;
    
    @ApiModelProperty(value = "年份")
    private Integer year;
    
    @ApiModelProperty(value = "作者")
    private String author;
    
    @TableField("exam_duration")
    @ApiModelProperty(value = "考试时长 (分钟)")
    private Integer examDuration;

    @ApiModelProperty(value = "试卷内容 (JSON格式，存储题目ID列表及分数)")
    private String content;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 