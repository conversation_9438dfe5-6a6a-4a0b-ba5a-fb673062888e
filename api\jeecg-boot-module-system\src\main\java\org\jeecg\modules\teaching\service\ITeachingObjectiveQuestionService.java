package org.jeecg.modules.teaching.service;

import org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**
 * @Description: 客观题
 * @Author: jeecg-boot
 * @Date:   2023-05-10
 * @Version: V1.0
 */
public interface ITeachingObjectiveQuestionService extends IService<TeachingObjectiveQuestion> {
    /**
     * 根据单元ID查询相关客观题
     * @param unitId 单元ID
     * @param courseId 课程ID
     * @return 客观题列表
     */
    List<TeachingObjectiveQuestion> queryListByUnitId(String unitId, String courseId);
    
    /**
     * 分页查询客观题并关联课程名称
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<TeachingObjectiveQuestion> pageWithCourseName(Page<TeachingObjectiveQuestion> page, QueryWrapper<TeachingObjectiveQuestion> queryWrapper);
} 