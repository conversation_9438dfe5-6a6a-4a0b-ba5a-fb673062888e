package org.jeecg.modules.system.dto;

import cloud.tianai.captcha.validator.common.model.dto.ImageCaptchaTrack;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 自定义验证码轨迹类
 */
public class CustomImageCaptchaTrack extends ImageCaptchaTrack {
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private Date startTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private Date stopTime;
    
    @Override
    public Date getStartTime() {
        return startTime;
    }
    
    @Override
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    @Override
    public Date getStopTime() {
        return stopTime;
    }
    
    @Override
    public void setStopTime(Date stopTime) {
        this.stopTime = stopTime;
    }
} 