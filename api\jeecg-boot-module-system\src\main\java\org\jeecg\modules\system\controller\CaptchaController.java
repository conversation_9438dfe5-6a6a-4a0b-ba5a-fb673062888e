package org.jeecg.modules.system.controller;

import cloud.tianai.captcha.application.ImageCaptchaApplication;
import cloud.tianai.captcha.application.vo.CaptchaResponse;
import cloud.tianai.captcha.application.vo.ImageCaptchaVO;
import cloud.tianai.captcha.common.response.ApiResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.dto.CaptchaTrackDto;
import org.jeecg.modules.system.dto.GenerateCaptchaDto;
import org.jeecg.modules.system.entity.SysConfig;
import org.jeecg.modules.system.enumeration.CaptchaType;
import org.jeecg.modules.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * 验证码控制器
 */
@RestController
@RequestMapping("/captcha")
@Slf4j
@Api(tags = "验证码接口")
public class CaptchaController {

    @Autowired
    private ImageCaptchaApplication imageCaptchaApplication;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 从系统配置中获取验证码类型
     * @return 验证码类型代码
     */
    private Integer getCaptchaTypeFromConfig() {
        // 默认为滑块验证码
        Integer defaultType = 1;
        
        try {
            // 从系统配置中获取验证码类型
            SysConfig config = sysConfigService.getOne(
                new QueryWrapper<SysConfig>().lambda().eq(SysConfig::getConfigKey, "captchaType")
            );
            
            if (config != null && config.getConfigValue() != null) {
                return Integer.parseInt(config.getConfigValue().trim());
            }
        } catch (Exception e) {
            log.error("获取验证码类型配置失败", e);
        }
        
        return defaultType;
    }

    /**
     * 生成验证码
     * @param generateCaptchaDto 生成验证码参数
     * @return 验证码数据
     */
    @PostMapping("/generate")
    @ApiOperation(value = "生成验证码", notes = "生成滑块、旋转等类型的验证码")
    public Result<?> generate(@RequestBody GenerateCaptchaDto generateCaptchaDto) {
        // 如果前端没有指定类型，则从系统配置中获取
        Integer typeCode = generateCaptchaDto.getType();
        if (typeCode == null) {
            typeCode = getCaptchaTypeFromConfig();
            generateCaptchaDto.setType(typeCode);
        }
        
        String captchaType = CaptchaType.getCaptchaTypeByTypeCode(typeCode);

        // 根据验证码类型生成对应的验证码
        CaptchaResponse<ImageCaptchaVO> captchaResponse = imageCaptchaApplication.generateCaptcha(captchaType);

        log.info("验证码唯一标识: {}, 类型: {}", captchaResponse.getId(), captchaType);

        return Result.ok(captchaResponse);
    }

    /**
     * 校验验证码
     * @param captchaTrackDto 验证码校验参数
     * @return 校验结果
     */
    @PostMapping("/check")
    @ApiOperation(value = "校验验证码", notes = "校验用户完成的验证码操作是否正确")
    public Result<?> check(@RequestBody CaptchaTrackDto captchaTrackDto) {
        log.info("验证码校验请求参数: {}", captchaTrackDto);
        
        // 直接使用自定义的CustomImageCaptchaTrack
        ApiResponse<?> response = imageCaptchaApplication.matching(captchaTrackDto.getId(), captchaTrackDto.getData());
        
        if (response.isSuccess()) {
            return Result.ok("验证成功");
        } else {
            return Result.error(response.getMsg());
        }
    }
} 