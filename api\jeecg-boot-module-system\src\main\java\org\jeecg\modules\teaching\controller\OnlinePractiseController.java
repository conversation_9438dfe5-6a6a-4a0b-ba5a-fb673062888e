package org.jeecg.modules.teaching.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Arrays;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;

/**
 * @Description: 在线刷题
 * @Author: jeecg-boot
 * @Date:   2023-06-18
 * @Version: V1.0
 */
@Api(tags="在线刷题")
@RestController
@RequestMapping("/teaching/examSystem/onlinePractise")
@Slf4j
public class OnlinePractiseController {

    @Autowired
    private IExamQuestionService examQuestionService;

    /**
     * 获取刷题题目列表
     * @param subject 科目 (Scratch, Python, C++)
     * @param level 级别 (Scratch:1-4, Python/C++:1-8)
     * @param questionType 题目类型 (1:单选题, 2:判断题, 3:编程题)
     * @param difficulty 难度 (1:简单, 2:中等, 3:困难)
     * @param count 题目数量
     * @return 题目列表
     */
    @AutoLog(value = "在线刷题-获取题目")
    @ApiOperation(value="在线刷题-获取题目", notes="支持按科目、级别、题型、数量等条件组合筛选题目")
    @GetMapping(value = "/getQuestions")
    public Result<?> getQuestions(
            @ApiParam(value = "科目", required = false) @RequestParam(required = false) String subject,
            @ApiParam(value = "级别", required = false) @RequestParam(required = false) String level,
            @ApiParam(value = "题目类型", required = false) @RequestParam(required = false) Integer questionType,
            @ApiParam(value = "难度", required = false) @RequestParam(required = false) Integer difficulty,
            @ApiParam(value = "题目数量", required = false) @RequestParam(required = false, defaultValue = "10") Integer count) {
        
        log.info("获取刷题题目，条件：subject={}, level={}, questionType={}, difficulty={}, count={}",
                subject, level, questionType, difficulty, count);
        
        // 调用service获取题目
        List<ExamQuestion> questions = examQuestionService.getQuestionsByCondition(null, subject, level, questionType, difficulty);
        
        // 如果指定了数量，并且题目数量超过了指定数量，则截取
        if (questions.size() > count) {
            questions = questions.subList(0, count);
        }
        
        // 处理题目，移除答案和解析信息
        questions.forEach(question -> {
            // 题目内容是JSON格式，需要移除答案和解析字段
            if (question.getContent() != null) {
                // 此处需要解析JSON，移除答案和解析后重新设置
                // 由于内容格式复杂，这里简化处理，实际应该使用JSON库处理
                String content = question.getContent();
                // 移除答案和解析的逻辑，这里简化处理
                // 实际开发中需要根据题目类型分别处理
                question.setContent(removeAnswerAndAnalysis(content, question.getQuestionType()));
            }
        });
        
        return Result.ok(questions);
    }
    
    /**
     * 移除题目内容中的答案和解析
     * @param content 题目内容
     * @param questionType 题目类型
     * @return 处理后的内容
     */
    private String removeAnswerAndAnalysis(String content, Integer questionType) {
        // 根据题目类型分别处理
        // 实际开发中应该使用JSON库解析处理
        // 这里为了简化，直接返回原内容，在实际项目中需要完善此逻辑
        return content;
    }
    
    /**
     * 获取随机刷题题目列表
     * @param subject 科目 (必填)
     * @param count 题目数量 (默认10)
     * @param mode 刷题模式 (count:按题目数量, time:按时间限制, free:自由模式)
     * @return 题目列表
     */
    @AutoLog(value = "在线刷题-获取随机题目")
    @ApiOperation(value="在线刷题-获取随机题目", notes="智能随机选择题目，只需提供科目即可")
    @GetMapping(value = "/getRandomQuestions")
    public Result<?> getRandomQuestions(
            @ApiParam(value = "科目", required = true) @RequestParam String subject,
            @ApiParam(value = "题目数量", required = false) @RequestParam(required = false, defaultValue = "10") Integer count,
            @ApiParam(value = "刷题模式", required = false) @RequestParam(required = false, defaultValue = "count") String mode) {
        
        log.info("获取随机刷题题目，条件：subject={}, count={}, mode={}", subject, count, mode);
        
        if (StringUtils.isBlank(subject)) {
            return Result.error("科目不能为空");
        }
        
        try {
            // 智能选择参数
            // 1. 获取题目分布信息
            Map<String, Object> distribution = getQuestionDistribution(subject);
            if (distribution == null || distribution.isEmpty()) {
                return Result.error("未找到该科目的题目");
            }
            
            // 2. 基于分布智能选择参数
            String level = selectOptimalLevel(distribution);
            List<Integer> questionTypes = selectOptimalQuestionTypes(distribution);
            int[] difficultyRange = selectOptimalDifficultyRange(distribution);
            
            // 3. 查询题目
            List<ExamQuestion> allQuestions = new ArrayList<>();
            
            // 如果有多种题型，分别查询每种题型
            for (Integer questionType : questionTypes) {
                // 按照选定的参数查询题目
                List<ExamQuestion> questions = examQuestionService.getQuestionsByCondition(
                        null, subject, level, questionType, null);
                
                // 过滤符合难度范围的题目
                questions = questions.stream()
                        .filter(q -> q.getDifficulty() >= difficultyRange[0] && q.getDifficulty() <= difficultyRange[1])
                        .collect(Collectors.toList());
                
                allQuestions.addAll(questions);
            }
            
            // 如果题目不足，尝试放宽条件
            if (allQuestions.size() < count) {
                // 放宽级别条件
                List<ExamQuestion> moreQuestions = examQuestionService.getQuestionsByCondition(
                        null, subject, null, null, null);
                
                // 添加未包含的题目
                for (ExamQuestion q : moreQuestions) {
                    if (allQuestions.stream().noneMatch(existing -> existing.getId().equals(q.getId()))) {
                        allQuestions.add(q);
                    }
                    
                    // 如果题目已足够，停止添加
                    if (allQuestions.size() >= count * 3) { // 预留足够的随机选择空间
                        break;
                    }
                }
            }
            
            // 4. 随机选择题目
            Collections.shuffle(allQuestions);
            
            // 5. 截取需要的题目数量
            if (allQuestions.size() > count) {
                allQuestions = allQuestions.subList(0, count);
            }
            
            // 6. 处理题目，移除答案和解析信息
            allQuestions.forEach(question -> {
                // 题目内容是JSON格式，需要移除答案和解析字段
                if (question.getContent() != null) {
                    String content = question.getContent();
                    question.setContent(removeAnswerAndAnalysis(content, question.getQuestionType()));
                }
            });
            
            return Result.ok(allQuestions);
        } catch (Exception e) {
            log.error("获取随机刷题题目失败", e);
            return Result.error("获取随机刷题题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取题库分布信息
     * @param subject 科目
     * @return 分布信息
     */
    private Map<String, Object> getQuestionDistribution(String subject) {
        Map<String, Object> distribution = new HashMap<>();
        
        // 查询该科目下所有题目
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subject", subject);
        List<ExamQuestion> questions = examQuestionService.list(queryWrapper);
        
        if (questions.isEmpty()) {
            return distribution;
        }
        
        // 级别分布
        Map<String, Integer> levelCounts = new HashMap<>();
        // 题型分布
        Map<Integer, Integer> typeCounts = new HashMap<>();
        // 难度分布
        Map<Integer, Integer> difficultyCounts = new HashMap<>();
        
        // 统计分布
        for (ExamQuestion question : questions) {
            // 级别分布
            String level = question.getLevel();
            levelCounts.put(level, levelCounts.getOrDefault(level, 0) + 1);
            
            // 题型分布
            Integer type = question.getQuestionType();
            typeCounts.put(type, typeCounts.getOrDefault(type, 0) + 1);
            
            // 难度分布
            Integer difficulty = question.getDifficulty();
            difficultyCounts.put(difficulty, difficultyCounts.getOrDefault(difficulty, 0) + 1);
        }
        
        distribution.put("levels", levelCounts);
        distribution.put("types", typeCounts);
        distribution.put("difficulties", difficultyCounts);
        distribution.put("total", questions.size());
        
        return distribution;
    }
    
    /**
     * 选择最优的级别
     * @param distribution 分布信息
     * @return 最优级别
     */
    private String selectOptimalLevel(Map<String, Object> distribution) {
        @SuppressWarnings("unchecked")
        Map<String, Integer> levelCounts = (Map<String, Integer>) distribution.get("levels");
        if (levelCounts == null || levelCounts.isEmpty()) {
            return null; // 没有级别信息
        }
        
        // 选择题目数量最多的级别
        return levelCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }
    
    /**
     * 选择最优的题型组合
     * @param distribution 分布信息
     * @return 题型列表
     */
    private List<Integer> selectOptimalQuestionTypes(Map<String, Object> distribution) {
        @SuppressWarnings("unchecked")
        Map<Integer, Integer> typeCounts = (Map<Integer, Integer>) distribution.get("types");
        if (typeCounts == null || typeCounts.isEmpty()) {
            return Arrays.asList(1, 2, 3); // 默认包含所有题型
        }
        
        List<Integer> selectedTypes = new ArrayList<>();
        
        // 按题目数量排序题型
        List<Map.Entry<Integer, Integer>> sortedTypes = typeCounts.entrySet().stream()
                .sorted(Map.Entry.<Integer, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());
        
        // 选择前2种或所有题型
        for (int i = 0; i < Math.min(2, sortedTypes.size()); i++) {
            selectedTypes.add(sortedTypes.get(i).getKey());
        }
        
        // 如果没有足够的题型，则添加更多
        if (selectedTypes.isEmpty()) {
            selectedTypes.addAll(Arrays.asList(1, 2, 3));
        }
        
        return selectedTypes;
    }
    
    /**
     * 选择最优的难度范围
     * @param distribution 分布信息
     * @return 难度范围[最小值, 最大值]
     */
    private int[] selectOptimalDifficultyRange(Map<String, Object> distribution) {
        @SuppressWarnings("unchecked")
        Map<Integer, Integer> difficultyCounts = (Map<Integer, Integer>) distribution.get("difficulties");
        if (difficultyCounts == null || difficultyCounts.isEmpty()) {
            return new int[]{1, 3}; // 默认包含所有难度
        }
        
        // 按题目数量排序难度
        List<Map.Entry<Integer, Integer>> sortedDifficulties = difficultyCounts.entrySet().stream()
                .sorted(Map.Entry.<Integer, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());
        
        // 获取题目最多的难度
        int mostCommonDifficulty = sortedDifficulties.get(0).getKey();
        
        // 根据最常见的难度设置合理的难度范围
        int minDifficulty = Math.max(1, mostCommonDifficulty - 1);
        int maxDifficulty = Math.min(3, mostCommonDifficulty + 1);
        
        return new int[]{minDifficulty, maxDifficulty};
    }
} 