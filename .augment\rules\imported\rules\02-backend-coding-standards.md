---
type: "always_apply"
---

# 后端开发规范

## 代码结构规范

### 包结构
```
org.jeecg.modules.teaching/
├── controller/          # 控制器层
├── service/            # 服务接口层
│   └── impl/           # 服务实现层
├── mapper/             # 数据访问层
├── entity/             # 实体类
├── vo/                 # 视图对象
├── dto/                # 数据传输对象
├── util/               # 工具类
└── config/             # 配置类
```

### 类命名规范
- Controller: `*Controller`
- Service接口: `I*Service`
- Service实现: `*ServiceImpl`
- Mapper: `*Mapper`
- Entity: 实体名 (如: `ExamQuestion`)
- VO: `*VO`
- DTO: `*DTO`

## Controller层规范

### 基础结构
```java
@RestController
@RequestMapping("/teaching/examQuestion")
@Slf4j
@Api(tags = "题库管理")
public class ExamQuestionController {
    
    @Autowired
    private IExamQuestionService examQuestionService;
    
    @GetMapping("/list")
    @ApiOperation("分页查询")
    @RequiresPermissions("teaching:exam:question:list")
    public Result<IPage<ExamQuestion>> queryPageList(
        ExamQuestion examQuestion,
        @RequestParam(defaultValue = "1") Integer pageNo,
        @RequestParam(defaultValue = "10") Integer pageSize) {
        // 实现逻辑
    }
}
```

### 接口设计原则
1. **统一前缀**: `/teaching/模块名/`
2. **RESTful风格**: 使用标准HTTP方法
3. **参数验证**: 使用`@Valid`和`@Validated`
4. **权限控制**: 使用`@RequiresPermissions`
5. **异常处理**: 统一异常处理机制

### 常用接口模板
```java
// 分页查询
@GetMapping("/list")
public Result<IPage<Entity>> queryPageList(Entity entity, 
    @RequestParam(defaultValue = "1") Integer pageNo,
    @RequestParam(defaultValue = "10") Integer pageSize)

// 新增
@PostMapping("/add")
public Result<String> add(@RequestBody @Valid Entity entity)

// 编辑
@PutMapping("/edit")
public Result<String> edit(@RequestBody @Valid Entity entity)

// 删除
@DeleteMapping("/delete")
public Result<String> delete(@RequestParam String id)

// 批量删除
@DeleteMapping("/deleteBatch")
public Result<String> deleteBatch(@RequestParam String ids)

// 详情查询
@GetMapping("/queryById")
public Result<Entity> queryById(@RequestParam String id)
```

## Service层规范

### 接口定义
```java
public interface IExamQuestionService extends IService<ExamQuestion> {
    
    /**
     * 分页查询题目
     */
    IPage<ExamQuestion> queryPageList(ExamQuestion examQuestion, 
                                     Integer pageNo, Integer pageSize);
    
    /**
     * 批量导入题目
     */
    Result<String> importQuestions(MultipartFile file, String subject, 
                                  String level, Integer difficulty);
    
    /**
     * 自动格式化模板
     */
    String autoFormatTemplate(MultipartFile file, String subject, 
                             String level, Integer difficulty);
}
```

### 实现类规范
```java
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ExamQuestionServiceImpl 
    extends ServiceImpl<ExamQuestionMapper, ExamQuestion> 
    implements IExamQuestionService {
    
    @Override
    public IPage<ExamQuestion> queryPageList(ExamQuestion examQuestion, 
                                            Integer pageNo, Integer pageSize) {
        log.info("开始分页查询题目，条件：{}", examQuestion);
        
        Page<ExamQuestion> page = new Page<>(pageNo, pageSize);
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.isNotBlank(examQuestion.getTitle())) {
            queryWrapper.like("title", examQuestion.getTitle());
        }
        
        return this.page(page, queryWrapper);
    }
}
```

## Result返回值规范

### 重要：避免Result.ok()重载陷阱
```java
// ❌ 错误：String类型会调用Result.ok(String msg)，数据进入message字段
return Result.ok(stringContent);

// ✅ 正确：强制转换为Object，数据进入result字段
return Result.ok((Object) stringContent);

// ✅ 或者明确使用带参数的构造方法
Result<String> result = new Result<>();
result.setSuccess(true);
result.setResult(stringContent);
return result;
```

### Result类方法说明
- `Result.ok()`: 无参数，返回默认成功消息
- `Result.ok(String msg)`: 设置message字段
- `Result.ok(Object data)`: 设置result字段
- `Result.error(String msg)`: 返回错误信息

## Entity层规范

### 基础实体类
```java
@Data
@TableName("exam_question")
@ApiModel(value = "ExamQuestion对象", description = "考试题目")
public class ExamQuestion implements Serializable {
    
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "题目标题")
    private String title;
    
    @ApiModelProperty(value = "题目类型")
    private String questionType;
    
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
```

## 异常处理规范

### 全局异常处理
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(Exception.class)
    public Result<String> handleException(Exception e) {
        log.error("系统异常：", e);
        return Result.error("系统异常，请联系管理员");
    }
}
```

## 日志规范

### 日志级别使用
- **ERROR**: 系统错误、异常
- **WARN**: 警告信息  
- **INFO**: 重要业务流程
- **DEBUG**: 调试信息

### 日志格式
```java
log.info("开始执行业务操作，参数：{}", param);
log.error("业务操作失败，原因：{}", e.getMessage(), e);
```
