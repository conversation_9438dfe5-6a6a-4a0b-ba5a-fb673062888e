{"@metadata": {"authors": ["Athena in Wonderland", "Diniscoelho", "<PERSON><PERSON><PERSON>", "<PERSON>", "Imperadeiro98", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "MokaAkashiyaPT", "<PERSON>", "Vicng", "Vitorvicentevalente", "Waldir", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "아라"]}, "VARIABLES_DEFAULT_NAME": "item", "UNNAMED_KEY": "sem nome", "TODAY": "Hoje", "DUPLICATE_BLOCK": "Duplicar", "ADD_COMMENT": "<PERSON><PERSON><PERSON><PERSON>", "REMOVE_COMMENT": "Remover Comentário", "DUPLICATE_COMMENT": "<PERSON>p<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "EXTERNAL_INPUTS": "Entradas Externas", "INLINE_INPUTS": "Entradas Em Lin<PERSON>", "DELETE_BLOCK": "Eliminar <PERSON>", "DELETE_X_BLOCKS": "Eliminar %1 Blocos", "DELETE_ALL_BLOCKS": "Eliminar todos os %1 blocos?", "CLEAN_UP": "Limpar <PERSON>", "COLLAPSE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON><PERSON>", "EXPAND_BLOCK": "Expandir <PERSON>", "EXPAND_ALL": "<PERSON>pan<PERSON>", "DISABLE_BLOCK": "Desati<PERSON>", "ENABLE_BLOCK": "Ativar <PERSON>", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON><PERSON>", "REDO": "<PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Alterar valor:", "RENAME_VARIABLE": "Renomear variável...", "RENAME_VARIABLE_TITLE": "Renomear todas as variáveis '%1' para:", "NEW_VARIABLE": "<PERSON><PERSON><PERSON>", "NEW_STRING_VARIABLE": "Criar variável de segmentos de texto...", "NEW_NUMBER_VARIABLE": "<PERSON>riar vari<PERSON>vel numérica...", "NEW_COLOUR_VARIABLE": "Criar variável colorida...", "NEW_VARIABLE_TYPE_TITLE": "Tipo da nova variável:", "NEW_VARIABLE_TITLE": "Nome da nova variável:", "VARIABLE_ALREADY_EXISTS": "Já existe uma variável com o nome de '%1'.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Já existe uma variável chamada '%1' para outra do tipo: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Eliminar %1 utilizações da variável '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Não se pode eliminar a variável '%1' porque faz parte da definição da função '%2'", "DELETE_VARIABLE": "Eliminar a variável '%1'", "COLOUR_PICKER_HELPURL": "http://pt.wikipedia.org/wiki/Cor", "COLOUR_PICKER_TOOLTIP": "Escolha uma cor da paleta de cores.", "COLOUR_RANDOM_TITLE": "cor aleat<PERSON><PERSON>", "COLOUR_RANDOM_TOOLTIP": "Escolha uma cor aleatoriamente.", "COLOUR_RGB_HELPURL": "https://www.december.com/html/spec/colorpercompact.html", "COLOUR_RGB_TITLE": "pinte com", "COLOUR_RGB_RED": "verm<PERSON><PERSON>", "COLOUR_RGB_GREEN": "verde", "COLOUR_RGB_BLUE": "azul", "COLOUR_RGB_TOOLTIP": "Cria uma cor de acordo com a quantidade especificada de vermelho, verde e azul. Todos os valores devem estar entre 0 e 100.", "COLOUR_BLEND_HELPURL": "https://meyerweb.com/eric/tools/color-blend/#:::rgbp", "COLOUR_BLEND_TITLE": "misturar", "COLOUR_BLEND_COLOUR1": "cor 1", "COLOUR_BLEND_COLOUR2": "cor 2", "COLOUR_BLEND_RATIO": "proporção", "COLOUR_BLEND_TOOLTIP": "Mistura duas cores com a proporção indicada (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "http://pt.wikipedia.org/wiki/Estrutura_de_repeti%C3%A7%C3%A3o#Repeti.C3.A7.C3.A3o_com_vari.C3.A1vel_de_controle", "CONTROLS_REPEAT_TITLE": "repetir %1 vez", "CONTROLS_REPEAT_INPUT_DO": "faça", "CONTROLS_REPEAT_TOOLTIP": "Faça algumas instruções várias vezes.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "repetir enquanto", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "repetir até", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Enquanto um valor for verdadeiro, então faça algumas instruções.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Enquanto um valor for falso, então faça algumas instruções.", "CONTROLS_FOR_TOOLTIP": "Faz com que a variável \"%1\" assuma os valores desde o número inicial até ao número final, contando de acordo com o intervalo especificado e executa os blocos especificados.", "CONTROLS_FOR_TITLE": "contar com %1 de %2 até %3 por %4", "CONTROLS_FOREACH_TITLE": "para cada item %1 na lista %2", "CONTROLS_FOREACH_TOOLTIP": "Para cada item numa lista, define a variável \"%1\" para o item e então faz algumas instruções.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "sair do ciclo", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "continuar com a próxima iteração do ciclo", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Sair do ciclo que está contido.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Ignorar o resto deste ciclo, e continuar com a próxima iteração.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Aviso: Este bloco só pode ser usado dentro de um ciclo.", "CONTROLS_IF_TOOLTIP_1": "Se um valor é verdadeiro, então realize alguns passos.", "CONTROLS_IF_TOOLTIP_2": "Se um valor é verdadeiro, então realize o primeiro bloco de instruções.  <PERSON>ão, realize o segundo bloco de instruções", "CONTROLS_IF_TOOLTIP_3": "Se o primeiro valor é verdadeiro, então realize o primeiro bloco de instruções.  <PERSON>ão, se o segundo valor é verdadeiro, realize o segundo bloco de instruções.", "CONTROLS_IF_TOOLTIP_4": "Se o primeiro valor é verdadeiro, então realize o primeiro bloco de instruções.  <PERSON><PERSON>, se o segundo valor é verdadeiro, realize o segundo bloco de instruções.  Se nenhum dos blocos for verdadeiro, realize o último bloco de instruções.", "CONTROLS_IF_MSG_IF": "se", "CONTROLS_IF_MSG_ELSEIF": "senão se", "CONTROLS_IF_MSG_ELSE": "senão", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remova ou reordene secções para reconfigurar este bloco se.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Acrescente uma condição ao bloco se.", "CONTROLS_IF_ELSE_TOOLTIP": "Acrescente uma condição de excepação final para o bloco se.", "IOS_OK": "Aceitar", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "Erro", "IOS_PROCEDURES_INPUTS": "ENTRADAS", "IOS_PROCEDURES_ADD_INPUT": "+ Adicionar entrada", "IOS_PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON><PERSON>", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Essa função tem entradas duplicadas.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "Renomear", "IOS_VARIABLES_DELETE_BUTTON": "Eliminar", "IOS_VARIABLES_VARIABLE_NAME": "<PERSON><PERSON> da variável", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Você não pode usar um nome de variável vazio.", "LOGIC_COMPARE_HELPURL": "http://pt.wikipedia.org/wiki/Inequa%C3%A7%C3%A3o", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON><PERSON> verda<PERSON>iro se ambas as entradas forem iguais entre si.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON><PERSON> verdadeiro se ambas as entradas forem diferentes entre si.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON><PERSON>na verdadeiro se a primeira entrada for menor que a segunda entrada.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON><PERSON>na verdadeiro se a primeira entrada for menor ou igual à segunda entrada.", "LOGIC_COMPARE_TOOLTIP_GT": "<PERSON><PERSON>na verdadeiro se a primeira entrada for maior que a segunda entrada.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON><PERSON>na verdadeiro se a primeira entrada for maior ou igual à segunda entrada.", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON><PERSON> verdade<PERSON> se ambas as entradas forem verdade<PERSON>.", "LOGIC_OPERATION_AND": "e", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON><PERSON><PERSON> verdadeiro se pelo menos uma das estradas for verdadeira.", "LOGIC_OPERATION_OR": "ou", "LOGIC_NEGATE_TITLE": "não %1", "LOGIC_NEGATE_TOOLTIP": "Retorna verdadeiro se a entrada for falsa.  Retorna falso se a entrada for verdadeira.", "LOGIC_BOOLEAN_TRUE": "<PERSON><PERSON><PERSON><PERSON>", "LOGIC_BOOLEAN_FALSE": "falso", "LOGIC_BOOLEAN_TOOLTIP": "<PERSON><PERSON><PERSON> verda<PERSON> ou falso.", "LOGIC_NULL_HELPURL": "http://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "nulo", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON><PERSON> nulo.", "LOGIC_TERNARY_HELPURL": "http://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "teste", "LOGIC_TERNARY_IF_TRUE": "se verdadeiro", "LOGIC_TERNARY_IF_FALSE": "se falso", "LOGIC_TERNARY_TOOLTIP": "Avalia a condição em \"teste\". Se a condição for verdadeira retorna o valor \"se verdadeiro\", senão retorna o valor \"se falso\".", "MATH_NUMBER_HELPURL": "http://pt.wikipedia.org/wiki/N%C3%BAmero", "MATH_NUMBER_TOOLTIP": "Um número.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "asin", "MATH_TRIG_ACOS": "acos", "MATH_TRIG_ATAN": "atan", "MATH_ARITHMETIC_HELPURL": "http://pt.wikipedia.org/wiki/Aritm%C3%A9tica", "MATH_ARITHMETIC_TOOLTIP_ADD": "Retorna a soma de dois números.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Retorna a diferença de dois números.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Retorna o produto de dois números.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Retorna o quociente da divisão de dois números.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Retorna o primeiro número elevado à potência do segundo número.", "MATH_SINGLE_HELPURL": "http://pt.wikipedia.org/wiki/Raiz_quadrada", "MATH_SINGLE_OP_ROOT": "ra<PERSON><PERSON> quadrada", "MATH_SINGLE_TOOLTIP_ROOT": "Retorna a raiz quadrada de um número.", "MATH_SINGLE_OP_ABSOLUTE": "absoluto", "MATH_SINGLE_TOOLTIP_ABS": "Retorna o valor absoluto de um número.", "MATH_SINGLE_TOOLTIP_NEG": "Retorna o oposto de um número.", "MATH_SINGLE_TOOLTIP_LN": "Retorna o logarítmo natural de um número.", "MATH_SINGLE_TOOLTIP_LOG10": "Retorna o logarítmo em base 10 de um número.", "MATH_SINGLE_TOOLTIP_EXP": "Retorna o número e elevado à potência de um número.", "MATH_SINGLE_TOOLTIP_POW10": "Retorna 10 elevado à potência de um número.", "MATH_TRIG_HELPURL": "http://pt.wikipedia.org/wiki/Fun%C3%A7%C3%A3o_trigonom%C3%A9trica", "MATH_TRIG_TOOLTIP_SIN": "Retorna o seno de um grau (não radiano).", "MATH_TRIG_TOOLTIP_COS": "Retorna o cosseno de um grau (não radiano).", "MATH_TRIG_TOOLTIP_TAN": "Retorna a tangente de um grau (não radiano).", "MATH_TRIG_TOOLTIP_ASIN": "Retorna o arco seno de um número.", "MATH_TRIG_TOOLTIP_ACOS": "Retorna o arco cosseno de um número.", "MATH_TRIG_TOOLTIP_ATAN": "Retorna o arco tangente de um número.", "MATH_CONSTANT_HELPURL": "http://pt.wikipedia.org/wiki/Anexo:Lista_de_constantes_matem%C3%A1ticas", "MATH_CONSTANT_TOOLTIP": "Retorna uma das constantes comuns: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), ou ∞ (infinito).", "MATH_IS_EVEN": "é par", "MATH_IS_ODD": "é impar", "MATH_IS_PRIME": "é primo", "MATH_IS_WHOLE": "é inteiro", "MATH_IS_POSITIVE": "é positivo", "MATH_IS_NEGATIVE": "é negativo", "MATH_IS_DIVISIBLE_BY": "é divisível por", "MATH_IS_TOOLTIP": "Verifica se um número é par, impar, primo, inteiro, positivo, negativo, ou se é divisível por outro número.  Retorna verdadeiro ou falso.", "MATH_CHANGE_HELPURL": "http://pt.wikipedia.org/wiki/Adi%C3%A7%C3%A3o", "MATH_CHANGE_TITLE": "alterar %1 por %2", "MATH_CHANGE_TOOLTIP": "Soma um número à variável \"%1\".", "MATH_ROUND_HELPURL": "http://pt.wikipedia.org/wiki/Arredondamento", "MATH_ROUND_TOOLTIP": "Arredonda um número para cima ou para baixo.", "MATH_ROUND_OPERATOR_ROUND": "a<PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "arredonda para cima", "MATH_ROUND_OPERATOR_ROUNDDOWN": "arredonda para baixo", "MATH_ONLIST_OPERATOR_SUM": "soma da lista", "MATH_ONLIST_TOOLTIP_SUM": "Retorna a soma de todos os números da lista.", "MATH_ONLIST_OPERATOR_MIN": "menor de uma lista", "MATH_ONLIST_TOOLTIP_MIN": "Retorna o menor número da lista.", "MATH_ONLIST_OPERATOR_MAX": "maior de uma lista", "MATH_ONLIST_TOOLTIP_MAX": "Retorna o maior número da lista.", "MATH_ONLIST_OPERATOR_AVERAGE": "média de uma lista", "MATH_ONLIST_TOOLTIP_AVERAGE": "Retorna a média aritmética dos valores números da lista.", "MATH_ONLIST_OPERATOR_MEDIAN": "mediana de uma lista", "MATH_ONLIST_TOOLTIP_MEDIAN": "Retorna a mediana da lista.", "MATH_ONLIST_OPERATOR_MODE": "moda de uma lista", "MATH_ONLIST_TOOLTIP_MODE": "Retorna a lista de item(ns) mais comum(ns) da lista.", "MATH_ONLIST_OPERATOR_STD_DEV": "<PERSON>vio pad<PERSON> de uma lista", "MATH_ONLIST_TOOLTIP_STD_DEV": "Retorna o desvio padrão dos números da lista.", "MATH_ONLIST_OPERATOR_RANDOM": "item aleatório de uma lista", "MATH_ONLIST_TOOLTIP_RANDOM": "Retorna um elemento aleatório da lista.", "MATH_MODULO_HELPURL": "http://pt.wikipedia.org/wiki/Opera%C3%A7%C3%A3o_m%C3%B3dulo", "MATH_MODULO_TITLE": "resto da divisão de %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Retorna o resto da divisão de dois números.", "MATH_CONSTRAIN_TITLE": "restringe %1 inferior %2 superior %3", "MATH_CONSTRAIN_TOOLTIP": "Restringe um número entre os limites especificados (inclusive).", "MATH_RANDOM_INT_HELPURL": "http://pt.wikipedia.org/wiki/N%C3%BAmero_aleat%C3%B3rio", "MATH_RANDOM_INT_TITLE": "inteiro aleatório entre %1 e %2", "MATH_RANDOM_INT_TOOLTIP": "Retorna um número inteiro entre os dois limites especificados, inclusive.", "MATH_RANDOM_FLOAT_HELPURL": "http://pt.wikipedia.org/wiki/N%C3%BAmero_aleat%C3%B3rio", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "fração aleatória", "MATH_RANDOM_FLOAT_TOOLTIP": "Insere uma fração aleatória entre 0.0 (inclusive) e 1.0 (exclusive).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 de X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Devolver o arco tangente do ponto (X, Y) em graus entre -180 e 180.", "TEXT_TEXT_HELPURL": "http://pt.wikipedia.org/wiki/<PERSON><PERSON>_de_caracteres", "TEXT_TEXT_TOOLTIP": "<PERSON>a letra, pala<PERSON>ra ou linha de texto.", "TEXT_JOIN_TITLE_CREATEWITH": "criar texto com", "TEXT_JOIN_TOOLTIP": "Criar um pedaço de texto juntando qualquer número de itens.", "TEXT_CREATE_JOIN_TITLE_JOIN": "unir", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remove ou reordena seções para reconfigurar este bloco de texto.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Acrescentar um item ao texto.", "TEXT_APPEND_TITLE": "para %1 acrescentar texto %2", "TEXT_APPEND_TOOLTIP": "Acrescentar um pedaço de texto à variável \"%1\".", "TEXT_LENGTH_TITLE": "tamanho de %1", "TEXT_LENGTH_TOOLTIP": "Devolve o número de letras (incluindo espaços) do texto fornecido.", "TEXT_ISEMPTY_TITLE": "%1 está vazio", "TEXT_ISEMPTY_TOOLTIP": "Retorna verdadeiro se o texto fornecido estiver vazio.", "TEXT_INDEXOF_TOOLTIP": "Retorna a posição da primeira/última ocorrência do primeiro texto no segundo texto. Retorna %1 se o texto não for encontrado.", "TEXT_INDEXOF_TITLE": "no texto %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "primeira ocorrência do texto", "TEXT_INDEXOF_OPERATOR_LAST": "última ocorrência do texto", "TEXT_CHARAT_TITLE": "no texto %1 %2", "TEXT_CHARAT_FROM_START": "obter letra nº", "TEXT_CHARAT_FROM_END": "obter letra nº a partir do final", "TEXT_CHARAT_FIRST": "obter primeira letra", "TEXT_CHARAT_LAST": "obter última letra", "TEXT_CHARAT_RANDOM": "obter letra aleatória", "TEXT_CHARAT_TOOLTIP": "Retorna a letra na posição especificada.", "TEXT_GET_SUBSTRING_TOOLTIP": "Retorna a parte especificada do texto.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "no texto", "TEXT_GET_SUBSTRING_START_FROM_START": "obter subsequência de tamanho #", "TEXT_GET_SUBSTRING_START_FROM_END": "obter subsequência de tamanho # a partir do final", "TEXT_GET_SUBSTRING_START_FIRST": "obter subsequência a partir da primeira letra", "TEXT_GET_SUBSTRING_END_FROM_START": "até letra nº", "TEXT_GET_SUBSTRING_END_FROM_END": "até letra nº a partir do final", "TEXT_GET_SUBSTRING_END_LAST": "até última letra", "TEXT_CHANGECASE_TOOLTIP": "Retorna uma cópia do texto em formato diferente.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "para MAIÚSCULAS", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "para minúsculas", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "para Iniciais Maiúsculas", "TEXT_TRIM_TOOLTIP": "Retorna uma cópia do texto com os espaços removidos de uma ou ambas as extremidades.", "TEXT_TRIM_OPERATOR_BOTH": "remover espaços de ambos os lados", "TEXT_TRIM_OPERATOR_LEFT": "remover espaços à esquerda de", "TEXT_TRIM_OPERATOR_RIGHT": "remover espaços à direita", "TEXT_PRINT_TITLE": "imprime %1", "TEXT_PRINT_TOOLTIP": "Imprime o texto, número ou outro valor especificado.", "TEXT_PROMPT_TYPE_TEXT": "Pede um texto com a mensagem", "TEXT_PROMPT_TYPE_NUMBER": "pede um número com a mensagem", "TEXT_PROMPT_TOOLTIP_NUMBER": "Pede ao utilizador um número.", "TEXT_PROMPT_TOOLTIP_TEXT": "Pede ao utilizador um texto.", "TEXT_COUNT_MESSAGE0": "contar %1 em %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Conte quantas vezes um certo texto aparece dentro de algum outro texto.", "TEXT_REPLACE_MESSAGE0": "substituir %1 por %2 em %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "Substituir to<PERSON> as ocorrências de um certo texto dentro de algum outro texto.", "TEXT_REVERSE_MESSAGE0": "inverter %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Inverte a ordem dos caracteres no texto.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "criar lista vazia", "LISTS_CREATE_EMPTY_TOOLTIP": "Re<PERSON>na uma lista, de taman<PERSON> 0, contendo nenhum registo", "LISTS_CREATE_WITH_TOOLTIP": "Cria uma lista com qualquer número de itens.", "LISTS_CREATE_WITH_INPUT_WITH": "criar lista com", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remova ou reordene as seções para reconfigurar este bloco lista.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Acrescenta um item à lista.", "LISTS_REPEAT_TOOLTIP": "Cria uma lista constituída por um dado valor repetido o número de vezes especificado.", "LISTS_REPEAT_TITLE": "criar lista com o item %1 repetido %2 vezes", "LISTS_LENGTH_TITLE": "tamanho de %1", "LISTS_LENGTH_TOOLTIP": "Retorna o tamanho de uma lista.", "LISTS_ISEMPTY_TITLE": "%1 está vazia", "LISTS_ISEMPTY_TOOLTIP": "Retona verdadeiro se a lista estiver vazia.", "LISTS_INLIST": "na lista", "LISTS_INDEX_OF_FIRST": "encontre a primeira ocorrência do item", "LISTS_INDEX_OF_LAST": "encontre a última ocorrência do item", "LISTS_INDEX_OF_TOOLTIP": "Retorna a posição da primeira/última ocorrência do item na lista.  Retorna %1 se o item não for encontrado.", "LISTS_GET_INDEX_GET": "obter", "LISTS_GET_INDEX_GET_REMOVE": "obter e remover", "LISTS_GET_INDEX_REMOVE": "remover", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# a partir do final", "LISTS_GET_INDEX_FIRST": "primeiro", "LISTS_GET_INDEX_LAST": "último", "LISTS_GET_INDEX_RANDOM": "aleatório", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 é o primeiro item.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 é o último item.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Retorna o item na posição especificada da lista.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Retorna o primeiro item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Retorna o último item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Retorna um item aleatório de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Remove e retorna o item na posição especificada de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Remove e retorna o primeiro item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Remove e retorna o último item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Remove e retorna um item aleatório de uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Remove o item de uma posição especifica da lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Remove o primeiro item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Remove o último item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Remove um item aleatório de uma lista.", "LISTS_SET_INDEX_SET": "definir", "LISTS_SET_INDEX_INSERT": "inserir em", "LISTS_SET_INDEX_INPUT_TO": "como", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Define o item na posição especificada de uma lista.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Define o primeiro item de uma lista.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Define o último item de uma lista.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Define um item aleatório de uma lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Insere o item numa posição especificada numa lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Insere o item no início da lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Insere o item no final da lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Insere o item numa posição aleatória de uma lista.", "LISTS_GET_SUBLIST_START_FROM_START": "obtem sublista de #", "LISTS_GET_SUBLIST_START_FROM_END": "obtem sublista de # a partir do final", "LISTS_GET_SUBLIST_START_FIRST": "obtem sublista da primeira lista", "LISTS_GET_SUBLIST_END_FROM_START": "até #", "LISTS_GET_SUBLIST_END_FROM_END": "até #, a partir do final", "LISTS_GET_SUBLIST_END_LAST": "para o último", "LISTS_GET_SUBLIST_TOOLTIP": "Cria uma cópia da porção especificada de uma lista.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "ordenar %1 %2 %3", "LISTS_SORT_TOOLTIP": "Ordenar uma cópia de uma lista.", "LISTS_SORT_ORDER_ASCENDING": "ascendente", "LISTS_SORT_ORDER_DESCENDING": "descendente", "LISTS_SORT_TYPE_NUMERIC": "numérica", "LISTS_SORT_TYPE_TEXT": "alfabética", "LISTS_SORT_TYPE_IGNORECASE": "alfabética, ignorar maiúsculas/minúsculas", "LISTS_SPLIT_LIST_FROM_TEXT": "fazer lista a partir de texto", "LISTS_SPLIT_TEXT_FROM_LIST": "fazer texto a partir da lista", "LISTS_SPLIT_WITH_DELIMITER": "com delimitador", "LISTS_SPLIT_TOOLTIP_SPLIT": "Dividir o texto numa lista de textos, separando-o em cada delimitador.", "LISTS_SPLIT_TOOLTIP_JOIN": "Juntar uma lista de textos num único texto, separado por um delimitador.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "inverter %1", "LISTS_REVERSE_TOOLTIP": "Inverter uma cópia da lista.", "VARIABLES_GET_TOOLTIP": "Retorna o valor desta variável.", "VARIABLES_GET_CREATE_SET": "Criar \"definir %1\"", "VARIABLES_SET": "definir %1 para %2", "VARIABLES_SET_TOOLTIP": "Define esta variável para o valor inserido.", "VARIABLES_SET_CREATE_GET": "Criar \"obter %1\"", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_DEFNORETURN_TITLE": "para", "PROCEDURES_DEFNORETURN_PROCEDURE": "faz algo", "PROCEDURES_BEFORE_PARAMS": "com:", "PROCEDURES_CALL_BEFORE_PARAMS": "com:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Cria uma função que não tem retorno.", "PROCEDURES_DEFNORETURN_COMMENT": "Descreva esta função...", "PROCEDURES_DEFRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_DEFRETURN_RETURN": "retorna", "PROCEDURES_DEFRETURN_TOOLTIP": "Cria uma função que possui um valor de retorno.", "PROCEDURES_ALLOW_STATEMENTS": "permitir declarações", "PROCEDURES_DEF_DUPLICATE_WARNING": "Aviso: Esta função tem parâmetros duplicados.", "PROCEDURES_CALLNORETURN_HELPURL": "https://pt.wikipedia.org/wiki/Sub-rotina", "PROCEDURES_CALLNORETURN_TOOLTIP": "Executa a função \"%1\".", "PROCEDURES_CALLRETURN_HELPURL": "https://pt.wikipedia.org/wiki/Sub-rotina", "PROCEDURES_CALLRETURN_TOOLTIP": "Executa a função \"%1\" e usa o seu retorno.", "PROCEDURES_MUTATORCONTAINER_TITLE": "entradas", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remover ou reordenar as entradas para esta função.", "PROCEDURES_MUTATORARG_TITLE": "nome da entrada:", "PROCEDURES_MUTATORARG_TOOLTIP": "Adicionar uma entrada para a função.", "PROCEDURES_HIGHLIGHT_DEF": "Destacar definição da função", "PROCEDURES_CREATE_DO": "Criar \"%1\"", "PROCEDURES_IFRETURN_TOOLTIP": "se o valor é verdadeiro, então retorna um segundo valor.", "PROCEDURES_IFRETURN_WARNING": "Aviso: Este bloco só pode ser utilizado dentro da definição de uma função.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Diz algo...", "WORKSPACE_ARIA_LABEL": "Espaço de trabalho de Blockly", "COLLAPSED_WARNINGS_WARNING": "Os blocos ocultados contêm avisos.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}