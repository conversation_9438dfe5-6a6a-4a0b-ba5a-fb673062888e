{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Ely en", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Minisarm", "<PERSON>eu", "<PERSON><PERSON><PERSON><PERSON>", "아라"]}, "VARIABLES_DEFAULT_NAME": "element", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "Duplicate", "ADD_COMMENT": "Adaugă un comentariu", "REMOVE_COMMENT": "Elimină comentariu", "DUPLICATE_COMMENT": "Coment duplicat", "EXTERNAL_INPUTS": "<PERSON><PERSON><PERSON><PERSON> externe", "INLINE_INPUTS": "<PERSON><PERSON><PERSON><PERSON> în linie", "DELETE_BLOCK": "Șterge Bloc", "DELETE_X_BLOCKS": "Ștergeți %1 Blocuri", "DELETE_ALL_BLOCKS": "<PERSON><PERSON><PERSON> toate cele %1 (de) blocuri?", "CLEAN_UP": "Cură<PERSON><PERSON> blocări", "COLLAPSE_BLOCK": "Restrang<PERSON> blocul", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON> bloc<PERSON>le", "EXPAND_BLOCK": "Extinde bloc", "EXPAND_ALL": "<PERSON><PERSON><PERSON> bloc<PERSON>", "DISABLE_BLOCK": "Dezactivați bloc", "ENABLE_BLOCK": "Permite bloc", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "Anulează", "REDO": "Refă", "CHANGE_VALUE_TITLE": "Schimbați valoarea:", "RENAME_VARIABLE": "Redenumirea variabilei...", "RENAME_VARIABLE_TITLE": "Redenumește toate variabilele „%1” în:", "NEW_VARIABLE": "Crează variabilă", "NEW_STRING_VARIABLE": "Crează o variabilă string", "NEW_NUMBER_VARIABLE": "Crează o variabilă număr", "NEW_COLOUR_VARIABLE": "Crează o variabilă culoare", "NEW_VARIABLE_TYPE_TITLE": "Tip nou de variabilă", "NEW_VARIABLE_TITLE": "Noul nume de variabilă:", "VARIABLE_ALREADY_EXISTS": "O variabilă cu numele '%1'  există deja.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "o variabilă numită '%1' există deja pentru alt tip: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Șterge %1 utilizările variabilei '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Nu se poate șterge variabila '%1' deoarece face parte din definiția funcției '%2'", "DELETE_VARIABLE": "Ștergeți variabila '%1'", "COLOUR_PICKER_HELPURL": "https://ro.wikipedia.org/wiki/Culoare", "COLOUR_PICKER_TOOLTIP": "Alege o culoare din paleta de culori.", "COLOUR_RANDOM_TITLE": "culoare aleatorie", "COLOUR_RANDOM_TOOLTIP": "Alege o culoare la întâmplare.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "colorează cu", "COLOUR_RGB_RED": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_RGB_GREEN": "verde", "COLOUR_RGB_BLUE": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_RGB_TOOLTIP": "Creează o culoare cu suma specificată de roșu, verde și albastru.  Toate valorile trebuie să fie între 0 și 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "amestec", "COLOUR_BLEND_COLOUR1": "culoare 1", "COLOUR_BLEND_COLOUR2": "culoare 2", "COLOUR_BLEND_RATIO": "<PERSON><PERSON>", "COLOUR_BLEND_TOOLTIP": "Amestecă două culori cu un raport dat (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "repetă de %1 ori", "CONTROLS_REPEAT_INPUT_DO": "fă", "CONTROLS_REPEAT_TOOLTIP": "Face unele afirmații de mai multe ori.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "repetă în timp ce", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "Repetați până când", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "<PERSON><PERSON><PERSON> timp cât o valoare este adevărat, atunci face unele declarații.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "<PERSON><PERSON><PERSON> timp cât o valoare este adevărat, atunci face unele declarații.", "CONTROLS_FOR_TOOLTIP": "Cu variablia \"%1\" ia o valoare din numărul început la numărul final, numara in intervalul specificat, apoi face blocurile specificate.", "CONTROLS_FOR_TITLE": "numără cu %1 de la %2 la %3 prin %4", "CONTROLS_FOREACH_TITLE": "pentru fiecare element %1 în listă %2", "CONTROLS_FOREACH_TOOLTIP": "Pentru fiecare element din listă, setați variabila „%1” ca valoarea elementului, și apoi faceți unele declarații.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "ieși din buclă", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "continuă cu următoarea iterație a buclei", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Ieși din buclă.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Sari peste restul acestei bucle și continuă cu următoarea iterație.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Avertisment: Acest bloc pote fi utilizat numai în interiorul unei bucle.", "CONTROLS_IF_TOOLTIP_1": "Dacă o valoare este adevărată, atunci fă unele declarații.", "CONTROLS_IF_TOOLTIP_2": "<PERSON><PERSON><PERSON> o valoare este adevărat, atunci face primul bloc de declarații.  <PERSON><PERSON><PERSON>, face al doilea bloc de declarații.", "CONTROLS_IF_TOOLTIP_3": "Dacă prima valoare este adevărat, atunci face primul bloc de declarații. <PERSON><PERSON><PERSON>, dacă a doua valoare este adevărat, face al doilea bloc de declarații.", "CONTROLS_IF_TOOLTIP_4": "Dacă prima valoare este adevărat, atunci face primul bloc de declarații. <PERSON><PERSON><PERSON>, dacă a doua valoare este adevărat, face al doilea bloc de declarații. În cazul în care niciuna din valori nu este adevărat, face ultimul bloc de declarații.", "CONTROLS_IF_MSG_IF": "dacă", "CONTROLS_IF_MSG_ELSEIF": "altfel da<PERSON>", "CONTROLS_IF_MSG_ELSE": "altfel", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON>, elimină sau reordonează secțiuni pentru a reconfigura acest bloc if.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Adăugați o condiție în blocul if.", "CONTROLS_IF_ELSE_TOOLTIP": "Adauga o stare finala, cuprinde toata conditia din blocul if.", "IOS_OK": "OK", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "Eroare", "IOS_PROCEDURES_INPUTS": "INTRĂRI", "IOS_PROCEDURES_ADD_INPUT": "+ Adă<PERSON><PERSON><PERSON> intrare", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Permite declarațiile", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Această funcție are intrări duplicate.", "IOS_VARIABLES_ADD_VARIABLE": "+Adăugați variabilă", "IOS_VARIABLES_ADD_BUTTON": "Adaugă", "IOS_VARIABLES_RENAME_BUTTON": "Redenumire", "IOS_VARIABLES_DELETE_BUTTON": "Ștergeți", "IOS_VARIABLES_VARIABLE_NAME": "N<PERSON>le variabilei", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Nu puteți utiliza un nume de variabilă gol.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "Returnează adevărat dacă ambele intrări sunt egale.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Returnează adevărat daca cele două intrări nu sunt egale.", "LOGIC_COMPARE_TOOLTIP_LT": "Returnează adevărat dacă prima intrare este mai mică decât a doua intrare.", "LOGIC_COMPARE_TOOLTIP_LTE": "Returnează adevărat dacă prima intrare este mai mică sau egală cu a doua intrare.", "LOGIC_COMPARE_TOOLTIP_GT": "Returnează adevărat dacă prima intrare este mai mare decât a doua intrare.", "LOGIC_COMPARE_TOOLTIP_GTE": "Returnează adevărat dacă prima intrare este mai mare sau egală cu a doua intrare.", "LOGIC_OPERATION_TOOLTIP_AND": "Returnează adevărat daca ambele intrări sunt adevărate.", "LOGIC_OPERATION_AND": "și", "LOGIC_OPERATION_TOOLTIP_OR": "Returnează adevărat dacă cel puțin una din intrări este adevărată.", "LOGIC_OPERATION_OR": "sau", "LOGIC_NEGATE_TITLE": "non %1", "LOGIC_NEGATE_TOOLTIP": "Returnează adevărat dacă intrarea este falsă.  Returnează fals dacă intrarea este adevărată.", "LOGIC_BOOLEAN_TRUE": "<PERSON><PERSON><PERSON><PERSON>", "LOGIC_BOOLEAN_FALSE": "fals", "LOGIC_BOOLEAN_TOOLTIP": "Returnează adevărat sau fals.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "nul", "LOGIC_NULL_TOOLTIP": "returnează nul.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "dacă este adevărat", "LOGIC_TERNARY_IF_FALSE": "dacă este fals", "LOGIC_TERNARY_TOOLTIP": "Verifică condiția din „test”. Dacă condiția este adevărată, returnează valoarea „în cazul în care adevărat”; în caz contrar întoarce valoarea „în cazul în care e fals”.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "Un număr.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tg", "MATH_TRIG_ASIN": "arcsin", "MATH_TRIG_ACOS": "arccos", "MATH_TRIG_ATAN": "arctg", "MATH_ARITHMETIC_HELPURL": "https://ro.wikipedia.org/wiki/Aritmetic%C4%83", "MATH_ARITHMETIC_TOOLTIP_ADD": "Returnează suma a două numere.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Returnează diferența dintre cele două numere.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Returnează produsul celor două numere.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Returnează câtul celor două numere.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Returneaza numărul rezultat prin ridicarea primului număr la puterea celui de-al doilea.", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "răd<PERSON>cina pă<PERSON>ă", "MATH_SINGLE_TOOLTIP_ROOT": "Returnează rădăcina pătrată a unui număr.", "MATH_SINGLE_OP_ABSOLUTE": "absolută", "MATH_SINGLE_TOOLTIP_ABS": "Returnează valoarea absolută a unui număr.", "MATH_SINGLE_TOOLTIP_NEG": "Returnează negația unui număr.", "MATH_SINGLE_TOOLTIP_LN": "<PERSON><PERSON><PERSON>ce logaritmul natural al unui număr.", "MATH_SINGLE_TOOLTIP_LOG10": "Returnează logaritmul în baza 10 a unui număr.", "MATH_SINGLE_TOOLTIP_EXP": "Returnează e la puterea unui număr.", "MATH_SINGLE_TOOLTIP_POW10": "Returnează 10 la puterea unui număr.", "MATH_TRIG_HELPURL": "https://ro.wikipedia.org/wiki/Funcții_trigonometrice", "MATH_TRIG_TOOLTIP_SIN": "<PERSON><PERSON><PERSON><PERSON> cosinusul unui grad (nu radianul).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON><PERSON><PERSON> cosinusul unui grad (nu radianul).", "MATH_TRIG_TOOLTIP_TAN": "<PERSON><PERSON><PERSON>ce tangenta unui grad (nu radianul).", "MATH_TRIG_TOOLTIP_ASIN": "Returnează arcsinusul unui număr.", "MATH_TRIG_TOOLTIP_ACOS": "Returnează arccosinusul unui număr.", "MATH_TRIG_TOOLTIP_ATAN": "Returnează arctangenta unui număr.", "MATH_CONSTANT_HELPURL": "https://ro.wikipedia.org/wiki/Constant%C4%83_matematic%C4%83", "MATH_CONSTANT_TOOLTIP": "Întoarcă una din constantele comune: π (3.141...), e (2.718...), φ (1,618...), sqrt(2) (1.414...), sqrt(½) (0.707...) sau ∞ (infinitate).", "MATH_IS_EVEN": "este par", "MATH_IS_ODD": "este impar", "MATH_IS_PRIME": "este prim", "MATH_IS_WHOLE": "este întreg", "MATH_IS_POSITIVE": "este pozitiv", "MATH_IS_NEGATIVE": "este negativ", "MATH_IS_DIVISIBLE_BY": "este divizibil cu", "MATH_IS_TOOLTIP": "Verifică dacă un număr este un par, impar, prim, î<PERSON><PERSON>g, pozitiv, negativ, sau dacă este divizibil cu un anumit număr.  Returnează true sau false.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "schimbă %1 de %2", "MATH_CHANGE_TOOLTIP": "Adaugă un număr variabilei '%1'.", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "Rotunjirea unui număr în sus sau în jos.", "MATH_ROUND_OPERATOR_ROUND": "rotund", "MATH_ROUND_OPERATOR_ROUNDUP": "rotunjește în sus", "MATH_ROUND_OPERATOR_ROUNDDOWN": "<PERSON><PERSON><PERSON>", "MATH_ONLIST_OPERATOR_SUM": "suma listei", "MATH_ONLIST_TOOLTIP_SUM": "Returnează suma tuturor numerelor din lista.", "MATH_ONLIST_OPERATOR_MIN": "minimul listei", "MATH_ONLIST_TOOLTIP_MIN": "Returnează cel mai mic număr din listă.", "MATH_ONLIST_OPERATOR_MAX": "maximul listei", "MATH_ONLIST_TOOLTIP_MAX": "Î<PERSON><PERSON>ce cel mai mare număr din listă.", "MATH_ONLIST_OPERATOR_AVERAGE": "media listei", "MATH_ONLIST_TOOLTIP_AVERAGE": "Întoarce media (aritmetică) a valorilor numerice în listă.", "MATH_ONLIST_OPERATOR_MEDIAN": "media listei", "MATH_ONLIST_TOOLTIP_MEDIAN": "<PERSON><PERSON><PERSON><PERSON> numărul median în listă.", "MATH_ONLIST_OPERATOR_MODE": "mod<PERSON> de <PERSON>", "MATH_ONLIST_TOOLTIP_MODE": "Returnează o listă cu cel(e) mai frecvent(e) element(e) din listă.", "MATH_ONLIST_OPERATOR_STD_DEV": "deviația standard a listei", "MATH_ONLIST_TOOLTIP_STD_DEV": "Întoarce deviația standard a listei.", "MATH_ONLIST_OPERATOR_RANDOM": "element aleatoriu din lista", "MATH_ONLIST_TOOLTIP_RANDOM": "Returnează un element aleatoriu din listă.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "restul la %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Întoarce restul din împărțirea celor două numere.", "MATH_CONSTRAIN_TITLE": "constrânge %1 redus %2 ridicat %3", "MATH_CONSTRAIN_TOOLTIP": "Constrânge un număr să fie între limitele specificate (inclusiv).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "un număr întreg aleator de la %1 la %2", "MATH_RANDOM_INT_TOOLTIP": "Returnează un număr întreg aleator aflat între cele două limite specificate, inclusiv.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "frac<PERSON><PERSON> aleatorii", "MATH_RANDOM_FLOAT_TOOLTIP": "Returnează o fracție aleatoare între 0.0 (inclusiv) și 1.0 (exclusiv).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 of X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Întoarceți arctangentul punctului (X, Y) în grade de la -180 la 180.", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "O literă, cuvânt sau linie de text.", "TEXT_JOIN_TITLE_CREATEWITH": "crează text cu", "TEXT_JOIN_TOOLTIP": "Creați o bucată de text prin unirea oricărui număr de elemente.", "TEXT_CREATE_JOIN_TITLE_JOIN": "alăturați-vă", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON>, elimină sau reordonează secțiuni ca să reconfigureze blocul text.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Adaugă un element în text.", "TEXT_APPEND_TITLE": "la %1 adăugați text %2", "TEXT_APPEND_TOOLTIP": "Adăugați text la variabila „%1”.", "TEXT_LENGTH_TITLE": "lungime de %1", "TEXT_LENGTH_TOOLTIP": "Returnează numărul de litere (inclusiv spațiile) în textul furnizat.", "TEXT_ISEMPTY_TITLE": "%1 este gol", "TEXT_ISEMPTY_TOOLTIP": "Returnează adevărat dacă textul furnizat este gol.", "TEXT_INDEXOF_TOOLTIP": "Returnează indicele primei/ultimei apariții din primul text în al doilea text. Returnează %1 dacă textul nu este găsit.", "TEXT_INDEXOF_TITLE": "în text %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "găsește prima apariție a textului", "TEXT_INDEXOF_OPERATOR_LAST": "găsește ultima apariție a textului", "TEXT_CHARAT_TITLE": "în text %1 %2", "TEXT_CHARAT_FROM_START": "obtine litera #", "TEXT_CHARAT_FROM_END": "obține litera # de la sfârșit", "TEXT_CHARAT_FIRST": "obține prima literă", "TEXT_CHARAT_LAST": "obține o literă oarecare", "TEXT_CHARAT_RANDOM": "obtine o litera oarecare", "TEXT_CHARAT_TOOLTIP": "Returnează litera la poziția specificată.", "TEXT_GET_SUBSTRING_TOOLTIP": "Returnează o anumită parte din text.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "în text", "TEXT_GET_SUBSTRING_START_FROM_START": "obține subșir de la litera #", "TEXT_GET_SUBSTRING_START_FROM_END": "obține un subșir de la litera # de la sfârșit", "TEXT_GET_SUBSTRING_START_FIRST": "obține un subșir de la prima literă", "TEXT_GET_SUBSTRING_END_FROM_START": "la litera #", "TEXT_GET_SUBSTRING_END_FROM_END": "la litera # de la sfarsit", "TEXT_GET_SUBSTRING_END_LAST": "la ultima literă", "TEXT_CHANGECASE_TOOLTIP": "Întoarce o copie a textului într-un caz diferit.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "la MAJUSCULE", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "la litere mici", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "<PERSON><PERSON><PERSON>z", "TEXT_TRIM_TOOLTIP": "Returnează o copie a textului fără spațiile de la unul sau ambele capete.", "TEXT_TRIM_OPERATOR_BOTH": "taie spațiile de pe ambele părți ale", "TEXT_TRIM_OPERATOR_LEFT": "tăiați spațiile din partea stângă a", "TEXT_TRIM_OPERATOR_RIGHT": "taie spațiile din partea dreaptă a", "TEXT_PRINT_TITLE": "imprimare %1", "TEXT_PRINT_TOOLTIP": "Afișează textul specificat, numărul sau altă valoare.", "TEXT_PROMPT_TYPE_TEXT": "solicită pentru text cu mesaj", "TEXT_PROMPT_TYPE_NUMBER": "solicită pentru număr cu mesaj", "TEXT_PROMPT_TOOLTIP_NUMBER": "Solicită utilizatorul pentru un număr.", "TEXT_PROMPT_TOOLTIP_TEXT": "Solicită utilizatorul pentru text.", "TEXT_COUNT_MESSAGE0": "numără %1 in %2", "TEXT_COUNT_TOOLTIP": "Aflați de câte ori apare un text într-un alt text.", "TEXT_REPLACE_MESSAGE0": "înlocuiți %1 cu %2 în %3", "TEXT_REPLACE_TOOLTIP": "Înlocuiți toate aparițiile anumitor texte într-un alt text.", "TEXT_REVERSE_MESSAGE0": "inversă %1", "TEXT_REVERSE_TOOLTIP": "Inversează ordinea caracterelor din text.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "creează listă goală", "LISTS_CREATE_EMPTY_TOOLTIP": "Returnează o listă, de lungime 0, care nu conține înregistrări de date", "LISTS_CREATE_WITH_TOOLTIP": "Creați o listă cu orice număr de elemente.", "LISTS_CREATE_WITH_INPUT_WITH": "creează listă cu", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "listă", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, elimină sau reordonează secțiuni pentru a reconfigura acest bloc de listă.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Adăugați un element la listă.", "LISTS_REPEAT_TOOLTIP": "Creează o listă alcătuită dintr-o anumită valoare repetată de numărul specificat de ori.", "LISTS_REPEAT_TITLE": "creaza lista cu %1 elemente repetate de %2 ori", "LISTS_LENGTH_TITLE": "lungime de %1", "LISTS_LENGTH_TOOLTIP": "Returnează lungimea unei liste.", "LISTS_ISEMPTY_TITLE": "%1 este gol", "LISTS_ISEMPTY_TOOLTIP": "Returnează adevărat dacă lista este goală.", "LISTS_INLIST": "în listă", "LISTS_INDEX_OF_FIRST": "Găsește prima apariție a elementului", "LISTS_INDEX_OF_LAST": "găsește ultima apariție a elementului", "LISTS_INDEX_OF_TOOLTIP": "Returnează indexul primei/ultimei apariții a articolului din listă. Returnează %1 dacă elementul nu este găsit.", "LISTS_GET_INDEX_GET": "ob<PERSON><PERSON>", "LISTS_GET_INDEX_GET_REMOVE": "obține și elimină", "LISTS_GET_INDEX_REMOVE": "elimin<PERSON>", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# de la sfârșit", "LISTS_GET_INDEX_FIRST": "primul", "LISTS_GET_INDEX_LAST": "ultimul", "LISTS_GET_INDEX_RANDOM": "aleator", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 este primul element.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 este ultimul element.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Returnează elementul de la poziția specificată dintr-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Returnează primul element dintr-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Returnează ultimul element într-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Returneaza un element aleatoriu într-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Elimină și returnează elementul de la poziția specificată dintr-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Elimină și returnează primul element dintr-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Elimină și returnează ultimul element dintr-o listă.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Elimină și returnează un element aleatoriu într-o listă.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Elimină elementul de la poziția specificată dintr-o listă.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Elimină primul element într-o listă.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Elimină ultimul element într-o listă.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Elimină un element aleatoriu într-o listă.", "LISTS_SET_INDEX_SET": "<PERSON><PERSON><PERSON>", "LISTS_SET_INDEX_INSERT": "introduceț<PERSON> la", "LISTS_SET_INDEX_INPUT_TO": "ca", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Setează elementul de la poziția specificată dintr-o listă.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Setează primul element într-o listă.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Setează ultimul element într-o listă.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Setează un element aleator într-o listă.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Inserează elementul la poziția specificată într-o listă.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Inserează elementul la începutul unei liste.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Adaugă elementul la sfârșitul unei liste.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Inserează elementul aleatoriu într-o listă.", "LISTS_GET_SUBLIST_START_FROM_START": "obține sub-lista de la #", "LISTS_GET_SUBLIST_START_FROM_END": "obține sub-lista de la # de la sfârșit", "LISTS_GET_SUBLIST_START_FIRST": "obține sub-lista de la primul", "LISTS_GET_SUBLIST_END_FROM_START": "la #", "LISTS_GET_SUBLIST_END_FROM_END": "la # de la sfarsit", "LISTS_GET_SUBLIST_END_LAST": "la ultima", "LISTS_GET_SUBLIST_TOOLTIP": "Creează o copie a porțiunii specificate dintr-o listă.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sortați %1 %2 %3", "LISTS_SORT_TOOLTIP": "Sortați o copie a unei liste.", "LISTS_SORT_ORDER_ASCENDING": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_ORDER_DESCENDING": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_NUMERIC": "numeric", "LISTS_SORT_TYPE_TEXT": "alfabetic", "LISTS_SORT_TYPE_IGNORECASE": "al<PERSON><PERSON><PERSON>, ignorați cazul", "LISTS_SPLIT_LIST_FROM_TEXT": "convertește textul în listă", "LISTS_SPLIT_TEXT_FROM_LIST": "convertește lista în text", "LISTS_SPLIT_WITH_DELIMITER": "cu separatorul", "LISTS_SPLIT_TOOLTIP_SPLIT": "Împarte textul într-o listă de texte, despărțite prin fiecare separator", "LISTS_SPLIT_TOOLTIP_JOIN": "Concatenează o listă de texte (alternate cu separatorul) într-un text unic", "LISTS_REVERSE_MESSAGE0": "inversă %1", "LISTS_REVERSE_TOOLTIP": "Inversați copia unei liste.", "VARIABLES_GET_TOOLTIP": "Returnează valoarea acestei variabile.", "VARIABLES_GET_CREATE_SET": "Crează 'set %1'", "VARIABLES_SET": "seteaza %1 la %2", "VARIABLES_SET_TOOLTIP": "Setează această variabilă sa fie egală la intrare.", "VARIABLES_SET_CREATE_GET": "Crează 'get %1'", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "la", "PROCEDURES_DEFNORETURN_PROCEDURE": "f<PERSON> ceva", "PROCEDURES_BEFORE_PARAMS": "cu:", "PROCEDURES_CALL_BEFORE_PARAMS": "cu:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Crează o funcție cu nicio i<PERSON>șire.", "PROCEDURES_DEFNORETURN_COMMENT": "Descrieți această funcție ...", "PROCEDURES_DEFRETURN_HELPURL": "https://ro.wikipedia.org/wiki/Subrutină", "PROCEDURES_DEFRETURN_RETURN": "returnează", "PROCEDURES_DEFRETURN_TOOLTIP": "Creează o funcție cu o ieșire.", "PROCEDURES_ALLOW_STATEMENTS": "permite declar<PERSON>", "PROCEDURES_DEF_DUPLICATE_WARNING": "Atenție: Această funcție are parametri duplicați.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Executați funcția '%1 'definită de utilizator.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Executați funcția „%1” definită de utilizator și folosiți producția sa.", "PROCEDURES_MUTATORCONTAINER_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, șterge sau reordonează parametrii de intrare ai acestei funcții.", "PROCEDURES_MUTATORARG_TITLE": "nume de intrare:", "PROCEDURES_MUTATORARG_TOOLTIP": "Adaugă un parametru de intrare pentru funcție.", "PROCEDURES_HIGHLIGHT_DEF": "Evidențiază definiția funcției", "PROCEDURES_CREATE_DO": "Creați „%1”", "PROCEDURES_IFRETURN_TOOLTIP": "Dacă o valoare este adevărată, atunci returnează valoarea a doua.", "PROCEDURES_IFRETURN_WARNING": "Avertisment: Acest bloc poate fi utilizat numai în definiția unei funcții.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Spune ceva...", "COLLAPSED_WARNINGS_WARNING": "<PERSON><PERSON><PERSON> blocate con<PERSON><PERSON> avertismente.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "Revocare"}