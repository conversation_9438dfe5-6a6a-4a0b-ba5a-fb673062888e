package org.jeecg.modules.teaching.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.service.IExamPaperService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.HashMap;
import org.jeecg.common.system.vo.LoginUser;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date:   2023-06-20
 * @Version: V1.0
 */
@Api(tags="试卷管理")
@RestController
@RequestMapping("/teaching/examSystem/testManage")
@Slf4j
public class ExamPaperController extends JeecgController<ExamPaper, IExamPaperService> {
    @Autowired
    private IExamPaperService examPaperService;
    
    // 创建一个线程池用于处理耗时操作
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);
    
    /**
     * 分页列表查询
     */
    @AutoLog(value = "试卷管理-分页列表查询")
    @ApiOperation(value="试卷管理-分页列表查询", notes="试卷管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExamPaper examPaper,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  @RequestParam(name="sortField", required=false) String sortField,
                                  @RequestParam(name="sortOrder", required=false) String sortOrder,
                                  HttpServletRequest req) {
        // 获取原始查询参数
        String title = req.getParameter("title");
        
        // 创建一个新的不包含title的查询对象
        ExamPaper queryObj = new ExamPaper();
        if(examPaper != null) {
            queryObj.setSubject(examPaper.getSubject());
            queryObj.setLevel(examPaper.getLevel());
            queryObj.setDifficulty(examPaper.getDifficulty());
            queryObj.setType(examPaper.getType());
            queryObj.setYear(examPaper.getYear());
            queryObj.setAuthor(examPaper.getAuthor());
            // 不设置title，后面会单独处理
        }
        
        // 初始化查询条件（不包含title的特殊处理）
        QueryWrapper<ExamPaper> queryWrapper = QueryGenerator.initQueryWrapper(queryObj, req.getParameterMap());
        
        // 对标题进行特殊处理，增强模糊查询和特殊字符处理
        if (title != null && !title.isEmpty()) {
            // 使用参数绑定方式处理特殊字符，并增强模糊查询能力
            queryWrapper.apply("INSTR(LOWER(title), LOWER({0})) > 0", title);
        }

        // 处理排序
        if (sortField != null && sortOrder != null) {
            log.info("试卷管理排序 - 原始字段: {}, 方向: {}", sortField, sortOrder);

            // 将前端的驼峰命名转换为数据库的下划线命名
            String dbFieldName = convertToDbFieldName(sortField);
            log.info("试卷管理排序 - 转换后字段: {}", dbFieldName);

            if ("asc".equals(sortOrder)) {
                queryWrapper.orderByAsc(dbFieldName);
            } else if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(dbFieldName);
            }
        } else {
            // 默认按创建时间降序排序
            queryWrapper.orderByDesc("create_time");
        }

        Page<ExamPaper> page = new Page<ExamPaper>(pageNo, pageSize);
        IPage<ExamPaper> pageList = examPaperService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 添加
     */
    @AutoLog(value = "试卷管理-添加")
    @ApiOperation(value="试卷管理-添加", notes="试卷管理-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ExamPaper examPaper) {
        // 标准化级别格式
        if (examPaper.getLevel() != null) {
            examPaper.setLevel(standardizeLevel(examPaper.getLevel()));
        }

        examPaperService.save(examPaper);
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("添加成功！");
        return result;
    }
    
    /**
     * 编辑
     */
    @AutoLog(value = "试卷管理-编辑")
    @ApiOperation(value="试卷管理-编辑", notes="试卷管理-编辑")
    @PutMapping(value = "/edit")
    public Result<String> edit(@RequestBody ExamPaper examPaper) {
        // 标准化级别格式
        if (examPaper.getLevel() != null) {
            examPaper.setLevel(standardizeLevel(examPaper.getLevel()));
        }

        examPaperService.updateById(examPaper);
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("编辑成功!");
        return result;
    }
    
    /**
     * 通过id删除
     */
    @AutoLog(value = "试卷管理-通过id删除")
    @ApiOperation(value="试卷管理-通过id删除", notes="试卷管理-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        examPaperService.removeById(id);
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("删除成功!");
        return result;
    }
    
    /**
     * 批量删除
     */
    @AutoLog(value = "试卷管理-批量删除")
    @ApiOperation(value="试卷管理-批量删除", notes="试卷管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.examPaperService.removeByIds(Arrays.asList(ids.split(",")));
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("批量删除成功!");
        return result;
    }
    
    /**
     * 通过id查询
     */
    @AutoLog(value = "试卷管理-通过id查询")
    @ApiOperation(value="试卷管理-通过id查询", notes="试卷管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ExamPaper> queryById(@RequestParam(name="id",required=true) String id) {
        ExamPaper examPaper = examPaperService.getById(id);
        Result<ExamPaper> result = new Result<>();
        if(examPaper==null) {
            result.setSuccess(false);
            result.setCode(500);
            result.setMessage("未找到对应数据");
            return result;
        }
        result.setSuccess(true);
        result.setCode(200);
        result.setResult(examPaper);
        return result;
    }

    /**
     * 手动选题创建试卷
     */
    @AutoLog(value = "试卷管理-手动选题创建试卷")
    @ApiOperation(value="试卷管理-手动选题创建试卷", notes="手动选择题目创建试卷")
    @PostMapping(value = "/createWithQuestions")
    public Result<?> createWithQuestions(@RequestBody JSONObject params) {
        ExamPaper examPaper = params.getObject("examPaper", ExamPaper.class);
        List<String> questionIds = params.getJSONArray("questionIds").toJavaList(String.class);
        List<Integer> scores = params.getJSONArray("scores").toJavaList(Integer.class);
        
        // 标准化级别格式
        if (examPaper.getLevel() != null) {
            examPaper.setLevel(standardizeLevel(examPaper.getLevel()));
        }
        
        try {
            String paperId = examPaperService.createPaperWithQuestions(examPaper, questionIds, scores);
            return Result.ok(paperId);
        } catch (Exception e) {
            log.error("创建试卷失败", e);
            return Result.error("创建试卷失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取试卷的所有题目
     */
    @AutoLog(value = "试卷管理-获取试卷的所有题目")
    @ApiOperation(value="试卷管理-获取试卷的所有题目", notes="获取试卷的所有题目信息")
    @GetMapping(value = "/getPaperQuestions")
    public Result<?> getPaperQuestions(@RequestParam(name="paperId",required=true) String paperId) {
        try {
            List<Map<String, Object>> questions = examPaperService.getPaperQuestions(paperId);
            return Result.ok(questions);
        } catch (Exception e) {
            log.error("获取试卷题目失败", e);
            return Result.error("获取试卷题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 导入试卷
     */
    @AutoLog(value = "试卷管理-导入试卷")
    @ApiOperation(value="试卷管理-导入试卷", notes="从TXT文件导入试卷，只从文件中获取元数据")
    @PostMapping(value = "/import")
    public Result<?> importPaper(@RequestParam("file") MultipartFile file,
                           @RequestParam(value = "author", required = false) String authorParam,
                           @RequestParam(value = "mode", required = false, defaultValue = "confirm") String mode,
                           @RequestParam(value = "allowReference", required = false, defaultValue = "true") Boolean allowReference) {
    if (file.isEmpty()) {
        return Result.error("请选择要导入的文件");
    }
    
    String filename = file.getOriginalFilename();
    if (!oConvertUtils.isEmpty(filename) && !filename.toLowerCase().endsWith(".txt")) {
        return Result.error("请上传txt格式的文件");
    }
    
    try {
        // 创建空的元数据参数Map，不再接收前端传入的元数据
        Map<String, Object> metadata = new HashMap<>();
        
        // 如果author参数为空，则使用当前登录用户的用户名作为作者
        // 获取当前登录用户
        String author = authorParam;
        LoginUser loginUser = getCurrentUser();
        if (loginUser != null && (author == null || author.trim().isEmpty())) {
            // 使用当前登录用户的真实名称作为作者
            author = loginUser.getRealname();
            // 添加日志
            log.info("试卷导入：使用当前登录用户 [{}] 作为作者", author);
        }
        
        // 设置作者参数
        if (author != null) {
            metadata.put("author", author);
        }
        
        // 记录日志，确认不使用表单元数据
        log.info("试卷导入：仅从文件中获取元数据，用户提供的author参数: " + (author != null ? author : "未提供"));
        
        // 对于大文件，使用异步处理
        if (file.getSize() > 1024 * 1024) { // 大于1MB的文件使用异步处理
            // 返回一个任务ID，前端可以通过这个ID查询导入进度
            String taskId = "import_" + System.currentTimeMillis();
            
            // 异步处理导入
            final String finalAuthor = author;
            CompletableFuture.runAsync(() -> {
                try {
                    Map<String, Object> result = examPaperService.importPaperFromFile(file, finalAuthor, metadata);
                    // 这里可以将结果保存到缓存或数据库中，供前端查询
                    log.info("异步导入试卷完成，任务ID: " + taskId + ", 结果: " + result);
                } catch (Exception e) {
                    log.error("异步导入试卷失败，任务ID: " + taskId, e);
                }
            }, executorService);
            
            return Result.ok("文件较大，已启动异步导入，任务ID: " + taskId);
        } else {
            // 小文件同步处理
            Map<String, Object> result;
            if ("preview".equals(mode)) {
                // 预览模式：只检测不实际导入
                result = examPaperService.previewPaperFromFile(file, author, metadata);
            } else {
                // 确认模式：实际导入
                result = examPaperService.importPaperFromFile(file, author, metadata, allowReference);
            }

            // 检查服务层返回的结果，如果服务层标记为失败，则返回失败响应
            if (result != null && result.containsKey("success") && !(Boolean) result.get("success")) {
                // 服务层返回失败，保持失败状态
                String message = (String) result.get("message");
                Result<Object> errorResult = Result.error(message != null ? message : "导入失败");
                errorResult.setResult(result);
                return errorResult;
            }

            return Result.ok(result);
        }
    } catch (Exception e) {
        log.error("导入试卷失败", e);
        return Result.error("导入试卷失败: " + e.getMessage());
    }
}
    
    /**
     * 导出试卷
     */
    @AutoLog(value = "试卷管理-导出试卷")
    @ApiOperation(value="试卷管理-导出试卷", notes="导出试卷为TXT格式")
    @GetMapping(value = "/export")
    public Result<?> exportPaper(
        @ApiParam(value = "标题") @RequestParam(required = false) String title,
        @ApiParam(value = "科目") @RequestParam(required = false) String subject,
        @ApiParam(value = "级别") @RequestParam(required = false) String level,
        @ApiParam(value = "难度") @RequestParam(required = false) Integer difficulty,
        @ApiParam(value = "类型") @RequestParam(required = false) String type,
        @ApiParam(value = "年份") @RequestParam(required = false) String year,
        HttpServletResponse response) {
    try {
        // 构建查询条件
        QueryWrapper<ExamPaper> queryWrapper = new QueryWrapper<>();
        if (title != null && !title.isEmpty()) {
            // 使用参数绑定方式处理特殊字符，并增强模糊查询能力
            // 直接使用数据库函数进行模糊匹配，避免特殊字符转义问题
            queryWrapper.apply("INSTR(LOWER(title), LOWER({0})) > 0", title);
        }
        if (subject != null && !subject.isEmpty()) {
            queryWrapper.eq("subject", subject);
        }
        if (level != null && !level.isEmpty()) {
            // 标准化级别格式
            level = standardizeLevel(level);
            queryWrapper.eq("level", level);
        }
        if (difficulty != null) {
            queryWrapper.eq("difficulty", difficulty);
        }
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq("type", type);
        }
        if (year != null && !year.isEmpty()) {
            queryWrapper.eq("year", year);
        }
        
        // 获取符合条件的试卷
        List<ExamPaper> papers = examPaperService.list(queryWrapper);
        
        if (papers.isEmpty()) {
            return Result.error("没有符合条件的试卷可供导出");
        }
        
        // 导出所有试卷
        StringBuilder content = new StringBuilder();
        
        for (int i = 0; i < papers.size(); i++) {
            ExamPaper paper = papers.get(i);

            // 导出试卷内容
            String paperContent = examPaperService.exportPaperToText(paper.getId());
            content.append(paperContent);
            
            // 如果不是最后一份试卷，添加分隔符
            if (i < papers.size() - 1) {
                content.append("\n\n");
                // 使用兼容的方式生成分隔符，而不是使用Java 11的repeat方法
                for (int j = 0; j < 50; j++) {
                    content.append("=");
                }
                content.append("\n\n");
            }
        }
        
        // 确保内容不为空，并使用自定义的结果类返回，避免框架序列化影响
        String exportContent = content.toString();
        if (exportContent.trim().isEmpty()) {
            return Result.error("导出内容为空");
        }
        
        // 直接使用HashMap，避免使用Result中的泛型可能导致的序列化问题
        java.util.Map<String, String> resultMap = new java.util.HashMap<>();
        resultMap.put("content", exportContent);
        
        return Result.ok(resultMap);
        
    } catch (Exception e) {
        log.error("导出试卷失败", e);
        return Result.error("导出试卷失败: " + e.getMessage());
    }
}


    
    /**
     * 查询导入/导出任务状态
     */
    @AutoLog(value = "试卷管理-查询任务状态")
    @ApiOperation(value="试卷管理-查询任务状态", notes="查询异步导入/导出任务的状态")
    @GetMapping(value = "/taskStatus")
    public Result<?> getTaskStatus(@RequestParam(name="taskId",required=true) String taskId) {
        // 实际项目中，这里应该从缓存或数据库中查询任务状态
        // 这里只是一个示例，返回模拟数据
        JSONObject status = new JSONObject();
        status.put("taskId", taskId);
        
        if (taskId.startsWith("import_")) {
            status.put("type", "import");
            status.put("status", "completed"); // 模拟状态：pending, processing, completed, failed
            status.put("progress", 100); // 进度百分比
            status.put("message", "导入完成");
        } else if (taskId.startsWith("export_")) {
            status.put("type", "export");
            status.put("status", "completed");
            status.put("progress", 100);
            status.put("message", "导出完成");
            status.put("downloadUrl", "/teaching/examSystem/testManage/downloadExport?taskId=" + taskId);
        } else {
            return Result.error("无效的任务ID");
        }
        
        return Result.ok(status);
    }
    
    /**
     * 下载异步导出的文件
     */
    @AutoLog(value = "试卷管理-下载异步导出文件")
    @ApiOperation(value="试卷管理-下载异步导出文件", notes="下载异步导出的试卷文件")
    @GetMapping(value = "/downloadExport")
    public void downloadExport(@RequestParam(name="taskId",required=true) String taskId,
                             HttpServletResponse response) {
        try {
            // 实际项目中，这里应该从文件系统或数据库中获取导出的内容
            // 这里只是一个示例，返回模拟数据
            String content = "这是异步导出的试卷内容，任务ID: " + taskId;
            
            // 设置响应头
            String fileName = "export_" + taskId + ".txt";
            response.setContentType("text/plain");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            
            // 将内容写入响应
            response.getWriter().write(content);
            response.getWriter().flush();
        } catch (Exception e) {
            log.error("下载异步导出文件失败", e);
        }
    }
    
    /**
     * 将各种格式的级别标准化为"X级"格式
     * @param level 原始级别文本，如"6"、"六"、"6级"、"六级"等
     * @return 标准化的级别文本，如"六级"
     */
    private String standardizeLevel(String level) {
        if (level == null || level.isEmpty()) {
            return level;
        }
        
        // 如果已经是"X级"格式，检查是否需要转换数字部分
        if (level.endsWith("级")) {
            String prefix = level.substring(0, level.length() - 1);
            // 如果前缀是数字，转换为中文
            try {
                int num = Integer.parseInt(prefix);
                return numberToChinese(num) + "级";
            } catch (NumberFormatException e) {
                // 前缀不是数字，可能已经是中文
                return level;
            }
        }
        
        // 如果是纯数字，转换为"X级"格式
        try {
            int num = Integer.parseInt(level);
            return numberToChinese(num) + "级";
        } catch (NumberFormatException e) {
            // 不是纯数字，检查是否是单个中文数字
            switch (level) {
                case "一": return "一级";
                case "二": return "二级";
                case "三": return "三级";
                case "四": return "四级";
                case "五": return "五级";
                case "六": return "六级";
                case "七": return "七级";
                case "八": return "八级";
                case "九": return "九级";
                default: return level; // 无法识别，返回原始值
            }
        }
    }
    
    /**
     * 将数字转换为中文数字
     * @param num 数字
     * @return 中文数字
     */
    private String numberToChinese(int num) {
        if (num <= 0 || num > 9) {
            return String.valueOf(num); // 超出范围，返回原始数字
        }
        
        switch (num) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "七";
            case 8: return "八";
            case 9: return "九";
            default: return String.valueOf(num);
        }
    }

    /**
     * 自动格式化试卷模板
     */
    @AutoLog(value = "试卷管理-自动格式化")
    @ApiOperation(value="试卷管理-自动格式化", notes="自动格式化试卷模板文件")
    @PostMapping(value = "/autoFormatTemplate")
    public Result<?> autoFormatTemplate(@RequestParam("file") MultipartFile file,
                                      @RequestParam("subject") String subject,
                                      @RequestParam("level") String level,
                                      @RequestParam("difficulty") Integer difficulty,
                                      @RequestParam("type") String type,
                                      @RequestParam("year") String year) {
        try {
            log.info("=== 控制器开始自动格式化试卷 ===");
            log.info("文件名: {}, 科目: {}, 级别: {}, 难度: {}, 类型: {}, 年份: {}",
                    file.getOriginalFilename(), subject, level, difficulty, type, year);

            if (file.isEmpty()) {
                return Result.error("请选择要格式化的文件");
            }

            // 调用服务进行自动格式化
            String formattedContent = examPaperService.autoFormatPaperTemplate(file, subject, level, difficulty, type, year);

            log.info("控制器收到格式化结果，长度: {}", formattedContent != null ? formattedContent.length() : 0);
            log.info("控制器返回结果前50字符: {}", formattedContent != null && formattedContent.length() > 0 ?
                    formattedContent.substring(0, Math.min(50, formattedContent.length())) : "null或空");

            return Result.ok((Object) formattedContent);

        } catch (Exception e) {
            log.error("自动格式化试卷失败", e);
            return Result.error("自动格式化试卷失败: " + e.getMessage());
        }
    }

    /**
     * 将前端驼峰命名转换为数据库下划线命名
     */
    private String convertToDbFieldName(String camelCaseField) {
        if (camelCaseField == null) {
            return null;
        }

        // 处理特定字段映射
        switch (camelCaseField) {
            case "examDuration":
                return "exam_duration";
            case "createBy":
                return "create_by";
            case "createTime":
                return "create_time";
            case "updateBy":
                return "update_by";
            case "updateTime":
                return "update_time";
            default:
                // 通用驼峰转下划线
                return camelCaseField.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
        }
    }
}