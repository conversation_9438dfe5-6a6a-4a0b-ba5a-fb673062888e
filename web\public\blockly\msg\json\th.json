{"@metadata": {"authors": ["Aefgh39622", "Azpirin", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nantapat", "Octahedron80", "<PERSON><PERSON><PERSON>", "Trisorn Triboon"]}, "VARIABLES_DEFAULT_NAME": "รายการ", "UNNAMED_KEY": "ไม่มีชื่อ", "TODAY": "วันนี้", "DUPLICATE_BLOCK": "ทำสำเนา", "ADD_COMMENT": "ใส่คำอธิบาย", "REMOVE_COMMENT": "เอาคำอธิบายออก", "DUPLICATE_COMMENT": "ทำสำเนาความเห็น", "EXTERNAL_INPUTS": "อินพุตภายนอก", "INLINE_INPUTS": "อินพุตในบรรทัด", "DELETE_BLOCK": "ลบบล็อก", "DELETE_X_BLOCKS": "ลบ %1 บล็อก", "DELETE_ALL_BLOCKS": "ลบ %1 บล็อกทั้งหมด?", "CLEAN_UP": "จัดเรียงบล็อกให้เป็นแถว", "COLLAPSE_BLOCK": "ย่อบล็อก", "COLLAPSE_ALL": "ย่อบล็อก", "EXPAND_BLOCK": "ขยายบล็อก", "EXPAND_ALL": "ขยายบล็อก", "DISABLE_BLOCK": "ปิดใช้งานบล็อก", "ENABLE_BLOCK": "เปิดใช้งานบล็อก", "HELP": "ช่วยเหลือ", "UNDO": "ย้อนกลับ", "REDO": "ทำซ้ำ", "CHANGE_VALUE_TITLE": "เปลี่ยนค่า:", "RENAME_VARIABLE": "เปลี่ยนชื่อตัวแปร...", "RENAME_VARIABLE_TITLE": "เปลี่ยนชื่อตัวแปร '%1' ทั้งหมดเป็น:", "NEW_VARIABLE": "สร้างตัวแปร...", "NEW_STRING_VARIABLE": "สร้างตัวแปร string", "NEW_NUMBER_VARIABLE": "สร้างตัวแปรจำนวน", "NEW_COLOUR_VARIABLE": "สร้างตัวแปรสี", "NEW_VARIABLE_TYPE_TITLE": "ชนิดตัวแปรใหม่", "NEW_VARIABLE_TITLE": "ชื่อตัวแปรใหม่:", "VARIABLE_ALREADY_EXISTS": "มีตัวแปรชื่อ '%1' อยู่แล้ว", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "ตัวแปรชื่อ '%1' มีอยู่แล้วสำหรับตัวแปรอื่นของชนิด: '%2'", "DELETE_VARIABLE_CONFIRMATION": "ลบการใช้ตัวแปร %2 %1 ครั้งหรือไม่", "CANNOT_DELETE_VARIABLE_PROCEDURE": "ไม่สามารถลบตัวแปร '%1' ได้เนื่องจากเป็นส่วนหนึ่งของนิยามของฟังก์ชัน '%2'", "DELETE_VARIABLE": "ลบตัวแปร '%1'", "COLOUR_PICKER_HELPURL": "https://th.wikipedia.org/wiki/สี", "COLOUR_PICKER_TOOLTIP": "เลือกสีจากจานสี", "COLOUR_RANDOM_TITLE": "สุ่มสี", "COLOUR_RANDOM_TOOLTIP": "เลือกสีแบบสุ่ม", "COLOUR_RGB_TITLE": "สีที่ประกอบด้วย", "COLOUR_RGB_RED": "ค่าสีแดง", "COLOUR_RGB_GREEN": "ค่าสีเขียว", "COLOUR_RGB_BLUE": "ค่าสีน้ำเงิน", "COLOUR_RGB_TOOLTIP": "สร้างสีด้วยการกำหนดค่าสีแดง เขียว และน้ำเงิน ค่าทั้งหมดต้องอยู่ระหว่าง 0 ถึง 100", "COLOUR_BLEND_TITLE": "ผสม", "COLOUR_BLEND_COLOUR1": "สีที่ 1", "COLOUR_BLEND_COLOUR2": "สีที่ 2", "COLOUR_BLEND_RATIO": "อัตราส่วน", "COLOUR_BLEND_TOOLTIP": "ผสมสองสีเข้าด้วยกันด้วยอัตราส่วน (0.0 - 1.0)", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "ทำซ้ำ %1 ครั้ง", "CONTROLS_REPEAT_INPUT_DO": "ทำ", "CONTROLS_REPEAT_TOOLTIP": "ทำซ้ำบางคำสั่งหลายครั้ง", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "ทำซ้ำขณะที่", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "ทำซ้ำจนกระทั่ง", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "ขณะที่ค่าเป็นจริง ก็จะทำบางคำสั่ง", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "ขณะที่ค่าเป็นเท็จ ก็จะทำบางคำสั่ง", "CONTROLS_FOR_TOOLTIP": "ตัวแปร '%1' จะเริ่มจากจำนวนเริ่มต้น ไปจนถึงจำนวนสุดท้าย ตามระยะที่กำหนด และ ทำบล็อกที่กำหนดไว้", "CONTROLS_FOR_TITLE": "นับด้วย %1 จาก %2 จนถึง %3 เปลี่ยนค่าทีละ %4", "CONTROLS_FOREACH_TITLE": "จากทุกรายการ %1 ในรายชื่อ %2", "CONTROLS_FOREACH_TOOLTIP": "จากทุกรายการในรายชื่อ ตั้งค่าตัวแปร \"%1\" เป็นรายการ และทำตามคำสั่งที่กำหนดไว้", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "ออกจากการวนซ้ำ", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "เริ่มการวนซ้ำรอบต่อไป", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "ออกจากการวนซ้ำที่อยู่", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "ข้ามคำสั่งที่เหลืออยู่ และเริ่มต้นวนซ้ำรอบต่อไป", "CONTROLS_FLOW_STATEMENTS_WARNING": "คำเตือน: บล็อกนี้ใช้งานได้ภายในการวนซ้ำเท่านั้น", "CONTROLS_IF_TOOLTIP_1": "ถ้าเงื่อนไขเป็นจริง ก็จะ \"ทำ\" ตามที่กำหนด", "CONTROLS_IF_TOOLTIP_2": "ถ้าเงื่อนไขเป็นจริง ก็จะ \"ทำ\" ตามที่กำหนด แต่ถ้าเงื่อนไขเป็นเท็จก็จะทำ \"นอกเหนือจากนี้\"", "CONTROLS_IF_TOOLTIP_3": "ถ้าเงื่อนไขแรกเป็นจริง ก็จะทำตามคำสั่งในบล็อกแรก แต่ถ้าไม่ก็จะไปตรวจเงื่อนไขที่สอง ถ้าเงื่อนไขที่สองเป็นจริง ก็จะทำตามเงื่อนไขในบล็อกที่สองนี้", "CONTROLS_IF_TOOLTIP_4": "ถ้าเงื่อนไขแรกเป็นจริง ก็จะทำคำสั่งในบล็อกแรก จากนั้นจะข้ามคำสั่งในบล็อกที่เหลือ แต่ถ้าเงื่อนไขแรกเป็นเท็จ ก็จะทำการตรวจเงื่อนไขที่สอง ถ้าเงื่อนไขที่สองเป็นจริง ก็จะทำตามคำสั่งในบล็อกที่สอง จากนั้นจะข้ามคำสั่งในบล็อกที่เหลือ แต่ถ้าทั้งเงื่อนไขแรกและเงื่อนไขที่สองเป็นเท็จทั้งหมด ก็จะมาทำบล็อกที่สาม", "CONTROLS_IF_MSG_IF": "ถ้า", "CONTROLS_IF_MSG_ELSEIF": "นอกเหนือจากนี้ ถ้า", "CONTROLS_IF_MSG_ELSE": "นอกเหนือจากนี้", "CONTROLS_IF_IF_TOOLTIP": "เพิ่ม ลบ หรือจัดเรียงบล็อก \"ถ้า\" นี้ใหม่", "CONTROLS_IF_ELSEIF_TOOLTIP": "กำหนดเงื่อนไขของบล็อก \"ถ้า\"", "CONTROLS_IF_ELSE_TOOLTIP": "เพิ่มสิ่งสุดท้าย ที่จะตรวจจับความเป็นไปได้ทั้งหมดของบล็อก \"ถ้า\"", "IOS_OK": "ตกลง", "IOS_CANCEL": "ยกเลิก", "IOS_ERROR": "ข้อผิดพลาด", "IOS_PROCEDURES_INPUTS": "อินพุต", "IOS_PROCEDURES_ADD_INPUT": "+ เพิ่มอินพุต", "IOS_PROCEDURES_ALLOW_STATEMENTS": "อนุญาตคำสั่ง", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "ฟังก์ชันนี้มีอินพุตที่ซ้ำกัน", "IOS_VARIABLES_ADD_VARIABLE": "+ เพิ่มตัวแปร", "IOS_VARIABLES_ADD_BUTTON": "เพิ่ม", "IOS_VARIABLES_RENAME_BUTTON": "เปลี่ยนชื่อ", "IOS_VARIABLES_DELETE_BUTTON": "ลบ", "IOS_VARIABLES_VARIABLE_NAME": "ชื่อตัวแปร", "IOS_VARIABLES_EMPTY_NAME_ERROR": "คุณไม่สามารถใช้ชื่อตัวแปรที่ว่างเปล่าได้", "LOGIC_COMPARE_HELPURL": "https://th.wikipedia.org/wiki/อสมการ", "LOGIC_COMPARE_TOOLTIP_EQ": "คืนค่าเป็น \"จริง\" ถ้าค่าที่ใส่ทั้งสองค่านั้นเท่ากัน", "LOGIC_COMPARE_TOOLTIP_NEQ": "คืนค่าเป็น \"จริง\" ถ้าค่าที่ใส่ทั้งสองค่านั้นไม่เท่ากัน", "LOGIC_COMPARE_TOOLTIP_LT": "คืนค่าเป็น \"จริง\" ถ้าค่าแรกน้อยกว่าค่าที่สอง", "LOGIC_COMPARE_TOOLTIP_LTE": "คืนค่าเป็น \"จริง\" ถ้าค่าแรกน้อยกว่าหรือเท่ากับค่าที่สอง", "LOGIC_COMPARE_TOOLTIP_GT": "คืนค่าเป็น \"จริง\" ถ้าค่าแรกมากกว่าค่าที่สอง", "LOGIC_COMPARE_TOOLTIP_GTE": "คืนค่าเป็น \"จริง\" ถ้าค่าแรกมากกว่าหรือเท่ากับค่าที่สอง", "LOGIC_OPERATION_TOOLTIP_AND": "คืนค่าเป็น \"จริง\" ถ้าค่าทั้งสองค่าเป็นจริง", "LOGIC_OPERATION_AND": "และ", "LOGIC_OPERATION_TOOLTIP_OR": "คืนค่าเป็น \"จริง\" ถ้ามีอย่างน้อยหนึ่งค่าที่เป็นจริง", "LOGIC_OPERATION_OR": "หรือ", "LOGIC_NEGATE_TITLE": "ไม่ %1", "LOGIC_NEGATE_TOOLTIP": "คืนค่าเป็น \"จริง\" ถ้าค่าที่ใส่เป็นเท็จ คืนค่าเป็น \"เท็จ\" ถ้าค่าที่ใส่เป็นจริง", "LOGIC_BOOLEAN_TRUE": "จริง", "LOGIC_BOOLEAN_FALSE": "เท็จ", "LOGIC_BOOLEAN_TOOLTIP": "คืนค่าเป็นจริงหรือเท็จ", "LOGIC_NULL": "ไม่กำหนด", "LOGIC_NULL_TOOLTIP": "คืนค่า \"ไม่กำหนด\"", "LOGIC_TERNARY_CONDITION": "ทดสอบ", "LOGIC_TERNARY_IF_TRUE": "ถ้า เป็นจริง", "LOGIC_TERNARY_IF_FALSE": "ถ้า เป็นเท็จ", "LOGIC_TERNARY_TOOLTIP": "ตรวจสอบเงื่อนไขใน \"ทดสอบ\" ถ้าเงื่อนไขเป็นจริง จะคืนค่า \"ถ้า เป็นจริง\" ถ้าเงื่อนไขเป็นเท็จ จะคืนค่า \"ถ้า เป็นเท็จ\"", "MATH_NUMBER_HELPURL": "https://th.wikipedia.org/wiki/จำนวน", "MATH_NUMBER_TOOLTIP": "จำนวน", "MATH_ARITHMETIC_HELPURL": "https://th.wikipedia.org/wiki/เลขคณิต", "MATH_ARITHMETIC_TOOLTIP_ADD": "คืนค่าผลรวมของตัวเลขทั้งสองจำนวน", "MATH_ARITHMETIC_TOOLTIP_MINUS": "คืนค่าผลต่างของตัวเลขทั้งสองจำนวน", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "คืนค่าผลคูณของตัวเลขทั้งสองจำนวน", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "คืนค่าผลหารของตัวเลขทั้งสองจำนวน", "MATH_ARITHMETIC_TOOLTIP_POWER": "คืนค่าผลการยกกำลัง โดยตัวเลขแรกเป็นฐาน และตัวเลขที่สองเป็นเลขชี้กำลัง", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "รากที่สอง", "MATH_SINGLE_TOOLTIP_ROOT": "คืนค่ารากที่สองของตัวเลข", "MATH_SINGLE_OP_ABSOLUTE": "ค่าสัมบูรณ์", "MATH_SINGLE_TOOLTIP_ABS": "คืนค่าค่าสัมบูรณ์ของตัวเลข", "MATH_SINGLE_TOOLTIP_NEG": "คืนค่าติดลบของตัวเลข", "MATH_SINGLE_TOOLTIP_LN": "คืนค่าลอการิทึมธรรมชาติของตัวเลข", "MATH_SINGLE_TOOLTIP_LOG10": "คืนค่าลอการิทึมฐานสิบของตัวเลข", "MATH_SINGLE_TOOLTIP_EXP": "คืนค่า e ยกกำลังด้วยตัวเลข", "MATH_SINGLE_TOOLTIP_POW10": "คืนค่า 10 ยกกำลังด้วยตัวเลข", "MATH_TRIG_HELPURL": "https://th.wikipedia.org/wiki/ฟังก์ชันตรีโกณมิติ", "MATH_TRIG_TOOLTIP_SIN": "คืนค่า sine ขององศา (ไม่ใช่เรเดียน)", "MATH_TRIG_TOOLTIP_COS": "คืนค่า cosine ขององศา (ไม่ใช่เรเดียน)", "MATH_TRIG_TOOLTIP_TAN": "คืนค่า tangent ขององศา (ไม่ใช่เรเดียน)", "MATH_TRIG_TOOLTIP_ASIN": "คืนค่า arcsine ของตัวเลข", "MATH_TRIG_TOOLTIP_ACOS": "คืนค่า arccosine ของตัวเลข", "MATH_TRIG_TOOLTIP_ATAN": "คืนค่า arctangent ของตัวเลข", "MATH_CONSTANT_HELPURL": "https://th.wikipedia.org/wiki/ค่าคงตัวทางคณิตศาสตร์", "MATH_CONSTANT_TOOLTIP": "คืนค่าคงตัวทางคณิตศาสตร์ที่พบบ่อยๆ เช่น π (3.141…), e (2.718…), φ (1.618…), รากที่สอง (1.414…), รากที่ ½ (0.707…), ∞ (อนันต์)", "MATH_IS_EVEN": "เป็นจำนวนคู่", "MATH_IS_ODD": "เป็นจำนวนคี่", "MATH_IS_PRIME": "เป็นจำนวนเฉพาะ", "MATH_IS_WHOLE": "เป็นเลขจำนวนเต็ม", "MATH_IS_POSITIVE": "เป็นเลขบวก", "MATH_IS_NEGATIVE": "เป็นเลขติดลบ", "MATH_IS_DIVISIBLE_BY": "หารลงตัว", "MATH_IS_TOOLTIP": "ตรวจว่าตัวเลขเป็นจำนวนคู่ จำนวนคี่ จำนวนเฉพาะ จำนวนเต็ม เลขบวก เลขติดลบ หรือหารด้วยเลขที่กำหนดลงตัวหรือไม่ คืนค่าเป็นจริงหรือเท็จ", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "เปลี่ยนค่า %1 เป็น %2", "MATH_CHANGE_TOOLTIP": "เพิ่มค่าของตัวแปร \"%1\"", "MATH_ROUND_HELPURL": "https://th.wikipedia.org/wiki/การปัดเศษ", "MATH_ROUND_TOOLTIP": "ปัดเศษของตัวเลขขึ้นหรือลง", "MATH_ROUND_OPERATOR_ROUND": "ปัดเศษ", "MATH_ROUND_OPERATOR_ROUNDUP": "ปัดเศษขึ้น", "MATH_ROUND_OPERATOR_ROUNDDOWN": "ปัดเศษลง", "MATH_ONLIST_OPERATOR_SUM": "ผลรวมของรายการ", "MATH_ONLIST_TOOLTIP_SUM": "คืนค่าผลรวมของตัวเลขทั้งหมดในรายการ", "MATH_ONLIST_OPERATOR_MIN": "น้อยที่สุดในรายการ", "MATH_ONLIST_TOOLTIP_MIN": "คืนค่าตัวเลขที่น้อยที่สุดในรายการ", "MATH_ONLIST_OPERATOR_MAX": "มากที่สุดในรายการ", "MATH_ONLIST_TOOLTIP_MAX": "คืนค่าตัวเลขที่มากที่สุดในรายการ", "MATH_ONLIST_OPERATOR_AVERAGE": "ค่าเฉลี่ยของรายการ", "MATH_ONLIST_TOOLTIP_AVERAGE": "คืนค่าเฉลี่ยของรายการ", "MATH_ONLIST_OPERATOR_MEDIAN": "ค่ามัธยฐานของรายการ", "MATH_ONLIST_TOOLTIP_MEDIAN": "คืนค่ามัธยฐานของรายการ", "MATH_ONLIST_OPERATOR_MODE": "ฐานนิยมของรายการ", "MATH_ONLIST_TOOLTIP_MODE": "คืนค่าฐานนิยมของรายการ", "MATH_ONLIST_OPERATOR_STD_DEV": "ส่วนเบี่ยงเบนมาตรฐานของรายการ", "MATH_ONLIST_TOOLTIP_STD_DEV": "คืนค่าส่วนเบี่ยงเบนมาตรฐานของรายการ", "MATH_ONLIST_OPERATOR_RANDOM": "สุ่มรายการ", "MATH_ONLIST_TOOLTIP_RANDOM": "สุ่มคืนค่าสิ่งที่อยู่ในรายการ", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "เศษของ %1 ÷ %2", "MATH_MODULO_TOOLTIP": "คืนค่าเศษที่ได้จากการหารของตัวเลขทั้งสองจำนวน", "MATH_CONSTRAIN_TITLE": "จำกัดค่า %1 ต่ำสุด %2 สูงสุด %3", "MATH_CONSTRAIN_TOOLTIP": "จำกัดค่าของตัวเลขให้อยู่ในช่วงที่กำหนด", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "สุ่มเลขจำนวนเต็มตั้งแต่ %1 จนถึง %2", "MATH_RANDOM_INT_TOOLTIP": "สุ่มเลขจำนวนเต็มจากช่วงที่กำหนด", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "สุ่มเลขเศษส่วน", "MATH_RANDOM_FLOAT_TOOLTIP": "สุ่มเลขเศษส่วน ตั้งแต่ 0.0 แต่ไม่เกิน 1.0", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 ของ X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "เปลี่ยนอาร์กแทนเจนต์ของชุด (X, Y) จากองศา 180 เป็น -180.", "TEXT_TEXT_HELPURL": "https://th.wikipedia.org/wiki/สายอักขระ", "TEXT_TEXT_TOOLTIP": "ตัวหนังสือ คำ หรือข้อความ", "TEXT_JOIN_TITLE_CREATEWITH": "สร้างข้อความด้วย", "TEXT_JOIN_TOOLTIP": "สร้างข้อความด้วยการรวมจำนวนของรายการเข้าด้วยกัน", "TEXT_CREATE_JOIN_TITLE_JOIN": "รวม", "TEXT_CREATE_JOIN_TOOLTIP": "เพิ่ม ลบ หรือจัดเรียงบล็อกข้อความนี้ใหม่", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "เพิ่มรายการเข้าไปในข้อความ", "TEXT_APPEND_TITLE": "นำเอา %1 ต่อด้วยข้อความ %2", "TEXT_APPEND_TOOLTIP": "ต่อข้อความให้ตัวแปร \"%1\"", "TEXT_LENGTH_TITLE": "ความยาวของ %1", "TEXT_LENGTH_TOOLTIP": "คืนค่าความยาวของข้อความ (รวมช่องว่าง)", "TEXT_ISEMPTY_TITLE": "%1 ว่าง", "TEXT_ISEMPTY_TOOLTIP": "คืนค่าจริง ถ้าข้อความยังว่างเปล่า", "TEXT_INDEXOF_TOOLTIP": "คืนค่าตำแหน่งที่พบข้อความแรกอยู่ในข้อความที่สอง คืนค่า %1 ถ้าหาไม่พบ", "TEXT_INDEXOF_TITLE": "ในข้อความ %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "หาข้อความแรกที่พบ", "TEXT_INDEXOF_OPERATOR_LAST": "หาข้อความสุดท้ายที่พบ", "TEXT_CHARAT_TITLE": "ในข้อความ %1 %2", "TEXT_CHARAT_FROM_START": "ดึง ตัวอักษรตัวที่", "TEXT_CHARAT_FROM_END": "ดึง ตัวอักษรตัวที่ # จากท้าย", "TEXT_CHARAT_FIRST": "ดึง ตัวอักษรตัวแรก", "TEXT_CHARAT_LAST": "ดึง ตัวอักษรตัวสุดท้าย", "TEXT_CHARAT_RANDOM": "ถึงตัวอักษรแบบสุ่ม", "TEXT_CHARAT_TOOLTIP": "คืนค่าตัวอักษรจากตำแหน่งที่ระบุ", "TEXT_GET_SUBSTRING_TOOLTIP": "คืนค่าบางส่วนของข้อความ", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ในข้อความ", "TEXT_GET_SUBSTRING_START_FROM_START": "แยกข้อความย่อยตั้งแต่ ตัวอักษรที่", "TEXT_GET_SUBSTRING_START_FROM_END": "แยกข้อความย่อยตั้งแต่ ตัวอักษรที่ # จากท้าย", "TEXT_GET_SUBSTRING_START_FIRST": "แยกข้อความย่อยตั้งแต่ ตัวอักษรแรก", "TEXT_GET_SUBSTRING_END_FROM_START": "จนถึง ตัวอักษรที่", "TEXT_GET_SUBSTRING_END_FROM_END": "จนถึง ตัวอักษรที่ # จากท้าย", "TEXT_GET_SUBSTRING_END_LAST": "จนถึง ตัวอักษรสุดท้าย", "TEXT_CHANGECASE_TOOLTIP": "คืนค่าสำเนาของข้อความในกรณีต่างๆ", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "เปลี่ยนเป็น ตัวพิมพ์ใหญ่", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "เปลี่ยนเป็น ตัวพิมพ์เล็ก", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "เปลี่ยนเป็น ตัวอักษรแรกเป็นตัวพิมพ์ใหญ่", "TEXT_TRIM_TOOLTIP": "คืนค่าสำเนาของข้อความที่ลบเอาช่องว่างหน้าและหลังข้อความออกแล้ว", "TEXT_TRIM_OPERATOR_BOTH": "ลบช่องว่างทั้งสองข้างของ", "TEXT_TRIM_OPERATOR_LEFT": "ลบช่องว่างด้านหน้าของ", "TEXT_TRIM_OPERATOR_RIGHT": "ลบช่องว่างข้างท้ายของ", "TEXT_PRINT_TITLE": "พิมพ์ %1", "TEXT_PRINT_TOOLTIP": "พิมพ์ข้อความ ตัวเลข หรือค่าอื่นๆ", "TEXT_PROMPT_TYPE_TEXT": "แสดงหน้าต่างข้อความ", "TEXT_PROMPT_TYPE_NUMBER": "แสดงหน้าต่างตัวเลข", "TEXT_PROMPT_TOOLTIP_NUMBER": "แสดงหน้าต่างให้ผู้ใช้ใส่ตัวเลข", "TEXT_PROMPT_TOOLTIP_TEXT": "แสดงหน้าต่างให้ผู้ใช้ใส่ข้อความ", "TEXT_COUNT_MESSAGE0": "นับ %1 ภายใน %2", "TEXT_COUNT_TOOLTIP": "นับจำนวนข้อความแรกที่พบอยู่ในข้อความที่สอง", "TEXT_REPLACE_MESSAGE0": "แทนที่ %1 ด้วย %2 ใน %3", "TEXT_REPLACE_TOOLTIP": "แทนที่ข้อความแรกทั้งหมดที่พบในข้อความที่สอง", "TEXT_REVERSE_MESSAGE0": "เรียง %1 แบบย้อนกลับ", "TEXT_REVERSE_TOOLTIP": "เรียงตัวอักษรทั้งหมดของข้อความแบบย้อนกลับ", "LISTS_CREATE_EMPTY_TITLE": "สร้างรายการเปล่า", "LISTS_CREATE_EMPTY_TOOLTIP": "สร้างรายการเปล่า (ความยาวเป็น 0) ยังไม่มีข้อมูลใดๆ อยู่", "LISTS_CREATE_WITH_TOOLTIP": "สร้างรายการพร้อมด้วยไอเท็ม", "LISTS_CREATE_WITH_INPUT_WITH": "สร้างข้อความด้วย", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "รายการ", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "เพิ่ม ลบ หรือจัดเรียงบล็อกรายการนี้ใหม่", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "เพิ่มไอเท็มเข้าไปในรายการ", "LISTS_REPEAT_TOOLTIP": "สร้างรายการที่ประกอบด้วยค่าตามที่ระบุในจำนวนตามที่ต้องการ", "LISTS_REPEAT_TITLE": "สร้างรายการที่มีไอเท็ม %1 จำนวน %2", "LISTS_LENGTH_TITLE": "ความยาวของ %1", "LISTS_LENGTH_TOOLTIP": "ส่งคืนค่าความยาวของรายการ", "LISTS_ISEMPTY_TITLE": "%1 ว่างเปล่า", "LISTS_ISEMPTY_TOOLTIP": "คืนค่าเป็นจริง ถ้ารายการยังว่างเปล่า", "LISTS_INLIST": "ในรายการ", "LISTS_INDEX_OF_FIRST": "หาอันแรกที่พบ", "LISTS_INDEX_OF_LAST": "หาอันสุดท้ายที่พบ", "LISTS_INDEX_OF_TOOLTIP": "คืนค่าตำแหน่งของไอเท็มอันแรก/สุดท้ายที่พบในรายการ คืนค่า %1 ถ้าหาไม่พบ", "LISTS_GET_INDEX_GET": "เรียกดู", "LISTS_GET_INDEX_GET_REMOVE": "เรียกดูและเอาออก", "LISTS_GET_INDEX_REMOVE": "เอาออก", "LISTS_GET_INDEX_FROM_END": "# จากท้าย", "LISTS_GET_INDEX_FIRST": "แรกสุด", "LISTS_GET_INDEX_LAST": "ท้ายสุด", "LISTS_GET_INDEX_RANDOM": "สุ่ม", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 คือไอเท็มอันแรกสุด", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 คือไอเท็มอันท้ายสุด", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "คืนค่าเป็นไอเท็มตามตำแหน่งที่ระบุ", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "คืนค่าไอเท็มอันแรกในรายการ", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "คืนค่าไอเท็มอันสุดท้ายในรายการ", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "คืนค่าไอเท็มแบบสุ่มจากรายการ", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "เอาออก และคืนค่าไอเท็มในตำแหน่งที่ระบุจากรายการ", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "เอาออก และคืนค่าไอเท็มอันแรกในรายการ", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "เอาออก และคืนค่าไอเท็มอันสุดท้ายในรายการ", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "เอาออก และคืนค่าไอเท็มแบบสุ่มจากรายการ", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "คืนค่าเป็นไอเท็มตามตำแหน่งที่ระบุ", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "เอาไอเท็มแรกสุดในรายการออก", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "เอาไอเท็มอันท้ายสุดในรายการออก", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "เอาไอเท็มแบบสุ่มจากรายการออก", "LISTS_SET_INDEX_SET": "กำหนด", "LISTS_SET_INDEX_INSERT": "แทรกที่", "LISTS_SET_INDEX_INPUT_TO": "เป็น", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "กำหนดไอเท็มในตำแหน่งที่ระบุในรายการ", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "กำหนดไอเท็มอันแรกในรายการ", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "กำหนดไอเท็มอันสุดท้ายในรายการ", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "กำหนดไอเท็มแบบสุ่มในรายการ", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "แทรกไอเท็มเข้าไปในตำแหน่งที่กำหนด", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "แทรกไอเท็มเข้าไปเป็นอันแรกสุดของรายการ", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "เพิ่มไอเท็มเข้าไปท้ายสุดของรายการ", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "เพิ่มไอเท็มเข้าไปในรายการแบบสุ่ม", "LISTS_GET_SUBLIST_START_FROM_START": "ดึงรายการย่อยจาก #", "LISTS_GET_SUBLIST_START_FROM_END": "ดึงรายการย่อยจาก # จากท้ายสุด", "LISTS_GET_SUBLIST_START_FIRST": "ดึงรายการย่อยทั้งแต่แรกสุด", "LISTS_GET_SUBLIST_END_FROM_START": "จนถึง #", "LISTS_GET_SUBLIST_END_FROM_END": "ถึง #  จากท้ายสุด", "LISTS_GET_SUBLIST_END_LAST": "ถึง ท้ายสุด", "LISTS_GET_SUBLIST_TOOLTIP": "สร้างสำเนารายการในช่วงที่กำหนด", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "เรียงลำดับ %1 %2 %3", "LISTS_SORT_TOOLTIP": "เรียงลำดับสำเนาของรายชื่อ", "LISTS_SORT_ORDER_ASCENDING": "น้อยไปหามาก", "LISTS_SORT_ORDER_DESCENDING": "มากไปหาน้อย", "LISTS_SORT_TYPE_NUMERIC": "ตัวเลข", "LISTS_SORT_TYPE_TEXT": "ตัวอักษร", "LISTS_SORT_TYPE_IGNORECASE": "ตัวอักษร", "LISTS_SPLIT_LIST_FROM_TEXT": "สร้างรายการจากข้อความ", "LISTS_SPLIT_TEXT_FROM_LIST": "สร้างข้อความจากรายการ", "LISTS_SPLIT_WITH_DELIMITER": "ด้วยตัวคั่น", "LISTS_SPLIT_TOOLTIP_SPLIT": "แบ่งข้อความเป็นรายการข้อความ แยกแต่ละรายการด้วยตัวคั่น", "LISTS_SPLIT_TOOLTIP_JOIN": "รวมรายการข้อความเป็นข้อความเดียว แบ่งด้วยตัวคั่น", "LISTS_REVERSE_MESSAGE0": "เรียง %1 แบบย้อนกลับ", "LISTS_REVERSE_TOOLTIP": "เรียงลำดับสำเนาของรายชื่อแบบย้อนกลับ", "VARIABLES_GET_TOOLTIP": "คืนค่าของตัวแปรนี้", "VARIABLES_GET_CREATE_SET": "สร้าง \"กำหนด %1\"", "VARIABLES_SET": "กำหนด %1 จนถึง %2", "VARIABLES_SET_TOOLTIP": "กำหนดให้ตัวแปรนี้เท่ากับการป้อนข้อมูล", "VARIABLES_SET_CREATE_GET": "สร้าง \"get %1\"", "PROCEDURES_DEFNORETURN_TITLE": "ถึง", "PROCEDURES_DEFNORETURN_PROCEDURE": "ทำอะไรบางอย่าง", "PROCEDURES_BEFORE_PARAMS": "ด้วย:", "PROCEDURES_CALL_BEFORE_PARAMS": "ด้วย:", "PROCEDURES_DEFNORETURN_TOOLTIP": "สร้างฟังก์ชันที่ไม่มีผลลัพธ์", "PROCEDURES_DEFNORETURN_COMMENT": "อธิบายฟังก์ชันนี้", "PROCEDURES_DEFRETURN_RETURN": "คืนค่า", "PROCEDURES_DEFRETURN_TOOLTIP": "สร้างฟังก์ชันที่มีผลลัพธ์", "PROCEDURES_ALLOW_STATEMENTS": "ข้อความที่ใช้ได้", "PROCEDURES_DEF_DUPLICATE_WARNING": "ระวัง: ฟังก์ชันนี้มีพารามิเตอร์ที่มีชื่อซ้ำกัน", "PROCEDURES_CALLNORETURN_HELPURL": "https://th.wikipedia.org/wiki/ซับรู้ทีน", "PROCEDURES_CALLNORETURN_TOOLTIP": "เรียกใช้ฟังก์ชันที่สร้างโดยผู้ใช้ \"%1\"", "PROCEDURES_CALLRETURN_HELPURL": "https://th.wikipedia.org/wiki/ซับรูทีน", "PROCEDURES_CALLRETURN_TOOLTIP": "เรียกใช้ฟังก์ชันที่สร้างโดยผู้ใช้ \"%1\" และใช้ผลลัพธ์ของมัน", "PROCEDURES_MUTATORCONTAINER_TITLE": "นำเข้า", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "เพิ่ม, ลบ, หรือจัดเรียง ข้อมูลที่ป้อนเข้าฟังก์ชันนี้", "PROCEDURES_MUTATORARG_TITLE": "ชื่อนำเข้า:", "PROCEDURES_MUTATORARG_TOOLTIP": "เพิ่มค่าป้อนเข้าฟังก์ชัน", "PROCEDURES_HIGHLIGHT_DEF": "เน้นฟังก์ชันนิยาม", "PROCEDURES_CREATE_DO": "สร้าง \"%1\"", "PROCEDURES_IFRETURN_TOOLTIP": "ถ้ามีค่าเป็นจริง ให้คืนค่าที่สอง", "PROCEDURES_IFRETURN_WARNING": "ระวัง: บล็อกนี้ใช้เฉพาะในการสร้างฟังก์ชันเท่านั้น", "WORKSPACE_COMMENT_DEFAULT_TEXT": "เขียนอะไรสักอย่าง", "WORKSPACE_ARIA_LABEL": "พื้นที่ทำงาน Blockly", "COLLAPSED_WARNINGS_WARNING": "บล็อกที่ถูกพับมีคำเตือนอยู่ข้างใน.", "DIALOG_OK": "ตกลง", "DIALOG_CANCEL": "ยกเลิก"}