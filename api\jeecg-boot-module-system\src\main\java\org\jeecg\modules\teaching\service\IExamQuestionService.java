package org.jeecg.modules.teaching.service;

import org.jeecg.modules.teaching.entity.ExamQuestion;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.teaching.util.QuestionImportUtil.SimilarQuestion;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Description: 考试题目
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
public interface IExamQuestionService extends IService<ExamQuestion> {

    /**
     * 从文本文件导入题目
     * @param file 文本文件
     * @param subject 科目
     * @param level 级别
     * @param difficulty 难度
     * @param author 作者
     * @return 导入结果，包含成功导入的题目列表、重复题目列表
     */
    Map<String, Object> importQuestionsFromFile(MultipartFile file, String subject, String level, Integer difficulty, String author);
    
    /**
     * 从文本文件导入题目（使用元数据Map）
     * @param file 文本文件
     * @param metadata 元数据，可包含author等信息
     * @return 导入结果，包含成功导入的题目列表、重复题目列表
     */
    Map<String, Object> importQuestionsFromFile(MultipartFile file, Map<String, Object> metadata);

    /**
     * 预览题目导入（只检测不实际导入）
     * @param file 文本文件
     * @param metadata 元数据，可包含author等信息
     * @return 预览结果，包含检测统计信息
     */
    Map<String, Object> previewQuestionsFromFile(MultipartFile file, Map<String, Object> metadata);
    
    /**
     * 检查题目是否存在相似的
     * @param question 待检查的题目
     * @return 相似题目列表
     */
    List<SimilarQuestion> checkSimilarQuestions(ExamQuestion question);

    /**
     * 在添加题目前进行重复检测
     * @param question 待添加的题目
     * @return 检测结果，包含精确重复和相似题目信息
     */
    Map<String, Object> checkDuplicateBeforeAdd(ExamQuestion question);
    
    /**
     * 根据条件查询题目
     * @param title 题目标题
     * @param subject 科目
     * @param level 级别
     * @param questionType 题目类型
     * @param difficulty 难度
     * @return 题目列表
     */
    List<ExamQuestion> getQuestionsByCondition(String title, String subject, String level, Integer questionType, Integer difficulty);

    /**
     * 自动模板化 - 将纯文本文件格式化为导入模板格式
     * @param file 纯文本文件
     * @param subject 科目
     * @param level 级别
     * @param difficulty 难度
     * @return 格式化后的模板内容
     */
    String autoFormatTemplate(MultipartFile file, String subject, String level, Integer difficulty) throws Exception;
}