package org.jeecg.modules.teaching.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;

import java.util.List;
 
/**
 * 客观题答题详情 Mapper
 */
public interface TeachingObjectiveQuizDetailMapper extends BaseMapper<TeachingObjectiveQuizDetail> {
    
    /**
     * 根据记录ID查询答题详情，按题目创建时间排序
     * @param recordId 答题记录ID
     * @return 答题详情列表
     */
    List<TeachingObjectiveQuizDetail> listByRecordIdOrderByQuestionCreateTime(@Param("recordId") String recordId);
} 