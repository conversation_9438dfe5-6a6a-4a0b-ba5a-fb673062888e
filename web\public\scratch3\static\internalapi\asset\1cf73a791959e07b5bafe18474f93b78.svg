<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="94.86618" height="250.21781"><g transform="translate(-191.99191,-53.12918)"><g data-paper-data="{&quot;isPaintingLayer&quot;:true}" fill-rule="nonzero" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" style="mix-blend-mode: normal"><g data-paper-data="{&quot;origPos&quot;:null}" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"><path d="M217.55198,135.49153c-1.41448,4.27454 -4.89157,17.34694 -5.90575,20.88556c-1.09097,2.45389 -3.88692,4.02826 -7.19232,3.26031c-3.34678,-0.77759 -2.90846,-4.41447 -2.96401,-6.52766c1.13938,-4.52879 4.16903,-31.79905 12.99256,-40.61049c2.96483,-2.96041 4.08283,-2.52994 6.89014,-3.03767" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#edc393"/><path d="M219.69784,143.82343c-2.4988,-0.89696 -15.49465,-4.45166 -17.85257,-5.58317c1.75973,-9.14078 4.10255,-18.47585 11.27397,-25.41931c3.5825,-3.46826 6.33475,-3.15592 9.59435,-3.63744" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#916f00"/></g><g data-paper-data="{&quot;origPos&quot;:null}" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"><path d="M257.47757,109.38006c2.80731,0.50774 3.92531,0.07726 6.89014,3.03767c8.82354,8.81143 11.85318,36.0817 12.99256,40.61049c-0.05555,2.11321 0.38277,5.75008 -2.96401,6.52766c-3.3054,0.76795 -6.10134,-0.80642 -7.19232,-3.26031c-1.01418,-3.53863 -4.49127,-16.61103 -5.90575,-20.88556" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#edc393"/><path d="M256.563,109.39466c3.2596,0.48152 6.01185,0.16919 9.59436,3.63744c7.17142,6.94347 9.51423,16.27853 11.27396,25.41931c-2.35793,1.1315 -14.72036,4.40469 -17.21916,5.30165" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#916f00"/></g><g stroke="none" stroke-width="0" stroke-linejoin="miter"><g data-paper-data="{&quot;origPos&quot;:null}" stroke-linecap="butt"><path d="M253.15586,109.23003c-4.72232,8.22842 -20.89894,8.58573 -27.52893,0.4508c2.74564,-0.96689 6.58908,-2.37226 6.58908,-2.37226l0.63746,-8.21227l12.99013,-0.18749l0.76898,7.87867c0,0 5.86611,2.11469 6.54328,2.44254z" data-paper-data="{&quot;origPos&quot;:null}" fill="#edc393"/><path d="M232.94526,99.12227l12.99013,-0.1875l0.6947,7.79293c-3.52326,0.04049 -11.58124,-0.28246 -14.1566,-2.28764z" data-paper-data="{&quot;origPos&quot;:null}" fill="#b5875c"/></g><path d="M254.96318,77.45114c0.07142,-0.00672 1.82056,-1.46199 2.66431,0.69629c0.65581,1.67754 -0.98881,6.66867 -2.89317,8.01203c-1.55192,10.49285 -2.79768,17.62537 -15.43604,17.48925c-12.06697,0.22376 -13.86026,-7.75039 -15.85431,-16.86452c-2.0575,-2.87337 -2.64827,-4.35723 -3.08961,-6.66148c-0.39734,-1.86684 0.96349,-3.78535 2.82004,-2.71494c0.22313,-13.25035 6.60816,-18.49142 16.49615,-18.87635c9.88799,-0.38494 15.84229,8.92484 15.29264,18.91972z" data-paper-data="{&quot;origPos&quot;:null}" fill="#edc393" stroke-linecap="round"/></g><path d="M212.75957,196.14397c1.15815,-17.16954 4.54257,-27.07992 4.54257,-27.07992l23.53859,0.24689c0,0 1.29768,11.10731 -1.12927,26.72553c-1.11514,7.1763 -4.28717,34.99522 -4.28717,34.99522c-0.68227,6.6317 -4.74846,11.77144 -12.14931,11.38869c-9.64059,-0.49859 -10.54922,-8.27747 -10.72404,-12.27588c0.10032,-7.71996 -0.70238,-20.49483 0.20864,-34.00053z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#755135" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M265.36656,195.4862c1.13167,13.489 1.1008,26.26366 1.3273,33.98095c-0.10943,4.00073 -1.4583,11.52356 -11.08946,12.17968c-7.39361,0.50368 -10.9849,-4.8596 -11.7617,-10.63483c0,0 -4.1939,-28.03562 -5.4262,-35.19274c-2.68192,-15.57646 -2.1401,-26.51216 -2.1401,-26.51216l25.49841,-0.84569c0,0 2.15308,9.87649 3.59173,27.02479z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#755135" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M227.33065,74.79486c2.54602,-1.37873 5.97802,-0.8993 7.93541,0.55821" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="miter"/><path d="M243.47569,75.3823c1.9574,-1.45754 5.38938,-1.93695 7.9354,-0.55822" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="miter"/><g data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#b5875c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="miter"><path d="M236.60622,86.67986c0,0 1.69097,-0.41365 2.04552,0.44742" data-paper-data="{&quot;origPos&quot;:null}"/><path d="M240.69304,87.16106c0.22501,-0.84419 1.67052,-0.62469 1.67052,-0.62469" data-paper-data="{&quot;origPos&quot;:null}"/></g><g data-paper-data="{&quot;origPos&quot;:null}" stroke-linejoin="miter"><path d="M230.46117,81.61289c-2.29264,-0.29274 -3.86009,-1.79849 -3.55993,-2.81176c0.30015,-1.01325 2.35426,-1.15034 4.6469,-0.8576c2.29264,0.29274 3.95572,1.43226 3.65557,2.44552c-0.30015,1.01326 -2.44993,1.51658 -4.74254,1.22384z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#ffffff" stroke="none" stroke-width="0" stroke-linecap="butt"/><path d="M230.11369,80.13321c-0.11195,-1.39234 0.84939,-2.0068 2.34798,-1.97581c3.02553,0.44404 3.94151,2.83623 0.93252,3.43371c-1.66057,0.32529 -3.17053,0.15844 -3.2805,-1.45789z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#000000" stroke="none" stroke-width="1" stroke-linecap="butt"/><path d="M226.91391,78.40551c2.64723,-0.8721 6.31185,-0.31454 8.14766,0.88122" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#000000" stroke-width="1.5" stroke-linecap="round"/></g><g data-paper-data="{&quot;origPos&quot;:null}" stroke-linejoin="miter"><path d="M252.14187,78.72511c0.40877,0.95903 -1.27495,2.32658 -3.53529,2.74326c-2.26034,0.41668 -4.41272,0.00368 -4.80747,-0.92248c-0.39476,-0.92615 1.11759,-2.01475 3.37792,-2.43143c2.26034,-0.41668 4.57009,-0.3155 4.96484,0.61065z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#ffffff" stroke="none" stroke-width="0" stroke-linecap="butt"/><path d="M249.31977,77.74334c3.089,-0.09351 3.85664,1.62389 1.53485,2.98445c-1.28426,0.74717 -2.74296,0.7861 -3.42804,-0.56626c-1.05614,-2.08485 0.41654,-2.33369 1.89319,-2.41819z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#000000" stroke="none" stroke-width="1" stroke-linecap="butt"/><path d="M243.69786,79.46842c2.06629,-1.52197 5.86565,-1.82411 8.14825,-1.21638" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#000000" stroke-width="1.5" stroke-linecap="round"/></g><path d="M243.93597,53.12918l-1.69599,3.36921l9.19941,-2.38691l-1.1339,4.16776l5.90617,-1.70845l-1.17622,4.40769l4.46282,-1.66737c0,0 -0.47345,12.38154 -4.75137,18.24238c-0.56351,-5.11355 -0.53793,-8.71944 -1.87243,-9.90355c-6.48572,-4.23883 -19.123,-4.29185 -26.72484,-0.41859c-1.56333,1.28496 -2.26779,11.3298 -2.80789,10.6703c0,0 -2.5903,-13.58498 1.83243,-17.70777c5.85384,-5.45683 18.76179,-7.06468 18.76179,-7.06468z" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#000000" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="round"/><path d="M256.38236,85.06336c-0.0519,0.48597 -0.46431,0.87316 -0.92116,0.86484c-0.45685,-0.00832 -0.78513,-0.40902 -0.73323,-0.89498c0.0519,-0.48597 0.46431,-0.87316 0.92116,-0.86483c0.45685,0.00832 0.78513,0.40903 0.73323,0.89498z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#a07b00" stroke="#000000" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M223.7093,85.69366c-0.0519,0.48597 -0.46431,0.87316 -0.92116,0.86484c-0.45685,-0.00832 -0.78513,-0.40902 -0.73323,-0.89498c0.0519,-0.48597 0.46431,-0.87316 0.92116,-0.86483c0.45685,0.00832 0.78512,0.40903 0.73323,0.89498z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#a07b00" stroke="#000000" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M240.58021,181.46573c-12.97253,0.138 -25.9492,-4.3794 -25.9492,-4.3794l1.92414,-66.95971c0,0 8.86279,-2.11327 12.56196,-2.36908c2.37596,8.85069 18.17406,9.31741 21.09539,-0.08769c1.29066,0.46771 11.25302,2.09335 11.25302,2.09335l2.59295,66.15651c0,0 -12.64115,5.34221 -23.47828,5.54601z" data-paper-data="{&quot;origPos&quot;:null}" fill="#a07b00" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><g data-paper-data="{&quot;origPos&quot;:null}" stroke-linejoin="miter"><path d="M237.76721,91.34235c0.68643,0.08786 1.32919,0.39804 1.83801,0.88695c0.51042,-0.49009 1.1555,-0.80034 1.84405,-0.88695c0.95703,0.15128 1.88721,0.45019 2.76005,0.88695c0,0 -0.15807,2.75457 -1.99609,3.64151c-1.46978,0.65891 -3.05526,0.74634 -4.52505,0.08742c-1.15123,-0.58884 -2.03359,-1.69069 -2.68703,-2.84199c0,0 1.84404,-1.77389 2.76606,-1.77389z" data-paper-data="{&quot;origPos&quot;:null}" fill="#d0ab81" stroke="none" stroke-width="1" stroke-linecap="butt"/><path d="M244.5183,92.8469c-2.53598,1.02703 -6.75288,1.35465 -9.54966,0.10581" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="none" stroke="#866143" stroke-width="1.5" stroke-linecap="round"/></g><g stroke-linejoin="miter"><g data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" stroke-linecap="round"><path d="M226.27949,285.35011c0,0 3.46356,5.16292 6.32938,7.30429c2.86583,2.14136 11.66702,1.13861 7.7829,6.34819c-2.65484,3.56081 -7.30772,2.85117 -11.83426,2.22314c-3.82437,-0.53061 -4.17651,-4.48898 -8.0374,-4.51793c-4.09555,-0.03069 -7.97456,0.55571 -10.3086,-2.10048c-0.82968,-0.94419 2.93083,-10.04716 2.93083,-10.04716c0,0 4.12871,1.98346 6.28132,2.03671c2.66135,0.06585 6.85583,-1.24676 6.85583,-1.24676z" data-paper-data="{&quot;origPos&quot;:null}" fill="#ffffff" stroke="none" stroke-width="0"/><path d="M211.59424,297.87738c-1.95484,-1.03241 -1.85861,-1.74106 -1.53458,-3.43267c2.95386,1.84535 2.40005,2.32886 10.27695,1.80599c2.81927,0.28148 4.41042,3.33956 7.93363,4.46063c9.36529,1.90708 11.79865,-1.67373 13.13197,-3.09463c-0.03114,1.09679 -0.30195,2.23809 -0.79085,2.89384c-2.65484,3.5608 -7.17566,3.00013 -11.70221,2.37208c-6.44326,-1.53416 -5.03643,-4.25865 -8.89732,-4.2876c-4.09555,-0.03069 -5.73945,0.69673 -8.41759,-0.71768z" data-paper-data="{&quot;origPos&quot;:null}" fill="#a5a5a5" stroke="none" stroke-width="0"/><path d="M229.82048,294.37985l2.29813,-2.03743" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M228.00941,292.33708l2.1777,-2.04467" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M226.08517,290.16662l2.29089,-1.917" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/></g><path d="M212.50625,232.34816c0.92775,-8.9465 5.51805,-14.55666 11.9486,-13.95651c6.46263,-0.45539 10.47167,4.19137 10.71927,14.34822l-2.09495,55.65334c0,0 -6.2776,2.32315 -10.52836,2.01457c-3.94052,-0.28604 -12.38088,-2.92461 -12.38088,-2.92461c0,0 2.20725,-54.81103 2.33633,-55.13501z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#755135" stroke="none" stroke-width="0" stroke-linecap="butt"/></g><g stroke-linejoin="miter"><g data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" stroke-linecap="round"><path d="M274.03613,292.66972c2.93472,2.04593 11.69815,0.75419 7.98751,6.08873c-2.53627,3.64622 -7.20995,3.09004 -11.75471,2.61124c-3.83976,-0.40452 -4.32193,-4.34916 -8.18167,-4.25108c-4.09435,0.10405 -7.95196,0.81775 -10.37211,-1.76023c-0.86029,-0.91639 2.59873,-10.13814 2.59873,-10.13814c0,0 3.54856,1.6489 7.24985,1.74943c1.23376,0.03351 5.90615,-1.39209 5.90615,-1.39209c0,0 3.63153,5.0462 6.56625,7.09214z" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#ffffff" stroke="none" stroke-width="0"/><path d="M251.558,295.20082c3.01298,1.74719 2.47537,2.24866 10.3308,1.46693c2.82699,0.18858 4.51789,3.19267 8.07607,4.19724c9.42297,1.59796 11.73723,-2.06096 13.02307,-3.52494c0.00496,1.09723 -0.22816,2.24682 -0.69524,2.91831c-2.53627,3.64622 -7.07308,3.23455 -11.61783,2.75577c-6.49023,-1.32137 -5.17378,-4.09067 -9.03355,-3.9926c-4.09435,0.10405 -5.71345,0.88518 -8.43665,-0.44038c-1.98775,-0.96756 -1.91488,-1.67897 -1.64667,-3.38033z" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#a5a5a5" stroke="none" stroke-width="0"/><path d="M273.53583,292.37413l-2.22985,2.11192" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M269.42872,292.50397l2.10926,-2.11519" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M267.43415,290.39799l2.22658,-1.99133" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/></g><path d="M259.0584,291.1296c-4.23078,0.51453 -11.73963,-1.4857 -11.73963,-1.4857l-3.105,-55.52025c-0.24567,-10.15688 4.37697,-14.73551 10.85409,-14.59434c6.39382,-0.91158 10.12566,4.21643 11.48656,13.10735c0.14467,0.31734 4.44837,54.68041 4.44837,54.68041c0,0 -8.02241,3.33557 -11.9444,3.81254z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#755135" stroke="none" stroke-width="0" stroke-linecap="butt"/></g><path d="M285.62869,188.59771c0,0 1.18086,3.03098 1.2294,4.2403c-0.0509,0.38442 0.16175,3.44348 -1.06993,3.23391c-1.09992,-0.07938 -0.67056,-1.33526 -0.9461,-2.44127c-0.26951,-1.0818 -0.99619,-3.26378 -0.99619,-3.26378c0,0 0.45403,2.61642 0.48789,4.16534c-0.21968,1.52627 0.18475,4.61586 -1.35668,4.40934c-1.2844,-0.39958 -0.71039,-2.92282 -0.83524,-3.50068c-0.28483,-1.31816 -0.45128,-3.44619 -0.98899,-3.63092c0.17718,1.32749 0.32901,2.97181 0.32658,4.2985c-0.00206,1.11289 -0.2049,3.45376 -0.85156,4.40735c-0.51501,0.75943 -1.5281,0.83766 -1.82996,-0.01372c-0.24031,-0.49902 0.24028,-3.03015 0.20558,-3.56709c-0.10411,-1.61087 -0.0789,-3.34624 -0.65141,-4.22511c0.05625,1.43638 0.03419,3.36074 -0.24737,5.4318c-0.2623,1.92935 -1.1134,4.68894 -1.98157,4.40244c-2.33882,-0.38734 -0.3186,-3.34014 -0.45656,-5.71683c-0.15468,-2.66453 -0.64316,-3.34214 -1.48629,-5.87701c-0.57432,1.30739 0.08341,3.01486 -0.97295,4.22419c-1.36578,1.2417 -1.95964,0.00504 -1.94581,-0.51491c0.0556,-1.83043 -0.86371,-5.11801 -0.21784,-6.8691c0.55343,-1.81634 1.04905,-4.17177 1.79414,-6.12302c-0.15859,-2.0481 -5.04248,-18.58072 -5.74876,-25.52928c-0.00488,-0.0485 0.09639,-5.01648 3.86925,-5.50098c3.76644,-0.72616 6.05959,2.43221 6.38492,3.67999c2.70913,5.75576 3.66121,26.18504 3.66121,26.18504z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#edc393" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M197.84555,180.58372c0,0 0.95208,-20.4293 3.66121,-26.18504c0.32533,-1.24778 2.61848,-4.40615 6.38492,-3.67999c3.77286,0.4845 3.87413,5.45248 3.86925,5.50098c-0.70628,6.94857 -5.59017,23.48118 -5.74876,25.52928c0.74508,1.95126 1.24072,4.30668 1.79414,6.12302c0.64587,1.7511 -0.27344,5.03867 -0.21784,6.8691c0.01384,0.51995 -0.58003,1.7566 -1.94581,0.51491c-1.05637,-1.20933 -0.39863,-2.91679 -0.97295,-4.22419c-0.84313,2.53487 -1.33161,3.21248 -1.48629,5.87701c-0.13796,2.37668 1.88225,5.32949 -0.45656,5.71683c-0.86817,0.2865 -1.71927,-2.4731 -1.98157,-4.40244c-0.28156,-2.07106 -0.30361,-3.99543 -0.24737,-5.4318c-0.57251,0.87886 -0.5473,2.61423 -0.65141,4.22511c-0.0347,0.53694 0.44589,3.06807 0.20558,3.56709c-0.30186,0.85137 -1.31495,0.77315 -1.82996,0.01372c-0.64666,-0.9536 -0.84951,-3.29445 -0.85156,-4.40735c-0.00244,-1.32668 0.14939,-2.97101 0.32658,-4.2985c-0.53772,0.18473 -0.70416,2.31276 -0.98899,3.63092c-0.12485,0.57786 0.44916,3.10112 -0.83524,3.50068c-1.54144,0.20652 -1.13701,-2.88307 -1.35668,-4.40934c0.03387,-1.54892 0.48789,-4.16534 0.48789,-4.16534c0,0 -0.72668,2.18198 -0.99619,3.26378c-0.27554,1.10603 0.15382,2.3619 -0.9461,2.44127c-1.23168,0.20957 -1.01904,-2.84949 -1.06993,-3.23391c0.04855,-1.20932 1.2294,-4.2403 1.2294,-4.2403z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#edc393" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/></g></g></svg>