package org.jeecg.modules.teaching.config;

import org.jeecg.modules.teaching.interceptor.CourseScheduleAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 课程排期权限控制配置类
 */
@Configuration
public class CourseScheduleWebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private CourseScheduleAuthInterceptor courseScheduleAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册课程排期权限拦截器，并指定拦截路径
        registry.addInterceptor(courseScheduleAuthInterceptor)
                .addPathPatterns("/teaching/coursesScheduling/**");
    }
} 