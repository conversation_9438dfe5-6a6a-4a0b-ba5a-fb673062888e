package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.teaching.entity.TeachingCourseNotification;
import org.jeecg.modules.teaching.entity.TeachingNotificationRead;
import org.jeecg.modules.teaching.mapper.TeachingNotificationReadMapper;
import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import org.jeecg.modules.teaching.service.ITeachingNotificationReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 课程通知已读状态
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
@Service
public class TeachingNotificationReadServiceImpl extends ServiceImpl<TeachingNotificationReadMapper, TeachingNotificationRead> implements ITeachingNotificationReadService {
    
    @Autowired
    private ITeachingCourseNotificationService teachingCourseNotificationService;
    
    @Override
    public int getUnreadCount(String userId) {
        // 1. 获取用户的所有通知
        LambdaQueryWrapper<TeachingCourseNotification> notificationQuery = new LambdaQueryWrapper<>();
        notificationQuery.like(TeachingCourseNotification::getReceivers, userId);
        notificationQuery.eq(TeachingCourseNotification::getIsSent, 1); // 只查询已发送的
        List<TeachingCourseNotification> notifications = teachingCourseNotificationService.list(notificationQuery);
        
        if (notifications == null || notifications.isEmpty()) {
            return 0;
        }
        
        // 2. 获取通知ID列表
        List<String> notificationIds = notifications.stream()
            .map(TeachingCourseNotification::getId)
            .collect(Collectors.toList());
        
        // 3. 查询已读记录
        LambdaQueryWrapper<TeachingNotificationRead> readQuery = new LambdaQueryWrapper<>();
        readQuery.eq(TeachingNotificationRead::getUserId, userId);
        readQuery.eq(TeachingNotificationRead::getReadFlag, "1"); // 已读
        readQuery.in(TeachingNotificationRead::getNotificationId, notificationIds);
        List<TeachingNotificationRead> readRecords = this.list(readQuery);
        
        // 4. 计算未读数量
        if (readRecords == null || readRecords.isEmpty()) {
            return notifications.size();
        }
        
        List<String> readNotificationIds = readRecords.stream()
            .map(TeachingNotificationRead::getNotificationId)
            .collect(Collectors.toList());
        
        return (int) notifications.stream()
            .filter(notification -> !readNotificationIds.contains(notification.getId()))
            .count();
    }
    
    @Override
    public List<String> getReadNotificationIds(String userId, List<String> notificationIds) {
        if (notificationIds == null || notificationIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询已读记录
        LambdaQueryWrapper<TeachingNotificationRead> readQuery = new LambdaQueryWrapper<>();
        readQuery.eq(TeachingNotificationRead::getUserId, userId);
        readQuery.eq(TeachingNotificationRead::getReadFlag, "1"); // 已读
        readQuery.in(TeachingNotificationRead::getNotificationId, notificationIds);
        List<TeachingNotificationRead> readRecords = this.list(readQuery);
        
        if (readRecords == null || readRecords.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 返回已读通知ID列表
        return readRecords.stream()
            .map(TeachingNotificationRead::getNotificationId)
            .collect(Collectors.toList());
    }
    
    /**
     * 标记通知为已读或创建未读记录
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @param isRead 是否标记为已读
     * @return 操作结果
     */
    @Override
    @Transactional
    public TeachingNotificationRead markReadStatus(String notificationId, String userId, boolean isRead) {
        // 查询是否存在读取记录
        LambdaQueryWrapper<TeachingNotificationRead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeachingNotificationRead::getNotificationId, notificationId);
        queryWrapper.eq(TeachingNotificationRead::getUserId, userId);
        TeachingNotificationRead record = this.getOne(queryWrapper);
        
        Date now = new Date();
        
        if (record == null) {
            // 不存在则创建一条记录
            record = new TeachingNotificationRead();
            record.setId(com.baomidou.mybatisplus.core.toolkit.IdWorker.getIdStr());
            record.setNotificationId(notificationId);
            record.setUserId(userId);
            record.setReadFlag(isRead ? "1" : "0"); // 根据参数设置已读/未读状态
            if (isRead) {
                record.setReadTime(now);
            }
            record.setCreateBy(userId);
            record.setCreateTime(now);
            this.save(record);
        } else if (isRead && "0".equals(record.getReadFlag())) {
            // 已存在未读记录，需要更新为已读
            record.setReadFlag("1");
            record.setReadTime(now);
            record.setUpdateBy(userId);
            record.setUpdateTime(now);
            this.updateById(record);
        }
        
        return record;
    }
} 