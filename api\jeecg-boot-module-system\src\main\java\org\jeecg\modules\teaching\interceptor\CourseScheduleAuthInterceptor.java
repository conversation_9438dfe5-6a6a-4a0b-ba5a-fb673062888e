package org.jeecg.modules.teaching.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.system.entity.SysUser;


import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.teaching.constant.RoleConstant;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import java.util.Set;

/**
 * 课程排期权限拦截器
 * 
 * 实现三级权限控制:
 * 1. 管理员: 可以管理所有课程排期
 * 2. 教师: 只能管理自己的课程排期
 * 3. 学生: 只能查看与自己相关的课程排期
 */
@Slf4j
@Component
public class CourseScheduleAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private ISysUserService sysUserService;
    


    @Autowired
    private ITeachingCourseScheduleService courseScheduleService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("======> 进入CourseScheduleAuthInterceptor");

        // 获取请求路径和方法
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // 登录用户名
        String token = request.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        
        if (username == null) {
            handleUnauthorized(response, "未登录或token无效");
            return false;
        }
        
        // 获取用户角色信息
        SysUser user = sysUserService.getUserByName(username);
        if (user == null) {
            handleUnauthorized(response, "用户不存在");
            return false;
        }
        
        // 获取用户角色集合
        Set<String> roleSet = sysUserService.getUserRolesSet(username);
        
        // 检查是否有管理员角色，有则直接通过
        if (roleSet.contains(RoleConstant.ADMIN)) {
            return true;
        }
        
        // 请求ID参数(用于编辑、删除等操作)
        String scheduleId = request.getParameter("id");
        
        // 教师角色权限判断 - 只能操作自己的课程
        if (roleSet.contains(RoleConstant.TEACHER)) {
            // 查询类操作可能没有ID参数，如list接口
            if ("GET".equals(method)) {
                // 如果是查询列表，在Service层会进行数据过滤，这里放行
                if (requestURI.endsWith("/list")) {
                    // 将教师ID添加到request属性中，后续查询时使用
                    request.setAttribute("currentTeacherId", user.getId());
                    return true;
                }
                
                // 查看具体排课信息时需要检查是否为该教师的课程
                if (scheduleId != null && !courseScheduleService.isTeacherOfSchedule(scheduleId, user.getId())) {
                    handleUnauthorized(response, "您没有权限查看这个课程排期");
                    return false;
                }
                return true;
            } 
            // 对于添加操作，需要确保教师ID是当前用户ID
            else if ("POST".equals(method) && requestURI.endsWith("/add")) {
                // 教师不能添加课程
                handleUnauthorized(response, "教师用户无权添加课程排期");
                return false;
            } 
            // 对于修改和删除操作，需要确保是该教师创建的课程
            else if (("PUT".equals(method) && requestURI.endsWith("/edit")) 
                    || ("DELETE".equals(method) && requestURI.endsWith("/delete"))) {
                // 教师不能修改或删除课程
                handleUnauthorized(response, "教师用户无权修改或删除课程排期");
                return false;
            }
            
            // 其他操作不允许
            handleUnauthorized(response, "教师用户无权进行此操作");
            return false;
        }
        
        // 学生角色权限判断 - 只允许查看
        if (roleSet.contains(RoleConstant.STUDENT)) {
            if ("GET".equals(method)) {
                // 如果是查询列表，在Service层会进行数据过滤，这里放行
                if (requestURI.endsWith("/list")) {
                    // 将学生ID添加到request属性中，后续查询时使用
                    request.setAttribute("currentStudentId", user.getId());
                    return true;
                }
                
                // 查看具体排课信息时需要检查是否与该学生相关
                if (scheduleId != null && !courseScheduleService.isStudentRelatedToSchedule(scheduleId, user.getId())) {
                    handleUnauthorized(response, "您没有权限查看这个课程排期");
                    return false;
                }
                return true;
            } else {
                // 学生不允许添加、修改、删除课程排期
                handleUnauthorized(response, "学生用户无权进行此操作");
                return false;
            }
        }
        
        // 其他情况不允许访问
        handleUnauthorized(response, "您没有权限访问此资源");
        return false;
    }
    
    /**
     * 处理未授权的情况
     */
    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"success\":false,\"message\":\"" + message + "\",\"code\":403}");
    }
}