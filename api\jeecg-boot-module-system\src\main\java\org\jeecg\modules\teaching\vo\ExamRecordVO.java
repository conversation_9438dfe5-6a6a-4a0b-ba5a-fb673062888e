package org.jeecg.modules.teaching.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 考试记录VO
 * @Author: jeecg-boot
 * @Date:   2023-06-18
 * @Version: V1.0
 */
@Data
@ApiModel(value="ExamRecordVO对象", description="考试记录视图对象")
public class ExamRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "考生ID")
    private String userId;
    
    @ApiModelProperty(value = "试卷ID")
    private String paperId;
    
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "考试得分")
    private Integer score;

    @ApiModelProperty(value = "详细分数统计 (JSON格式，包含各题型得分详情)")
    private String scoreDetails;

    @ApiModelProperty(value = "状态 (1:已提交)")
    private Integer status;
    
    // 以下是试卷信息
    
    @ApiModelProperty(value = "试卷标题")
    private String paperTitle;
    
    @ApiModelProperty(value = "所属科目 (Scratch, Python, C++)")
    private String subject;
    
    @ApiModelProperty(value = "题目级别 (Scratch:1-4, Python/C++:1-8)")
    private String level;
    
    @ApiModelProperty(value = "难度 (1:简单, 2:中等, 3:困难)")
    private Integer difficulty;
    
    @ApiModelProperty(value = "年份")
    private Integer year;
    
    @ApiModelProperty(value = "考试时长 (分钟)")
    private Integer examDuration;
    
    @ApiModelProperty(value = "耗时 (秒)")
    public Integer getDuration() {
        if (startTime == null || endTime == null) {
            return null;
        }

        // 计算实际用时（从开始时间到结束时间），精确到秒
        long durationMillis = endTime.getTime() - startTime.getTime();
        long durationSeconds = durationMillis / 1000;

        // 返回精确的秒数，最小为1秒
        return durationSeconds > 0 ? (int) durationSeconds : 1;
    }
    
    @ApiModelProperty(value = "是否通过")
    public Boolean getPassed() {
        // 60分及以上为通过
        return score != null && score >= 60;
    }
} 