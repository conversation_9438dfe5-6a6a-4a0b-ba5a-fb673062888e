CREATE TABLE `teaching_objective_question` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `question_type` tinyint(1) NOT NULL COMMENT '题目类型：1-单选题，2-判断题',
  `question_content` text NOT NULL COMMENT '题目内容',
  `options` text COMMENT '选项内容，JSON格式存储，单选题使用',
  `correct_answer` varchar(10) NOT NULL COMMENT '正确答案，单选为选项索引(A,B,C,D)，判断为(T,F)',
  `explanation` text COMMENT '答案解析',
  `course_id` varchar(36) DEFAULT NULL COMMENT '关联课程ID',
  `unit_id` varchar(36) DEFAULT NULL COMMENT '关联单元ID',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_course_unit` (`course_id`,`unit_id`) COMMENT '课程单元索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客观题库表'; 