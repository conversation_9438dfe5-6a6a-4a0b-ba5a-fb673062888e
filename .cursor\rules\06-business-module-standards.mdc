---
alwaysApply: true
---
# 业务模块开发规范

## 模块划分原则

### 1. 教学管理模块 (teaching)
**职责**: 课程、班级、学员、教师管理
**包路径**: `org.jeecg.modules.teaching`

### 2. 考试系统模块 (examSystem)
**职责**: 题库、试卷、考试、成绩管理
**包路径**: `org.jeecg.modules.teaching.examSystem`

### 3. 练习系统模块 (practice)
**职责**: 在线刷题、错题管理、进度跟踪
**包路径**: `org.jeecg.modules.teaching.practice`

### 4. 排课系统模块 (scheduling)
**职责**: 课程排期、教室管理、时间冲突检测
**包路径**: `org.jeecg.modules.teaching.scheduling`

### 5. 通知系统模块 (notification)
**职责**: 消息通知、课程提醒、系统公告
**包路径**: `org.jeecg.modules.teaching.notification`

## CRUD开发模板

### 1. Controller层模板
```java
@RestController
@RequestMapping("/teaching/examQuestion")
@Slf4j
@Api(tags = "题库管理")
public class ExamQuestionController extends JeecgController<ExamQuestion, IExamQuestionService> {
    
    @Autowired
    private IExamQuestionService examQuestionService;
    
    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    @ApiOperation("题库-分页列表查询")
    @RequiresPermissions("teaching:exam:question:list")
    public Result<IPage<ExamQuestion>> queryPageList(
        ExamQuestion examQuestion,
        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
        HttpServletRequest req) {
        
        QueryWrapper<ExamQuestion> queryWrapper = QueryGenerator.initQueryWrapper(examQuestion, req.getParameterMap());
        Page<ExamQuestion> page = new Page<ExamQuestion>(pageNo, pageSize);
        IPage<ExamQuestion> pageList = examQuestionService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 添加
     */
    @PostMapping(value = "/add")
    @ApiOperation("题库-添加")
    @RequiresPermissions("teaching:exam:question:add")
    public Result<String> add(@RequestBody @Valid ExamQuestion examQuestion) {
        examQuestionService.save(examQuestion);
        return Result.ok("添加成功！");
    }
    
    /**
     * 编辑
     */
    @PutMapping(value = "/edit")
    @ApiOperation("题库-编辑")
    @RequiresPermissions("teaching:exam:question:edit")
    public Result<String> edit(@RequestBody @Valid ExamQuestion examQuestion) {
        examQuestionService.updateById(examQuestion);
        return Result.ok("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    @ApiOperation("题库-通过id删除")
    @RequiresPermissions("teaching:exam:question:delete")
    public Result<String> delete(@RequestParam(name="id") String id) {
        examQuestionService.removeById(id);
        return Result.ok("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @DeleteMapping(value = "/deleteBatch")
    @ApiOperation("题库-批量删除")
    @RequiresPermissions("teaching:exam:question:delete")
    public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
        this.examQuestionService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }
    
    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    @ApiOperation("题库-通过id查询")
    public Result<ExamQuestion> queryById(@RequestParam(name="id") String id) {
        ExamQuestion examQuestion = examQuestionService.getById(id);
        if(examQuestion == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(examQuestion);
    }
    
    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    @ApiOperation("题库-导出")
    @RequiresPermissions("teaching:exam:question:export")
    public ModelAndView exportXls(HttpServletRequest request, ExamQuestion examQuestion) {
        return super.exportXls(request, examQuestion, ExamQuestion.class, "题库");
    }
    
    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @ApiOperation("题库-导入")
    @RequiresPermissions("teaching:exam:question:import")
    public Result<String> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ExamQuestion.class);
    }
}
```

### 2. Service层模板
```java
public interface IExamQuestionService extends IService<ExamQuestion> {
    
    /**
     * 根据条件分页查询题目
     */
    IPage<ExamQuestion> queryPageList(ExamQuestion examQuestion, Integer pageNo, Integer pageSize);
    
    /**
     * 批量导入题目
     */
    Result<String> importQuestions(MultipartFile file, String subject, String level, Integer difficulty);
    
    /**
     * 自动格式化题目模板
     */
    String autoFormatTemplate(MultipartFile file, String subject, String level, Integer difficulty);
    
    /**
     * 检查题目相似度
     */
    List<ExamQuestion> findSimilarQuestions(String title, String content, Double threshold);
}
```

### 3. ServiceImpl模板
```java
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements IExamQuestionService {
    
    @Override
    public IPage<ExamQuestion> queryPageList(ExamQuestion examQuestion, Integer pageNo, Integer pageSize) {
        log.info("开始分页查询题目，条件：{}", examQuestion);
        
        Page<ExamQuestion> page = new Page<>(pageNo, pageSize);
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.isNotBlank(examQuestion.getTitle())) {
            queryWrapper.like("title", examQuestion.getTitle());
        }
        if (StringUtils.isNotBlank(examQuestion.getSubject())) {
            queryWrapper.eq("subject", examQuestion.getSubject());
        }
        if (StringUtils.isNotBlank(examQuestion.getQuestionType())) {
            queryWrapper.eq("question_type", examQuestion.getQuestionType());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        return this.page(page, queryWrapper);
    }
    
    @Override
    public Result<String> importQuestions(MultipartFile file, String subject, String level, Integer difficulty) {
        try {
            log.info("开始导入题目，文件名：{}，科目：{}，级别：{}，难度：{}", 
                    file.getOriginalFilename(), subject, level, difficulty);
            
            // 读取文件内容
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            
            // 解析题目
            List<ExamQuestion> questions = parseQuestions(content, subject, level, difficulty);
            
            // 批量保存
            this.saveBatch(questions);
            
            log.info("题目导入完成，共导入{}道题目", questions.size());
            return Result.ok("导入成功，共导入" + questions.size() + "道题目");
            
        } catch (Exception e) {
            log.error("题目导入失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }
    
    private List<ExamQuestion> parseQuestions(String content, String subject, String level, Integer difficulty) {
        // 题目解析逻辑
        List<ExamQuestion> questions = new ArrayList<>();
        // ... 解析实现
        return questions;
    }
}
```

## 业务逻辑规范

### 1. 参数验证
```java
// 使用Bean Validation
@Data
public class ExamQuestionDTO {
    
    @NotBlank(message = "题目标题不能为空")
    @Length(max = 255, message = "题目标题长度不能超过255个字符")
    private String title;
    
    @NotBlank(message = "题目类型不能为空")
    @Pattern(regexp = "^(SINGLE_CHOICE|MULTIPLE_CHOICE|FILL_BLANK|ESSAY|PROGRAMMING)$", 
             message = "题目类型不正确")
    private String questionType;
    
    @NotBlank(message = "科目不能为空")
    private String subject;
    
    @DecimalMin(value = "0.1", message = "分值不能小于0.1")
    @DecimalMax(value = "100.0", message = "分值不能大于100.0")
    private BigDecimal score;
}
```

### 2. 业务异常处理
```java
public class TeachingBusinessException extends RuntimeException {
    private String errorCode;
    
    public TeachingBusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}

// 使用示例
if (existingQuestion != null) {
    throw new TeachingBusinessException("QUESTION_DUPLICATE", "题目已存在");
}
```

### 3. 事务管理
```java
@Transactional(rollbackFor = Exception.class)
public void createExamPaper(ExamPaperDTO paperDTO) {
    // 1. 创建试卷
    ExamPaper paper = new ExamPaper();
    BeanUtils.copyProperties(paperDTO, paper);
    this.save(paper);
    
    // 2. 添加题目关联
    List<ExamPaperQuestion> paperQuestions = paperDTO.getQuestions().stream()
        .map(q -> {
            ExamPaperQuestion pq = new ExamPaperQuestion();
            pq.setPaperId(paper.getId());
            pq.setQuestionId(q.getQuestionId());
            pq.setScore(q.getScore());
            return pq;
        })
        .collect(Collectors.toList());
    
    examPaperQuestionService.saveBatch(paperQuestions);
    
    // 3. 更新试卷统计信息
    updatePaperStatistics(paper.getId());
}
```

## 权限控制规范

### 1. 权限定义
```java
// 权限常量
public class TeachingPermissions {
    // 题库管理权限
    public static final String QUESTION_LIST = "teaching:exam:question:list";
    public static final String QUESTION_ADD = "teaching:exam:question:add";
    public static final String QUESTION_EDIT = "teaching:exam:question:edit";
    public static final String QUESTION_DELETE = "teaching:exam:question:delete";
    public static final String QUESTION_EXPORT = "teaching:exam:question:export";
    public static final String QUESTION_IMPORT = "teaching:exam:question:import";
    
    // 试卷管理权限
    public static final String PAPER_LIST = "teaching:exam:paper:list";
    public static final String PAPER_ADD = "teaching:exam:paper:add";
    public static final String PAPER_EDIT = "teaching:exam:paper:edit";
    public static final String PAPER_DELETE = "teaching:exam:paper:delete";
}
```

### 2. 权限注解使用
```java
@RequiresPermissions("teaching:exam:question:add")
@RequiresRoles("teacher")
public Result<String> addQuestion(@RequestBody ExamQuestion question) {
    // 业务逻辑
}

// 数据权限控制
@DataScope(tableAlias = "eq", userAlias = "create_by")
public IPage<ExamQuestion> queryPageList(ExamQuestion examQuestion, Integer pageNo, Integer pageSize) {
    // 查询逻辑
}
```

## 缓存使用规范

### 1. Redis缓存
```java
@Service
public class ExamQuestionCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String QUESTION_CACHE_KEY = "teaching:exam:question:";
    private static final long CACHE_EXPIRE_TIME = 3600; // 1小时
    
    public ExamQuestion getQuestionFromCache(String questionId) {
        String key = QUESTION_CACHE_KEY + questionId;
        return (ExamQuestion) redisTemplate.opsForValue().get(key);
    }
    
    public void cacheQuestion(ExamQuestion question) {
        String key = QUESTION_CACHE_KEY + question.getId();
        redisTemplate.opsForValue().set(key, question, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
    }
    
    public void removeQuestionFromCache(String questionId) {
        String key = QUESTION_CACHE_KEY + questionId;
        redisTemplate.delete(key);
    }
}
```

### 2. 本地缓存
```java
@Component
public class SubjectCacheService {
    
    private final Cache<String, List<String>> subjectCache = Caffeine.newBuilder()
        .maximumSize(100)
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .build();
    
    public List<String> getSubjectList() {
        return subjectCache.get("subjects", key -> loadSubjectsFromDatabase());
    }
    
    private List<String> loadSubjectsFromDatabase() {
        // 从数据库加载科目列表
        return examQuestionService.getDistinctSubjects();
    }
}
```

## 文件处理规范

### 1. 文件上传
```java
@PostMapping("/upload")
public Result<String> uploadFile(@RequestParam("file") MultipartFile file) {
    try {
        // 文件类型验证
        String fileName = file.getOriginalFilename();
        if (!isValidFileType(fileName)) {
            return Result.error("不支持的文件类型");
        }
        
        // 文件大小验证
        if (file.getSize() > MAX_FILE_SIZE) {
            return Result.error("文件大小超过限制");
        }
        
        // 保存文件
        String filePath = fileService.saveFile(file);
        return Result.ok(filePath);
        
    } catch (Exception e) {
        log.error("文件上传失败", e);
        return Result.error("文件上传失败");
    }
}
```

### 2. Excel导入导出
```java
// 导出
@RequestMapping(value = "/exportXls")
public ModelAndView exportXls(HttpServletRequest request, ExamQuestion examQuestion) {
    // 查询数据
    List<ExamQuestion> questions = examQuestionService.list(
        new QueryWrapper<ExamQuestion>().eq("subject", examQuestion.getSubject()));
    
    // 导出Excel
    ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
    mv.addObject(NormalExcelConstants.FILE_NAME, "题库数据");
    mv.addObject(NormalExcelConstants.CLASS, ExamQuestion.class);
    mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("题库数据", "导出人:" + getUsername()));
    mv.addObject(NormalExcelConstants.DATA_LIST, questions);
    return mv;
}

// 导入
@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
    try {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        
        List<ExamQuestion> questions = ExcelImportUtil.importExcel(
            file.getInputStream(), ExamQuestion.class, params);
        
        examQuestionService.saveBatch(questions);
        return Result.ok("导入成功，共导入" + questions.size() + "条数据");
        
    } catch (Exception e) {
        log.error("Excel导入失败", e);
        return Result.error("导入失败：" + e.getMessage());
    }
}
```
