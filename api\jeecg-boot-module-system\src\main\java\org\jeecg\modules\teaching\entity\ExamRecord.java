package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 考试记录
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@ApiModel(value="exam_record对象", description="考试记录")
@Data
@TableName("exam_record")
public class ExamRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    
    @TableField("user_id")
    @ApiModelProperty(value = "考生ID")
    private String userId;

    @TableField("paper_id")
    @ApiModelProperty(value = "试卷ID")
    private String paperId;

    @TableField("start_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @TableField("end_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "考试得分")
    private Integer score;
    
    @ApiModelProperty(value = "用户答案 (JSON格式)")
    private String answers;

    @ApiModelProperty(value = "详细分数统计 (JSON格式，包含各题型得分详情)")
    private String scoreDetails;

    @ApiModelProperty(value = "状态 (1:已提交)")
    private Integer status;
} 