package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 礼品兑换记录实体类
 */
@Data
@TableName("teaching_gift_exchange")
public class TeachingGiftExchange implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    /** 用户ID */
    private String userId;
    
    /** 礼品ID */
    private String giftId;
    
    /** 礼品名称 */
    private String giftName;
    
    /** 消费金币 */
    private Integer coinCount;
    
    /** 兑换时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exchangeTime;
    
    /** 状态（0:待领取, 1:已领取） */
    private Integer status;
    
    /** 领取时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;
} 