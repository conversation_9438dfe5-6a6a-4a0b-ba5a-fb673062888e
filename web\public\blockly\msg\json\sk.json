{"@metadata": {"authors": ["<PERSON>", "Genhis", "Jaroslav.micek", "<PERSON><PERSON><PERSON><PERSON>", "Lexected", "<PERSON><PERSON>s<PERSON>", "<PERSON>", "Nykta 1917", "Pmikolas44", "TomášPolonec", "Yardom78"]}, "VARIABLES_DEFAULT_NAME": "prvok", "UNNAMED_KEY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TODAY": "Dnes", "DUPLICATE_BLOCK": "Du<PERSON>likovať", "ADD_COMMENT": "Pridať komentár", "REMOVE_COMMENT": "Odstrániť komentár", "DUPLICATE_COMMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "EXTERNAL_INPUTS": "Vonkajš<PERSON> vstupy", "INLINE_INPUTS": "<PERSON><PERSON><PERSON><PERSON><PERSON> vs<PERSON><PERSON>", "DELETE_BLOCK": "Odstrániť blok", "DELETE_X_BLOCKS": "Odstrániť %1 blokov", "DELETE_ALL_BLOCKS": "Zmazať všetkých %1 dielcov?", "CLEAN_UP": "Narovnať bloky", "COLLAPSE_BLOCK": "Zvinúť blok", "COLLAPSE_ALL": "Zvinúť bloky", "EXPAND_BLOCK": "Rozvinúť blok", "EXPAND_ALL": "Rozvinúť bloky", "DISABLE_BLOCK": "Vypnúť blok", "ENABLE_BLOCK": "Povoliť blok", "HELP": "Pomoc", "UNDO": "Späť", "REDO": "<PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Zmeniť hodnotu:", "RENAME_VARIABLE": "Premenovať premennú...", "RENAME_VARIABLE_TITLE": "Premenovať všetky premenné '%1' na:", "NEW_VARIABLE": "Vytvoriť premennú...", "NEW_STRING_VARIABLE": "Vytvoriť reťazovú premennú...", "NEW_NUMBER_VARIABLE": "Vytvoriť číselnú premennú...", "NEW_COLOUR_VARIABLE": "vytvoriť farbu premennej", "NEW_VARIABLE_TYPE_TITLE": "nový typ premennej", "NEW_VARIABLE_TITLE": "Názov novej premennej:", "VARIABLE_ALREADY_EXISTS": "Premenná s názvom %1 už existuje.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Premenná s názvom '%1' už existuje pre inú premennú typu '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Odstrániť %1 použití premennej '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Nie je možné zmazať premennú „%1“, pretože je súčasťou definície funkcie „%2“", "DELETE_VARIABLE": "Odstrániť premennú '%1'", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Zvoľte farbu z palety.", "COLOUR_RANDOM_TITLE": "náhodná farba", "COLOUR_RANDOM_TOOLTIP": "Zvoliť farbu náhodne.", "COLOUR_RGB_TITLE": "ofarbiť s", "COLOUR_RGB_RED": "červená", "COLOUR_RGB_GREEN": "zelená", "COLOUR_RGB_BLUE": "modr<PERSON>", "COLOUR_RGB_TOOLTIP": "Vytvoriť farbu pomocou zadaného množstva červenej, zelenej a modrej. Množstvo musí byť medzi 0 a 100.", "COLOUR_BLEND_TITLE": "zmiešať", "COLOUR_BLEND_COLOUR1": "farba 1", "COLOUR_BLEND_COLOUR2": "farba 2", "COLOUR_BLEND_RATIO": "pomer", "COLOUR_BLEND_TOOLTIP": "Zmieša dve farby v danom pomere (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "opakuj %1 krát", "CONTROLS_REPEAT_INPUT_DO": "rob", "CONTROLS_REPEAT_TOOLTIP": "Opakuj určité príkazy viackrát.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "op<PERSON><PERSON><PERSON> kým nebude", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "<PERSON><PERSON><PERSON> je hodnota pravdivá, vykonávaj príkazy.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "<PERSON><PERSON><PERSON> je hodnota nepravdivá, vykonávaj príkazy.", "CONTROLS_FOR_TOOLTIP": "Nechá premennú '%1' nadobúdať hodnoty od začiatočného čísla po konečné s daným medzikrokom a vykoná zadané bloky.", "CONTROLS_FOR_TITLE": "počítať s %1 od %2 do %3 o %4", "CONTROLS_FOREACH_TITLE": "pre každý prvok %1 v zozname %2", "CONTROLS_FOREACH_TOOLTIP": "Pre každý prvok v zozname priraď jeho hodnotu do premenej '%1' a vykonaj príkazy.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "opustiť slučku", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "prejdi na nasledujúce opakovanie slučky", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Opustiť túto slučku.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Vynechať zvyšok tejto slučky a pokračovať ďalším opakovaním.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Upozornenie: Tento blok sa môže používať len v rámci slučky.", "CONTROLS_IF_TOOLTIP_1": "Ak je hodnota pravda, vykonaj príkazy.", "CONTROLS_IF_TOOLTIP_2": "Ak je hodnota pravda, vykonaj príkazy v prvom bloku. Inak vykonaj príkazy v druhom bloku.", "CONTROLS_IF_TOOLTIP_3": "Ak je prvá hodnota pravda, vykonaj príkazy v prvom bloku. Inak, ak je druhá hodnota pravda, vykonaj príkazy v druhom bloku.", "CONTROLS_IF_TOOLTIP_4": "Ak je prvá hodnota pravda, vykonaj príkazy v prvom bloku. Inak, ak je druhá hodnota pravda, vykonaj príkazy v druhom bloku. Ak ani jedna hodnota nie je pravda, vykonaj príkazy v poslednom bloku.", "CONTROLS_IF_MSG_IF": "ak", "CONTROLS_IF_MSG_ELSEIF": "inak ak", "CONTROLS_IF_MSG_ELSE": "inak", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON>, odstrániť alebo zmeniť poradie oddielov tohto \"ak\" bloku.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Pridať podmienku k \"ak\" bloku.", "CONTROLS_IF_ELSE_TOOLTIP": "Pridať poslednú záchytnú podmienku k \"ak\" bloku.", "IOS_OK": "OK", "IOS_CANCEL": "Zrušiť", "IOS_ERROR": "Chyba", "IOS_PROCEDURES_INPUTS": "Vstupy", "IOS_PROCEDURES_ADD_INPUT": "+ Pridať vstup", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Povoľujú príkazy", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "<PERSON><PERSON><PERSON> obsahuje duplikáty vstupov.", "IOS_VARIABLES_ADD_VARIABLE": "+ Pridať premennú", "IOS_VARIABLES_ADD_BUTTON": "Pridať", "IOS_VARIABLES_RENAME_BUTTON": "Premenovať", "IOS_VARIABLES_DELETE_BUTTON": "Zmazať", "IOS_VARIABLES_VARIABLE_NAME": "Názov premennej", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Nie je možné použ<PERSON>ť premennú bez mena.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak sú vstupy rovna<PERSON>.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak vstupy nie sú rovnak<PERSON>.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak prvý vstup je menš<PERSON> než d<PERSON>.", "LOGIC_COMPARE_TOOLTIP_LTE": "Vráť hodnotu pravda ak prvý vstup je menší alebo rovný druhému.", "LOGIC_COMPARE_TOOLTIP_GT": "Vráť hodnotu pravda ak prvý vstup je väčší než dru<PERSON>.", "LOGIC_COMPARE_TOOLTIP_GTE": "Vráť hodnotu pravda ak prvý vstup je väčší alebo rovný druhému.", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak sú vstupy pravdivé.", "LOGIC_OPERATION_AND": "a", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak je aspoň jeden vstup pravda.", "LOGIC_OPERATION_OR": "alebo", "LOGIC_NEGATE_TITLE": "nie je %1", "LOGIC_NEGATE_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak je vstup nepravda. <PERSON><PERSON><PERSON>ti hodnotu nepravda ak je vstup pravda.", "LOGIC_BOOLEAN_TRUE": "pravda", "LOGIC_BOOLEAN_FALSE": "<PERSON><PERSON><PERSON><PERSON>", "LOGIC_BOOLEAN_TOOLTIP": "Vráť buď hodnotu pravda alebo nepravda.", "LOGIC_NULL": "<PERSON><PERSON>", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> hodnotu nula.", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "ak pravda", "LOGIC_TERNARY_IF_FALSE": "ak nepravda", "LOGIC_TERNARY_TOOLTIP": "Skontroluj podmienku testom. Ak je podmienka pravda, vr<PERSON><PERSON> hodnotu \"ak pravda\", inak vr<PERSON>ť hodnotu \"ak nepravda\".", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "Číslo.", "MATH_TRIG_ASIN": "arcsin", "MATH_TRIG_ACOS": "arccos", "MATH_TRIG_ATAN": "arctan", "MATH_ARITHMETIC_HELPURL": "https://en.wikipedia.org/wiki/Arithmetic", "MATH_ARITHMETIC_TOOLTIP_ADD": "Vráť súčet dvoch čísel.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Vráť rozdiel dvoch čísel.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Vrá<PERSON> s<PERSON><PERSON><PERSON> d<PERSON>.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Vráť podiel dvoch čísel.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Vráť prvé č<PERSON>lo um<PERSON>é druhým.", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "<PERSON><PERSON><PERSON><PERSON> od<PERSON>a", "MATH_SINGLE_TOOLTIP_ROOT": "Vráť druhú odmocninu čísla.", "MATH_SINGLE_OP_ABSOLUTE": "absolútna hodnota", "MATH_SINGLE_TOOLTIP_ABS": "Vráť absolútnu hodnotu čísla.", "MATH_SINGLE_TOOLTIP_NEG": "Vráť opačné číslo.", "MATH_SINGLE_TOOLTIP_LN": "Vráť prirodzený logaritmus čísla.", "MATH_SINGLE_TOOLTIP_LOG10": "Vráť logaritmus čísla so základom 10.", "MATH_SINGLE_TOOLTIP_EXP": "Vráť e umocnené číslom.", "MATH_SINGLE_TOOLTIP_POW10": "Vráť 10 umocnené číslom.", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "Vráť sínus uhla (v stupňoch).", "MATH_TRIG_TOOLTIP_COS": "Vráť kosínus uhla (v stupňoch).", "MATH_TRIG_TOOLTIP_TAN": "Vráť tangens uhla (v stupňoch).", "MATH_TRIG_TOOLTIP_ASIN": "Vráť arkus s<PERSON>us <PERSON>.", "MATH_TRIG_TOOLTIP_ACOS": "Vráť arkus kosínus č<PERSON>.", "MATH_TRIG_TOOLTIP_ATAN": "Vráť arkus tangens čísla.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant‎", "MATH_CONSTANT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> jednu zo zvyčajných konštánt: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), alebo ∞ (ne<PERSON><PERSON><PERSON><PERSON>).", "MATH_IS_EVEN": "je p<PERSON>rne", "MATH_IS_ODD": "je ne<PERSON><PERSON><PERSON>", "MATH_IS_PRIME": "je pr<PERSON><PERSON><PERSON><PERSON>", "MATH_IS_WHOLE": "je celé <PERSON>", "MATH_IS_POSITIVE": "je kladné", "MATH_IS_NEGATIVE": "je z<PERSON><PERSON>", "MATH_IS_DIVISIBLE_BY": "je deliteľn<PERSON>", "MATH_IS_TOOLTIP": "Skontroluj či je <PERSON>, <PERSON><PERSON><PERSON><PERSON>, cel<PERSON>, klad<PERSON><PERSON>, záporné alebo deliteľné určitým číslom. Vráť hodnotu pravda alebo nepravda.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "zmeniť %1 o %2", "MATH_CHANGE_TOOLTIP": "Pridaj číslo do premennej \"%1\".", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "Zaokrúhli číslo nahor alebo nadol.", "MATH_ROUND_OPERATOR_ROUND": "zaok<PERSON><PERSON><PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "zaokr<PERSON><PERSON><PERSON> nahor", "MATH_ROUND_OPERATOR_ROUNDDOWN": "zaokrúhli nadol", "MATH_ONLIST_OPERATOR_SUM": "súčet zoznamu", "MATH_ONLIST_TOOLTIP_SUM": "Vráť súčet všetkých čísel v zozname.", "MATH_ONLIST_OPERATOR_MIN": "najmenšie v zozname", "MATH_ONLIST_TOOLTIP_MIN": "Vrátiť najmenšie číslo v zozname.", "MATH_ONLIST_OPERATOR_MAX": "najväčšie v zozname", "MATH_ONLIST_TOOLTIP_MAX": "Vrátiť najväčšie číslo v zozname.", "MATH_ONLIST_OPERATOR_AVERAGE": "prie<PERSON> z<PERSON>", "MATH_ONLIST_TOOLTIP_AVERAGE": "Vráť aritmetický priemer čísel v zozname.", "MATH_ONLIST_OPERATOR_MEDIAN": "medi<PERSON>", "MATH_ONLIST_TOOLTIP_MEDIAN": "Vráť medián čísel v zozname.", "MATH_ONLIST_OPERATOR_MODE": "najčastejšie v zozname", "MATH_ONLIST_TOOLTIP_MODE": "Vrátiť najčastejší prvok v zozname.", "MATH_ONLIST_OPERATOR_STD_DEV": "smerodajná odchýlka zoznamu", "MATH_ONLIST_TOOLTIP_STD_DEV": "Vráť smeroddajnú odchýlku zoznamu.", "MATH_ONLIST_OPERATOR_RANDOM": "náhodný prvok zoznamu", "MATH_ONLIST_TOOLTIP_RANDOM": "Vráť náhodne zvolený prvok zoznamu.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "zvyšok po delení %1 + %2", "MATH_MODULO_TOOLTIP": "Vráť zvyšok po delení jedného čísla druhým.", "MATH_CONSTRAIN_TITLE": "obmedz %1 od %2 do %3", "MATH_CONSTRAIN_TOOLTIP": "Obmedzí číslo do zadaných hraníc (vrátane).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "náhodné celé číslo od %1 do %2", "MATH_RANDOM_INT_TOOLTIP": "Vráť náhodné celé číslo z určeného intervalu (vrátane).", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "náhodné číslo od 0 do 1", "MATH_RANDOM_FLOAT_TOOLTIP": "Vráť náhodné číslo z intervalu 0.0 (vrátane) až 1.0.", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 of X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Vráťte arktangent bodu (X, Y) v stupňoch od -180 do 180.", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, slovo alebo riadok textu.", "TEXT_JOIN_TITLE_CREATEWITH": "vytvor text z", "TEXT_JOIN_TOOLTIP": "Vytvor text spojením určitého počtu prvkov.", "TEXT_CREATE_JOIN_TITLE_JOIN": "spoj", "TEXT_CREATE_JOIN_TOOLTIP": "Pridaj, odstráň alebo zmeň poradie oddielov v tomto textovom bloku.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Pridaj prvok do textu.", "TEXT_APPEND_TITLE": "do %1 pridaj text %2", "TEXT_APPEND_TOOLTIP": "Pridaj určitý text do premennej '%1'.", "TEXT_LENGTH_TITLE": "dĺžka %1", "TEXT_LENGTH_TOOLTIP": "Vráti počet písmen (s medzerami) v zadanom texte.", "TEXT_ISEMPTY_TITLE": "%1 je prázdny", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> hodnotu pravda, ak zadaný text je prázdny.", "TEXT_INDEXOF_TOOLTIP": "Vráti index prvého/posledného výskytu prvého textu v druhom texte. Ak nenájde, vráti %1.", "TEXT_INDEXOF_TITLE": "v texte %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "nájdi prvý výskyt textu", "TEXT_INDEXOF_OPERATOR_LAST": "nájdi posledný výskyt textu", "TEXT_CHARAT_TITLE": "v texte %1 %2", "TEXT_CHARAT_FROM_START": "zisti písmeno #", "TEXT_CHARAT_FROM_END": "zisti # písmeno od konca", "TEXT_CHARAT_FIRST": "zisti prvé písmeno", "TEXT_CHARAT_LAST": "zisti posledné písmeno", "TEXT_CHARAT_RANDOM": "vyber náhodné písmeno", "TEXT_CHARAT_TOOLTIP": "Vráti písmeno na určenej pozícii.", "TEXT_GET_SUBSTRING_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> určenú časť textu.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "v texte", "TEXT_GET_SUBSTRING_START_FROM_START": "vyber podreťazec od písmena #", "TEXT_GET_SUBSTRING_START_FROM_END": "vyber podreťazec od # písmena od konca", "TEXT_GET_SUBSTRING_START_FIRST": "vyber podreťazec od začiatku", "TEXT_GET_SUBSTRING_END_FROM_START": "po písmeno #", "TEXT_GET_SUBSTRING_END_FROM_END": "po # písmeno od konca", "TEXT_GET_SUBSTRING_END_LAST": "po koniec", "TEXT_CHANGECASE_TOOLTIP": "Vráť kópiu textu s inou veľkosťou písmen.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "na VEĽKÉ PÍSMENÁ", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "na malé písmená", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "na Veľké Začiatočné Písmená", "TEXT_TRIM_TOOLTIP": "Vráť kópiu textu bez medzier na jednom alebo oboch koncoch.", "TEXT_TRIM_OPERATOR_BOTH": "odstráň medzery z oboch strán", "TEXT_TRIM_OPERATOR_LEFT": "odstráň medzery z ľavej strany", "TEXT_TRIM_OPERATOR_RIGHT": "odstráň medzery z pravej strany", "TEXT_PRINT_TITLE": "píš %1", "TEXT_PRINT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> text, číslo alebo hodnotu.", "TEXT_PROMPT_TYPE_TEXT": "výzva za zadanie textu so správou", "TEXT_PROMPT_TYPE_NUMBER": "výzva na zadanie čísla so správou", "TEXT_PROMPT_TOOLTIP_NUMBER": "Výzva pre používateľa na zadanie čísla.", "TEXT_PROMPT_TOOLTIP_TEXT": "Výzva pre používateľa na zadanie textu.", "TEXT_COUNT_MESSAGE0": "počet výskytov %1 v %2", "TEXT_COUNT_TOOLTIP": "Počet výskytov textu nachádzajúcom sa v inom texte.", "TEXT_REPLACE_MESSAGE0": "zameniť %1 za %2 v reťazci %3", "TEXT_REPLACE_TOOLTIP": "Zameniť všetky výskyty textu za iný text.", "TEXT_REVERSE_MESSAGE0": "text odzadu %1", "TEXT_REVERSE_TOOLTIP": "Obrátiť poradie písmen v texte.", "LISTS_CREATE_EMPTY_TITLE": "prázdny zoznam", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> nulovej dĺžky, k<PERSON><PERSON> neobsahuje žiadne prvky.", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "Vytvor zoznam s ľubovoľným počtom prvkov.", "LISTS_CREATE_WITH_INPUT_WITH": "vyt<PERSON> zoznam s", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "zoznam", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "Pridaj, odstráň alebo zmeň poradie v tomto zoznamovom bloku.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Pridaj prvok do zoznamu.", "LISTS_REPEAT_TOOLTIP": "Vytvorí zoznam s niekoľkými rovnakými prvkami s danou hodnotou.", "LISTS_REPEAT_TITLE": "vytvor zoznam s prvkom %1 opakovaným %2 krát", "LISTS_LENGTH_TITLE": "dĺžka %1", "LISTS_LENGTH_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> dĺžku zoznamu", "LISTS_ISEMPTY_TITLE": "%1 je prázdny", "LISTS_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, ak je zoznam prázdny.", "LISTS_INLIST": "v zozname", "LISTS_INDEX_OF_FIRST": "nájdi prvý výskyt prvku", "LISTS_INDEX_OF_LAST": "nájdi posledný výskyt prvku", "LISTS_INDEX_OF_TOOLTIP": "Vráti index prvého/posledného výskytu prvku v zozname. Ak sa nič nenašlo, vráti %1.", "LISTS_GET_INDEX_GET": "zisti", "LISTS_GET_INDEX_GET_REMOVE": "zisti a odstráň", "LISTS_GET_INDEX_REMOVE": "odstráň", "LISTS_GET_INDEX_FROM_END": "# od konca", "LISTS_GET_INDEX_FIRST": "prvý", "LISTS_GET_INDEX_LAST": "p<PERSON><PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_RANDOM": "<PERSON>á<PERSON><PERSON>ý", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 je počiatočný prvok.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 je posledný prvok.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Vráti prvok na určenej pozícii v zozname.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "<PERSON><PERSON><PERSON><PERSON> počiatočný prvok zoznamu.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "<PERSON><PERSON><PERSON><PERSON> posledný prvok zoznamu.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "<PERSON><PERSON><PERSON><PERSON> náhodný prvok zoznamu.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Odstráni a vráti prvok z určenej pozície v zozname.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Odstráni a vráti prvý prvok v zozname.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Odstráni a vráti posledný prvok v zozname.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Odstráni a vráti náhodný prvok v zozname.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Odstráni prvok na určenej pozícii v zozname.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Odstráni prvý prvok v zozname.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Odstráni posledný prvok v zozname.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Odstráni náhodný prvok v zozname.", "LISTS_SET_INDEX_SET": "nastaviť", "LISTS_SET_INDEX_INSERT": "vložiť na", "LISTS_SET_INDEX_INPUT_TO": "ako", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Nastaví prvok na určenej pozícii v zozname.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Nastaví prvý prvok v zozname.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Nastaví posledný prvok v zozname.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Nastaví posledný prvok v zozname.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Vsunie prvok na určenú pozíciu v zozname.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Vsunie prvok na začiatok zoznamu.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Pripojí prvok na koniec zoznamu.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Vsunie prvok na náhodné miesto v zozname.", "LISTS_GET_SUBLIST_START_FROM_START": "získať podzoznam od #", "LISTS_GET_SUBLIST_START_FROM_END": "Získať podzoznam od # od konca", "LISTS_GET_SUBLIST_START_FIRST": "Získať podzoznam od začiatku", "LISTS_GET_SUBLIST_END_FROM_START": "po #", "LISTS_GET_SUBLIST_END_FROM_END": "po # od konca", "LISTS_GET_SUBLIST_END_LAST": "po koniec", "LISTS_GET_SUBLIST_TOOLTIP": "Skopíruje určený úsek zoznamu.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "zoradiť %1 %2 %3", "LISTS_SORT_TOOLTIP": "Zoradiť kópiu zoznamu.", "LISTS_SORT_ORDER_ASCENDING": "Vzostupne", "LISTS_SORT_ORDER_DESCENDING": "Zostupne", "LISTS_SORT_TYPE_NUMERIC": "numericky", "LISTS_SORT_TYPE_TEXT": "abecedne", "LISTS_SORT_TYPE_IGNORECASE": "a<PERSON><PERSON><PERSON>, ignorovať veľkosť písmen", "LISTS_SPLIT_LIST_FROM_TEXT": "vytvoriť zoznam z textu", "LISTS_SPLIT_TEXT_FROM_LIST": "vytvoriť text zo zoznamu", "LISTS_SPLIT_WITH_DELIMITER": "s oddeľovačom", "LISTS_SPLIT_TOOLTIP_SPLIT": "Rozdelenie textu do zoznamu textov, lámanie na oddeľovačoch.", "LISTS_SPLIT_TOOLTIP_JOIN": "Spojiť zoznam textov do jedného textu s oddeľovačmi.", "LISTS_REVERSE_MESSAGE0": "obrátiť %1", "LISTS_REVERSE_TOOLTIP": "Obrátiť kópiu zoznamu.", "VARIABLES_GET_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> hodnotu tejto premen<PERSON>.", "VARIABLES_GET_CREATE_SET": "Vytvoriť \"nastaviť %1\"", "VARIABLES_SET": "nastaviť %1 na %2", "VARIABLES_SET_TOOLTIP": "Nastaví túto premennú, aby sa rovnala vstupu.", "VARIABLES_SET_CREATE_GET": "Vytvoriť \"získať %1\"", "PROCEDURES_DEFNORETURN_TITLE": "na", "PROCEDURES_DEFNORETURN_PROCEDURE": "<PERSON><PERSON><PERSON>", "PROCEDURES_BEFORE_PARAMS": "s:", "PROCEDURES_CALL_BEFORE_PARAMS": "s:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Vytvorí funciu bez výstupu.", "PROCEDURES_DEFNORETURN_COMMENT": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>o rob<PERSON> t<PERSON>...", "PROCEDURES_DEFRETURN_RETURN": "vrátiť", "PROCEDURES_DEFRETURN_TOOLTIP": "Vytvorí funkciu s výstupom.", "PROCEDURES_ALLOW_STATEMENTS": "povoliť príkazy", "PROCEDURES_DEF_DUPLICATE_WARNING": "Upozornenie: <PERSON><PERSON><PERSON> má duplicitné parametre.", "PROCEDURES_CALLNORETURN_HELPURL": "https://sk.wikipedia.org/wiki/Podprogram", "PROCEDURES_CALLNORETURN_TOOLTIP": "Spustí používateľom definovanú funkciu '%1'.", "PROCEDURES_CALLRETURN_HELPURL": "https://sk.wikipedia.org/wiki/Podprogram", "PROCEDURES_CALLRETURN_TOOLTIP": "Spustí používateľom definovanú funkciu '%1' a použije jej výstup.", "PROCEDURES_MUTATORCONTAINER_TITLE": "vstupy", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, odstrániť alebo zmeniť poradie vstupov tejto funkcie.", "PROCEDURES_MUTATORARG_TITLE": "názov vstupu:", "PROCEDURES_MUTATORARG_TOOLTIP": "Pridať vstup do funkcie.", "PROCEDURES_HIGHLIGHT_DEF": "Zvýrazniť definíciu funk<PERSON>", "PROCEDURES_CREATE_DO": "Vytvoriť '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Ak je hodnota pravda, tak vr<PERSON>ti druh<PERSON> hodnotu.", "PROCEDURES_IFRETURN_WARNING": "Upozornenie: <PERSON><PERSON> blok môže byť len vo vn<PERSON><PERSON> funkcie.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "<PERSON><PERSON>z niečo...", "WORKSPACE_ARIA_LABEL": "Pracovisko Bloskly", "COLLAPSED_WARNINGS_WARNING": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloky obs<PERSON><PERSON><PERSON>.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "Zrušiť"}