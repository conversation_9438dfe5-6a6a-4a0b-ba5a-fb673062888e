package org.jeecg.modules.teaching.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 用于即时判题的数据传输对象，包含了判题所需的所有信息，与HOJ代理API通信。
 */
@Data
public class TransientJudgeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 判题的核心代码与语言
    private String code;
    private String language;

    // 题目的动态信息 (替代了固定的pid)
    private Integer timeLimit;    // e.g., 1000 (ms)
    private Integer memoryLimit;  // e.g., 256 (MB)
    private Integer stackLimit;   // e.g., 128 (MB)

    // 判题模式，例如 "default", "spj"
    private String judgeMode;

    // HOJ需要的额外字段
    private String problemId;
    private Boolean isUploadCase;
    private Integer type; // 0: ACM, 1: OI

    // 所有的测试用例
    private List<TestCase> testCases;

    /**
     * 内部静态类，用于定义单个测试用例的结构
     */
    @Data
    public static class TestCase implements Serializable {
        private static final long serialVersionUID = 1L;
        private String input;  // 标准输入
        private String output; // 期望的标准输出
    }
} 