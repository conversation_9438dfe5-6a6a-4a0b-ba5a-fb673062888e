{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>"]}, "VARIABLES_DEFAULT_NAME": "element", "UNNAMED_KEY": "anònim", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "ADD_COMMENT": "Gionté un coment", "REMOVE_COMMENT": "Scancelé un coment", "DUPLICATE_COMMENT": "<PERSON><PERSON><PERSON><PERSON> coment", "EXTERNAL_INPUTS": "Imission esterne", "INLINE_INPUTS": "Imission an linia", "DELETE_BLOCK": "Scance<PERSON> bl<PERSON>ch", "DELETE_X_BLOCKS": "Scancelé %1 blòch", "DELETE_ALL_BLOCKS": "Scancelé tuti ij %1 blòch?", "CLEAN_UP": "Dëscancelé ij blòch", "COLLAPSE_BLOCK": "<PERSON><PERSON><PERSON> <PERSON><PERSON> bl<PERSON>ch", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON> ij bl<PERSON>ch", "EXPAND_BLOCK": "Dësvlup<PERSON> bl<PERSON>ch", "EXPAND_ALL": "Dësvlupé ij blòch", "DISABLE_BLOCK": "Di<PERSON><PERSON><PERSON> bl<PERSON>", "ENABLE_BLOCK": "<PERSON><PERSON><PERSON> bl<PERSON>", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON><PERSON>", "REDO": "<PERSON><PERSON> torna", "CHANGE_VALUE_TITLE": "Modific<PERSON><PERSON>l valor:", "RENAME_VARIABLE": "<PERSON><PERSON><PERSON> la variàbil...", "RENAME_VARIABLE_TITLE": "<PERSON><PERSON><PERSON> tute le variàbij '%1' 'me:", "NEW_VARIABLE": "Creé na variàbil...", "NEW_STRING_VARIABLE": "Creé na variàbil dë stringa...", "NEW_NUMBER_VARIABLE": "Creé na variàbil numérica...", "NEW_COLOUR_VARIABLE": "Creé na variàbil ëd color...", "NEW_VARIABLE_TYPE_TITLE": "Neuva sòrt ëd variàbil:", "NEW_VARIABLE_TITLE": "Nòm ëd la neuva variàbil:", "VARIABLE_ALREADY_EXISTS": "Na variàbil con ël nòm '%1' a esist già.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Na variàbil ciamà '%1' a esist già për n'àutra sòrt: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Eliminé %1 utilisassion ëd la variàbil '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "As peul nen eliminesse la variàbil '%1' përchè a l'é part ëd la definission dla fonsion '%2'", "DELETE_VARIABLE": "Eliminé la variàbil '%1'", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Serne un color ant la taulòssa.", "COLOUR_RANDOM_TITLE": "color a asar", "COLOUR_RANDOM_TOOLTIP": "Serne un color a asar.", "COLOUR_RGB_TITLE": "coloré con", "COLOUR_RGB_RED": "ross", "COLOUR_RGB_GREEN": "verd", "COLOUR_RGB_BLUE": "bleu", "COLOUR_RGB_TOOLTIP": "Creé un color con la quantità spessificà ëd ross, verd e bleu. Tuti ij valor a devo esse antra 0 e 100.", "COLOUR_BLEND_TITLE": "mës-cé", "COLOUR_BLEND_COLOUR1": "color 1", "COLOUR_BLEND_COLOUR2": "color 2", "COLOUR_BLEND_RATIO": "<PERSON><PERSON><PERSON>", "COLOUR_BLEND_TOOLTIP": "A mës-cia doi color ansema con un rapòrt dàit (0,0 - 1,0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "arpete %1 vire", "CONTROLS_REPEAT_INPUT_DO": "fé", "CONTROLS_REPEAT_TOOLTIP": "Eseguì chèiche anstrussion vàire vire.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "arpete antramentre che", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "arpete fin-a a", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Cand un valor a l'é ver, eseguì chèiche anstrussion.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Cand un valor a l'é fàuss, eseguì chèiche anstrussion.", "CONTROLS_FOR_TOOLTIP": "Fé an manera che la variàbil \"%1\" a pija ij valor dal nùmer inissial fin-a al nùmer final, an contand për l'antërval ëspessificà, e eseguì ij bloch ëspessificà.", "CONTROLS_FOR_TITLE": "conté con %1 da %2 a %3 për %4", "CONTROLS_FOREACH_TITLE": "për minca n'element %1 ant la lista %2", "CONTROLS_FOREACH_TOOLTIP": "Për minca element an na lista, dé ël valor ëd l'element a la variàbil '%1', peui eseguì chèiche anstrussion.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "seurte da la liassa", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "continué con l'iterassion sucessiva dla liassa", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "<PERSON><PERSON><PERSON> da la liassa anglobanta.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Sauté ël rest ëd sa liassa, e continué con l'iterassion apress.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Atension: Ës blòch a peul mach esse dovrà andrinta a na liassa.", "CONTROLS_IF_TOOLTIP_1": "Si un valor a l'é ver, antlora eseguì chèiche anstrussion.", "CONTROLS_IF_TOOLTIP_2": "Si un valor a l'é ver, antlora eseguì ël prim blòch d'anstrussion. <PERSON><PERSON><PERSON><PERSON><PERSON>, esegu<PERSON> ël second blòch d'anstrussion.", "CONTROLS_IF_TOOLTIP_3": "Si ël prim valor a l'é ver, antlora fé andé ël prim blòch d'anstrussion. <PERSON><PERSON><PERSON><PERSON><PERSON>, si ël second valor a l'é ver, fé and<PERSON> ël second blòch d'anstrussion.", "CONTROLS_IF_TOOLTIP_4": "Si ël prim valor a l'é ver, antlora fé andé ël prim blòch d'anstrussion. <PERSON><PERSON><PERSON><PERSON><PERSON>, si ël second valor a l'é ver, fé and<PERSON> ël second blòcj d'anstrussion. Si gnun dij valor a l'é ver, fé andé l'ùltim blòch d'anstrussion.", "CONTROLS_IF_MSG_IF": "si", "CONTROLS_IF_MSG_ELSEIF": "d<PERSON><PERSON><PERSON><PERSON> si", "CONTROLS_IF_MSG_ELSE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON>, gavé o riordiné le session për cinfiguré torna ës blòch si.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Gionté na condission al blòch si.", "CONTROLS_IF_ELSE_TOOLTIP": "Gionté na condission final ch'a cheuj tut al blòch si.", "IOS_OK": "<PERSON><PERSON> <PERSON>", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "<PERSON><PERSON><PERSON>", "IOS_PROCEDURES_INPUTS": "IMISSION", "IOS_PROCEDURES_ADD_INPUT": "+ Gionté n'imission", "IOS_PROCEDURES_ALLOW_STATEMENTS": "P<PERSON>rm<PERSON>tte le diciairassion", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Costa imission a dj'imission duplicà.", "IOS_VARIABLES_ADD_VARIABLE": "+ Gionté na variàbil", "IOS_VARIABLES_ADD_BUTTON": "Gionté", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_VARIABLE_NAME": "Nòm ëd la variàbil", "IOS_VARIABLES_EMPTY_NAME_ERROR": "A peul nen dovré un nòm ëd variàbil veuid.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "Rëspond<PERSON> ver si le doe imission a son uguaj.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Rësponde ver si le doe imission a son nen uguaj.", "LOGIC_COMPARE_TOOLTIP_LT": "Rësponde ver si la prima imission a l'é pi cita dla sconda.", "LOGIC_COMPARE_TOOLTIP_LTE": "Rësponde ver si la prima imission a l'é pi cita o ugual a la sconda.", "LOGIC_COMPARE_TOOLTIP_GT": "Rësponde ver si la prima imission a l'é pi granda che la sconda.", "LOGIC_COMPARE_TOOLTIP_GTE": "Rësponde ver si la prima imission a l'é pi granda o ugual a la sconda.", "LOGIC_OPERATION_TOOLTIP_AND": "Rësponde ver se tute doe j'imission a son vere.", "LOGIC_OPERATION_AND": "e", "LOGIC_OPERATION_TOOLTIP_OR": "Rësponde ver se almanch un-a d'imission a l'é vera.", "LOGIC_OPERATION_OR": "o", "LOGIC_NEGATE_TITLE": "nen %1", "LOGIC_NEGATE_TOOLTIP": "A rëspond ver se l'imission a l'é fàussa. A rëspond fàuss se l'imission a l'é vera.", "LOGIC_BOOLEAN_TRUE": "ver", "LOGIC_BOOLEAN_FALSE": "<PERSON><PERSON><PERSON>", "LOGIC_BOOLEAN_TOOLTIP": "A rëspond ver o fàuss.", "LOGIC_NULL": "gnente", "LOGIC_NULL_TOOLTIP": "A rëspond gnente.", "LOGIC_TERNARY_CONDITION": "preuva", "LOGIC_TERNARY_IF_TRUE": "se ver", "LOGIC_TERNARY_IF_FALSE": "se fàuss", "LOGIC_TERNARY_TOOLTIP": "Controlé la condission an 'preuva'. Se la condission a l'é vera, a rëspond con ël valor 'se ver'; dësnò a rëspond con ël valor 'se fàuss'.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "Un nùmer.", "MATH_ARITHMETIC_HELPURL": "https://en.wikipedia.org/wiki/Arithmetic", "MATH_ARITHMETIC_TOOLTIP_ADD": "A smon la soma ëd doi nùmer.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "A smon la diferensa dij doi nùmer.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "A smon ël prodot dij doi nùmer.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "A smon ël cossient dij doi nùmer.", "MATH_ARITHMETIC_TOOLTIP_POWER": "A smon ël prim nùmer alvà a la potensa dël second.", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "r<PERSON><PERSON> quadra", "MATH_SINGLE_TOOLTIP_ROOT": "A smon la rèis quadra d'un nùmer.", "MATH_SINGLE_OP_ABSOLUTE": "assolù", "MATH_SINGLE_TOOLTIP_ABS": "A smon ël valor assolù d'un nùmer.", "MATH_SINGLE_TOOLTIP_NEG": "A smon l'opòst d'un nùmer.", "MATH_SINGLE_TOOLTIP_LN": "A smon ël logaritm natural d'un nùmer.", "MATH_SINGLE_TOOLTIP_LOG10": "A smon ël logaritm an base 10 d'un nùmer.", "MATH_SINGLE_TOOLTIP_EXP": "A smon e a la potensa d'un nùmer.", "MATH_SINGLE_TOOLTIP_POW10": "A smon 10 a la potensa d'un nùmer.", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "A smon ël sen ëd n'àngol an gré (pa an radiant).", "MATH_TRIG_TOOLTIP_COS": "A smon ël cosen ëd n'àngol an gré (pa an radiant).", "MATH_TRIG_TOOLTIP_TAN": "A smon la tangenta ëd n'àngol an gré (pa an radiant).", "MATH_TRIG_TOOLTIP_ASIN": "A smon l'arch-sen d'un nùmer.", "MATH_TRIG_TOOLTIP_ACOS": "A smon l'arch-cosen d'un nùmer.", "MATH_TRIG_TOOLTIP_ATAN": "A smon l'arch-tangenta d'un nùmer.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant", "MATH_CONSTANT_TOOLTIP": "A smon un-a dle costante comun-e π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…) o ∞ (infinì).", "MATH_IS_EVEN": "a l'é cobi", "MATH_IS_ODD": "a l'é dëscobi", "MATH_IS_PRIME": "a l'é prim", "MATH_IS_WHOLE": "a l'é antregh", "MATH_IS_POSITIVE": "a l'é positiv", "MATH_IS_NEGATIVE": "a l'é negativ", "MATH_IS_DIVISIBLE_BY": "a l'é divisìbil për", "MATH_IS_TOOLTIP": "A contròla si un nùmer a l'é cobi, d<PERSON><PERSON><PERSON>, prim, antre<PERSON>m positiv, negativ, o s'a l'é divisìbil për un nùmer dàit. A rëspond ver o fàuss.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "ancrementé %1 për %2", "MATH_CHANGE_TOOLTIP": "Gionté un nùmer a la variàbil '%1'.", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "A arionda un nùmer për difet o ecess.", "MATH_ROUND_OPERATOR_ROUND": "<PERSON><PERSON><PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "a<PERSON><PERSON><PERSON> për ecess", "MATH_ROUND_OPERATOR_ROUNDDOWN": "a<PERSON><PERSON><PERSON> p<PERSON> di<PERSON>t", "MATH_ONLIST_OPERATOR_SUM": "soma dla lista", "MATH_ONLIST_TOOLTIP_SUM": "A smon la soma ëd tuti ij nùmer ant la lista.", "MATH_ONLIST_OPERATOR_MIN": "mìnim ëd la lista", "MATH_ONLIST_TOOLTIP_MIN": "A smon ël pi cit nùmer ëd la lista.", "MATH_ONLIST_OPERATOR_MAX": "màssim ëd la lista", "MATH_ONLIST_TOOLTIP_MAX": "A smon ël pi gròss nùmer ëd la lista.", "MATH_ONLIST_OPERATOR_AVERAGE": "media dla lista", "MATH_ONLIST_TOOLTIP_AVERAGE": "A smon la media (aritmética) dij valor numérich ant la lista.", "MATH_ONLIST_OPERATOR_MEDIAN": "mesan-a dla lista", "MATH_ONLIST_TOOLTIP_MEDIAN": "A smon ël nùmer mesan ëd la lista.", "MATH_ONLIST_OPERATOR_MODE": "mòde dla lista", "MATH_ONLIST_TOOLTIP_MODE": "A smon na lista dj'element pi frequent ëd la lista.", "MATH_ONLIST_OPERATOR_STD_DEV": "deviassion ëstàndard ëd la lista", "MATH_ONLIST_TOOLTIP_STD_DEV": "A smon la deviassion ëstàndard ëd la lista.", "MATH_ONLIST_OPERATOR_RANDOM": "element a l'ancàpit ëd la lista", "MATH_ONLIST_TOOLTIP_RANDOM": "A smon n'element a l'ancàpit da la lista.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "resta ëd %1:%2", "MATH_MODULO_TOOLTIP": "A smon la resta ëd la division dij doi nùmer.", "MATH_CONSTRAIN_TITLE": "limité %1 antra %2 e %3", "MATH_CONSTRAIN_TOOLTIP": "Limité un nùmer a esse antra le limitassion ëspessificà (comprèise).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "antregh aleatòri antra %1 e %2", "MATH_RANDOM_INT_TOOLTIP": "A smon n'antregh aleatòri antra ij doi lìmit ëspessificà, compr<PERSON><PERSON>.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "frassion aleatòria", "MATH_RANDOM_FLOAT_TOOLTIP": "A smon na frassion aleatòria antra 0,0 (compr<PERSON><PERSON>) e 1,0 (esclus).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 ëd X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "A rëspond con l'arch-tangent dël pont (X, Y) an gre da -180 a 180.", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "Na litra, na paròla o na linia ëd test.", "TEXT_JOIN_TITLE_CREATEWITH": "creé <PERSON>l test con", "TEXT_JOIN_TOOLTIP": "Creé un tòch ëd test an gionzend un nùmer qualsëssìa d'element.", "TEXT_CREATE_JOIN_TITLE_JOIN": "gion<PERSON>", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON>, gavé o riordiné le session për configuré torna ës blòch ëd test.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Gionté n'element al test.", "TEXT_APPEND_TITLE": "a %1 taché ël test %2", "TEXT_APPEND_TOOLTIP": "Taché dël test a la variàbil '%1'.", "TEXT_LENGTH_TITLE": "longheur ëd %1", "TEXT_LENGTH_TOOLTIP": "A smon ël nùmer ëd litre (spassi comprèis) ant ël test fornì.", "TEXT_ISEMPTY_TITLE": "%1 a l'é veuid", "TEXT_ISEMPTY_TOOLTIP": "A smon ver se ël test fornì a l'é veuid.", "TEXT_INDEXOF_TOOLTIP": "A smon l'ìndes dla prima/ùltima ocorensa dël prim test ant ël second test. A smon %1 se ël test a l'é nen trovà.", "TEXT_INDEXOF_TITLE": "ant ël test %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "trové la prima ocorensa dël test", "TEXT_INDEXOF_OPERATOR_LAST": "trové l'ùltima ocorensa dël test", "TEXT_CHARAT_TITLE": "ant ël test %1 %2", "TEXT_CHARAT_FROM_START": "oten-e la litra #", "TEXT_CHARAT_FROM_END": "oten-e la litra # da la fin", "TEXT_CHARAT_FIRST": "oten-e la prima litra", "TEXT_CHARAT_LAST": "oten-e l'ùltima litra", "TEXT_CHARAT_RANDOM": "oten-e na litra a l'ancàpit", "TEXT_CHARAT_TOOLTIP": "A smon la litra ant la posission ëspessificà.", "TEXT_GET_SUBSTRING_TOOLTIP": "A smon un tòch ëspessificà dël test.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ant ël test", "TEXT_GET_SUBSTRING_START_FROM_START": "oten-e la sota-stringa da la litra #", "TEXT_GET_SUBSTRING_START_FROM_END": "oten-e la sota-stringa da la litra # da la fin", "TEXT_GET_SUBSTRING_START_FIRST": "oten-e la sota-stringa da la prima litra", "TEXT_GET_SUBSTRING_END_FROM_START": "fin-a a la litra #", "TEXT_GET_SUBSTRING_END_FROM_END": "fin-a a la litra # da la fin", "TEXT_GET_SUBSTRING_END_LAST": "fin-a a l'ùltima litra", "TEXT_CHANGECASE_TOOLTIP": "A smon na còpia dël test ant un caràter diferent.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "an MAJÙSCOL", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "an minùscul", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "an Majùscol A L'Ancamin Ëd Minca Paròla", "TEXT_TRIM_TOOLTIP": "A smon na còpia dël test con jë spassi gavà da n'estremità o da tute doe.", "TEXT_TRIM_OPERATOR_BOTH": "gavé jë spassi da le doe bande ëd", "TEXT_TRIM_OPERATOR_LEFT": "gavé j<PERSON> spassi da la banda snistra ëd", "TEXT_TRIM_OPERATOR_RIGHT": "gavé j<PERSON> spassi da la banda drita ëd", "TEXT_PRINT_TITLE": "smon-e %1", "TEXT_PRINT_TOOLTIP": "<PERSON><PERSON>-e ël test, ë<PERSON> nùmer o n'àutr valor ëspessificà.", "TEXT_PROMPT_TYPE_TEXT": "anvit për un test con un mëssagi", "TEXT_PROMPT_TYPE_NUMBER": "an<PERSON>t për un nùmer con un mëssagi", "TEXT_PROMPT_TOOLTIP_NUMBER": "<PERSON><PERSON>é un nùmer a l'utent.", "TEXT_PROMPT_TOOLTIP_TEXT": "Ciamé un test a l'utent.", "TEXT_COUNT_MESSAGE0": "nùmer %1 su %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Conté vàire vire un test dàit a compariss an n'àutr test.", "TEXT_REPLACE_MESSAGE0": "rampiassé %1 con %2 an %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "Rampiassé tute j'ocorense d'un test con n'àutr.", "TEXT_REVERSE_MESSAGE0": "Anversé %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Anversé l'òrdin dij caràter ant ël test.", "LISTS_CREATE_EMPTY_TITLE": "creé na lista veuida", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> na lista, ë<PERSON> <PERSON> 0, ch'a conten gnun-a argistrassion", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "Creé na lista con un nùmer qualsëssìa d'element.", "LISTS_CREATE_WITH_INPUT_WITH": "creé na lista con", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, gavé o riordiné le session për configuré torna cost blòch ëd lista.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Gionté n'element a la lista.", "LISTS_REPEAT_TOOLTIP": "A crea na lista ch'a consist dël valor dàit arpetù ël nùmer ëspessificà ëd vire.", "LISTS_REPEAT_TITLE": "creé na lista con l'element %1 arpetù %2 vire", "LISTS_LENGTH_TITLE": "longheur ëd %1", "LISTS_LENGTH_TOOLTIP": "A smon la longheur ¨d na lista.", "LISTS_ISEMPTY_TITLE": "%1 a l'é veuid", "LISTS_ISEMPTY_TOOLTIP": "A smon ver se la lista a l'é veuida.", "LISTS_INLIST": "ant la lista", "LISTS_INDEX_OF_FIRST": "trové la prima ocorensa dl'element", "LISTS_INDEX_OF_LAST": "trové l'ùltima ocorensa dl'element", "LISTS_INDEX_OF_TOOLTIP": "A smon l'ìndes ëd la prima/ùltima ocorensa dl'element ant la lista. A smon %1 se l'element a l'é nen trovà.", "LISTS_GET_INDEX_GET": "oten-e", "LISTS_GET_INDEX_GET_REMOVE": "oten-e e eliminé", "LISTS_GET_INDEX_REMOVE": "eliminé", "LISTS_GET_INDEX_FROM_END": "# da la fin", "LISTS_GET_INDEX_FIRST": "prim", "LISTS_GET_INDEX_LAST": "ùlt<PERSON>", "LISTS_GET_INDEX_RANDOM": "a l'ancàpit", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 a l'é ël prim element.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 a l'é l'ùltim element.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "A smon l'element a la posission ëspessificà an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "A smon ël prim element an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "A smon l'ùltim element an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "A smon n'element a l'ancàpit an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "A gava e a smon l'element a la posission ëspessificà an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "A gava e a smon ël prim element an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "A gava e a smon l'ùltim element an na lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "A gava e a smon n'element a l'ancàpit an na lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "A gava l'element a la posission ëspessificà an na lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "A gava ël prim element an na lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "A gava l'ùltim element an na lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "A gava n'element a l'ancàpit da na lista.", "LISTS_SET_INDEX_SET": "buté", "LISTS_SET_INDEX_INSERT": "an<PERSON><PERSON> an", "LISTS_SET_INDEX_INPUT_TO": "tanme", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "A fissa l'element a la posission ëspessificà an na lista.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "A fissa ël prim element an na lista.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "A fissa l'ùltim element an na lista.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "A fissa n'element a l'ancàpit an na lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "A anseriss l'element a la posission ëspessificà an na lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "A anseriss l'element al prinsipi ëd na lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Gionté l'element a la fin ëd na lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "A anseriss l'element a l'ancàpit an na lista.", "LISTS_GET_SUBLIST_START_FROM_START": "oten-e la sot-lista da #", "LISTS_GET_SUBLIST_START_FROM_END": "oten-e la sot-lista da # da la fin", "LISTS_GET_SUBLIST_START_FIRST": "oten-e la sot-lista dal prim", "LISTS_GET_SUBLIST_END_FROM_START": "fin-a a #", "LISTS_GET_SUBLIST_END_FROM_END": "fin-a a # da la fin", "LISTS_GET_SUBLIST_END_LAST": "fin-a a l'ùltim", "LISTS_GET_SUBLIST_TOOLTIP": "A crea na còpia dël tòch ëspessificà ëd na lista.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "ordiné %1 %2 %3", "LISTS_SORT_TOOLTIP": "Ordiné na còpia ëd na lista.", "LISTS_SORT_ORDER_ASCENDING": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_ORDER_DESCENDING": "calant", "LISTS_SORT_TYPE_NUMERIC": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_IGNORECASE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON>l car<PERSON>ter minù<PERSON>l o majùscol", "LISTS_SPLIT_HELPURL": "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists", "LISTS_SPLIT_LIST_FROM_TEXT": "fé na lista da 'n test", "LISTS_SPLIT_TEXT_FROM_LIST": "fé 'n test da na lista", "LISTS_SPLIT_WITH_DELIMITER": "con ël separator", "LISTS_SPLIT_TOOLTIP_SPLIT": "Divide un test an na lista ëd test, tajand a minca 'n separator.", "LISTS_SPLIT_TOOLTIP_JOIN": "Gionze na lista ëd test ant un test sol, separandje con un separator.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "anversé %1", "LISTS_REVERSE_TOOLTIP": "Anversé na còpia ëd na lista", "VARIABLES_GET_TOOLTIP": "A smon ël valor ëd sa variàbil.", "VARIABLES_GET_CREATE_SET": "Creé 'fissé %1'", "VARIABLES_SET": "fissé %1 a %2", "VARIABLES_SET_TOOLTIP": "Fissé costa variàbil ugual al valor d'imission.", "VARIABLES_SET_CREATE_GET": "<PERSON><PERSON><PERSON> 'oten-e %1'", "PROCEDURES_DEFNORETURN_TITLE": "a", "PROCEDURES_DEFNORETURN_PROCEDURE": "fé cheic<PERSON>s", "PROCEDURES_BEFORE_PARAMS": "con:", "PROCEDURES_CALL_BEFORE_PARAMS": "con:", "PROCEDURES_DEFNORETURN_TOOLTIP": "A crea na fonsion sensa surtìa.", "PROCEDURES_DEFNORETURN_COMMENT": "Descrive sa fonsion...", "PROCEDURES_DEFRETURN_RETURN": "art<PERSON>", "PROCEDURES_DEFRETURN_TOOLTIP": "A crea na fonsion con na surtìa.", "PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON><PERSON><PERSON><PERSON> le diciairassion", "PROCEDURES_DEF_DUPLICATE_WARNING": "Atension: Costa fonsion a l'ha dij paràmeter duplicà.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Eseguì la fonsion '%1' definìa da l'utent.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Eseguì la fonsion '%1' definìa da l'utent e dovré sò a<PERSON>à.", "PROCEDURES_MUTATORCONTAINER_TITLE": "imission", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, gavé o riordiné j'imission ëd sa fonsion.", "PROCEDURES_MUTATORARG_TITLE": "nòm ëd l'imission:", "PROCEDURES_MUTATORARG_TOOLTIP": "Gionté n'imission a la fonsion.", "PROCEDURES_HIGHLIGHT_DEF": "Sot-ligné la definission dla fonsion", "PROCEDURES_CREATE_DO": "Creé '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Se un valor a l'é ver, ant<PERSON> smon-e un second valor.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Atension: Ës blòch a podria esse dovrà mach an na definission ëd fonsion.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "<PERSON>ì cheicòs...", "WORKSPACE_ARIA_LABEL": "Spassi ëd travaj ëd <PERSON>ly", "COLLAPSED_WARNINGS_WARNING": "<PERSON><PERSON> bl<PERSON><PERSON> sarà a conten-o dj'avertense.", "DIALOG_OK": "<PERSON><PERSON> <PERSON>", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}