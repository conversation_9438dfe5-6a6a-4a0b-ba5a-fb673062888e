[{"name": "clear()", "trans": ["clear()方法会移除Map对象中的所有元素。"]}, {"name": "delete()", "trans": [" delete() 方法用于移除 Map 对象中指定的元素。"]}, {"name": "entries()", "trans": ["entries() 方法返回一个新的包含 [key, value] 对的 Iterator 对象，返回的迭代器的迭代顺序与 Map 对象的插入顺序相同。"]}, {"name": "forEach()", "trans": ["forEach() 方法将会以插入顺序对 Map 对象中的每一个键值对执行一次参数中提供的回调函数。"]}, {"name": "get()", "trans": ["get() 方法返回某个 Map 对象中的一个指定元素。"]}, {"name": "has()", "trans": ["方法has() 返回一个bool值，用来表明map 中是否存在指定元素."]}, {"name": "keys()", "trans": ["keys() 返回一个新的 Iterator 对象。它包含按照顺序插入 Map 对象中每个元素的key值。"]}, {"name": "set()", "trans": ["set() 方法为 Map 对象添加或更新一个指定了键（key）和值（value）的（新）键值对。"]}, {"name": "values()", "trans": ["一个新的 Map 可迭代对象."]}, {"name": "add()", "trans": ["add() 方法用来向一个 Set 对象的末尾添加一个指定的值。"]}, {"name": "clear()", "trans": ["clear() 方法用来清空一个 Set 对象中的所有元素。"]}, {"name": "delete()", "trans": ["delete() 方法可以从一个 Set 对象中删除指定的元素。"]}, {"name": "entries()", "trans": ["entries() 方法返回一个新的迭代器对象 ，这个对象的元素是类似 [value, value] 形式的数组，value 是集合对象中的每个元素，迭代器对象元素的顺序即集合对象中元素插入的顺序。由于集合对象不像 Map 对象那样拥有 key，然而，为了与 Map 对象的 API 形式保持一致，故使得每一个 entry 的 key 和 value 都拥有相同的值，因而最终返回一个 [value, value] 形式的数组。"]}, {"name": "forEach()", "trans": ["forEach 方法会根据集合中元素的插入顺序，依次执行提供的回调函数。"]}, {"name": "has()", "trans": ["has() 方法返回一个布尔值来指示对应的值value是否存在Set对象中。"]}, {"name": "values()", "trans": ["values() 方法返回一个 Iterator  对象，该对象按照原Set 对象元素的插入顺序返回其所有元素。"]}]