{"@metadata": {"authors": ["Baonguyen21022003", "Dstream", "<PERSON><PERSON><PERSON>", "Leducthn", "Nguyenvanduocit", "<PERSON><PERSON><PERSON><PERSON>", "Qneutron", "SierraNguyen", "Withoutaname"]}, "VARIABLES_DEFAULT_NAME": "v<PERSON><PERSON>", "UNNAMED_KEY": "kh<PERSON>ng tên", "TODAY": "<PERSON><PERSON><PERSON> nay", "DUPLICATE_BLOCK": "Tạo Bản <PERSON>", "ADD_COMMENT": "<PERSON><PERSON><PERSON><PERSON>", "REMOVE_COMMENT": "Xóa Chú <PERSON>", "DUPLICATE_COMMENT": "<PERSON><PERSON><PERSON> luận trùng", "EXTERNAL_INPUTS": "Chỗ <PERSON><PERSON><PERSON>", "INLINE_INPUTS": "Chỗ Gắn <PERSON>", "DELETE_BLOCK": "<PERSON><PERSON><PERSON>", "DELETE_X_BLOCKS": "Xóa %1 Mảnh", "DELETE_ALL_BLOCKS": "Xóa hết %1 mảnh?", "CLEAN_UP": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lại c<PERSON>c k<PERSON>i", "COLLAPSE_BLOCK": "<PERSON><PERSON> Nhỏ <PERSON>", "COLLAPSE_ALL": "<PERSON><PERSON> Nhỏ <PERSON><PERSON>", "EXPAND_BLOCK": "Mở <PERSON><PERSON><PERSON>", "EXPAND_ALL": "Mở L<PERSON><PERSON>", "DISABLE_BLOCK": "Ngưng <PERSON>", "ENABLE_BLOCK": "<PERSON><PERSON><PERSON>", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON><PERSON>", "REDO": "<PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Thay giá trị thành:", "RENAME_VARIABLE": "Thay tên biến...", "RENAME_VARIABLE_TITLE": "Thay tên tất cả các biến \"%1\" thành:", "NEW_VARIABLE": "<PERSON><PERSON><PERSON>...", "NEW_STRING_VARIABLE": "Tạo một giá trị dạng chuỗi...", "NEW_NUMBER_VARIABLE": "<PERSON><PERSON>o một giá trị số...", "NEW_COLOUR_VARIABLE": "T<PERSON>o một giá trị màu sắc...", "NEW_VARIABLE_TYPE_TITLE": "<PERSON><PERSON><PERSON> giá trị mới:", "NEW_VARIABLE_TITLE": "Tên của biến mới:", "VARIABLE_ALREADY_EXISTS": "<PERSON><PERSON>t biến có tên '%1' đã tồn tại.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Giá trị '%1' đã tồn tại dưới dạng: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Xóa %1 lần dùng của giá trị '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "<PERSON><PERSON><PERSON><PERSON> thể xóa giá trị '%1' vì nó đư<PERSON><PERSON> bao gồm trong định nghĩa của chức năng '%2'", "DELETE_VARIABLE": "Xóa giá trị '%1'", "COLOUR_PICKER_HELPURL": "https://vi.wikipedia.org/wiki/M%C3%A0u_s%E1%BA%AFc", "COLOUR_PICKER_TOOLTIP": "<PERSON><PERSON><PERSON> một màu từ bảng màu.", "COLOUR_RANDOM_TITLE": "m<PERSON><PERSON> b<PERSON><PERSON>", "COLOUR_RANDOM_TOOLTIP": "<PERSON><PERSON><PERSON> một màu b<PERSON><PERSON> k<PERSON>.", "COLOUR_RGB_TITLE": "tạo màu từ", "COLOUR_RGB_RED": "màu đỏ", "COLOUR_RGB_GREEN": "màu xanh lá cây", "COLOUR_RGB_BLUE": "m<PERSON><PERSON> x<PERSON>h <PERSON>", "COLOUR_RGB_TOOLTIP": "T<PERSON><PERSON> màu từ ba màu: đỏ, xanh lá c<PERSON>, xanh dương với số lượng cụ thể.  Mỗi số phải có giá trị từ 0 đến 100.", "COLOUR_BLEND_TITLE": "pha", "COLOUR_BLEND_COLOUR1": "màu 1", "COLOUR_BLEND_COLOUR2": "màu 2", "COLOUR_BLEND_RATIO": "tỉ lệ", "COLOUR_BLEND_TOOLTIP": "<PERSON>a hai màu với nhau theo tỉ lệ (0 - 100).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "lặp lại %1 lần", "CONTROLS_REPEAT_INPUT_DO": "th<PERSON><PERSON> hi<PERSON>n", "CONTROLS_REPEAT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> hiện các lệnh vài lần.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "lặp lại trong khi", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "lặp lại cho đến khi", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "<PERSON><PERSON><PERSON> là điều kiện còn đúng, thì thực hiện các l<PERSON>nh.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "<PERSON><PERSON><PERSON> là điều kiện còn sai, thì thực hiện các lệnh.  Khi điều kiện đúng thì ngưng.", "CONTROLS_FOR_TOOLTIP": "Cho biến '%1' l<PERSON>y các giá trị từ số bắt đầu đến số kết thúc, đếm theo kho<PERSON>ng thời gian đã chỉ định và thực hiện các khối được chỉ định.", "CONTROLS_FOR_TITLE": "đếm theo %1 từ %2 đến %3 mỗi lần thêm %4", "CONTROLS_FOREACH_TITLE": "với mỗi thành phần %1 trong danh sách %2", "CONTROLS_FOREACH_TOOLTIP": "<PERSON>rong một danh s<PERSON>ch, <PERSON><PERSON><PERSON> từng thành phần, g<PERSON> v<PERSON><PERSON> bi<PERSON> \"%1\", r<PERSON><PERSON> thự<PERSON> hiện một số lệnh.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "tho<PERSON>t", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "sang lần lặp tiếp theo", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "<PERSON><PERSON><PERSON><PERSON> khỏi vòng lặp hiện tại.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Bỏ qua phần còn lại trong vòng lặp này, và sang lần lặp tiếp theo.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Chú ý: <PERSON><PERSON><PERSON> n<PERSON> chỉ có thế dùng trong các vòng lặp.", "CONTROLS_IF_TOOLTIP_1": "<PERSON><PERSON><PERSON> điều ki<PERSON> đ<PERSON>, thực hiện các l<PERSON>nh.", "CONTROLS_IF_TOOLTIP_2": "<PERSON><PERSON><PERSON> điều ki<PERSON> đ<PERSON>, thực hiện các lệnh đầu.  <PERSON><PERSON><PERSON>, thực hiện các lệnh sau.", "CONTROLS_IF_TOOLTIP_3": "Nếu điều kiện đúng, thực hiện các lệnh đầu.  <PERSON><PERSON><PERSON>, nếu điều kiện thứ hai đúng, thực hiện các lệnh thứ hai.", "CONTROLS_IF_TOOLTIP_4": "Nếu điều kiện đúng, thực hiện các lệnh đầu.  <PERSON><PERSON><PERSON>, nếu điều kiện thứ hai đúng, thực hiện các lệnh thứ hai.  Nếu không điều kiện nào đúng, thực hiện các lệnh cuối cùng.", "CONTROLS_IF_MSG_IF": "<PERSON><PERSON><PERSON>", "CONTROLS_IF_MSG_ELSEIF": "n<PERSON>u không nếu", "CONTROLS_IF_MSG_ELSE": "<PERSON><PERSON><PERSON> kh<PERSON>ng", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, bỏ, hoặc đổi thứ tự các mảnh con để tạo cấu trúc mới cho mảnh nếu.", "CONTROLS_IF_ELSEIF_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> một điều kiện vào mảnh nếu.", "CONTROLS_IF_ELSE_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng, khi không điều kiện nào đúng.", "IOS_OK": "Đồng ý", "IOS_CANCEL": "Hủy bỏ", "IOS_ERROR": "Lỗi", "IOS_PROCEDURES_INPUTS": "NHẬP VÀO", "IOS_PROCEDURES_ADD_INPUT": "+ Thêm đầu vào", "IOS_PROCEDURES_ALLOW_STATEMENTS": "<PERSON> phép báo cáo", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "<PERSON><PERSON><PERSON> n<PERSON> có ch<PERSON>a các input trùng nhau.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON>hê<PERSON> bi<PERSON>n", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON> tên", "IOS_VARIABLES_DELETE_BUTTON": "Xóa", "IOS_VARIABLES_VARIABLE_NAME": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_EMPTY_NAME_ERROR": "<PERSON><PERSON><PERSON> không thể không nhập tên biến.", "LOGIC_COMPARE_HELPURL": "https://vi.wikipedia.org/wiki/B%E1%BA%A5t_%C4%91%E1%BA%B3ng_th%E1%BB%A9c", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON>àn trả giá trị \"đúng\" (true) nếu giá trị hai đầu vào bằng nhau.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON>àn trả giá trị \"đúng\" (true) nếu giá trị hai đầu vào không bằng nhau.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON>àn trả giá trị \"đúng\" (true) nếu đầu vào thứ nhất nhỏ hơn đầu vào thứ hai.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON>àn trả giá trị \"đúng\" (true) nếu đầu vào thứ nhất nhỏ hơn hoặc bằng đầu vào thứ hai.", "LOGIC_COMPARE_TOOLTIP_GT": "<PERSON>àn trả giá trị \"đúng\" (true) nếu đầu vào thứ nhất lớn hơn đầu vào thứ hai.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON>àn trả giá trị \"đúng\" (true) nếu đầu vào thứ nhất lớn hơn hoặc bằng đầu vào thứ hai.", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON><PERSON> trả \"đúng\" (true) nếu cả hai đầu vào đều đúng.", "LOGIC_OPERATION_AND": "và", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON><PERSON><PERSON> tr<PERSON> \"đúng\" (true) nếu ít nhất một trong hai đầu vào đúng.", "LOGIC_OPERATION_OR": "hoặc", "LOGIC_NEGATE_TITLE": "không %1", "LOGIC_NEGATE_TOOLTIP": "<PERSON><PERSON><PERSON> trả \"đúng\" (true) nếu đầu vào sai.  <PERSON><PERSON>n trả \"sai\" (false) nếu đầu vào đúng.", "LOGIC_BOOLEAN_TRUE": "<PERSON><PERSON><PERSON>", "LOGIC_BOOLEAN_FALSE": "sai", "LOGIC_BOOLEAN_TOOLTIP": "Hoàn trả \"đúng\" hoặc \"sai\".", "LOGIC_NULL": "tr<PERSON><PERSON> kh<PERSON>ng", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON><PERSON> tr<PERSON> tr<PERSON>ng không.", "LOGIC_TERNARY_CONDITION": "kiểm tra", "LOGIC_TERNARY_IF_TRUE": "<PERSON><PERSON><PERSON>g", "LOGIC_TERNARY_IF_FALSE": "n<PERSON>u sai", "LOGIC_TERNARY_TOOLTIP": "<PERSON><PERSON><PERSON> tra điều kiện. Nếu điều kiện đúng, hoàn trả giá trị từ mệnh đề \"nếu đúng\" nếu không đúng, hoàn trả giá trị từ mệnh đề \"nếu sai\".", "MATH_NUMBER_HELPURL": "https://vi.wikipedia.org/wiki/S%E1%BB%91", "MATH_NUMBER_TOOLTIP": "<PERSON><PERSON><PERSON> con số.", "MATH_ARITHMETIC_HELPURL": "https://vi.wikipedia.org/wiki/S%E1%BB%91_h%E1%BB%8Dc", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON><PERSON><PERSON> tr<PERSON> tổng của hai con số.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "<PERSON><PERSON><PERSON> tr<PERSON> hiệu của hai con số.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "<PERSON><PERSON><PERSON> tr<PERSON> tích của hai con số.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "<PERSON><PERSON><PERSON> tr<PERSON> thương của hai con số.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Hoàn trả số lũy thừa với số thứ nhất là cơ số và số thứ hai là số mũ.", "MATH_SINGLE_HELPURL": "https://vi.wikipedia.org/wiki/C%C4%83n_b%E1%BA%ADc_hai", "MATH_SINGLE_OP_ROOT": "c<PERSON>n b<PERSON>t hai", "MATH_SINGLE_TOOLTIP_ROOT": "<PERSON><PERSON><PERSON> trả căn bật hai của số đầu vào.", "MATH_SINGLE_OP_ABSOLUTE": "gi<PERSON> trị tuyệt đối", "MATH_SINGLE_TOOLTIP_ABS": "<PERSON><PERSON>n trả giá trị tuyệt đối của số đầu vào.", "MATH_SINGLE_TOOLTIP_NEG": "Đổi dấu của số đầu vào: âm thành dương và dương thành âm, và hoàn trả số mới.", "MATH_SINGLE_TOOLTIP_LN": "<PERSON><PERSON>n trả lôgarit tự nhiên của số đầu vào.", "MATH_SINGLE_TOOLTIP_LOG10": "<PERSON><PERSON>n trả lôgarit cơ số 10 của số đầu vào.", "MATH_SINGLE_TOOLTIP_EXP": "<PERSON><PERSON><PERSON> trả lũy thừa của số e với số mũ đầu vào.", "MATH_SINGLE_TOOLTIP_POW10": "<PERSON><PERSON><PERSON> trả lũy thừa của số 10 với số mũ đầu vào.", "MATH_TRIG_HELPURL": "https://vi.wikipedia.org/wiki/H%C3%A0m_l%C6%B0%E1%BB%A3ng_gi%C3%A1c", "MATH_TRIG_TOOLTIP_SIN": "<PERSON><PERSON><PERSON> tr<PERSON> Sin của một góc (theo độ).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON><PERSON> tr<PERSON> củ<PERSON> m<PERSON> g<PERSON> (theo độ).", "MATH_TRIG_TOOLTIP_TAN": "<PERSON><PERSON><PERSON> tr<PERSON> <PERSON> của mộ<PERSON> gó<PERSON> (theo độ).", "MATH_TRIG_TOOLTIP_ASIN": "<PERSON><PERSON><PERSON> tr<PERSON> <PERSON><PERSON> củ<PERSON> m<PERSON> g<PERSON> (theo độ).", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> của m<PERSON> g<PERSON> (theo độ).", "MATH_TRIG_TOOLTIP_ATAN": "<PERSON><PERSON><PERSON> tr<PERSON> của m<PERSON> gó<PERSON> (theo độ).", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant", "MATH_CONSTANT_TOOLTIP": "<PERSON><PERSON><PERSON> tr<PERSON> các đẳng số thường gặp: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (vô cực).", "MATH_IS_EVEN": "là số chẵn", "MATH_IS_ODD": "là số lẻ", "MATH_IS_PRIME": "là số nguyên tố", "MATH_IS_WHOLE": "là số nguyên", "MATH_IS_POSITIVE": "là số dương", "MATH_IS_NEGATIVE": "là số âm", "MATH_IS_DIVISIBLE_BY": "chia hết cho", "MATH_IS_TOOLTIP": "<PERSON><PERSON><PERSON> tra con số xem nó có phải là số chẵn, lẻ, nguy<PERSON><PERSON> tố, ng<PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>, <PERSON><PERSON>,  hay xem nó có chia hết cho số đầu vào hay không.  <PERSON><PERSON>n trả đúng hay sai.", "MATH_CHANGE_HELPURL": "https://vi.wikipedia.org/wiki/Ph%C3%A9p_c%E1%BB%99ng", "MATH_CHANGE_TITLE": "cộng vào %1 giá trị %2", "MATH_CHANGE_TOOLTIP": "Cộng số đầu vào vào biến \"%1\".", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "<PERSON><PERSON>m tròn lên hoặc tròn xuống số đầu vào.", "MATH_ROUND_OPERATOR_ROUND": "làm tròn", "MATH_ROUND_OPERATOR_ROUNDUP": "làm tròn lên", "MATH_ROUND_OPERATOR_ROUNDDOWN": "làm tròn xuống", "MATH_ONLIST_HELPURL": "", "MATH_ONLIST_OPERATOR_SUM": "tổng c<PERSON>a một danh s<PERSON>ch", "MATH_ONLIST_TOOLTIP_SUM": "<PERSON><PERSON><PERSON> trả tổng số của tất cả các số trong danh sách.", "MATH_ONLIST_OPERATOR_MIN": "số nhỏ nhất của một danh sách", "MATH_ONLIST_TOOLTIP_MIN": "<PERSON><PERSON><PERSON> trả số nhỏ nhất trong tất cả các số trong danh sách.", "MATH_ONLIST_OPERATOR_MAX": "số lớn nhât của một danh sách", "MATH_ONLIST_TOOLTIP_MAX": "<PERSON>àn trả số lớn nhất trong tất cả các số trong danh sách.", "MATH_ONLIST_OPERATOR_AVERAGE": "giá trị trung bình của một danh sách", "MATH_ONLIST_TOOLTIP_AVERAGE": "<PERSON><PERSON>n trả giá trị trung bình từ của danh sách số.", "MATH_ONLIST_OPERATOR_MEDIAN": "số trung vị của một danh sách", "MATH_ONLIST_TOOLTIP_MEDIAN": "<PERSON><PERSON><PERSON> trả số trung vị của danh sách số.", "MATH_ONLIST_OPERATOR_MODE": "các mode của một danh sách", "MATH_ONLIST_TOOLTIP_MODE": "<PERSON><PERSON><PERSON> tr<PERSON> các số có mặt nhiều nhất trong danh sách.", "MATH_ONLIST_OPERATOR_STD_DEV": "<PERSON><PERSON> lệch chuẩn của một danh sách", "MATH_ONLIST_TOOLTIP_STD_DEV": "<PERSON><PERSON><PERSON> trả độ lệch chuẩn của danh sách số.", "MATH_ONLIST_OPERATOR_RANDOM": "một số bất kỳ của một danh sách", "MATH_ONLIST_TOOLTIP_RANDOM": "<PERSON><PERSON><PERSON> trả một số bất kỳ từ các số trong danh sách.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "số dư của %1 ÷ %2", "MATH_MODULO_TOOLTIP": "<PERSON>a số thứ nhất cho số thứ hai rồi hoàn trả số dư từ.", "MATH_CONSTRAIN_TITLE": "giới hạn %1 không dưới %2 không hơn %3", "MATH_CONSTRAIN_TOOLTIP": "G<PERSON>ới hạn số đầu vào để không dưới số thứ nhất và không hơn số thứ hai.", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "Một số nguyên bất kỳ từ %1 đến %2", "MATH_RANDOM_INT_TOOLTIP": "<PERSON><PERSON>n trả một số nguyên bất kỳ lớn hơn hoặc bằng số đầu và nhỏ hơn hoặc bằng số sau.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "ph<PERSON> số bất kỳ", "MATH_RANDOM_FLOAT_TOOLTIP": "Hoàn trả một phân số bất kỳ không nhỏ hơn 0.0 và không lớn hơn 1.0.", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 của X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "T<PERSON><PERSON> về arctangent củ<PERSON> điểm (X, Y) trong khoảng từ -180 độ đến 180 độ.", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/string_(computer_science)", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON><PERSON> ký tự, m<PERSON><PERSON> từ, hay một dòng.", "TEXT_JOIN_HELPURL": "", "TEXT_JOIN_TITLE_CREATEWITH": "t<PERSON>o văn bản từ", "TEXT_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON> một văn bản từ các thành phần.", "TEXT_CREATE_JOIN_TITLE_JOIN": "k<PERSON><PERSON>", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, bỏ, hoặc sắp xếp lại các thành phần để tạo dựng mảnh văn bản này.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "thêm vật mới vào văn bản.", "TEXT_APPEND_TITLE": "ở cuối %1 thêm văn bản %2", "TEXT_APPEND_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> một mảng văn bản vào biến \"%1\".", "TEXT_LENGTH_TITLE": "độ dài của %1", "TEXT_LENGTH_TOOLTIP": "Hoàn trả số lượng ký tự (kể cả khoảng trắng) trong văn bản đầu vào.", "TEXT_ISEMPTY_TITLE": "%1 tr<PERSON>ng không", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON> trả “đúng nếu văn bản không có ký tự nào.", "TEXT_INDEXOF_TOOLTIP": "Hoàn trả vị trí xuất hiện đầu/cuối của văn bản thứ nhất trong văn bản thứ hai.  <PERSON><PERSON><PERSON> không tìm thấy thì hoàn trả số %1.", "TEXT_INDEXOF_TITLE": "trong văn bản %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "tìm sự có mặt đầu tiên của", "TEXT_INDEXOF_OPERATOR_LAST": "tìm sự có mặt cuối cùng của", "TEXT_CHARAT_TITLE": "trong văn bản %1 %2", "TEXT_CHARAT_FROM_START": "l<PERSON>y ký tự thứ", "TEXT_CHARAT_FROM_END": "l<PERSON>y từ phía cuối, ký tự thứ", "TEXT_CHARAT_FIRST": "l<PERSON>y ký tự đầu tiên", "TEXT_CHARAT_LAST": "l<PERSON>y ký tự cuối cùng", "TEXT_CHARAT_RANDOM": "l<PERSON><PERSON> ký tự b<PERSON>t kỳ", "TEXT_CHARAT_TOOLTIP": "<PERSON><PERSON><PERSON> trả ký tự ở vị trí đặt ra.", "TEXT_GET_SUBSTRING_TOOLTIP": "<PERSON><PERSON><PERSON> trả một mảng ký tự ấn định từ trong văn bản.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "trong văn bản", "TEXT_GET_SUBSTRING_START_FROM_START": "l<PERSON>y từ ký tự thứ", "TEXT_GET_SUBSTRING_START_FROM_END": "l<PERSON>y từ phía cuối, ký tự thứ", "TEXT_GET_SUBSTRING_START_FIRST": "l<PERSON>y từ ký tự đầu tiên", "TEXT_GET_SUBSTRING_END_FROM_START": "<PERSON><PERSON><PERSON> ký tự thứ", "TEXT_GET_SUBSTRING_END_FROM_END": "đến từ phía cuối, ký tự thứ", "TEXT_GET_SUBSTRING_END_LAST": "đ<PERSON>n ký tự cuối cùng", "TEXT_CHANGECASE_TOOLTIP": "Hoàn trả văn bản sau khi chuyển đổi chữ in hoa hay thường.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "thành CHỮ IN HOA", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "thành chữ thường", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "thà<PERSON> In Đầu Mỗi Từ", "TEXT_TRIM_TOOLTIP": "<PERSON><PERSON>n trả bản sao của văn bản sau khi xóa khoảng trắng từ một hoặc hai bên.", "TEXT_TRIM_OPERATOR_BOTH": "c<PERSON>t c<PERSON>c không gian từ cả hai mặt của", "TEXT_TRIM_OPERATOR_LEFT": "c<PERSON>t c<PERSON>c không gian từ bên trái của", "TEXT_TRIM_OPERATOR_RIGHT": "c<PERSON>t c<PERSON>c không gian từ bên phải của", "TEXT_PRINT_TITLE": "in lên màng hình %1", "TEXT_PRINT_TOOLTIP": "In ra màng hình một văn bản, con số, hay một giá trị đầu vào k<PERSON>.", "TEXT_PROMPT_TYPE_TEXT": "Xin người dùng nhập vào văn bản với dòng hướng dẫn", "TEXT_PROMPT_TYPE_NUMBER": "<PERSON>n người dùng nhập vào con số với dòng hướng dẫn", "TEXT_PROMPT_TOOLTIP_NUMBER": "<PERSON><PERSON> ng<PERSON><PERSON>i dùng nhập vào một con số.", "TEXT_PROMPT_TOOLTIP_TEXT": "<PERSON><PERSON> ngư<PERSON>i dùng nhập vào một văn bản.", "TEXT_COUNT_MESSAGE0": "đến %1 trong %2", "TEXT_COUNT_TOOLTIP": "<PERSON><PERSON><PERSON> số lần một đoạn văn bản xuất hiện trong một đoạn văn bản k<PERSON>.", "TEXT_REPLACE_MESSAGE0": "thay thế %1 bằng %2 trong %3", "TEXT_REPLACE_TOOLTIP": "<PERSON>hay thế tất cả các lần xuất hiện của văn bản bằng văn bản khác.", "TEXT_REVERSE_MESSAGE0": "đ<PERSON>o ngượ<PERSON> %1", "TEXT_REVERSE_TOOLTIP": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> thứ tự của các chữ cái trong văn bản.", "LISTS_CREATE_EMPTY_TITLE": "tạo danh sách trống", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON><PERSON> tr<PERSON> một danh s<PERSON>ch, v<PERSON>i độ dài 0, <PERSON><PERSON><PERSON><PERSON> có thành tố nào cả", "LISTS_CREATE_WITH_TOOLTIP": "<PERSON><PERSON><PERSON> một danh sách bao gồm nhi<PERSON>u vậts, với một số lượ<PERSON> bất kỳ.", "LISTS_CREATE_WITH_INPUT_WITH": "t<PERSON>o danh s<PERSON>ch gồm", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "danh s<PERSON>ch", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, bỏ, hoặc sắp xếp lại các thành phần để tạo dựng mảnh danh sách này.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> vật v<PERSON><PERSON> danh s<PERSON>ch.", "LISTS_REPEAT_TOOLTIP": "<PERSON><PERSON><PERSON> danh sách gồm một số lượng vật nhất định với mỗi vật đều giống nhau.", "LISTS_REPEAT_TITLE": "tạo danh sách gồm một vật %1 lặp lại %2 lần", "LISTS_LENGTH_TITLE": "độ dài của %1", "LISTS_LENGTH_TOOLTIP": "<PERSON><PERSON><PERSON> trả độ dài của một danh sách.", "LISTS_ISEMPTY_TITLE": "%1 trống rỗng", "LISTS_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON> tr<PERSON> <PERSON>đúng\" nếu danh sách không có thành tử nào.", "LISTS_INLIST": "trong d<PERSON>h s<PERSON>ch", "LISTS_INDEX_OF_FIRST": "tìm sự có mặt đầu tiên của vật", "LISTS_INDEX_OF_LAST": "tìm sự có mặt cuối cùng của vật", "LISTS_INDEX_OF_TOOLTIP": "Hoàn trả vị trí xuất hiện đầu tiên/cuối cùng của vật trong danh sách. Nếu không tìm thấy thì hoàn trả số %1.", "LISTS_GET_INDEX_GET": "l<PERSON>y thành tố", "LISTS_GET_INDEX_GET_REMOVE": "l<PERSON>y và xóa thành tố", "LISTS_GET_INDEX_REMOVE": "x<PERSON>a thành tố", "LISTS_GET_INDEX_FROM_START": "thứ", "LISTS_GET_INDEX_FROM_END": "(đếm từ cuối) thứ", "LISTS_GET_INDEX_FIRST": "<PERSON><PERSON><PERSON> tiên", "LISTS_GET_INDEX_LAST": "cu<PERSON>i c<PERSON>ng", "LISTS_GET_INDEX_RANDOM": "b<PERSON><PERSON>", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 là thành tố đầu tiên.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 là thành tố cuối cùng.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "<PERSON><PERSON><PERSON> trả thành tố trong danh sách ở vị trí ấn định.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "<PERSON><PERSON><PERSON> trả thành tố đầu tiên trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "<PERSON><PERSON><PERSON> trả thành tố cuối cùng trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "<PERSON><PERSON><PERSON> trả một thành tố bất kỳ trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "<PERSON>àn trả và xóa thành tố trong danh sách ở vị trí ấn định.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "<PERSON><PERSON>n trả và xóa thành tố đầu tiên trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "<PERSON>àn trả và xóa thành tố cuối cùng trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Hoàn trả và xóa mộtthành tố bất kỳ trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "<PERSON><PERSON><PERSON> thành tố trong danh sách ở vị trí ấn định.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "<PERSON><PERSON><PERSON> thành tố đầu tiên trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "<PERSON><PERSON><PERSON> thành tố cuối cùng trong danh sách.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "<PERSON><PERSON><PERSON> thành tố bất kỳ trong danh sách.", "LISTS_SET_INDEX_SET": "đặt", "LISTS_SET_INDEX_INSERT": "ch<PERSON>n vào vị trí", "LISTS_SET_INDEX_INPUT_TO": "gi<PERSON> trị", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Đặt giá trị của thành tố ở vị trí ấn định trong một danh sách.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Đặt giá trị của thành tố đầu tiên trong danh sách.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Đặt giá trị của thành tố cuối cùng trong danh sách.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Đặt giá trị của thành tố ngẫu nhiên trong danh sách.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "<PERSON><PERSON><PERSON> vật v<PERSON>o danh sách theo vị trí ấn định.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "<PERSON><PERSON><PERSON> vật vào đầu danh s<PERSON>ch.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "<PERSON><PERSON><PERSON> thêm vật vào cuối danh s<PERSON>ch.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "<PERSON><PERSON><PERSON> chèn vật vào danh sách ở vị trí ngẫu nhiên.", "LISTS_GET_SUBLIST_START_FROM_START": "l<PERSON><PERSON> một danh sách con từ vị trí thứ", "LISTS_GET_SUBLIST_START_FROM_END": "l<PERSON>y một danh sách con từ (đếm từ cuối) từ vị trí thứ", "LISTS_GET_SUBLIST_START_FIRST": "l<PERSON><PERSON> một danh sách con từ đầu tiên", "LISTS_GET_SUBLIST_END_FROM_START": "<PERSON><PERSON><PERSON> thứ", "LISTS_GET_SUBLIST_END_FROM_END": "đến (đếm từ cuối) thứ", "LISTS_GET_SUBLIST_END_LAST": "<PERSON><PERSON>n cu<PERSON>i cùng", "LISTS_GET_SUBLIST_TOOLTIP": "<PERSON><PERSON><PERSON> một mảng của danh sách này để tạo danh sách con.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sắp xếp %1 %2 %3", "LISTS_SORT_TOOLTIP": "<PERSON><PERSON><PERSON> xếp một bản sao của một danh sách.", "LISTS_SORT_ORDER_ASCENDING": "t<PERSON><PERSON> d<PERSON>n", "LISTS_SORT_ORDER_DESCENDING": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_NUMERIC": "số", "LISTS_SORT_TYPE_TEXT": "chữ cái", "LISTS_SORT_TYPE_IGNORECASE": "chữ cái, kh<PERSON>ng phân biệt hoa/thư<PERSON><PERSON>", "LISTS_SPLIT_LIST_FROM_TEXT": "tạo danh sách từ văn bản", "LISTS_SPLIT_TEXT_FROM_LIST": "t<PERSON>o văn bản từ danh sách", "LISTS_SPLIT_WITH_DELIMITER": "với ký tự phân cách", "LISTS_SPLIT_TOOLTIP_SPLIT": "<PERSON><PERSON><PERSON> văn bản thành một danh sách các văn bản nhỏ, ngắt bởi các ký tự phân cách.", "LISTS_SPLIT_TOOLTIP_JOIN": "<PERSON><PERSON><PERSON> kết một danh sách các văn bản vào một văn bản, cách nhau bằng dấu phân cách.", "LISTS_REVERSE_MESSAGE0": "đ<PERSON>o ngượ<PERSON> %1", "LISTS_REVERSE_TOOLTIP": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> bản sao chép của một danh sách.", "VARIABLES_GET_TOOLTIP": "<PERSON><PERSON><PERSON> trả giá trị của.", "VARIABLES_GET_CREATE_SET": "Tạo mảnh \"đặt vào %1\"", "VARIABLES_SET": "cho %1 bằng %2", "VARIABLES_SET_TOOLTIP": "Đặt giá trị của biến này thành...", "VARIABLES_SET_CREATE_GET": "<PERSON><PERSON><PERSON> mảnh \"lấy %1\"", "PROCEDURES_DEFNORETURN_TITLE": "thủ tục để", "PROCEDURES_DEFNORETURN_PROCEDURE": "làm gì đó", "PROCEDURES_BEFORE_PARAMS": "với:", "PROCEDURES_CALL_BEFORE_PARAMS": "với:", "PROCEDURES_DEFNORETURN_DO": "", "PROCEDURES_DEFNORETURN_TOOLTIP": "<PERSON><PERSON><PERSON> thủ tục không có giá trị hoàn trả.", "PROCEDURES_DEFNORETURN_COMMENT": "<PERSON><PERSON> tả hàm này...", "PROCEDURES_DEFRETURN_RETURN": "ho<PERSON>n trả", "PROCEDURES_DEFRETURN_TOOLTIP": "<PERSON><PERSON><PERSON> thủ tục có giá trị hoàn trả.", "PROCEDURES_ALLOW_STATEMENTS": "cho phép báo cáo", "PROCEDURES_DEF_DUPLICATE_WARNING": "Chú ý: <PERSON><PERSON><PERSON> tục này có lặp lại tên các tham số.", "PROCEDURES_CALLNORETURN_HELPURL": "https://vi.wikipedia.org/wiki/Ch%C6%B0%C6%A1ng_tr%C3%ACnh_con", "PROCEDURES_CALLNORETURN_TOOLTIP": "<PERSON><PERSON><PERSON> một thủ tục không có giá trị hoàn trả.", "PROCEDURES_CALLRETURN_HELPURL": "https://vi.wikipedia.org/wiki/Ch%C6%B0%C6%A1ng_tr%C3%ACnh_con", "PROCEDURES_CALLRETURN_TOOLTIP": "<PERSON><PERSON><PERSON> một thủ tục có giá trị hoàn trả.", "PROCEDURES_MUTATORCONTAINER_TITLE": "các tham số", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, xóa hoặc sắp xếp lại các đầu vào cho hàm này.", "PROCEDURES_MUTATORARG_TITLE": "biến:", "PROCEDURES_MUTATORARG_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> một đầu vào cho hàm.", "PROCEDURES_HIGHLIGHT_DEF": "<PERSON><PERSON><PERSON> n<PERSON> bật thủ tục", "PROCEDURES_CREATE_DO": "<PERSON><PERSON><PERSON> m<PERSON> \"thực hiện %1\"", "PROCEDURES_IFRETURN_TOOLTIP": "<PERSON><PERSON> điều kiện đúng thì hoàn trả một giá trị.", "PROCEDURES_IFRETURN_WARNING": "Chú ý: <PERSON><PERSON><PERSON> nà<PERSON> chỉ có thể dùng trong một thủ tục.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "<PERSON><PERSON><PERSON> nói gì đó...", "WORKSPACE_ARIA_LABEL": "<PERSON><PERSON><PERSON><PERSON> gian làm v<PERSON><PERSON><PERSON>", "COLLAPSED_WARNINGS_WARNING": "<PERSON><PERSON><PERSON><PERSON><PERSON> bị sập có chứa cảnh báo.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}