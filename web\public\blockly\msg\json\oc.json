{"@metadata": {"authors": ["Cedric31"]}, "VARIABLES_DEFAULT_NAME": "element", "TODAY": "<PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "Duplicar", "ADD_COMMENT": "Apondre un comentari", "REMOVE_COMMENT": "Suprimir un comentari", "EXTERNAL_INPUTS": "Entradas extèrnas", "INLINE_INPUTS": "Entradas en linha", "DELETE_BLOCK": "<PERSON><PERSON><PERSON><PERSON> lo blòt", "DELETE_X_BLOCKS": "Suprimir %1 blòts", "DELETE_ALL_BLOCKS": "Sup<PERSON>ir totes los %1 blòts ?", "CLEAN_UP": "Netejar los blòts", "COLLAPSE_BLOCK": "Redusir lo blòt", "COLLAPSE_ALL": "Redusir los blòts", "EXPAND_BLOCK": "Desvolopar lo blòt", "EXPAND_ALL": "Desvolopar los blòts", "DISABLE_BLOCK": "Desactivar lo blòt", "ENABLE_BLOCK": "Activar lo blòt", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON><PERSON>", "REDO": "<PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Modificar la valor :", "RENAME_VARIABLE": "Renomenar la variabla…", "RENAME_VARIABLE_TITLE": "Renomenar totas las variablas « %1 » a :", "NEW_VARIABLE": "Crear una variabla...", "NEW_STRING_VARIABLE": "<PERSON><PERSON>r una variabla cadena…", "NEW_NUMBER_VARIABLE": "<PERSON><PERSON>r una variabla nombre…", "NEW_COLOUR_VARIABLE": "Crear una variabla color…", "NEW_VARIABLE_TYPE_TITLE": "Novèl tipe de variabla :", "NEW_VARIABLE_TITLE": "Nom de la novèla variabla :", "VARIABLE_ALREADY_EXISTS": "Existís ja una variabla nomenada \"%1\".", "DELETE_VARIABLE": "Suprimir la variabla '%1'", "COLOUR_PICKER_HELPURL": "https://oc.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Causir una color dins la paleta.", "COLOUR_RANDOM_TITLE": "color aleatòria", "COLOUR_RANDOM_TOOLTIP": "Causir una color a l'azard.", "COLOUR_RGB_TITLE": "coloriar amb", "COLOUR_RGB_RED": "roge", "COLOUR_RGB_GREEN": "verd", "COLOUR_RGB_BLUE": "blau", "COLOUR_BLEND_TITLE": "mesclar", "COLOUR_BLEND_COLOUR1": "color 1", "COLOUR_BLEND_COLOUR2": "color 2", "COLOUR_BLEND_RATIO": "ratio", "CONTROLS_REPEAT_HELPURL": "https://oc.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "repetir %1 còps", "CONTROLS_REPEAT_INPUT_DO": "far", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "repetir tant que", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "repetir fins a", "CONTROLS_FOR_TITLE": "comptar amb %1 de %2 a %3 per %4", "CONTROLS_FOREACH_TITLE": "per cada element %1 dins la lista %2", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "quitar la bocla", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "passar a l’iteracion de bocla seguenta", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Sortir de la bocla englobanta.", "CONTROLS_IF_MSG_IF": "se", "CONTROLS_IF_MSG_ELSEIF": "siquenon se", "CONTROLS_IF_MSG_ELSE": "sique<PERSON>", "CONTROLS_IF_ELSEIF_TOOLTIP": "Apondre una condicion al blòt 'se'.", "IOS_OK": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "Error", "IOS_PROCEDURES_INPUTS": "ENTRADAS", "IOS_PROCEDURES_ADD_INPUT": "+ Apondre una entrada", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Òrdres autorizats", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Aquesta foncion a d'entradas duplicadas.", "IOS_VARIABLES_ADD_VARIABLE": "+ Apondre una variabla", "IOS_VARIABLES_ADD_BUTTON": "Apondre", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_VARIABLE_NAME": "Nom de la variabla", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Podètz pas utilizar un nom de variabla void.", "LOGIC_COMPARE_HELPURL": "https://fr.wikipedia.org/wiki/Inegalite_(mathematiques)", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON><PERSON> verai se las doas entradas son egalas.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON><PERSON> verai se las doas entradas son diferentas.", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON><PERSON> verai se las doas entradas son vertadi<PERSON><PERSON>.", "LOGIC_OPERATION_AND": "e", "LOGIC_OPERATION_OR": "o", "LOGIC_NEGATE_TITLE": "pas %1", "LOGIC_BOOLEAN_TRUE": "verai", "LOGIC_BOOLEAN_FALSE": "fals", "LOGIC_BOOLEAN_TOOLTIP": "Ren<PERSON> siá verai siá fals.", "LOGIC_NULL": "nul", "LOGIC_NULL_TOOLTIP": "Renvia nul.", "LOGIC_TERNARY_CONDITION": "t<PERSON>t", "LOGIC_TERNARY_IF_TRUE": "se verai", "LOGIC_TERNARY_IF_FALSE": "se fals", "MATH_NUMBER_HELPURL": "https://oc.wikipedia.org/wiki/Nombre", "MATH_NUMBER_TOOLTIP": "Un nombre.", "MATH_ARITHMETIC_HELPURL": "https://oc.wikipedia.org/wiki/Aritmetica", "MATH_ARITHMETIC_TOOLTIP_ADD": "Renvia la soma dels dos nombres.", "MATH_SINGLE_HELPURL": "https://fr.wikipedia.org/wiki/<PERSON><PERSON>_carree", "MATH_SINGLE_OP_ROOT": "r<PERSON><PERSON>", "MATH_SINGLE_TOOLTIP_ROOT": "Ren<PERSON> la raiç carrada d’un nombre.", "MATH_SINGLE_OP_ABSOLUTE": "absolut", "MATH_SINGLE_TOOLTIP_ABS": "<PERSON>via la valor absoluda d’un nombre.", "MATH_SINGLE_TOOLTIP_NEG": "Renvia l’opausat d’un nombre", "MATH_SINGLE_TOOLTIP_LN": "Renvia lo logaritme natural d’un nombre.", "MATH_SINGLE_TOOLTIP_LOG10": "Renvia lo logaritme decimal d’un nombre.", "MATH_SINGLE_TOOLTIP_EXP": "Renvia a la poténcia d’un nombre.", "MATH_SINGLE_TOOLTIP_POW10": "Renvia 10 a la poténcia d’un nombre.", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "<PERSON>via lo sinus d’un angle en grases (pas en radians).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON> lo cosinus d’un angle en grases (pas en radians).", "MATH_TRIG_TOOLTIP_TAN": "Renvia la tangenta d’un angle en grases (pas en radians).", "MATH_TRIG_TOOLTIP_ASIN": "<PERSON><PERSON> l’arcsinus d’un nombre.", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON> l’arccosinus d’un nombre.", "MATH_TRIG_TOOLTIP_ATAN": "<PERSON><PERSON> l’arctangenta d’un nombre.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant", "MATH_IS_EVEN": "es par", "MATH_IS_ODD": "es impar", "MATH_IS_PRIME": "es primièr", "MATH_IS_WHOLE": "es entièr", "MATH_IS_POSITIVE": "es positiu", "MATH_IS_NEGATIVE": "es negatiu", "MATH_IS_DIVISIBLE_BY": "es devesible per", "MATH_CHANGE_TITLE": "incrementar %1 per %2", "MATH_ROUND_OPERATOR_ROUND": "arredondir", "MATH_ROUND_OPERATOR_ROUNDUP": "arredondir al superior", "MATH_ROUND_OPERATOR_ROUNDDOWN": "arredondir a l’inferior", "MATH_ONLIST_OPERATOR_SUM": "soma de la lista", "MATH_ONLIST_OPERATOR_MIN": "minimum de la lista", "MATH_ONLIST_OPERATOR_MAX": "maximum de la lista", "MATH_ONLIST_OPERATOR_AVERAGE": "mejana de la lista", "MATH_ONLIST_OPERATOR_MEDIAN": "mediana de la lista", "MATH_ONLIST_OPERATOR_MODE": "majoritaris de la lista", "MATH_ONLIST_OPERATOR_STD_DEV": "escart-tipe de la lista", "MATH_ONLIST_OPERATOR_RANDOM": "element aleatòri de lista", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "residú de %1 ÷ %2", "MATH_CONSTRAIN_TITLE": "constrénher %1 entre %2 e %3", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "fraccion aleatòria", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "Una letra, un mot o una linha de tèxte.", "TEXT_JOIN_TITLE_CREATEWITH": "crear un tèxte amb", "TEXT_CREATE_JOIN_TITLE_JOIN": "<PERSON><PERSON><PERSON>", "TEXT_APPEND_TITLE": "a %1 apondre lo tèxte %2", "TEXT_LENGTH_TITLE": "longor de %1", "TEXT_ISEMPTY_TITLE": "%1 es void", "TEXT_INDEXOF_TITLE": "dins lo tèxte %1 %2 %3", "TEXT_CHARAT_TITLE": "dins lo tèxte %1 %2", "TEXT_CHARAT_FROM_START": "obténer la letra #", "TEXT_CHARAT_FROM_END": "obténer la letra # dempuèi la fin", "TEXT_CHARAT_FIRST": "obténer la primièra letra", "TEXT_CHARAT_LAST": "obténer la darrièra letra", "TEXT_CHARAT_RANDOM": "obténer una letra a l'azard", "TEXT_CHARAT_TOOLTIP": "Renvia la letra a la posicion indicada.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "dins lo tèxte", "TEXT_GET_SUBSTRING_END_FROM_START": "fins a la letra #", "TEXT_GET_SUBSTRING_END_LAST": "fins a la darrièra letra", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "en MAJUSCULAS", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "en minusculas", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "en Majuscula Al Començament De Cada Mot", "TEXT_TRIM_OPERATOR_BOTH": "suprimir los espacis dels dos costats", "TEXT_PRINT_TITLE": "afichar %1", "TEXT_COUNT_MESSAGE0": "nombre %1 sus %2", "TEXT_REVERSE_MESSAGE0": "inversar %1", "LISTS_CREATE_EMPTY_TITLE": "crear una lista voida", "LISTS_CREATE_WITH_INPUT_WITH": "crear una lista amb", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Apondre un element a la lista.", "LISTS_LENGTH_TITLE": "longitud de %1", "LISTS_ISEMPTY_TITLE": "%1 es voida", "LISTS_INLIST": "dins la lista", "LISTS_GET_INDEX_GET": "obt<PERSON>er", "LISTS_GET_INDEX_GET_REMOVE": "obténer e suprimir", "LISTS_GET_INDEX_REMOVE": "suprimit", "LISTS_GET_INDEX_FROM_END": "# dempuèi la fin", "LISTS_GET_INDEX_FIRST": "prim<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_LAST": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_RANDOM": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 es lo primièr element.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 es lo darrièr element.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Renvia l'element de la posicion especificada dins la lista.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Renvia lo primièr element dins una lista.", "LISTS_SET_INDEX_SET": "metre", "LISTS_SET_INDEX_INSERT": "inserir en", "LISTS_SET_INDEX_INPUT_TO": "coma", "LISTS_GET_SUBLIST_END_FROM_START": "fins a #", "LISTS_GET_SUBLIST_END_FROM_END": "fins a # dempuèi la fin", "LISTS_GET_SUBLIST_END_LAST": "fins a la fin", "LISTS_SORT_TITLE": "triar %1 %2 %3", "LISTS_SORT_ORDER_ASCENDING": "creissent", "LISTS_SORT_ORDER_DESCENDING": "descreissent", "LISTS_SORT_TYPE_NUMERIC": "numeric", "LISTS_SORT_TYPE_TEXT": "alfabetic", "LISTS_SPLIT_WITH_DELIMITER": "amb lo separador", "LISTS_REVERSE_MESSAGE0": "inversar %1", "LISTS_REVERSE_TOOLTIP": "Inversar la còpia d’una lista.", "VARIABLES_GET_CREATE_SET": "Crear 'fixar %1'", "VARIABLES_SET": "fixar %1 a %2", "VARIABLES_SET_CREATE_GET": "Crear 'obténer %1'", "PROCEDURES_DEFNORETURN_TITLE": "a", "PROCEDURES_DEFNORETURN_PROCEDURE": "far quicòm", "PROCEDURES_BEFORE_PARAMS": "amb :", "PROCEDURES_CALL_BEFORE_PARAMS": "amb :", "PROCEDURES_DEFRETURN_RETURN": "retorn", "PROCEDURES_ALLOW_STATEMENTS": "autorizar los òrdres", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_MUTATORCONTAINER_TITLE": "entradas", "PROCEDURES_MUTATORARG_TITLE": "nom de l’entrada :", "PROCEDURES_CREATE_DO": "Crear '%1'", "DIALOG_OK": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}