package org.jeecg.modules.teaching.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.jeecg.modules.teaching.service.QuestionContentService;
import java.util.HashMap;
import org.jeecg.modules.teaching.util.QuestionImportUtil;
import org.jeecg.modules.teaching.util.QuestionImportUtil.SimilarQuestion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.*;
import java.util.stream.Collectors;
import org.jeecg.common.system.vo.LoginUser;
import java.util.Collection;

/**
 * @Description: 考试题目管理
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@Api(tags="题库管理")
@RestController
@RequestMapping("/teaching/examSystem/problemManage")
@Slf4j
public class ExamQuestionController extends JeecgController<ExamQuestion, IExamQuestionService> {
    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    private QuestionContentService questionContentService;
    
    /**
     * 分页列表查询
     */
    @AutoLog(value = "题库管理-分页列表查询")
    @ApiOperation(value="题库管理-分页列表查询", notes="题库管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExamQuestion examQuestion,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  @RequestParam(name="sortField", required=false) String sortField,
                                  @RequestParam(name="sortOrder", required=false) String sortOrder,
                                  HttpServletRequest req) {
        // 获取原始查询参数
        String title = req.getParameter("title");
        
        // 创建一个新的不包含title的查询对象
        ExamQuestion queryObj = new ExamQuestion();
        if(examQuestion != null) {
            queryObj.setQuestionType(examQuestion.getQuestionType());
            queryObj.setSubject(examQuestion.getSubject());
            queryObj.setLevel(examQuestion.getLevel());
            queryObj.setDifficulty(examQuestion.getDifficulty());
            queryObj.setAuthor(examQuestion.getAuthor());
            // 不设置title，后面会单独处理
        }
        
        // 初始化查询条件（不包含title的特殊处理）
        QueryWrapper<ExamQuestion> queryWrapper = QueryGenerator.initQueryWrapper(queryObj, req.getParameterMap());
        
        // 对标题进行特殊处理，增强模糊查询和特殊字符处理
        if (title != null && !title.isEmpty()) {
            // 使用参数绑定方式处理特殊字符，并增强模糊查询能力
            queryWrapper.apply("INSTR(LOWER(title), LOWER({0})) > 0", title);
        }

        // 处理排序
        if (sortField != null && sortOrder != null) {
            log.info("题库管理排序 - 原始字段: {}, 方向: {}", sortField, sortOrder);

            // 将前端的驼峰命名转换为数据库的下划线命名
            String dbFieldName = convertToDbFieldName(sortField);
            log.info("题库管理排序 - 转换后字段: {}", dbFieldName);

            if ("asc".equals(sortOrder)) {
                queryWrapper.orderByAsc(dbFieldName);
            } else if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(dbFieldName);
            }
        } else {
            // 默认按创建时间降序排序
            queryWrapper.orderByDesc("create_time");
        }

        Page<ExamQuestion> page = new Page<ExamQuestion>(pageNo, pageSize);
        IPage<ExamQuestion> pageList = examQuestionService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 添加
     */
    @AutoLog(value = "题库管理-添加")
    @ApiOperation(value="题库管理-添加", notes="题库管理-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ExamQuestion examQuestion, HttpServletRequest request) {
        try {
            // 标准化级别格式
            if (examQuestion.getLevel() != null) {
                examQuestion.setLevel(standardizeLevel(examQuestion.getLevel()));
            }

            // 检查是否强制添加
            String forceAdd = request.getParameter("forceAdd");
            boolean isForceAdd = "true".equals(forceAdd);

            if (!isForceAdd) {
                // 进行重复检测
                Map<String, Object> duplicateCheckResult = examQuestionService.checkDuplicateBeforeAdd(examQuestion);

                if (duplicateCheckResult.containsKey("exactDuplicate") && (Boolean) duplicateCheckResult.get("exactDuplicate")) {
                    // 发现精确重复
                    int duplicateCount = (Integer) duplicateCheckResult.getOrDefault("exactCount", 1);
                    String message = String.format("题目添加失败：检测到 %d 个精确重复的题目！\n" +
                        "重复题目的标题、科目、级别和类型完全相同。\n" +
                        "建议：请修改题目内容或检查是否确实需要添加此题目。", duplicateCount);
                    return Result.error(message);
                }

                if (duplicateCheckResult.containsKey("similarQuestions")) {
                    @SuppressWarnings("unchecked")
                    List<Object> similarQuestions = (List<Object>) duplicateCheckResult.get("similarQuestions");
                    if (!similarQuestions.isEmpty()) {
                        // 发现相似题目，返回详细的警告信息供用户确认
                        Map<String, Object> warningResult = new HashMap<>();
                        warningResult.put("warning", true);

                        String message = String.format("⚠️ 检测到 %d 个相似题目！\n" +
                            "系统发现已存在内容相似的题目，建议您仔细检查。\n" +
                            "如果确认要添加此题目，请点击\"确认添加\"；\n" +
                            "如果是重复题目，请点击\"取消\"并修改内容。", similarQuestions.size());

                        warningResult.put("message", message);
                        warningResult.put("similarQuestions", similarQuestions);
                        warningResult.put("newQuestion", examQuestion);
                        Result<Map<String, Object>> result = new Result<>();
                        result.setSuccess(true);
                        result.setCode(200);
                        result.setResult(warningResult);
                        return result;
                    }
                }
            }

            // 没有重复，处理内容并保存
            questionContentService.processQuestionBeforeSave(examQuestion);
            examQuestionService.save(examQuestion);
            Result<String> result = new Result<>();
            result.setSuccess(true);
            result.setCode(200);
            result.setResult("添加成功！");
            return result;

        } catch (Exception e) {
            log.error("添加题目失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }
    
    /**
     * 编辑
     */
    @AutoLog(value = "题库管理-编辑")
    @ApiOperation(value="题库管理-编辑", notes="题库管理-编辑")
    @PutMapping(value = "/edit")
    public Result<String> edit(@RequestBody ExamQuestion examQuestion) {
        // 处理内容并更新
        questionContentService.processQuestionBeforeSave(examQuestion);
        examQuestionService.updateById(examQuestion);
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("编辑成功!");
        return result;
    }
    
    /**
     * 通过id删除
     */
    @AutoLog(value = "题库管理-通过id删除")
    @ApiOperation(value="题库管理-通过id删除", notes="题库管理-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        examQuestionService.removeById(id);
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("删除成功!");
        return result;
    }
    
    /**
     * 批量删除
     */
    @AutoLog(value = "题库管理-批量删除")
    @ApiOperation(value="题库管理-批量删除", notes="题库管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.examQuestionService.removeByIds(Arrays.asList(ids.split(",")));
        Result<String> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setResult("批量删除成功!");
        return result;
    }
    
    /**
     * 通过id查询
     */
    @AutoLog(value = "题库管理-通过id查询")
    @ApiOperation(value="题库管理-通过id查询", notes="题库管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ExamQuestion> queryById(@RequestParam(name="id",required=true) String id) {
        ExamQuestion examQuestion = examQuestionService.getById(id);
        Result<ExamQuestion> result = new Result<>();
        if(examQuestion==null) {
            result.setSuccess(false);
            result.setCode(500);
            result.setMessage("未找到对应数据");
            return result;
        }

        // 处理内容兼容性
        questionContentService.processQuestionAfterLoad(examQuestion);

        result.setSuccess(true);
        result.setCode(200);
        result.setResult(examQuestion);
        return result;
    }
    
    /**
     * 批量查询题目
     */
    @AutoLog(value = "题库管理-批量查询题目")
    @ApiOperation(value="题库管理-批量查询题目", notes="题库管理-批量查询题目")
    @GetMapping(value = "/queryBatch")
    public Result<?> queryBatch(@RequestParam(name="ids") String ids) {
        if (oConvertUtils.isEmpty(ids)) {
            return Result.error("参数不能为空");
        }
        String[] idArray = ids.split(",");
        Collection<ExamQuestion> questionCollection = examQuestionService.listByIds(Arrays.asList(idArray));
        List<ExamQuestion> questions = new ArrayList<>(questionCollection);
        
        // 只返回必要的字段信息，减少数据传输量
        List<Map<String, Object>> resultList = questions.stream().map(question -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", question.getId());
            map.put("title", question.getTitle());
            map.put("questionType", question.getQuestionType());
            map.put("subject", question.getSubject());
            map.put("level", question.getLevel());
            map.put("difficulty", question.getDifficulty());
            return map;
        }).collect(Collectors.toList());
        
        return Result.ok(resultList);
    }
    
    /**
     * 导入题目
     */
    @AutoLog(value = "题库管理-导入题目", paramLog = false)
    @ApiOperation(value="题库管理-导入题目", notes="从TXT文件导入题目，只从文件中获取元数据")
    @PostMapping(value = "/import")
    public Result<?> importQuestions(
        @RequestParam("file") MultipartFile file,
        @RequestParam(value = "author", required = false) String authorParam) {
    if (file.isEmpty()) {
        return Result.error("请选择要导入的文件");
    }
    
    String filename = file.getOriginalFilename();
    if (!oConvertUtils.isEmpty(filename) && !filename.toLowerCase().endsWith(".txt")) {
        return Result.error("请上传txt格式的文件");
    }
    
    try {
        log.info("开始导入题目，文件名: {}，大小: {} bytes", filename, file.getSize());
        
        // 创建空的元数据参数Map，不再接收前端传入的元数据
        Map<String, Object> metadata = new HashMap<>();
        
        // 如果author参数为空，则使用当前登录用户的用户名作为作者
        String author = authorParam;
        LoginUser loginUser = getCurrentUser();
        if (loginUser != null && (author == null || author.trim().isEmpty())) {
            // 使用当前登录用户的真实名称作为作者
            author = loginUser.getRealname();
            // 添加日志
            log.info("题目导入：使用当前登录用户 [{}] 作为作者", author);
        }
        
        // 设置作者参数
        if (author != null) {
            metadata.put("author", author);
        }
        
        // 记录日志，确认不使用表单元数据
        log.info("题目导入：仅从文件中获取元数据，用户提供的author参数: " + (author != null ? author : "未提供"));
        
        // 调用服务导入题目
        Map<String, Object> result = examQuestionService.importQuestionsFromFile(file, metadata);
        
        // 首先获取统计信息
        int totalParsed = (Integer) result.getOrDefault("total", 0);
        int successImported = (Integer) result.getOrDefault("success", 0);
        int similarCount = (Integer) result.getOrDefault("similar_count", 0);
        int skippedCount = totalParsed - successImported - similarCount;

        // 过滤和转换结果，避免直接暴露内部数据
        Map<String, Object> safeResult = new HashMap<>();

        // 添加统计信息
        safeResult.put("total", totalParsed);
        safeResult.put("success", successImported);
        safeResult.put("similarCount", similarCount);
        safeResult.put("skippedCount", skippedCount);

        // 如果有导入成功的题目，返回导入题目数量
        if (result.containsKey("imported")) {
            @SuppressWarnings("unchecked")
            List<ExamQuestion> imported = (List<ExamQuestion>) result.get("imported");
            List<Map<String, Object>> safeImported = new ArrayList<>();

            for (ExamQuestion question : imported) {
                Map<String, Object> safeQuestion = new HashMap<>();
                safeQuestion.put("id", question.getId());
                safeQuestion.put("title", question.getTitle());
                safeQuestion.put("questionType", question.getQuestionType());
                safeQuestion.put("subject", question.getSubject());
                safeQuestion.put("level", question.getLevel());
                safeQuestion.put("difficulty", question.getDifficulty());
                safeImported.add(safeQuestion);
            }

            safeResult.put("count", imported.size());
            safeResult.put("imported", safeImported);
        }
        
        // 处理相似题目列表，只保留关键信息
        if (result.containsKey("similar")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> similarList = (List<Map<String, Object>>) result.get("similar");
            List<Map<String, Object>> safeSimilar = new ArrayList<>();
            
            for (Map<String, Object> similar : similarList) {
                Map<String, Object> safeSimilarItem = new HashMap<>();
                ExamQuestion newQuestion = (ExamQuestion) similar.get("newQuestion");
                
                Map<String, Object> safeNewQuestion = new HashMap<>();
                safeNewQuestion.put("title", newQuestion.getTitle());
                safeNewQuestion.put("questionType", newQuestion.getQuestionType());
                safeNewQuestion.put("subject", newQuestion.getSubject());
                safeNewQuestion.put("level", newQuestion.getLevel());
                safeNewQuestion.put("difficulty", newQuestion.getDifficulty());
                
                safeSimilarItem.put("newQuestion", safeNewQuestion);
                
                @SuppressWarnings("unchecked")
                List<QuestionImportUtil.SimilarQuestion> similarQuestions =
                    (List<QuestionImportUtil.SimilarQuestion>) similar.get("similarQuestions");
                List<Map<String, Object>> safeSimilarQuestions = new ArrayList<>();
                
                for (QuestionImportUtil.SimilarQuestion sq : similarQuestions) {
                    Map<String, Object> safeSQ = new HashMap<>();
                    safeSQ.put("id", sq.getQuestion().getId());
                    safeSQ.put("title", sq.getQuestion().getTitle());
                    safeSQ.put("similarity", sq.getSimilarity());
                    safeSimilarQuestions.add(safeSQ);
                }
                
                safeSimilarItem.put("similarQuestions", safeSimilarQuestions);
                safeSimilar.add(safeSimilarItem);
            }
            
            safeResult.put("similar", safeSimilar);
        }
        
        log.debug("文件处理完成，准备返回结果");

        // 添加响应标识，明确告知前端操作已完成
        safeResult.put("status", "completed");
        safeResult.put("timestamp", System.currentTimeMillis());

        // 根据结果生成合适的消息
        String message;
        if (successImported > 0 && similarCount == 0 && skippedCount == 0) {
            message = String.format("🎉 题目导入成功！\n共导入 %d 个题目", successImported);
        } else if (successImported == 0 && (similarCount > 0 || skippedCount > 0)) {
            message = String.format("⚠️ 检测到重复题目！\n" +
                    "• 共解析：%d 个题目\n" +
                    "• 跳过重复：%d 个\n" +
                    "• 跳过相似：%d 个\n" +
                    "💡 所有题目都已存在",
                    totalParsed, skippedCount, similarCount);
        } else {
            message = String.format("📊 题目导入完成！\n" +
                    "• 共解析：%d 个题目\n" +
                    "• 成功导入：%d 个\n" +
                    "• 跳过重复：%d 个\n" +
                    "• 跳过相似：%d 个",
                    totalParsed, successImported, skippedCount, similarCount);
        }

        safeResult.put("message", message);

        return Result.ok(safeResult);
    } catch (Exception e) {
        log.error("导入题目失败", e);
        return Result.error("导入题目失败: " + e.getMessage());
    }
}

    /**
     * 预览题目导入
     */
    @AutoLog(value = "题库管理-预览题目导入", paramLog = false)
    @ApiOperation(value="题库管理-预览题目导入", notes="预览TXT文件导入，检测重复和相似题目")
    @PostMapping(value = "/preview")
    public Result<?> previewQuestions(
        @RequestParam("file") MultipartFile file,
        @RequestParam(value = "author", required = false) String authorParam) {

        if (file.isEmpty()) {
            return Result.error("请选择要预览的文件");
        }

        String filename = file.getOriginalFilename();
        if (!oConvertUtils.isEmpty(filename) && !filename.toLowerCase().endsWith(".txt")) {
            return Result.error("请上传txt格式的文件");
        }

        try {
            log.info("开始预览题目导入，文件名: {}，大小: {} bytes", filename, file.getSize());

            // 创建元数据参数Map
            Map<String, Object> metadata = new HashMap<>();

            // 处理作者参数
            String author = authorParam;
            LoginUser loginUser = getCurrentUser();
            if (loginUser != null && (author == null || author.trim().isEmpty())) {
                author = loginUser.getRealname();
                log.info("题目预览：使用当前登录用户 [{}] 作为作者", author);
            }

            if (author != null) {
                metadata.put("author", author);
            }

            log.info("题目预览：仅从文件中获取元数据，用户提供的author参数: " + (author != null ? author : "未提供"));

            // 调用服务预览题目
            Map<String, Object> result = examQuestionService.previewQuestionsFromFile(file, metadata);

            return Result.ok(result);

        } catch (Exception e) {
            log.error("预览题目导入失败", e);
            return Result.error("预览失败: " + e.getMessage());
        }
    }

    /**
     * 检查题目相似度
     */
    @AutoLog(value = "题库管理-检查题目相似度")
    @ApiOperation(value="题库管理-检查题目相似度", notes="检查题目是否存在相似的")
    @PostMapping(value = "/checkSimilar")
    public Result<?> checkSimilarQuestions(@RequestBody ExamQuestion examQuestion) {
        List<SimilarQuestion> similarQuestions = examQuestionService.checkSimilarQuestions(examQuestion);
        return Result.ok(similarQuestions);
    }
    
    /**
     * 按条件筛选题目
     */
    @AutoLog(value = "题库管理-按条件筛选题目")
    @ApiOperation(value="题库管理-按条件筛选题目", notes="按科目、级别、题型、难度筛选题目")
    @GetMapping(value = "/filter")
    public Result<?> filterQuestions(
            @ApiParam(value = "科目") @RequestParam(required = false) String subject,
            @ApiParam(value = "级别") @RequestParam(required = false) String level,
            @ApiParam(value = "题型") @RequestParam(required = false) Integer questionType,
            @ApiParam(value = "难度") @RequestParam(required = false) Integer difficulty) {
            
        // 注意：无需在此处标准化级别，examQuestionService.getQuestionsByCondition内部已处理级别标准化
        List<ExamQuestion> questions = examQuestionService.getQuestionsByCondition(
                null, subject, level, questionType, difficulty);
        return Result.ok(questions);
    }
    
    /**
     * 导出题目
     */
    @AutoLog(value = "题库管理-导出题目")
    @ApiOperation(value="题库管理-导出题目", notes="导出题目为TXT格式")
    @GetMapping(value = "/export")
    public Result<?> exportQuestions(
            @ApiParam(value = "题目标题") @RequestParam(required = false) String title,
            @ApiParam(value = "科目") @RequestParam(required = false) String subject,
            @ApiParam(value = "级别") @RequestParam(required = false) String level,
            @ApiParam(value = "题型") @RequestParam(required = false) Integer questionType,
            @ApiParam(value = "难度") @RequestParam(required = false) Integer difficulty,
            HttpServletResponse response) {
            
        try {
            // 获取符合条件的题目
            // 注意：无需在此处标准化级别，examQuestionService.getQuestionsByCondition内部已处理级别标准化
            List<ExamQuestion> questions = examQuestionService.getQuestionsByCondition(
                    title, subject, level, questionType, difficulty);
                
            if (questions.isEmpty()) {
                return Result.error("没有符合条件的题目可供导出");
            }
            
            // 根据题目类型分组并导出
            StringBuilder content = new StringBuilder();
            
            // 分组导出单选题
            List<ExamQuestion> singleChoiceQuestions = questions.stream()
                    .filter(q -> q.getQuestionType() == 1)
                    .collect(java.util.stream.Collectors.toList());
            
            if (!singleChoiceQuestions.isEmpty()) {
                content.append("【一、单选题】\n\n");
                for (int i = 0; i < singleChoiceQuestions.size(); i++) {
                    ExamQuestion question = singleChoiceQuestions.get(i);
                    content.append("【").append(i + 1).append(".】").append(question.getTitle()).append("\n");
                    
                    // 解析JSON内容获取选项和答案
                    com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(question.getContent());
                    List<String> options = jsonObject.getJSONArray("options").toJavaList(String.class);
                    String answer = jsonObject.getString("answer");
                    String analysis = jsonObject.getString("analysis");
                    
                    // 添加选项
                    String[] optionLabels = {"A", "B", "C", "D", "E", "F", "G", "H"};
                    for (int j = 0; j < options.size(); j++) {
                        content.append("【").append(optionLabels[j]).append(". 】").append(options.get(j)).append("\n");
                    }
                    
                    // 添加答案和解析
                    content.append("【答案】").append(answer).append("\n");
                    if (analysis != null && !analysis.isEmpty()) {
                        content.append("【解析】").append(analysis).append("\n");
                    }
                    content.append("\n");
                }
            }
            
            // 分组导出判断题
            List<ExamQuestion> judgmentQuestions = questions.stream()
                    .filter(q -> q.getQuestionType() == 2)
                    .collect(java.util.stream.Collectors.toList());
            
            if (!judgmentQuestions.isEmpty()) {
                content.append("【二、判断题】\n\n");
                for (int i = 0; i < judgmentQuestions.size(); i++) {
                    ExamQuestion question = judgmentQuestions.get(i);
                    content.append("【").append(i + 1).append(".】").append(question.getTitle()).append("\n");
                    
                    // 解析JSON内容获取答案
                    com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(question.getContent());
                    String answer = jsonObject.getString("answer");
                    String analysis = jsonObject.getString("analysis");
                    
                    // 将T/F转换为正确/错误
                    String displayAnswer = "T".equals(answer) ? "正确" : "错误";
                    content.append("【答案】").append(displayAnswer).append("\n");
                    
                    if (analysis != null && !analysis.isEmpty()) {
                        content.append("【解析】").append(analysis).append("\n");
                    }
                    content.append("\n");
                }
            }
            
            // 分组导出编程题
            List<ExamQuestion> programmingQuestions = questions.stream()
                    .filter(q -> q.getQuestionType() == 3)
                    .collect(java.util.stream.Collectors.toList());
            
            if (!programmingQuestions.isEmpty()) {
                content.append("【三、编程题】\n\n");
                for (int i = 0; i < programmingQuestions.size(); i++) {
                    ExamQuestion question = programmingQuestions.get(i);
                    content.append("【").append(i + 1).append(".】").append(question.getTitle()).append("\n");
                    
                    // 解析JSON内容
                    com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(question.getContent());
                    
                    // 添加时间和内存限制
                    int timeLimit = json.getIntValue("time_limit");
                    int memoryLimit = json.getIntValue("memory_limit");
                    content.append("【- 时间限制：").append(timeLimit / 1000.0).append(" s - 内存限制：")
                            .append(memoryLimit).append(" MB】\n");
                    
                    // 添加题目描述
                    content.append("【题目描述】\n").append(json.getString("description")).append("\n\n");
                    
                    // 添加输入输出格式
                    content.append("【输入格式】\n").append(json.getString("input_format")).append("\n\n");
                    content.append("【输出格式】\n").append(json.getString("output_format")).append("\n\n");
                    
                    // 添加样例
                    @SuppressWarnings("rawtypes")
                    List<Map> samples = json.getJSONArray("sample_cases").toJavaList(Map.class);
                    for (int j = 0; j < samples.size(); j++) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> sample = samples.get(j);
                        content.append("【输入样例 ").append(j + 1).append("】\n").append(sample.get("input")).append("\n\n");
                        content.append("【输出样例 ").append(j + 1).append("】\n").append(sample.get("output")).append("\n\n");
                    }
                    
                    // 添加提示或数据范围
                    String hint = json.getString("hint");
                    if (hint != null && !hint.isEmpty()) {
                        content.append("【数据范围】\n").append(hint).append("\n\n");
                    }
                    
                    content.append("\n");
                }
            }
            
            // 确保内容不为空，并使用自定义的结果类返回，避免框架序列化影响
            String exportContent = content.toString();
            if (exportContent.trim().isEmpty()) {
                return Result.error("导出内容为空");
            }
            
            // 直接使用HashMap，避免使用Result中的泛型可能导致的序列化问题
            java.util.Map<String, String> resultMap = new java.util.HashMap<>();
            resultMap.put("content", exportContent);
            
            return Result.ok(resultMap);
            
        } catch (Exception e) {
            log.error("导出题目失败", e);
            return Result.error("导出题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载纯文本模板
     */
    @AutoLog(value = "题库管理-下载纯文本模板")
    @ApiOperation(value="题库管理-下载纯文本模板", notes="下载纯文本题目模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {

        try {
            // 设置响应头，指定下载文件名（使用URL编码处理中文文件名）
            String fileName = "纯文本模板.txt";
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            // 设置响应头
            response.setContentType("text/plain;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            // 设置更完整的Content-Disposition头，兼容不同浏览器
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName);
            // 禁用缓存
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 纯文本模板内容（基于测试纯文本题目.txt的格式）
            String pureTextTemplate = "单选题\n\n" +
                    "关于C++语言的描述，下面哪个是正确的？\n" +
                    "A. C++语言不支持面向对象编程。\n" +
                    "B. C++语言的程序最终会被编译成机器指令执行。\n" +
                    "C. C++语言只能用于游戏开发。\n" +
                    "D. C++语言中，变量可以不声明就直接使用。\n" +
                    "答案：B\n" +
                    "解析：C++是一种支持面向对象的高级编程语言，应用广泛，变量使用前必须声明。程序需要通过编译器转换为机器码才能运行。\n\n" +
                    "判断题\n\n" +
                    "在C++中，表达式 N * 2 % N 中如果 N 的值为正整数，则其值为0。\n" +
                    "答案：正确\n" +
                    "解析：N*2可以被N整除，所以余数为0。\n\n" +
                    "编程题\n\n" +
                    "四舍五入\n" +
                    "- 时间限制：1.0 s - 内存限制：512.0 MB\n" +
                    "题目描述\n" +
                    "四舍五入是一种常见的近似计算方法。现在，给定 n 个整数，你需要将每个整数四舍五入到最接近的整十数。\n" +
                    "输入格式\n" +
                    "共 n + 1 行，第一行是一个整数 n，表示接下来输入的整数个数。\n" +
                    "接下来 n 行，每行一个整数 ai。\n" +
                    "输出格式\n" +
                    "n 行，每行一个整数，表示每个整数四舍五入后的结果。\n" +
                    "输入样例\n" +
                    "5\n" +
                    "43\n" +
                    "58\n" +
                    "25\n" +
                    "输出样例\n" +
                    "40\n" +
                    "60\n" +
                    "30\n" +
                    "数据范围\n" +
                    "对于所有测试点，保证 1 ≤ n ≤ 100，1 ≤ ai ≤ 10000。\n";

            // 使用输出流写入内容
            try (java.io.OutputStream os = response.getOutputStream()) {
                byte[] bytes = pureTextTemplate.getBytes("UTF-8");
                os.write(bytes);
                os.flush();
            }
            
        } catch (Exception e) {
            log.error("下载模板失败", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "下载模板失败");
            } catch (Exception ignored) {
                // 忽略异常
            }
        }
    }

    /**
     * 自动格式化
     */
    @AutoLog(value = "题库管理-自动格式化")
    @ApiOperation(value="题库管理-自动格式化", notes="将纯文本模板文件自动格式化为标准导入格式")
    @PostMapping(value = "/autoFormatTemplate")
    public Result<?> autoFormatTemplate(
            @RequestParam("file") MultipartFile file,
            @ApiParam(value = "科目") @RequestParam String subject,
            @ApiParam(value = "级别") @RequestParam String level,
            @ApiParam(value = "难度") @RequestParam Integer difficulty) {

        try {
            // 验证文件
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".txt")) {
                return Result.error("请上传.txt格式的文本文件");
            }

            // 验证参数
            if (subject == null || subject.trim().isEmpty()) {
                return Result.error("科目不能为空");
            }
            if (level == null || level.trim().isEmpty()) {
                return Result.error("级别不能为空");
            }
            if (difficulty == null || difficulty < 1 || difficulty > 3) {
                return Result.error("难度必须为1-3之间的数字");
            }

            // 调用服务进行自动格式化
            String formattedContent = examQuestionService.autoFormatTemplate(file, subject, level, difficulty);

            log.info("控制器收到格式化结果，长度: {}", formattedContent != null ? formattedContent.length() : 0);
            log.info("控制器返回结果前50字符: {}", formattedContent != null && formattedContent.length() > 0 ?
                    formattedContent.substring(0, Math.min(50, formattedContent.length())) : "null或空");

            return Result.ok((Object) formattedContent);

        } catch (Exception e) {
            log.error("自动模板化失败", e);
            return Result.error("自动模板化失败: " + e.getMessage());
        }
    }

    /**
     * 将各种格式的级别标准化为"X级"格式
     * @param level 原始级别文本，如"6"、"六"、"6级"、"六级"等
     * @return 标准化的级别文本，如"六级"
     */
    private static String standardizeLevel(String level) {
        if (level == null || level.isEmpty()) {
            return level;
        }

        // 如果已经是"X级"格式，检查是否需要转换数字部分
        if (level.endsWith("级")) {
            String prefix = level.substring(0, level.length() - 1);
            // 如果前缀是数字，转换为中文
            try {
                int num = Integer.parseInt(prefix);
                return numberToChinese(num) + "级";
            } catch (NumberFormatException e) {
                // 前缀不是数字，可能已经是中文
                return level;
            }
        }

        // 如果是纯数字，转换为"X级"格式
        try {
            int num = Integer.parseInt(level);
            return numberToChinese(num) + "级";
        } catch (NumberFormatException e) {
            // 不是纯数字，检查是否是单个中文数字
            switch (level) {
                case "一": return "一级";
                case "二": return "二级";
                case "三": return "三级";
                case "四": return "四级";
                case "五": return "五级";
                case "六": return "六级";
                case "七": return "七级";
                case "八": return "八级";
                case "九": return "九级";
                default: return level; // 无法识别，返回原始值
            }
        }
    }

    /**
     * 将数字转换为中文数字
     * @param num 数字
     * @return 中文数字
     */
    private static String numberToChinese(int num) {
        if (num <= 0 || num > 9) {
            return String.valueOf(num); // 超出范围，返回原始数字
        }

        switch (num) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "七";
            case 8: return "八";
            case 9: return "九";
            default: return String.valueOf(num);
        }
    }

    /**
     * 将前端驼峰命名转换为数据库下划线命名
     */
    private String convertToDbFieldName(String camelCaseField) {
        if (camelCaseField == null) {
            return null;
        }

        // 处理特定字段映射
        switch (camelCaseField) {
            case "questionType":
                return "question_type";
            case "createBy":
                return "create_by";
            case "createTime":
                return "create_time";
            case "updateBy":
                return "update_by";
            case "updateTime":
                return "update_time";
            default:
                // 通用驼峰转下划线
                return camelCaseField.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
        }
    }
}