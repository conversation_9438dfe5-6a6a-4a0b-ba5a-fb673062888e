package org.jeecg.modules.teaching.controller;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuestionService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 客观题
 * @Author: jeecg-boot
 * @Date:   2023-05-10
 * @Version: V1.0
 */
@Api(tags="客观题")
@RestController
@RequestMapping("/teaching/objectiveQuestion")
@Slf4j
public class TeachingObjectiveQuestionController extends JeecgController<TeachingObjectiveQuestion, ITeachingObjectiveQuestionService> {
    @Autowired
    private ITeachingObjectiveQuestionService teachingObjectiveQuestionService;
    
    /**
     * 分页列表查询
     */
    @AutoLog(value = "客观题-分页列表查询")
    @ApiOperation(value="客观题-分页列表查询", notes="客观题-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingObjectiveQuestion teachingObjectiveQuestion,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
        QueryWrapper<TeachingObjectiveQuestion> queryWrapper = QueryGenerator.initQueryWrapper(teachingObjectiveQuestion, req.getParameterMap());
        Page<TeachingObjectiveQuestion> page = new Page<TeachingObjectiveQuestion>(pageNo, pageSize);
        IPage<TeachingObjectiveQuestion> pageList = teachingObjectiveQuestionService.pageWithCourseName(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 根据单元ID查询相关客观题
     */
    @AutoLog(value = "客观题-根据单元ID查询")
    @ApiOperation(value="客观题-根据单元ID查询", notes="客观题-根据单元ID查询")
    @GetMapping(value = "/listByUnit")
    public Result<?> queryListByUnitId(@RequestParam(name="unitId", required=true) String unitId,
                                      @RequestParam(name="courseId", required=true) String courseId) {
        List<TeachingObjectiveQuestion> list = teachingObjectiveQuestionService.queryListByUnitId(unitId, courseId);
        return Result.ok(list);
    }
    
    /**
     * 根据多个ID批量查询客观题信息
     */
    @AutoLog(value = "客观题-批量查询")
    @ApiOperation(value="客观题-批量查询", notes="根据多个ID批量查询客观题信息")
    @GetMapping(value = "/queryBatchIds")
    public Result<?> queryBatchIds(@RequestParam(name="ids", required=true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        Collection<TeachingObjectiveQuestion> questions = teachingObjectiveQuestionService.listByIds(idList);
        return Result.ok(questions);
    }
    
    /**
     * 添加
     */
    @AutoLog(value = "客观题-添加")
    @ApiOperation(value="客观题-添加", notes="客观题-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TeachingObjectiveQuestion teachingObjectiveQuestion) {
        teachingObjectiveQuestionService.save(teachingObjectiveQuestion);
        return Result.ok("添加成功！");
    }
    
    /**
     * 编辑
     */
    @AutoLog(value = "客观题-编辑")
    @ApiOperation(value="客观题-编辑", notes="客观题-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody TeachingObjectiveQuestion teachingObjectiveQuestion) {
        teachingObjectiveQuestionService.updateById(teachingObjectiveQuestion);
        return Result.ok("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @AutoLog(value = "客观题-通过id删除")
    @ApiOperation(value="客观题-通过id删除", notes="客观题-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        teachingObjectiveQuestionService.removeById(id);
        return Result.ok("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @AutoLog(value = "客观题-批量删除")
    @ApiOperation(value="客观题-批量删除", notes="客观题-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.teachingObjectiveQuestionService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }
    
    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TeachingObjectiveQuestion teachingObjectiveQuestion) {
        return super.exportXls(request, teachingObjectiveQuestion, TeachingObjectiveQuestion.class, "客观题");
    }
    
    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TeachingObjectiveQuestion.class);
    }
    
    /**
     * 通过id查询
     */
    @AutoLog(value = "客观题-通过id查询")
    @ApiOperation(value="客观题-通过id查询", notes="客观题-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        TeachingObjectiveQuestion question = teachingObjectiveQuestionService.getById(id);
        if(question==null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(question);
    }
} 