[{"name": "int", "trans": ["n. [计算机] 整型变量；C++ 中的基本数据类型，用于表示整数"]}, {"name": "for", "trans": ["n. [计算机] 循环控制结构，用于多次执行代码块"]}, {"name": "return", "trans": ["vt. [计算机] 返回，函数执行完毕后返回一个值"]}, {"name": "const", "trans": ["adj. [计算机] 常量修饰符，用于声明不可修改的变量"]}, {"name": "std", "trans": ["n. [计算机] 标准命名空间，C++ 中标准库的命名空间"]}, {"name": "void", "trans": ["n. [计算机] 空类型，表示函数没有返回值"]}, {"name": "cur", "trans": ["n. [计算机] 当前，常用于指代当前元素或变量"]}, {"name": "include", "trans": ["n. [计算机] 头文件包含指令，用于引用外部文件"]}, {"name": "vector", "trans": ["n. [计算机] 动态数组容器，C++ 标准库中的数据结构"]}, {"name": "else", "trans": ["conj. [计算机] 条件控制结构中的分支语句"]}, {"name": "size", "trans": ["n. [计算机] 大小，常用于数组、向量等数据结构的长度或容量"]}, {"name": "root", "trans": ["n. [计算机] 树形结构的根节点"]}, {"name": "while", "trans": ["n. [计算机] 循环结构，用于在条件为真时重复执行代码块"]}, {"name": "val", "trans": ["n. [计算机] 值，常用于表示变量的内容"]}, {"name": "ans", "trans": ["n. [计算机] 答案，通常用于表示计算结果"]}, {"name": "cnt", "trans": ["n. [计算机] 计数器，常用于循环或统计次数"]}, {"name": "long", "trans": ["adj. [计算机] C++ 中的基本数据类型，用于表示长整型"]}, {"name": "Node", "trans": ["n. [计算机] 节点，常用于树或链表数据结构"]}, {"name": "mid", "trans": ["n. [计算机] 中间值，常用于二分查找等算法"]}, {"name": "res", "trans": ["n. [计算机] 结果，通常用于存储运算结果"]}, {"name": "code", "trans": ["n. [计算机] 代码，指编写的程序或脚本"]}, {"name": "cout", "trans": ["n. [计算机] 输出流对象，C++ 标准库中的输出流，用于打印信息"]}, {"name": "bool", "trans": ["n. [计算机] 布尔型变量，表示真（true）或假（false）"]}, {"name": "len", "trans": ["n. [计算机] 长度，通常用于表示字符串、数组等的长度"]}, {"name": "docs", "trans": ["n. [计算机] 文档，通常用于存储或描述项目信息"]}, {"name": "cpp", "trans": ["n. [计算机] C++ 文件扩展名，用于保存 C++ 源代码"]}, {"name": "mod", "trans": ["n. [计算机] 取模运算，通常用于求余数"]}, {"name": "key", "trans": ["n. [计算机] 键，常用于字典或键值对结构"]}, {"name": "nullptr", "trans": ["n. [计算机] 空指针，用于表示不指向任何对象的指针"]}, {"name": "main", "trans": ["n. [计算机] 主函数，程序的入口点"]}, {"name": "operator", "trans": ["n. [计算机] 操作符，用于重载 C++ 中的运算符"]}, {"name": "sum", "trans": ["n. [计算机] 和，通常用于累加结果"]}, {"name": "struct", "trans": ["n. [计算机] 结构体，C++ 中用于定义复合数据类型"]}, {"name": "auto", "trans": ["n. [计算机] 自动类型推导关键字，C++11 引入的用于自动推断变量类型"]}, {"name": "node", "trans": ["n. [计算机] 节点，通常用于树形结构或链表中"]}, {"name": "double", "trans": ["n. [计算机] C++ 中的浮点型数据类型，用于表示双精度浮点数"]}, {"name": "push", "trans": ["n. [计算机] 入栈操作，通常用于栈数据结构"]}, {"name": "scanf", "trans": ["n. [计算机] 输入函数，C语言中的标准输入函数"]}, {"name": "printf", "trans": ["n. [计算机] 输出函数，C语言中的标准输出函数"]}, {"name": "break", "trans": ["n. [计算机] 跳出循环或控制结构的语句"]}, {"name": "top", "trans": ["n. [计算机] 栈顶元素，通常用于栈数据结构的操作"]}, {"name": "true", "trans": ["n. [计算机] 布尔值，表示逻辑真"]}, {"name": "vis", "trans": ["n. [计算机] 访问标记，通常用于图算法中表示某个节点是否被访问"]}, {"name": "tmp", "trans": ["n. [计算机] 临时变量，常用于存储临时计算结果"]}, {"name": "poly", "trans": ["n. [计算机] 多项式，通常用于数学计算"]}, {"name": "false", "trans": ["n. [计算机] 布尔值，表示逻辑假"]}, {"name": "siz", "trans": ["n. [计算机] 大小，类似于 size，表示数据结构的长度"]}, {"name": "dis", "trans": ["n. [计算机] 距离，常用于图论算法中的最短路径计算"]}, {"name": "end", "trans": ["n. [计算机] 结束符，C++ 中用于表示标准输出的结束"]}, {"name": "string", "trans": ["n. [计算机] 字符串，C++ 标准库中的字符串类"]}, {"name": "push_back", "trans": ["n. [计算机] 向量尾部插入元素的方法，C++ 标准库中的常用方法"]}, {"name": "first", "trans": ["n. [计算机] 键值对的第一个元素，常用于 pair 类型"]}, {"name": "max", "trans": ["n. [计算机] 最大值，常用于获取一组数中的最大值"]}, {"name": "begin", "trans": ["n. [计算机] 容器的起始迭代器，常用于 C++ 容器类"]}, {"name": "char", "trans": ["n. [计算机] 字符型数据类型，表示单个字符"]}, {"name": "using", "trans": ["n. [计算机] 声明特定命名空间的关键字"]}, {"name": "head", "trans": ["n. [计算机] 链表或树的头节点"]}, {"name": "endl", "trans": ["n. [计算机] 行结束符，C++ 输出流中的换行符"]}, {"name": "data", "trans": ["n. [计算机] 数据，常用于存储某种信息或数组的底层存储"]}, {"name": "swap", "trans": ["n. [计算机] 交换两个变量的值"]}, {"name": "dfs", "trans": ["n. [计算机] 深度优先搜索算法，用于图的遍历"]}, {"name": "merge", "trans": ["n. [计算机] 合并操作，常用于归并排序或合并数据结构"]}, {"name": "update", "trans": ["n. [计算机] 更新操作，常用于修改数据或结构"]}, {"name": "second", "trans": ["n. [计算机] 键值对的第二个元素，常用于 pair 类型"]}, {"name": "sta", "trans": ["n. [计算机] 栈，通常表示堆栈结构"]}, {"name": "add", "trans": ["n. [计算机] 加法运算或添加元素"]}, {"name": "class", "trans": ["n. [计算机] 类，面向对象编程中的基本概念"]}, {"name": "tot", "trans": ["n. [计算机] 总数，常用于累计某个值"]}, {"name": "namespace", "trans": ["n. [计算机] 命名空间，C++ 中用于避免名称冲突"]}, {"name": "sizeof", "trans": ["n. [计算机] 用于获取类型或对象的大小"]}, {"name": "edges", "trans": ["n. [计算机] 图中边的集合，常用于图算法"]}, {"name": "size_t", "trans": ["n. [计算机] 无符号整型，用于表示对象的大小"]}, {"name": "temp", "trans": ["n. [计算机] 临时变量，用于存储中间结果"]}, {"name": "parent", "trans": ["n. [计算机] 父节点，常用于树形结构"]}, {"name": "Key", "trans": ["n. [计算机] 键，常用于表示键值对中的键"]}, {"name": "next", "trans": ["n. [计算机] 指向下一个元素的指针，常用于链表"]}, {"name": "cin", "trans": ["n. [计算机] C++ 中的标准输入流，用于读取输入"]}, {"name": "define", "trans": ["n. [计算机] 预处理指令，用于定义宏"]}, {"name": "now", "trans": ["n. [计算机] 当前时间或状态"]}, {"name": "arr", "trans": ["n. [计算机] 数组，存储多个元素的容器"]}, {"name": "clear", "trans": ["n. [计算机] 清空操作，常用于清空容器中的所有元素"]}, {"name": "MAXN", "trans": ["n. [计算机] 表示数组或数据结构的最大长度"]}, {"name": "iostream", "trans": ["n. [计算机] C++ 标准输入输出流库的头文件"]}, {"name": "this", "trans": ["n. [计算机] 当前对象的指针"]}, {"name": "Point", "trans": ["n. [计算机] 点，通常用于几何算法中表示二维或三维空间的点"]}, {"name": "dep", "trans": ["n. [计算机] 深度，常用于树形结构的深度表示"]}, {"name": "resize", "trans": ["n. [计算机] 调整容器大小，常用于动态数组或向量"]}, {"name": "find", "trans": ["n. [计算机] 查找操作，常用于在容器中查找元素"]}, {"name": "insert", "trans": ["n. [计算机] 插入操作，常用于在容器中插入元素"]}, {"name": "nxt", "trans": ["n. [计算机] 下一项，常用于链表或循环"]}, {"name": "graph", "trans": ["n. [计算机] 图形结构，常用于表示点和边的集合"]}, {"name": "memset", "trans": ["n. [计算机] 内存设置函数，用于初始化数组或块内存"]}, {"name": "tree", "trans": ["n. [计算机] 树形结构，常用于层级数据表示"]}, {"name": "pop", "trans": ["n. [计算机] 出栈操作，常用于栈数据结构中"]}, {"name": "pos", "trans": ["n. [计算机] 位置，常用于表示数组或字符串中的索引"]}, {"name": "query", "trans": ["n. [计算机] 查询操作，常用于检索数据"]}, {"name": "min", "trans": ["n. [计算机] 最小值，常用于获取一组数中的最小值"]}, {"name": "ret", "trans": ["n. [计算机] 返回值，通常用于函数的返回结果"]}, {"name": "son", "trans": ["n. [计算机] 子节点，常用于树形结构中"]}, {"name": "splay", "trans": ["n. [计算机] 伸展树，一种自平衡二叉搜索树"]}, {"name": "empty", "trans": ["n. [计算机] 检查容器是否为空的操作"]}, {"name": "template", "trans": ["n. [计算机] 模板，C++ 中的泛型编程工具"]}, {"name": "state", "trans": ["n. [计算机] 状态，常用于表示程序或系统的运行状态"]}, {"name": "maintain", "trans": ["n. [计算机] 维护，常用于维持数据结构的性质"]}, {"name": "cstdio", "trans": ["n. [计算机] C 标准 I/O 库头文件"]}, {"name": "continue", "trans": ["n. [计算机] 控制结构中的语句，跳过当前循环的剩余部分"]}, {"name": "is_prime", "trans": ["n. [计算机] 判断某个数是否为素数的函数"]}, {"name": "read", "trans": ["n. [计算机] 读取操作，常用于从文件或输入流中读取数据"]}, {"name": "flow", "trans": ["n. [计算机] 流量，常用于图算法中的网络流问题"]}, {"name": "Set", "trans": ["n. [计算机] 集合，C++ 标准库中的集合容器"]}, {"name": "case", "trans": ["n. [计算机] 条件语句中的分支，用于 switch 语句中"]}, {"name": "Big", "trans": ["n. [计算机] 大数，常用于处理超出基本类型范围的大整数"]}, {"name": "split", "trans": ["n. [计算机] 分割操作，常用于字符串或数组的拆分"]}, {"name": "typename", "trans": ["n. [计算机] 用于模板参数中的类型声明"]}, {"name": "left", "trans": ["n. [计算机] 左子节点，常用于树结构中"]}, {"name": "low", "trans": ["n. [计算机] 低位，常用于表示二进制数据的低位部分"]}, {"name": "dist", "trans": ["n. [计算机] 距离，常用于图算法中的路径长度计算"]}, {"name": "cmp", "trans": ["n. [计算机] 比较函数，常用于排序算法中的自定义比较"]}, {"name": "argv", "trans": ["n. [计算机] 命令行参数数组"]}, {"name": "num", "trans": ["n. [计算机] 数字，常用于表示某种计数或数值"]}, {"name": "Edge", "trans": ["n. [计算机] 边，常用于图结构中表示顶点之间的连接"]}, {"name": "last", "trans": ["n. [计算机] 最后一个元素，常用于链表或数组操作"]}, {"name": "trie", "trans": ["n. [计算机] 字典树，一种高效的字符串查找树"]}, {"name": "pri_j", "trans": ["n. [计算机] 素数表，常用于素数筛选算法"]}, {"name": "static", "trans": ["n. [计算机] 静态变量，常用于存储全局唯一的数据"]}, {"name": "dfn", "trans": ["n. [计算机] 深度优先搜索中的访问顺序"]}, {"name": "level", "trans": ["n. [计算机] 层级，常用于树形结构或图结构的深度表示"]}, {"name": "LEN", "trans": ["n. [计算机] 长度，常用于数组或字符串的长度表示"]}, {"name": "pans", "trans": ["n. [计算机] 上一次的答案，常用于多次查询中的结果缓存"]}, {"name": "INF", "trans": ["n. [计算机] 无穷大，常用于表示图中的无法到达的距离"]}, {"name": "print", "trans": ["n. [计算机] 打印输出，常用于显示数据"]}, {"name": "ptr", "trans": ["n. [计算机] 指针，存储变量的内存地址"]}, {"name": "mat", "trans": ["n. [计算机] 矩阵，常用于图形学或数学计算"]}, {"name": "from", "trans": ["n. [计算机] 起始点，常用于表示数据的来源"]}, {"name": "jans", "trans": ["n. [计算机] 记录当前答案的变量"]}, {"name": "sort", "trans": ["n. [计算机] 排序函数，常用于对数组或容器进行排序"]}, {"name": "queue", "trans": ["n. [计算机] 队列，一种先进先出的数据结构"]}, {"name": "pair", "trans": ["n. [计算机] 键值对，常用于存储两个相关联的数据"]}, {"name": "algorithm", "trans": ["n. [计算机] 算法，指某个特定问题的解决步骤"]}, {"name": "typedef", "trans": ["n. [计算机] 类型别名，用于给已有类型定义新名字"]}, {"name": "new", "trans": ["n. [计算机] 动态分配内存的操作"]}, {"name": "front", "trans": ["n. [计算机] 队列的前端元素"]}, {"name": "path", "trans": ["n. [计算机] 路径，常用于图中的最短路径问题"]}, {"name": "weight", "trans": ["n. [计算机] 权重，常用于加权图中的边"]}, {"name": "cursor", "trans": ["n. [计算机] 游标，常用于数据库中的记录指针"]}, {"name": "color", "trans": ["n. [计算机] 颜色，常用于图形学中的像素或节点标记"]}, {"name": "upd_siz", "trans": ["n. [计算机] 更新大小，常用于平衡树中的节点更新"]}, {"name": "count", "trans": ["n. [计算机] 计数器，用于记录某一操作的次数"]}, {"name": "u32", "trans": ["n. [计算机] 32位无符号整数"]}, {"name": "link", "trans": ["n. [计算机] 链接，常用于链表或其他数据结构的连接"]}, {"name": "inf", "trans": ["n. [计算机] 无穷大，常用于表示不可达距离"]}, {"name": "l_tr", "trans": ["n. [计算机] 左子树，常用于二叉树结构中"]}, {"name": "Vector", "trans": ["n. [计算机] 向量，C++ STL 中的动态数组"]}, {"name": "inv", "trans": ["n. [计算机] 逆元，常用于数学运算中的模逆"]}, {"name": "rev", "trans": ["n. [计算机] 反转，常用于数组或字符串的反向操作"]}, {"name": "sibling", "trans": ["n. [计算机] 兄弟节点，常用于树形结构"]}, {"name": "seg", "trans": ["n. [计算机] 线段树，一种高效的区间查询数据结构"]}, {"name": "solve", "trans": ["n. [计算机] 求解函数，通常用于解决某个算法问题"]}, {"name": "build", "trans": ["n. [计算机] 构建操作，常用于构建树或其他数据结构"]}, {"name": "delete", "trans": ["n. [计算机] 删除操作，常用于释放动态分配的内存"]}, {"name": "forward", "trans": ["n. [计算机] 前向声明，常用于提前声明类型"]}, {"name": "cap", "trans": ["n. [计算机] 容量，常用于存储数据结构的容量表示"]}, {"name": "stack", "trans": ["n. [计算机] 栈，后进先出的数据结构"]}, {"name": "FILE", "trans": ["n. [计算机] 文件类型，表示文件流"]}, {"name": "lst", "trans": ["n. [计算机] 列表，常用于链表或容器表示"]}, {"name": "idx", "trans": ["n. [计算机] 索引，常用于数组或字符串中的位置标记"]}, {"name": "father", "trans": ["n. [计算机] 父节点，常用于树形结构中"]}, {"name": "maxs", "trans": ["n. [计算机] 最大值，常用于保存数组或区间的最大值"]}, {"name": "start", "trans": ["n. [计算机] 起始点，常用于表示操作的起点"]}, {"name": "erase", "trans": ["n. [计算机] 擦除操作，常用于容器中的元素删除"]}, {"name": "tag", "trans": ["n. [计算机] 标记，常用于标记数据或节点的状态"]}, {"name": "layer", "trans": ["n. [计算机] 层，常用于表示图或网络中的层次结构"]}, {"name": "not_prime", "trans": ["n. [计算机] 非素数标记，用于素数筛选算法"]}, {"name": "index", "trans": ["n. [计算机] 索引，常用于查找数组或表中的元素"]}, {"name": "pre", "trans": ["n. [计算机] 前一个元素或节点，常用于链表或树形结构"]}, {"name": "rotate", "trans": ["n. [计算机] 旋转操作，常用于自平衡树的平衡调整"]}, {"name": "readInt", "trans": ["n. [计算机] 读取整数值的函数"]}, {"name": "child", "trans": ["n. [计算机] 子节点，常用于树形结构"]}, {"name": "unsigned", "trans": ["n. [计算机] 无符号整数类型"]}, {"name": "prime", "trans": ["n. [计算机] 素数，常用于素数判定或筛选算法"]}, {"name": "fopen", "trans": ["n. [计算机] 文件打开函数"]}, {"name": "flag", "trans": ["n. [计算机] 标志位，常用于标记某种状态或条件"]}, {"name": "right", "trans": ["n. [计算机] 右子树，常用于二叉树结构中"]}, {"name": "out", "trans": ["n. [计算机] 输出流，常用于打印数据或写入文件"]}, {"name": "pushdown", "trans": ["n. [计算机] 递归向下操作，常用于线段树的懒标记更新"]}, {"name": "makeroot", "trans": ["n. [计算机] 设定根节点的操作，常用于动态树结构"]}, {"name": "is_red", "trans": ["n. [计算机] 红黑树中红色节点的判断标记"]}, {"name": "lld", "trans": ["n. [计算机] 长整型数据类型"]}, {"name": "cstring", "trans": ["n. [计算机] C风格的字符串，通常以空字符结尾"]}, {"name": "dir", "trans": ["n. [计算机] 方向，常用于图中的路径查找或递归搜索"]}, {"name": "abs", "trans": ["n. [计算机] 绝对值函数"]}, {"name": "access", "trans": ["n. [计算机] 访问权限或操作"]}, {"name": "adj", "trans": ["n. [计算机] 邻接表，常用于存储图的顶点和边关系"]}, {"name": "make_pair", "trans": ["n. [计算机] 创建一对键值对"]}, {"name": "priority_queue", "trans": ["n. [计算机] 优先队列，一种特殊的队列数据结构"]}]