package org.jeecg.modules.teaching.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.modules.system.service.ISysConfigService;
import org.jeecg.modules.teaching.entity.TeachingCourseNotification;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance;
import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleInstanceService;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.*;

/**
 * @Description: 即将开始课程提醒定时任务
 * 用于检查未来24小时内将开始的课程，并为它们创建提醒通知
 * 支持处理以下两类课程：
 * 1. 未修改的单次课程：基于父课程设置的重复规则动态计算出来的课程实例
 * 2. 已修改的单次课程：在teaching_course_schedule_instance表中有独立记录的课程实例
 * @Date: 2025-06-25
 * @Version: V2.0
 */
@Slf4j
@Component
public class CourseUpcomingReminderJob implements Job {

    @Autowired
    private ITeachingCourseScheduleService teachingCourseScheduleService;

    @Autowired
    private ITeachingCourseScheduleInstanceService teachingCourseScheduleInstanceService;

    @Autowired
    private ITeachingCourseNotificationService teachingCourseNotificationService;

    @Autowired
    private ISysConfigService sysConfigService;

    private static final String CONFIG_KEY_NOTIFY_BEFORE_MINUTES = "courseNotifyBeforeMinutes";

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("=====开始执行即将开始课程提醒定时任务，时间：{}=====", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        
        try {
            // 获取当前时间和24小时后的时间
            Date now = new Date();
            Date twentyFourHoursLater = DateUtils.addHours(now, 24);
            
            log.info("检查时间范围: {} 至 {}", 
                    DateFormatUtils.format(now, "yyyy-MM-dd HH:mm:ss"),
                    DateFormatUtils.format(twentyFourHoursLater, "yyyy-MM-dd HH:mm:ss"));
            
            // 获取系统默认提醒提前时间(分钟)
            Integer defaultNotifyBeforeMinutes = getDefaultNotifyBeforeMinutes();
            // 记录使用的默认提醒时间
            log.info("系统默认提前提醒时间: {} 分钟", defaultNotifyBeforeMinutes);
            
            // 处理计数器
            int totalCreatedCount = 0;
            
            // =====================================================
            // 1. 处理已修改的单次课程实例（teaching_course_schedule_instance表中的记录）
            // =====================================================
            log.info("【第一阶段】开始处理已修改的单次课程实例...");
            int modifiedInstanceCount = processModifiedCourseInstances(now, twentyFourHoursLater, defaultNotifyBeforeMinutes);
            totalCreatedCount += modifiedInstanceCount;
            log.info("【第一阶段】已完成处理已修改的单次课程实例，共创建 {} 条提醒通知", modifiedInstanceCount);
            
            // =====================================================
            // 2. 处理未修改的单次课程实例（根据父课程的重复规则动态计算）
            // =====================================================
            log.info("【第二阶段】开始处理未修改的单次课程实例...");
            int unmodifiedInstanceCount = processUnmodifiedCourseInstances(now, twentyFourHoursLater, defaultNotifyBeforeMinutes);
            totalCreatedCount += unmodifiedInstanceCount;
            log.info("【第二阶段】已完成处理未修改的单次课程实例，共创建 {} 条提醒通知", unmodifiedInstanceCount);
            
            log.info("=====即将开始课程提醒定时任务执行完毕，共创建 {} 条提醒通知=====", totalCreatedCount);
        } catch (Exception e) {
            log.error("即将开始课程提醒定时任务执行异常", e);
            // 添加更详细的异常信息
            log.error("异常详情: {}", e.getMessage());
            throw new JobExecutionException("执行即将开始课程提醒定时任务失败", e, false);
        }
    }
    
    /**
     * 处理已修改的单次课程实例
     * 这些实例在teaching_course_schedule_instance表中有独立记录
     * 
     * @param now 当前时间
     * @param twentyFourHoursLater 24小时后的时间
     * @param defaultNotifyBeforeMinutes 默认提醒提前分钟数
     * @return 创建的提醒数量
     */
    private int processModifiedCourseInstances(Date now, Date twentyFourHoursLater, Integer defaultNotifyBeforeMinutes) {
        int createdCount = 0;
        
        try {
            log.info("开始处理已修改的单次课程实例...");
            
            // 查询未来24小时内将开始的单次课程实例
            // 注意：这里不再限制只查询reminder_created=0的记录，而是查询所有未来24小时内的实例
            // 这样可以处理已经创建过提醒但后来又被修改的实例
            QueryWrapper<TeachingCourseScheduleInstance> queryWrapper = new QueryWrapper<>();
            queryWrapper.ge("start_time", now);
            queryWrapper.le("start_time", twentyFourHoursLater);
            queryWrapper.eq("status", 1); // 状态正常
            // 不再限制reminder_created字段
            // 删除: queryWrapper.and(wrapper -> wrapper.eq("reminder_created", 0).or().isNull("reminder_created"));
            
            List<TeachingCourseScheduleInstance> upcomingInstances = teachingCourseScheduleInstanceService.list(queryWrapper);
            
            if (upcomingInstances == null || upcomingInstances.isEmpty()) {
                log.info("未找到需要处理的已修改单次课程实例");
                return 0;
            }
            
            log.info("找到 {} 个已修改的单次课程实例", upcomingInstances.size());
            
            // 为每个课程实例处理提醒通知
            for (TeachingCourseScheduleInstance instance : upcomingInstances) {
                if (instance == null || instance.getStartTime() == null) {
                    log.warn("跳过无效课程实例或开始时间为空的实例");
                    continue;
                }
                
                // 获取实例的日期字符串，用于日志记录和后续处理
                String instanceDateStr = DateFormatUtils.format(instance.getStartTime(), "yyyy-MM-dd");
                
                // 检查是否已经创建过提醒，用于日志记录
                boolean hasReminderCreated = instance.getReminderCreated() != null && instance.getReminderCreated() == 1;
                
                log.info("处理已修改的单次课程实例 ID: {}, 标题: {}, 开始时间: {}, 实例日期: {}, 已创建提醒: {}", 
                        instance.getId(), 
                        instance.getScheduleTitle(),
                        DateFormatUtils.format(instance.getStartTime(), "yyyy-MM-dd HH:mm:ss"),
                        instanceDateStr,
                        hasReminderCreated ? "是" : "否");
                
                // 如果已经创建过提醒，仍然需要检查是否有旧的提醒需要删除
                // 特别是课程时间已经修改，但reminder_created字段没有正确重置的情况
                if (hasReminderCreated) {
                    log.info("实例 {} 已标记为创建过提醒，但仍检查是否存在旧提醒需要删除", instance.getId());
                }
                
                // 优化：删除该实例可能存在的未发送的旧提醒通知，记录删除数量
                int deletedCount = deleteExistingPendingReminders(instance.getId());
                if (deletedCount > 0) {
                    log.info("已删除实例 {} 的 {} 个旧提醒通知", instance.getId(), deletedCount);
                    // 注意：deleteExistingPendingReminders方法中已经重置了reminder_created字段
                    // 此处重新获取实例，以确保使用最新的reminder_created值
                    String instanceId = instance.getId();
                    instance = teachingCourseScheduleInstanceService.getById(instanceId);
                    if (instance == null) {
                        log.warn("实例 {} 在删除旧提醒后无法重新获取，跳过处理", instanceId);
                        continue;
                    }
                }
                
                // 只有当reminder_created为0或null时，才创建新的提醒
                if (instance.getReminderCreated() == null || instance.getReminderCreated() == 0) {
                    // 获取课程配置的提醒时间，如果没有则使用系统默认值
                    Integer courseNotifyBeforeMinutes = instance.getNotifyBeforeMinutes();
                    if (courseNotifyBeforeMinutes == null) {
                        courseNotifyBeforeMinutes = defaultNotifyBeforeMinutes;
                        log.info("实例 {} 未设置提醒时间，使用系统默认值: {} 分钟", instance.getId(), defaultNotifyBeforeMinutes);
                    } else {
                        log.info("实例 {} 已设置提醒时间: {} 分钟", instance.getId(), courseNotifyBeforeMinutes);
                    }
                
                    // 计算实际的提醒时间
                    Date remindTime = DateUtils.addMinutes(instance.getStartTime(), -courseNotifyBeforeMinutes);
                
                    // 如果提醒时间已过，调整为当前时间后5分钟
                    if (remindTime.before(now)) {
                        Date originalRemindTime = remindTime;
                        remindTime = DateUtils.addMinutes(now, 5);
                        log.info("实例 {} 的原提醒时间 {} 已过，调整为当前时间后5分钟: {}", 
                                instance.getId(), 
                                DateFormatUtils.format(originalRemindTime, "yyyy-MM-dd HH:mm:ss"),
                                DateFormatUtils.format(remindTime, "yyyy-MM-dd HH:mm:ss"));
                    }
                
                    // 确定通知对象
                    boolean notifyTeachers = instance.getNotifyTeachers() == null || instance.getNotifyTeachers() == 1;
                    boolean notifyStudents = instance.getNotifyStudents() == null || instance.getNotifyStudents() == 1;
                    
                    // 检查并删除可能存在的动态实例提醒通知
                    // 这是为了解决当"未修改的单次课程"被修改为"已修改的单次课程"时，避免重复提醒的问题
                    if (instance.getParentId() != null && instance.getInstanceDate() != null) {
                        String instanceDateFormatted = DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd");
                        int dynamicReminderDeleted = deleteExistingDynamicInstanceReminders(instance.getParentId(), instanceDateFormatted);
                        if (dynamicReminderDeleted > 0) {
                            log.info("已删除实例 {} 对应的 {} 个动态实例提醒通知，避免重复提醒", instance.getId(), dynamicReminderDeleted);
                        }
                    }
                    
                    log.info("为实例 {} 创建提醒通知，提醒时间: {}, 通知教师: {}, 通知学生: {}", 
                            instance.getId(), 
                            DateFormatUtils.format(remindTime, "yyyy-MM-dd HH:mm:ss"),
                            notifyTeachers ? "是" : "否",
                            notifyStudents ? "是" : "否");
                    
                    // 创建课程提醒通知
                    boolean success = createReminderForInstance(instance, remindTime, notifyTeachers, notifyStudents);
                
                    if (success) {
                        createdCount++;
                        // 标记课程实例已创建提醒通知
                        boolean markSuccess = markInstanceReminderCreated(instance.getId());
                        if (markSuccess) {
                            log.info("成功标记实例 {} 已创建提醒通知", instance.getId());
                        } else {
                            log.warn("标记实例 {} 已创建提醒通知失败", instance.getId());
                        }
                    } else {
                        log.warn("为实例 {} 创建提醒通知失败", instance.getId());
                    }
                } else {
                    log.info("实例 {} 已创建提醒通知(reminder_created=1)，跳过创建新提醒", instance.getId());
                }
            }
            
            log.info("已处理 {} 个已修改的单次课程实例，成功创建 {} 个提醒", upcomingInstances.size(), createdCount);
        } catch (Exception e) {
            log.error("处理已修改的单次课程实例时发生异常", e);
            log.error("异常详情: {}", e.getMessage());
        }
        
        return createdCount;
    }
    
    /**
     * 处理未修改的单次课程实例
     * 这些实例是根据父课程设置的重复规则(repeat_type, repeat_end_date, week_days)动态计算出来的
     * 
     * @param now 当前时间
     * @param twentyFourHoursLater 24小时后的时间
     * @param defaultNotifyBeforeMinutes 默认提醒提前分钟数
     * @return 创建的提醒数量
     */
    private int processUnmodifiedCourseInstances(Date now, Date twentyFourHoursLater, Integer defaultNotifyBeforeMinutes) {
        int createdCount = 0;
        
        try {
            log.info("开始处理未修改的单次课程实例...");
            
            // 查询重复类型不为0的课程排期
            QueryWrapper<TeachingCourseSchedule> queryWrapper = new QueryWrapper<>();
            queryWrapper.ne("repeat_type", 0); // 非单次课程
            queryWrapper.eq("status", 1); // 状态正常
            
            List<TeachingCourseSchedule> repeatSchedules = teachingCourseScheduleService.list(queryWrapper);
            
            if (repeatSchedules == null || repeatSchedules.isEmpty()) {
                log.info("未找到需要处理的重复课程");
                return 0;
            }
            
            log.info("找到 {} 个重复课程需要计算动态实例", repeatSchedules.size());
            
            // 处理每个重复课程
            for (TeachingCourseSchedule schedule : repeatSchedules) {
                if (schedule == null || schedule.getStartTime() == null || schedule.getEndTime() == null) {
                    log.warn("跳过无效课程或时间为空的课程");
                    continue;
                }
                
                log.info("处理重复课程 ID: {}, 标题: {}, 重复类型: {}", 
                        schedule.getId(), 
                        schedule.getScheduleTitle(),
                        schedule.getRepeatType());
                
                // 计算未来24小时内该重复课程的动态实例
                List<Date> instanceDates = calculateInstanceDates(schedule, now, twentyFourHoursLater);
                
                if (instanceDates.isEmpty()) {
                    log.info("课程 ID: {} 在未来24小时内没有实例", schedule.getId());
                    continue;
                }
                
                log.info("课程 ID: {}, 标题: {} 在未来24小时内有 {} 个动态实例", 
                        schedule.getId(), schedule.getScheduleTitle(), instanceDates.size());
                
                // 获取课程的已删除实例列表
                Set<String> deletedDates = getDeletedInstanceDates(schedule);
                if (!deletedDates.isEmpty()) {
                    log.info("课程 ID: {} 有 {} 个已删除的实例日期: {}", 
                            schedule.getId(), deletedDates.size(), String.join(", ", deletedDates));
                }
                
                // 获取课程配置的提醒时间，如果没有则使用系统默认值
                Integer courseNotifyBeforeMinutes = schedule.getNotifyBeforeMinutes();
                if (courseNotifyBeforeMinutes == null) {
                    courseNotifyBeforeMinutes = defaultNotifyBeforeMinutes;
                    log.info("课程 ID: {} 未设置提醒时间，使用系统默认值: {} 分钟", 
                            schedule.getId(), defaultNotifyBeforeMinutes);
                } else {
                    log.info("课程 ID: {} 已设置提醒时间: {} 分钟", schedule.getId(), courseNotifyBeforeMinutes);
                }
                
                // 确定通知对象
                boolean notifyTeachers = schedule.getNotifyTeachers() == null || schedule.getNotifyTeachers() == 1;
                boolean notifyStudents = schedule.getNotifyStudents() == null || schedule.getNotifyStudents() == 1;
                
                // 处理每个动态实例
                for (Date instanceDate : instanceDates) {
                    String instanceDateStr = DateFormatUtils.format(instanceDate, "yyyy-MM-dd");
                    
                    // 检查该日期是否在已删除实例列表中
                    if (deletedDates.contains(instanceDateStr)) {
                        log.info("跳过已删除的实例日期: {}", instanceDateStr);
                        continue;
                    }
                    
                    // 检查该日期是否已有修改后的实例
                    TeachingCourseScheduleInstance existingInstance = 
                        teachingCourseScheduleInstanceService.getInstanceByParentIdAndDate(schedule.getId(), instanceDateStr);
                    
                    if (existingInstance != null) {
                        log.info("跳过已存在修改实例的日期: {}, 实例ID: {}", instanceDateStr, existingInstance.getId());
                        continue;
                    }
                    
                    // 创建该实例日期的动态开始和结束时间
                    Date instanceStartTime = createInstanceTime(schedule.getStartTime(), instanceDate);
                    Date instanceEndTime = createInstanceTime(schedule.getEndTime(), instanceDate);
                    
                    // 检查实例开始时间是否在所需时间范围内
                    if (instanceStartTime.before(now) || instanceStartTime.after(twentyFourHoursLater)) {
                        log.info("实例开始时间 {} 不在目标时间范围内，跳过", 
                                DateFormatUtils.format(instanceStartTime, "yyyy-MM-dd HH:mm:ss"));
                        continue;
                    }
                    
                    log.info("处理动态课程实例 - 父课程ID: {}, 实例日期: {}, 开始时间: {}", 
                            schedule.getId(), 
                            instanceDateStr,
                            DateFormatUtils.format(instanceStartTime, "yyyy-MM-dd HH:mm:ss"));
                    
                    // 计算实际的提醒时间
                    Date remindTime = DateUtils.addMinutes(instanceStartTime, -courseNotifyBeforeMinutes);
                    
                    // 如果提醒时间已过，调整为当前时间后5分钟
                    if (remindTime.before(now)) {
                        Date originalRemindTime = remindTime;
                        remindTime = DateUtils.addMinutes(now, 5);
                        log.info("动态实例的原提醒时间 {} 已过，调整为当前时间后5分钟: {}", 
                                DateFormatUtils.format(originalRemindTime, "yyyy-MM-dd HH:mm:ss"),
                                DateFormatUtils.format(remindTime, "yyyy-MM-dd HH:mm:ss"));
                    }
                    
                    // 检查是否已经为该动态实例创建过提醒通知
                    if (!hasExistingReminderForDynamicInstance(schedule.getId(), instanceDateStr)) {
                        log.info("为动态实例创建提醒通知 - 父课程ID: {}, 实例日期: {}, 提醒时间: {}", 
                                schedule.getId(), instanceDateStr, 
                                DateFormatUtils.format(remindTime, "yyyy-MM-dd HH:mm:ss"));
                        
                        // 创建课程提醒通知
                        boolean success = createReminderForDynamicInstance(
                            schedule, instanceStartTime, instanceEndTime, instanceDate, remindTime, notifyTeachers, notifyStudents);
                        
                        if (success) {
                            createdCount++;
                            log.info("成功为动态实例创建提醒通知 - 父课程ID: {}, 实例日期: {}", 
                                    schedule.getId(), instanceDateStr);
                        } else {
                            log.warn("为动态实例创建提醒通知失败 - 父课程ID: {}, 实例日期: {}", 
                                    schedule.getId(), instanceDateStr);
                        }
                    } else {
                        log.info("动态实例已存在提醒通知，跳过创建 - 父课程ID: {}, 实例日期: {}", 
                                schedule.getId(), instanceDateStr);
                    }
                }
            }
            
            log.info("已处理未修改的单次课程实例，成功创建 {} 个提醒", createdCount);
        } catch (Exception e) {
            log.error("处理未修改的单次课程实例时发生异常", e);
            log.error("异常详情: {}", e.getMessage());
        }
        
        return createdCount;
    }
    
    /**
     * 根据父课程的重复规则计算指定时间范围内的动态实例日期
     * 
     * @param schedule 父课程
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实例日期列表
     */
    private List<Date> calculateInstanceDates(TeachingCourseSchedule schedule, Date startTime, Date endTime) {
        List<Date> instanceDates = new ArrayList<>();
        
        try {
            // 获取课程的开始日期（仅日期部分，不含时间）
            Date courseStartDate = DateUtils.truncate(schedule.getStartTime(), Calendar.DATE);
            // 获取课程的重复结束日期
            Date repeatEndDate = schedule.getRepeatEndDate();
            // 获取重复类型：1-每天重复，2-每周重复
            Integer repeatType = schedule.getRepeatType();
            
            // 记录详细日志
            log.debug("计算课程实例日期 - 课程ID: {}, 课程开始日期: {}, 重复结束日期: {}, 重复类型: {}", 
                    schedule.getId(), 
                    DateFormatUtils.format(courseStartDate, "yyyy-MM-dd"),
                    repeatEndDate != null ? DateFormatUtils.format(repeatEndDate, "yyyy-MM-dd") : "无",
                    repeatType);
            
            // 检查重复结束日期，如果没有设置或超出查询范围，则使用查询结束日期
            if (repeatEndDate == null || repeatEndDate.after(endTime)) {
                repeatEndDate = DateUtils.truncate(endTime, Calendar.DATE);
                log.debug("使用查询结束日期作为重复结束日期: {}", DateFormatUtils.format(repeatEndDate, "yyyy-MM-dd"));
            }
            
            // 确定开始检查的日期
            Date checkStartDate = courseStartDate.before(startTime) ? 
                    DateUtils.truncate(startTime, Calendar.DATE) : courseStartDate;
            
            log.debug("开始检查日期: {}, 结束检查日期: {}", 
                    DateFormatUtils.format(checkStartDate, "yyyy-MM-dd"),
                    DateFormatUtils.format(repeatEndDate, "yyyy-MM-dd"));
            
            // 当前检查日期
            Date currentDate = checkStartDate;
            
            // 根据重复类型计算实例日期
            switch (repeatType) {
                case 1: // 每天重复
                    log.debug("使用每天重复模式计算实例日期");
                    while (!currentDate.after(repeatEndDate)) {
                        instanceDates.add(currentDate);
                        currentDate = DateUtils.addDays(currentDate, 1);
                    }
                    break;
                    
                case 2: // 每周重复
                    String weekdays = schedule.getWeekdays();
                    if (StringUtils.isBlank(weekdays)) {
                        log.warn("课程 ID: {} 重复类型为每周，但未设置重复星期", schedule.getId());
                        break;
                    }
                    
                    // 解析重复星期 (0-6，逗号分隔)
                    Set<Integer> weekDaySet = parseWeekdays(weekdays);
                    if (weekDaySet.isEmpty()) {
                        log.warn("课程 ID: {} 重复星期解析为空", schedule.getId());
                        break;
                    }
                    
                    log.debug("使用每周重复模式计算实例日期，重复星期: {}", weekdays);
                    
                    while (!currentDate.after(repeatEndDate)) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(currentDate);
                        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK) - 1; // Calendar.DAY_OF_WEEK从1开始，转换为0-6
                        if (dayOfWeek < 0) dayOfWeek = 6; // 处理星期日
                        
                        if (weekDaySet.contains(dayOfWeek)) {
                            log.debug("添加实例日期: {}, 星期: {}", 
                                    DateFormatUtils.format(currentDate, "yyyy-MM-dd"), dayOfWeek);
                            instanceDates.add(currentDate);
                        }
                        
                        currentDate = DateUtils.addDays(currentDate, 1);
                    }
                    break;
                    
                default:
                    log.warn("课程 ID: {} 重复类型不支持: {}", schedule.getId(), repeatType);
                    break;
            }
            
            log.debug("计算完成，共找到 {} 个实例日期", instanceDates.size());
        } catch (Exception e) {
            log.error("计算课程实例日期时发生异常", e);
            log.error("异常详情: {}", e.getMessage());
        }
        
        return instanceDates;
    }
    
    /**
     * 解析重复星期字符串为整数集合
     * 
     * @param weekdays 重复星期字符串 (0-6，逗号分隔)
     * @return 整数集合
     */
    private Set<Integer> parseWeekdays(String weekdays) {
        Set<Integer> result = new HashSet<>();
        
        if (StringUtils.isBlank(weekdays)) {
            log.warn("重复星期字符串为空");
            return result;
        }
        
        try {
            log.debug("解析重复星期字符串: {}", weekdays);
            
            String[] days = weekdays.split(",");
            for (String day : days) {
                if (StringUtils.isNotBlank(day)) {
                    int dayValue = Integer.parseInt(day.trim());
                    // 验证星期值范围 (0-6)
                    if (dayValue >= 0 && dayValue <= 6) {
                        result.add(dayValue);
                        log.debug("添加星期值: {}", dayValue);
                    } else {
                        log.warn("忽略无效的星期值: {}, 有效范围为0-6", dayValue);
                }
            }
            }
            
            log.debug("解析结果，共{}个有效星期值: {}", result.size(), result);
        } catch (NumberFormatException e) {
            log.error("解析重复星期时发生数字格式错误: {}", weekdays);
        } catch (Exception e) {
            log.error("解析重复星期时发生异常: {}", weekdays, e);
            log.error("异常详情: {}", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取课程的已删除实例日期集合
     * 
     * @param schedule 课程排期
     * @return 已删除日期集合
     */
    private Set<String> getDeletedInstanceDates(TeachingCourseSchedule schedule) {
        Set<String> deletedDates = new HashSet<>();
        
        if (schedule == null) {
            log.warn("课程排期对象为空，无法获取已删除实例日期");
            return deletedDates;
        }
        
        // 如果没有已删除实例，直接返回空集合
        if (StringUtils.isBlank(schedule.getDeletedInstances())) {
            return deletedDates;
        }
        
        try {
            log.debug("解析课程ID:{}的已删除实例日期: {}", schedule.getId(), schedule.getDeletedInstances());
            
            String[] dates = schedule.getDeletedInstances().split(",");
            for (String date : dates) {
                if (StringUtils.isNotBlank(date)) {
                    String trimmedDate = date.trim();
                    
                    // 验证日期格式
                    if (trimmedDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        deletedDates.add(trimmedDate);
                        log.debug("添加已删除日期: {}", trimmedDate);
                    } else {
                        log.warn("忽略格式无效的日期: {}, 格式应为yyyy-MM-dd", trimmedDate);
                }
            }
            }
            
            log.debug("解析结果，共{}个有效已删除日期: {}", deletedDates.size(), deletedDates);
        } catch (Exception e) {
            log.error("解析已删除实例日期时发生异常: {}", schedule.getDeletedInstances(), e);
            log.error("异常详情: {}", e.getMessage());
        }
        
        return deletedDates;
    }
    
    /**
     * 创建实例的具体时间（保持小时、分钟、秒）
     * 
     * @param baseTime 基准时间
     * @param instanceDate 实例日期
     * @return 组合后的时间
     */
    private Date createInstanceTime(Date baseTime, Date instanceDate) {
        Calendar baseCal = Calendar.getInstance();
        baseCal.setTime(baseTime);
        
        Calendar instanceCal = Calendar.getInstance();
        instanceCal.setTime(instanceDate);
        
        // 保留baseTime的时分秒，使用instanceDate的年月日
        instanceCal.set(Calendar.HOUR_OF_DAY, baseCal.get(Calendar.HOUR_OF_DAY));
        instanceCal.set(Calendar.MINUTE, baseCal.get(Calendar.MINUTE));
        instanceCal.set(Calendar.SECOND, baseCal.get(Calendar.SECOND));
        
        return instanceCal.getTime();
    }
    
    /**
     * 为已修改的单次课程实例创建提醒通知
     * 
     * @param instance 课程实例
     * @param remindTime 提醒时间
     * @param notifyTeachers 是否通知教师
     * @param notifyStudents 是否通知学生
     * @return 是否成功创建
     */
    private boolean createReminderForInstance(TeachingCourseScheduleInstance instance, Date remindTime, 
                                              boolean notifyTeachers, boolean notifyStudents) {
        try {
            log.info("为已修改的单次课程实例创建提醒通知, ID: {}, 标题: {}, 提醒时间: {}", 
                    instance.getId(), 
                    instance.getScheduleTitle(),
                    DateFormatUtils.format(remindTime, "yyyy-MM-dd HH:mm:ss"));
            
            // 将实例转换为课程排期格式，以便复用现有的通知创建逻辑
            TeachingCourseSchedule scheduleFormat = convertInstanceToSchedule(instance);
            
            // 创建提醒通知
            return teachingCourseNotificationService.createCourseReminderNotification(
                scheduleFormat, remindTime, notifyTeachers, notifyStudents);
        } catch (Exception e) {
            log.error("为已修改的单次课程实例创建提醒通知时发生异常", e);
            return false;
        }
    }
    
    /**
     * 为动态计算的课程实例创建提醒通知
     * 
     * @param parentSchedule 父课程
     * @param instanceStartTime 实例开始时间
     * @param instanceEndTime 实例结束时间
     * @param instanceDate 实例日期
     * @param remindTime 提醒时间
     * @param notifyTeachers 是否通知教师
     * @param notifyStudents 是否通知学生
     * @return 是否成功创建
     */
    private boolean createReminderForDynamicInstance(TeachingCourseSchedule parentSchedule, 
                                                    Date instanceStartTime, 
                                                    Date instanceEndTime,
                                                    Date instanceDate,
                                                    Date remindTime, 
                                                    boolean notifyTeachers, 
                                                    boolean notifyStudents) {
        try {
            String instanceDateStr = DateFormatUtils.format(instanceDate, "yyyy-MM-dd");
            
            log.info("为动态课程实例创建提醒通知, 父课程ID: {}, 标题: {}, 实例日期: {}, 提醒时间: {}", 
                    parentSchedule.getId(), 
                    parentSchedule.getScheduleTitle(),
                    instanceDateStr,
                    DateFormatUtils.format(remindTime, "yyyy-MM-dd HH:mm:ss"));
            
            // 创建临时课程对象，包含该实例的具体时间
            TeachingCourseSchedule tempSchedule = new TeachingCourseSchedule();
            
            // 为动态实例创建特殊ID，格式: 父课程ID_yyyyMMdd
            String dynamicInstanceId = parentSchedule.getId() + "_" + DateFormatUtils.format(instanceDate, "yyyyMMdd");
            tempSchedule.setId(dynamicInstanceId);
            
            // 复制父课程的基本信息
            tempSchedule.setScheduleTitle(parentSchedule.getScheduleTitle());
            tempSchedule.setCourseId(parentSchedule.getCourseId());
            tempSchedule.setClassroomId(parentSchedule.getClassroomId());
            tempSchedule.setTeacherId(parentSchedule.getTeacherId());
            tempSchedule.setClassId(parentSchedule.getClassId());
            tempSchedule.setStudentNames(parentSchedule.getStudentNames());
            tempSchedule.setStartTime(instanceStartTime);
            tempSchedule.setEndTime(instanceEndTime);
            tempSchedule.setColor(parentSchedule.getColor());
            tempSchedule.setDescription(parentSchedule.getDescription());
            tempSchedule.setStatus(parentSchedule.getStatus());
            
            // 添加动态实例标记到描述中，但不要改变原始描述
            String enhancedDescription = tempSchedule.getDescription();
            if (enhancedDescription == null) {
                enhancedDescription = "动态生成的课程实例 (原课程ID: " + parentSchedule.getId() + ")";
            } else {
                enhancedDescription += "\n\n动态生成的课程实例 (原课程ID: " + parentSchedule.getId() + ")";
            }
            tempSchedule.setDescription(enhancedDescription);
            
            // 创建提醒通知，并在通知内容中添加动态实例标记
            boolean success = teachingCourseNotificationService.createCourseReminderNotification(
                tempSchedule, remindTime, notifyTeachers, notifyStudents);
            
            if (success) {
                log.info("成功为动态课程实例创建提醒通知，动态实例ID: {}", dynamicInstanceId);
            } else {
                log.warn("为动态课程实例创建提醒通知失败，动态实例ID: {}", dynamicInstanceId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("为动态课程实例创建提醒通知时发生异常", e);
            log.error("异常详情: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 将课程实例对象转换为课程排期对象格式
     * 
     * @param instance 课程实例
     * @return 课程排期格式
     */
    private TeachingCourseSchedule convertInstanceToSchedule(TeachingCourseScheduleInstance instance) {
        if (instance == null) {
            return null;
        }
        
        TeachingCourseSchedule schedule = new TeachingCourseSchedule();
        schedule.setId(instance.getId());
        schedule.setScheduleTitle(instance.getScheduleTitle());
        schedule.setCourseId(instance.getCourseId());
        schedule.setClassroomId(instance.getClassroomId());
        schedule.setTeacherId(instance.getTeacherId());
        schedule.setClassId(instance.getClassId());
        schedule.setStudentNames(instance.getStudentNames());
        schedule.setStartTime(instance.getStartTime());
        schedule.setEndTime(instance.getEndTime());
        schedule.setColor(instance.getColor());
        schedule.setDescription(instance.getDescription());
        schedule.setStatus(instance.getStatus());
        schedule.setNotifyBeforeMinutes(instance.getNotifyBeforeMinutes());
        schedule.setNotifyTeachers(instance.getNotifyTeachers());
        schedule.setNotifyStudents(instance.getNotifyStudents());
        schedule.setCreateBy(instance.getCreateBy());
        schedule.setCreateTime(instance.getCreateTime());
        schedule.setUpdateBy(instance.getUpdateBy());
        schedule.setUpdateTime(instance.getUpdateTime());
        
        return schedule;
    }
    
    /**
     * 标记课程实例已创建提醒通知
     * 
     * @param instanceId 实例ID
     * @return 是否更新成功
     */
    private boolean markInstanceReminderCreated(String instanceId) {
        try {
            if (StringUtils.isBlank(instanceId)) {
                log.warn("实例ID为空，无法标记为已创建提醒通知");
                return false;
            }
            
            // 查询课程实例
            TeachingCourseScheduleInstance instance = teachingCourseScheduleInstanceService.getById(instanceId);
            if (instance == null) {
                log.warn("未找到ID为{}的课程实例，无法标记为已创建提醒通知", instanceId);
                return false;
            }
            
            // 如果已经标记过，不需要再次更新
            if (instance.getReminderCreated() != null && instance.getReminderCreated() == 1) {
                log.info("实例 {} 已标记为已创建提醒通知，无需再次更新", instanceId);
                return true;
            }
            
            // 更新提醒创建标记
            instance.setReminderCreated(1);
            boolean success = teachingCourseScheduleInstanceService.updateById(instance);
            
            if (success) {
                log.info("成功标记实例 {} 为已创建提醒通知", instanceId);
            } else {
                log.warn("标记实例 {} 为已创建提醒通知失败", instanceId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("标记课程实例已创建提醒通知时发生异常，实例ID: {}", instanceId, e);
            log.error("异常详情: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 删除课程实例已存在的未发送提醒通知
     * 
     * @param instanceId 实例ID
     * @return 删除的通知数量
     */
    private int deleteExistingPendingReminders(String instanceId) {
        try {
            if (StringUtils.isBlank(instanceId)) {
                log.warn("实例ID为空，无法删除相关提醒通知");
                return 0;
            }
            
            // 查询与该实例相关的未发送提醒通知
            QueryWrapper<TeachingCourseNotification> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("schedule_id", instanceId);
            queryWrapper.eq("notification_type", "REMIND");
            queryWrapper.eq("is_sent", 0);
            
            List<TeachingCourseNotification> notifications = teachingCourseNotificationService.list(queryWrapper);
            
            if (notifications == null || notifications.isEmpty()) {
                log.debug("实例 {} 没有未发送的提醒通知需要删除", instanceId);
                return 0;
            }
            
            log.info("发现实例 {} 有 {} 个未发送的提醒通知，准备删除", instanceId, notifications.size());
            
            // 收集通知ID
            List<String> notificationIds = new ArrayList<>();
            StringBuilder logDetails = new StringBuilder();
            logDetails.append("待删除的通知详情：\n");
            
            for (TeachingCourseNotification notification : notifications) {
                notificationIds.add(notification.getId());
                logDetails.append(String.format(
                        "ID: %s, 标题: %s, 提醒时间: %s\n",
                        notification.getId(),
                        notification.getTitle(),
                        notification.getRemindTime() != null ? 
                            DateFormatUtils.format(notification.getRemindTime(), "yyyy-MM-dd HH:mm:ss") : "无"
                ));
            }
            
            log.debug(logDetails.toString());
            
            // 批量删除通知
            boolean success = teachingCourseNotificationService.removeByIds(notificationIds);
            
            if (success) {
                log.info("成功删除实例 {} 的 {} 个未发送提醒通知", instanceId, notificationIds.size());
                
                // 删除成功后，重置实例的reminder_created字段为0，允许重新创建提醒
                try {
                    TeachingCourseScheduleInstance instance = teachingCourseScheduleInstanceService.getById(instanceId);
                    if (instance != null) {
                        log.info("重置实例 {} 的reminder_created字段为0，以便重新创建提醒通知", instanceId);
                        instance.setReminderCreated(0);
                        boolean updateResult = teachingCourseScheduleInstanceService.updateById(instance);
                        // 增加更新结果日志
                        if (updateResult) {
                            log.info("成功重置实例 {} 的reminder_created字段为0", instanceId);
                        } else {
                            log.warn("重置实例 {} 的reminder_created字段失败", instanceId);
                        }
                    } else {
                        log.warn("未找到实例ID: {}，无法重置reminder_created字段", instanceId);
                    }
                } catch (Exception e) {
                    log.error("重置实例reminder_created字段失败, 实例ID: {}", instanceId, e);
                }
                
                return notificationIds.size();
            } else {
                log.warn("删除实例 {} 的未发送提醒通知失败", instanceId);
                return 0;
            }
        } catch (Exception e) {
            log.error("删除课程实例已存在的未发送提醒通知时发生异常，实例ID: {}", instanceId, e);
            log.error("异常详情: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 检查动态课程实例是否已经存在提醒通知
     * 
     * @param parentId 父课程ID
     * @param instanceDate 实例日期
     * @return 是否存在提醒通知
     */
    private boolean hasExistingReminderForDynamicInstance(String parentId, String instanceDate) {
        try {
            // 构建动态实例ID，格式: 父课程ID_yyyyMMdd
            String formattedDate = instanceDate.replace("-", "");
            String dynamicInstanceId = parentId + "_" + formattedDate;
            
            log.debug("检查动态实例是否已存在提醒通知，动态实例ID: {}", dynamicInstanceId);
            
            // 查询与该动态实例相关的提醒通知
            QueryWrapper<TeachingCourseNotification> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("schedule_id", dynamicInstanceId);
            queryWrapper.eq("notification_type", "REMIND");
            
            // 检查是否存在通知记录
            int count = (int) teachingCourseNotificationService.count(queryWrapper); // 阶段二：MyBatis-Plus 3.4.x兼容性修复
            
            if (count > 0) {
                log.info("动态实例已存在提醒通知，找到 {} 条记录，动态实例ID: {}", count, dynamicInstanceId);
                return true;
            } else {
                log.debug("动态实例不存在提醒通知，动态实例ID: {}", dynamicInstanceId);
                return false;
            }
        } catch (Exception e) {
            log.error("检查动态课程实例是否已存在提醒通知时发生异常", e);
            log.error("异常详情: {}", e.getMessage());
            // 如果出现异常，为安全起见返回false，允许创建提醒（避免漏发提醒）
            return false;
        }
    }
    
    /**
     * 删除动态课程实例的未发送提醒通知
     * 用于解决"未修改的单次课程"被修改为"已修改的单次课程"时的重复提醒问题
     * 
     * @param parentId 父课程ID
     * @param instanceDate 实例日期 (格式: yyyy-MM-dd)
     * @return 删除的通知数量
     */
    private int deleteExistingDynamicInstanceReminders(String parentId, String instanceDate) {
        try {
            if (StringUtils.isBlank(parentId) || StringUtils.isBlank(instanceDate)) {
                log.warn("父课程ID或实例日期为空，无法删除动态实例提醒通知");
                return 0;
            }
            
            // 构建动态实例ID，格式: 父课程ID_yyyyMMdd
            String formattedDate = instanceDate.replace("-", "");
            String dynamicInstanceId = parentId + "_" + formattedDate;
            
            log.info("检查是否存在动态实例提醒通知需要删除，动态实例ID: {}", dynamicInstanceId);
            
            // 查询与该动态实例相关的未发送提醒通知
            QueryWrapper<TeachingCourseNotification> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("schedule_id", dynamicInstanceId);
            queryWrapper.eq("notification_type", "REMIND");
            queryWrapper.eq("is_sent", 0);
            
            List<TeachingCourseNotification> notifications = teachingCourseNotificationService.list(queryWrapper);
            
            if (notifications == null || notifications.isEmpty()) {
                log.debug("动态实例 {} 没有未发送的提醒通知需要删除", dynamicInstanceId);
                return 0;
            }
            
            log.info("发现动态实例 {} 有 {} 个未发送的提醒通知，准备删除", dynamicInstanceId, notifications.size());
            
            // 收集通知ID
            List<String> notificationIds = new ArrayList<>();
            StringBuilder logDetails = new StringBuilder();
            logDetails.append("待删除的动态实例通知详情：\n");
            
            for (TeachingCourseNotification notification : notifications) {
                notificationIds.add(notification.getId());
                logDetails.append(String.format(
                        "ID: %s, 标题: %s, 提醒时间: %s\n",
                        notification.getId(),
                        notification.getTitle(),
                        notification.getRemindTime() != null ? 
                            DateFormatUtils.format(notification.getRemindTime(), "yyyy-MM-dd HH:mm:ss") : "无"
                ));
            }
            
            log.debug(logDetails.toString());
            
            // 批量删除通知
            boolean success = teachingCourseNotificationService.removeByIds(notificationIds);
            
            if (success) {
                log.info("成功删除动态实例 {} 的 {} 个未发送提醒通知", dynamicInstanceId, notificationIds.size());
                return notificationIds.size();
            } else {
                log.warn("删除动态实例 {} 的未发送提醒通知失败", dynamicInstanceId);
                return 0;
            }
        } catch (Exception e) {
            log.error("删除动态课程实例提醒通知时发生异常，父课程ID: {}, 实例日期: {}", parentId, instanceDate, e);
            log.error("异常详情: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 获取系统默认通知提前时间(分钟)
     * 
     * @return 默认通知提前时间，如果配置不存在或无效则返回15分钟
     */
    private Integer getDefaultNotifyBeforeMinutes() {
        final Integer DEFAULT_NOTIFY_MINUTES = 15; // 默认提前15分钟
        
        try {
            // 从系统配置中获取默认通知时间
            String configValue = sysConfigService.getConfigItem(CONFIG_KEY_NOTIFY_BEFORE_MINUTES);
            
            if (StringUtils.isNotBlank(configValue)) {
                try {
                    Integer minutes = Integer.parseInt(configValue);
                    
                    // 验证配置值是否合理（至少1分钟，最多1440分钟/24小时）
                    if (minutes >= 1 && minutes <= 1440) {
                        log.info("从系统配置获取默认提醒时间: {} 分钟", minutes);
                        return minutes;
                    } else {
                        log.warn("系统配置的提醒时间值 {} 超出有效范围(1-1440)，使用默认值: {} 分钟", 
                                minutes, DEFAULT_NOTIFY_MINUTES);
                    }
                } catch (NumberFormatException e) {
                    log.warn("系统配置的提醒时间值 {} 格式无效，使用默认值: {} 分钟", 
                            configValue, DEFAULT_NOTIFY_MINUTES);
                }
            } else {
                log.info("系统未配置默认提醒时间，使用默认值: {} 分钟", DEFAULT_NOTIFY_MINUTES);
            }
        } catch (Exception e) {
            log.error("获取默认通知时间配置异常", e);
            log.error("异常详情: {}", e.getMessage());
        }
        
        // 如果获取失败，返回15分钟作为默认值
        return DEFAULT_NOTIFY_MINUTES;
    }
} 