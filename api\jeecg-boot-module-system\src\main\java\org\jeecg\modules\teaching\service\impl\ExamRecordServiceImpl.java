package org.jeecg.modules.teaching.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.entity.ExamRecord;
import org.jeecg.modules.teaching.mapper.ExamRecordMapper;
import org.jeecg.modules.teaching.service.IExamMistakeService;
import org.jeecg.modules.teaching.service.IExamPaperService;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.jeecg.modules.teaching.service.IExamRecordService;
import org.jeecg.modules.teaching.service.ITeachingJudgeService;
import org.jeecg.modules.teaching.dto.TestJudgeRequestDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import java.util.stream.Collectors;

/**
 * @Description: 考试记录
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class ExamRecordServiceImpl extends ServiceImpl<ExamRecordMapper, ExamRecord> implements IExamRecordService {

    @Autowired
    private IExamPaperService examPaperService;

    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    private IExamMistakeService examMistakeService;

    @Autowired
    private ITeachingJudgeService teachingJudgeService;

    // 注意：已移除ONGOING_EXAMS，因为不再需要"进行中"状态

    // 注意：已移除startExam方法，因为不再需要"进行中"状态
    // 考试记录将在submitExam时直接创建

    @Override
    @Transactional
    public Result<?> submitExam(String userId, String paperId, Date startTime, Map<String, Object> answers) {
        log.info("=== 开始提交考试 ===");
        log.info("用户ID: {}, 试卷ID: {}, 开始时间: {}", userId, paperId, startTime);
        log.info("提交的答案数据: {}", answers);

        // 验证参数
        if (userId == null || paperId == null || startTime == null) {
            log.error("参数不完整: userId={}, paperId={}, startTime={}", userId, paperId, startTime);
            return Result.error("参数不完整");
        }

        // 查询试卷
        ExamPaper paper = examPaperService.getById(paperId);
        if (paper == null) {
            log.error("试卷不存在，paperId: {}", paperId);
            return Result.error("试卷不存在");
        }
        log.info("找到试卷: title={}", paper.getTitle());

        // 获取试卷内容，解析题目
        JSONObject paperContent = JSON.parseObject(paper.getContent());
        log.info("=== 试卷配置调试 ===");
        log.info("试卷ID: {}", paperId);
        log.info("试卷内容: {}", paper.getContent());
        List<Map<String, Object>> questions = new ArrayList<>();
        paperContent.getJSONArray("questions").forEach(item -> {
            @SuppressWarnings("unchecked")
            Map<String, Object> questionMap = (Map<String, Object>) item;
            questions.add(questionMap);
        });
        log.info("试卷包含 {} 道题目", questions.size());

        // 查询所有题目信息
        List<String> questionIds = questions.stream()
                .map(q -> q.get("questionId").toString())
                .collect(Collectors.toList());
        log.info("题目ID列表: {}", questionIds);

        List<ExamQuestion> questionList = new ArrayList<>(examQuestionService.listByIds(questionIds));
        Map<String, ExamQuestion> questionMap = questionList.stream()
                .collect(Collectors.toMap(ExamQuestion::getId, q -> q));
        log.info("成功查询到 {} 道题目详情", questionList.size());

        // 计算得分并记录错题
        int totalScore = 0;
        int singleChoiceScore = 0;  // 单选题得分
        int judgmentScore = 0;      // 判断题得分
        int programmingScore = 0;   // 编程题得分
        int singleChoiceTotal = 0;  // 单选题满分
        int judgmentTotal = 0;      // 判断题满分
        int programmingTotal = 0;   // 编程题满分
        Date now = new Date();
        log.info("开始逐题评分...");

        for (Map<String, Object> question : questions) {
            String questionId = question.get("questionId").toString();
            int score = Integer.parseInt(question.get("score").toString());

            log.info("--- 开始评分题目 ---");
            log.info("题目ID: {}, 满分: {}", questionId, score);
            log.info("题目配置详情: {}", question);

            ExamQuestion examQuestion = questionMap.get(questionId);
            if (examQuestion == null) {
                log.warn("题目详情不存在，跳过题目: {}", questionId);
                continue;
            }

            // 根据题型获取用户答案
            String userAnswer = getUserAnswer(answers, questionId, examQuestion.getQuestionType());
            log.info("用户答案: {}", userAnswer);

            // 根据题型判断答案正确性
            Integer questionType = examQuestion.getQuestionType();
            JSONObject content = JSON.parseObject(examQuestion.getContent());
            log.info("题目类型: {} (1:单选题, 2:判断题, 3:编程题)", questionType);

            if (questionType == 1) { // 单选题
                log.info("处理单选题评分");
                singleChoiceTotal += score; // 累计单选题满分
                String correctAnswer = content.getString("answer");
                log.info("正确答案: {}", correctAnswer);

                if (correctAnswer != null && correctAnswer.equalsIgnoreCase(userAnswer)) {
                    singleChoiceScore += score; // 单选题得分
                    totalScore += score; // 总分
                    log.info("单选题答案正确，得分: {}, 单选题总分: {}, 当前总分: {}", score, singleChoiceScore, totalScore);
                } else {
                    // 记录错题
                    examMistakeService.recordMistake(userId, questionId, userAnswer, now);
                    log.info("单选题答案错误，得分: 0, 已记录错题");
                }
            } else if (questionType == 2) { // 判断题
                log.info("处理判断题评分");
                judgmentTotal += score; // 累计判断题满分
                String correctAnswer = content.getString("answer");
                log.info("正确答案: {}", correctAnswer);

                if (correctAnswer != null && correctAnswer.equalsIgnoreCase(userAnswer)) {
                    judgmentScore += score; // 判断题得分
                    totalScore += score; // 总分
                    log.info("判断题答案正确，得分: {}, 判断题总分: {}, 当前总分: {}", score, judgmentScore, totalScore);
                } else {
                    // 记录错题
                    examMistakeService.recordMistake(userId, questionId, userAnswer, now);
                    log.info("判断题答案错误，得分: 0, 已记录错题");
                }
            } else if (questionType == 3) { // 编程题
                log.info("*** 开始处理编程题评分 ***");
                log.info("编程题ID: {}, 满分: {}", questionId, score);
                log.info("用户提交的代码内容: {}", userAnswer);

                programmingTotal += score; // 累计编程题满分

                // 处理编程题评分 - 检查是否已有判题结果
                int currentProgrammingScore = evaluateProgrammingQuestionFromSubmission(questionId, userAnswer, score);
                log.info("*** 编程题评分结果: {} / {} ***", currentProgrammingScore, score);

                programmingScore += currentProgrammingScore; // 编程题得分
                totalScore += currentProgrammingScore; // 总分
                log.info("编程题加分后，编程题总分: {}, 当前总分: {}", programmingScore, totalScore);

                // 如果编程题得分为0，记录为错题
                if (currentProgrammingScore == 0) {
                    examMistakeService.recordMistake(userId, questionId, userAnswer, now);
                    log.info("编程题得分为0，已记录错题");
                } else {
                    log.info("编程题得分正常: {}, 未记录错题", currentProgrammingScore);
                }
            }
            log.info("--- 题目评分完成 ---");
        }

        // 构建详细分数统计
        Map<String, Object> scoreDetailsMap = new HashMap<>();

        // 单选题得分详情
        Map<String, Object> singleChoiceScoreDetail = new HashMap<>();
        singleChoiceScoreDetail.put("score", singleChoiceScore);
        singleChoiceScoreDetail.put("totalScore", singleChoiceTotal);
        scoreDetailsMap.put("singleChoice", singleChoiceScoreDetail);

        // 判断题得分详情
        Map<String, Object> judgmentScoreDetail = new HashMap<>();
        judgmentScoreDetail.put("score", judgmentScore);
        judgmentScoreDetail.put("totalScore", judgmentTotal);
        scoreDetailsMap.put("judgment", judgmentScoreDetail);

        // 编程题得分详情
        Map<String, Object> programmingScoreDetail = new HashMap<>();
        programmingScoreDetail.put("score", programmingScore);
        programmingScoreDetail.put("totalScore", programmingTotal);
        scoreDetailsMap.put("programming", programmingScoreDetail);

        log.info("=== 详细分数统计构建完成 ===");
        log.info("单选题: {} / {}", singleChoiceScore, singleChoiceTotal);
        log.info("判断题: {} / {}", judgmentScore, judgmentTotal);
        log.info("编程题: {} / {}", programmingScore, programmingTotal);
        log.info("分数详情JSON: {}", JSON.toJSONString(scoreDetailsMap));

        // 创建考试记录
        ExamRecord record = new ExamRecord();
        record.setUserId(userId);
        record.setPaperId(paperId);
        record.setStartTime(startTime);
        record.setEndTime(now);
        record.setScore(totalScore);
        record.setAnswers(JSON.toJSONString(answers));
        record.setScoreDetails(JSON.toJSONString(scoreDetailsMap)); // 保存详细分数统计
        record.setStatus(1); // 1:已提交（注意：数据库注释已更新，1表示已提交）
        this.save(record);

        log.info("考试记录创建成功，recordId: {}", record.getId());
        
        // 返回考试结果
        Map<String, Object> result = new HashMap<>();
        result.put("recordId", record.getId());
        result.put("score", totalScore);
        result.put("passingScore", 60); // 通过分数为60分
        result.put("isPassed", totalScore >= 60);
        result.put("paperTitle", paper.getTitle()); // 试卷标题
        result.put("submitTime", now); // 提交时间

        // 详细得分信息
        Map<String, Object> details = new HashMap<>();

        // 单选题得分详情
        Map<String, Object> singleChoiceDetails = new HashMap<>();
        singleChoiceDetails.put("score", singleChoiceScore);
        singleChoiceDetails.put("totalScore", singleChoiceTotal);
        details.put("singleChoice", singleChoiceDetails);

        // 判断题得分详情
        Map<String, Object> judgmentDetails = new HashMap<>();
        judgmentDetails.put("score", judgmentScore);
        judgmentDetails.put("totalScore", judgmentTotal);
        details.put("judgment", judgmentDetails);

        // 编程题得分详情
        Map<String, Object> programmingDetails = new HashMap<>();
        programmingDetails.put("score", programmingScore);
        programmingDetails.put("totalScore", programmingTotal);
        details.put("programming", programmingDetails);

        result.put("details", details);
        result.put("fullScore", singleChoiceTotal + judgmentTotal + programmingTotal); // 总满分

        log.info("=== 考试提交完成 ===");
        log.info("总得分: {} / {}", totalScore, singleChoiceTotal + judgmentTotal + programmingTotal);
        log.info("单选题得分: {} / {}", singleChoiceScore, singleChoiceTotal);
        log.info("判断题得分: {} / {}", judgmentScore, judgmentTotal);
        log.info("编程题得分: {} / {}", programmingScore, programmingTotal);

        return Result.ok((Object) result);
    }

    @Override
    public Result<?> getPaperDetail(String paperId) {
        // 查询试卷信息
        ExamPaper paper = examPaperService.getById(paperId);
        if (paper == null) {
            return Result.error("试卷不存在");
        }
        
        // 获取试卷内容
        JSONObject paperContent = JSON.parseObject(paper.getContent());
        List<Map<String, Object>> questions = new ArrayList<>();
        paperContent.getJSONArray("questions").forEach(item -> {
            @SuppressWarnings("unchecked")
            Map<String, Object> questionMap = (Map<String, Object>) item;
            questions.add(questionMap);
        });
        
        // 查询所有题目信息
        List<String> questionIds = questions.stream()
                .map(q -> q.get("questionId").toString())
                .collect(Collectors.toList());
        
        List<ExamQuestion> questionList = new ArrayList<>(examQuestionService.listByIds(questionIds));
        
        // 准备返回数据，按题型分类
        List<Map<String, Object>> singleChoiceList = new ArrayList<>();
        List<Map<String, Object>> judgmentList = new ArrayList<>();
        List<Map<String, Object>> programmingList = new ArrayList<>();
        
        for (ExamQuestion question : questionList) {
            // 将题目转换为前端所需格式
            Map<String, Object> questionMap = new HashMap<>();
            questionMap.put("id", question.getId());
            questionMap.put("title", question.getTitle());
            questionMap.put("questionType", question.getQuestionType());
            questionMap.put("subject", question.getSubject());
            questionMap.put("level", question.getLevel());
            questionMap.put("difficulty", question.getDifficulty());
            
            JSONObject content = JSON.parseObject(question.getContent());
            
            switch (question.getQuestionType()) {
                case 1: // 单选题
                    questionMap.put("options", content.getJSONArray("options"));
                    // 不包含答案和解析
                    singleChoiceList.add(questionMap);
                    break;
                case 2: // 判断题
                    // 不包含答案和解析
                    judgmentList.add(questionMap);
                    break;
                case 3: // 编程题
                    questionMap.put("description", content.getString("description"));
                    questionMap.put("timeLimit", content.getInteger("time_limit"));
                    questionMap.put("memoryLimit", content.getInteger("memory_limit"));
                    questionMap.put("inputFormat", content.getString("input_format"));
                    questionMap.put("outputFormat", content.getString("output_format"));
                    questionMap.put("sampleCases", content.getJSONArray("sample_cases"));
                    questionMap.put("hint", content.getString("hint"));
                    programmingList.add(questionMap);
                    break;
            }
        }
        
        // 组装试卷信息
        Map<String, Object> result = new HashMap<>();
        result.put("paperId", paper.getId());
        result.put("title", paper.getTitle());
        result.put("subject", paper.getSubject());
        result.put("level", paper.getLevel());
        result.put("difficulty", paper.getDifficulty());
        result.put("examDuration", paper.getExamDuration());
        result.put("singleChoiceQuestions", singleChoiceList);
        result.put("judgmentQuestions", judgmentList);
        result.put("programmingQuestions", programmingList);
        
        return Result.ok(result);
    }



    /**
     * 从前端提交的数据中评估编程题得分（优先使用已有判题结果）
     * @param questionId 题目ID
     * @param userAnswer 用户提交的答案数据
     * @param maxScore 题目满分
     * @return 实际得分
     */
    private int evaluateProgrammingQuestionFromSubmission(String questionId, String userAnswer, int maxScore) {
        log.info(">>> 开始从提交数据评估编程题 <<<");
        log.info("题目ID: {}, 满分: {}", questionId, maxScore);
        log.info("用户答案数据: {}", userAnswer);

        if (userAnswer == null || userAnswer.trim().isEmpty()) {
            log.warn("用户答案数据为空，返回0分");
            return 0;
        }

        try {
            // 解析前端提交的答案数据
            JSONObject answerJson = JSON.parseObject(userAnswer);

            // 检查是否已经有判题结果
            Boolean isCorrect = answerJson.getBoolean("isCorrect");
            Boolean submitted = answerJson.getBoolean("submitted");

            log.info("是否已提交评测: {}", submitted);
            log.info("判题结果: {}", isCorrect);

            if (submitted != null && submitted && isCorrect != null) {
                // 如果已经提交过评测且有结果，直接使用结果
                int score = isCorrect ? maxScore : 0;
                log.info("使用已有判题结果，得分: {} / {}", score, maxScore);
                return score;
            } else {
                // 如果没有判题结果，则进行判题
                log.info("未找到已有判题结果，进行实时判题");
                String code = answerJson.getString("code");
                String language = answerJson.getString("language");

                if (code == null || code.trim().isEmpty()) {
                    log.warn("代码为空，返回0分");
                    return 0;
                }

                // 构造代码JSON字符串，调用原有判题方法
                JSONObject codeJson = new JSONObject();
                codeJson.put("code", code);
                codeJson.put("language", language);

                return evaluateProgrammingQuestion(questionId, codeJson.toJSONString(), maxScore);
            }

        } catch (Exception e) {
            log.error("解析编程题答案数据失败", e);
            // 如果解析失败，尝试按原有格式处理
            return evaluateProgrammingQuestion(questionId, userAnswer, maxScore);
        }
    }

    /**
     * 评估编程题得分（实时判题）
     * @param questionId 题目ID
     * @param userCode 用户提交的代码
     * @param maxScore 题目满分
     * @return 实际得分
     */
    private int evaluateProgrammingQuestion(String questionId, String userCode, int maxScore) {
        log.info(">>> 开始评估编程题 <<<");
        log.info("题目ID: {}, 满分: {}", questionId, maxScore);
        log.info("用户代码原始数据: {}", userCode);

        if (userCode == null || userCode.trim().isEmpty()) {
            log.warn("用户代码为空，返回0分");
            return 0; // 没有提交代码，得0分
        }

        try {
            // 解析用户提交的代码（JSON格式）
            JSONObject codeJson = JSON.parseObject(userCode);
            String code = codeJson.getString("code");
            String language = codeJson.getString("language");
            log.info("解析后的代码: {}", code);
            log.info("编程语言: {}", language);

            if (code == null || code.trim().isEmpty()) {
                log.warn("解析后的代码为空，返回0分");
                return 0; // 代码为空，得0分
            }

            // 构建判题请求
            TestJudgeRequestDTO judgeRequest = new TestJudgeRequestDTO();
            judgeRequest.setPid(questionId);
            judgeRequest.setCode(code);
            judgeRequest.setLanguage(language);
            log.info("构建判题请求: pid={}, language={}", questionId, language);

            // 提交判题
            log.info("开始提交判题请求...");
            Map<String, Object> submitResult = teachingJudgeService.submitTestJudge(judgeRequest);
            log.info("判题提交结果: {}", submitResult);

            String submissionId = (String) submitResult.get("submissionId");
            log.info("获得提交ID: {}", submissionId);

            if (submissionId == null) {
                log.error("提交ID为空，判题失败，返回0分");
                return 0;
            }

            // 轮询获取判题结果（最多等待30秒）
            log.info("开始轮询判题结果，最多等待30秒...");
            Map<String, Object> judgeResult = null;
            int maxRetries = 30; // 30次，每次等待1秒
            int retryCount = 0;

            while (retryCount < maxRetries) {
                try {
                    Thread.sleep(1000); // 等待1秒
                    log.info("第{}次查询判题结果...", retryCount + 1);
                    judgeResult = teachingJudgeService.getTestJudgeResult(submissionId);
                    log.info("查询到的判题结果: {}", judgeResult);

                    if (judgeResult != null) {
                        Object statusObj = judgeResult.get("status");
                        log.info("判题状态: {} (类型: {})", statusObj, statusObj != null ? statusObj.getClass().getSimpleName() : "null");

                        if (statusObj != null) {
                            // 处理字符串状态（如 "Accepted", "Pending" 等）
                            if (statusObj instanceof String) {
                                String status = (String) statusObj;
                                log.info("字符串状态: {}", status);
                                // 如果不是等待状态，则判题完成
                                if (!"Pending".equals(status) && !"Judging".equals(status) && !"Compiling".equals(status) && !"Submitting".equals(status)) {
                                    log.info("判题完成，状态: {}", status);
                                    break;
                                } else {
                                    log.info("判题仍在进行中，状态: {}", status);
                                }
                            }
                            // 处理数字状态码（兼容性）
                            else if (statusObj instanceof Integer) {
                                Integer status = (Integer) statusObj;
                                log.info("数字状态码: {}", status);
                                if (status != 5 && status != 6 && status != 7 && status != 9) { // 5=Pending, 6=Compiling, 7=Judging, 9=Submitting
                                    log.info("判题完成，状态码: {}", status);
                                    break;
                                } else {
                                    log.info("判题仍在进行中，状态码: {}", status);
                                }
                            }
                        }
                    } else {
                        log.warn("查询到的判题结果为null");
                    }
                    retryCount++;
                } catch (InterruptedException e) {
                    log.error("轮询被中断", e);
                    Thread.currentThread().interrupt();
                    return 0;
                }
            }

            if (judgeResult == null) {
                log.error("轮询超时，未获取到判题结果，返回0分");
                return 0;
            }

            log.info("最终判题结果: {}", judgeResult);
            // 根据判题结果计算分数
            int finalScore = calculateProgrammingScore(judgeResult, maxScore);
            log.info(">>> 编程题评估完成，最终得分: {} / {} <<<", finalScore, maxScore);
            return finalScore;

        } catch (Exception e) {
            log.error("编程题评估异常", e);
            return 0;
        }
    }

    /**
     * 根据判题结果计算编程题分数
     * @param judgeResult 判题结果
     * @param maxScore 满分
     * @return 实际得分
     */
    private int calculateProgrammingScore(Map<String, Object> judgeResult, int maxScore) {
        log.info("=== 开始计算编程题分数 ===");
        log.info("判题结果: {}", judgeResult);
        log.info("满分: {}", maxScore);

        Object statusObj = judgeResult.get("status");
        log.info("状态对象: {} (类型: {})", statusObj, statusObj != null ? statusObj.getClass().getSimpleName() : "null");

        if (statusObj == null) {
            log.warn("状态对象为null，返回0分");
            return 0;
        }

        // 处理字符串状态（如 "Accepted"）
        if (statusObj instanceof String) {
            String status = (String) statusObj;
            log.info("处理字符串状态: {}", status);
            if ("Accepted".equals(status)) {
                log.info("状态为Accepted，返回满分: {}", maxScore);
                return maxScore; // 满分
            } else {
                log.info("状态为{}，不是Accepted，返回0分", status);
                return 0; // 其他情况都是0分
            }
        }

        // 处理数字状态码（如 0, -1, -2 等）
        if (statusObj instanceof Integer) {
            Integer status = (Integer) statusObj;
            log.info("处理数字状态码: {}", status);
            switch (status) {
                case 0: // Accepted
                    log.info("状态码为0 (Accepted)，返回满分: {}", maxScore);
                    return maxScore; // 满分
                case -1: // Wrong Answer
                    log.info("状态码为-1 (Wrong Answer)，返回0分");
                    return 0;
                case -2: // Time Limit Exceeded
                    log.info("状态码为-2 (Time Limit Exceeded)，返回0分");
                    return 0;
                case -3: // Memory Limit Exceeded
                    log.info("状态码为-3 (Memory Limit Exceeded)，返回0分");
                    return 0;
                case -4: // Runtime Error
                    log.info("状态码为-4 (Runtime Error)，返回0分");
                    return 0;
                case -5: // Compile Error
                    log.info("状态码为-5 (Compile Error)，返回0分");
                    return 0;
                case -6: // Presentation Error
                    log.info("状态码为-6 (Presentation Error)，返回0分");
                    return 0;
                case -7: // System Error
                    log.info("状态码为-7 (System Error)，返回0分");
                    return 0;
                case -8: // Security Error
                    log.info("状态码为-8 (Security Error)，返回0分");
                    return 0;
                default:
                    log.info("未知状态码: {}，返回0分", status);
                    return 0; // 其他情况都是0分
            }
        }

        log.warn("未知状态类型: {}，返回0分", statusObj.getClass().getSimpleName());
        return 0; // 未知状态类型
    }

    /**
     * 根据题型获取用户答案
     * @param answers 前端提交的答案Map
     * @param questionId 题目ID
     * @param questionType 题目类型
     * @return 用户答案
     */
    private String getUserAnswer(Map<String, Object> answers, String questionId, Integer questionType) {
        String answerKey = null;

        // 根据题型构建答案的key
        if (questionType == 1) { // 单选题
            answerKey = "single_" + questionId;
        } else if (questionType == 2) { // 判断题
            answerKey = "judgment_" + questionId;
        } else if (questionType == 3) { // 编程题
            answerKey = "programming_" + questionId;
        }

        if (answerKey == null || !answers.containsKey(answerKey)) {
            return "";
        }

        Object answerObj = answers.get(answerKey);
        if (answerObj == null) {
            return "";
        }

        // 解析答案对象
        if (answerObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> answerMap = (Map<String, Object>) answerObj;

            if (questionType == 1 || questionType == 2) { // 客观题
                Object answer = answerMap.get("answer");
                return answer != null ? answer.toString() : "";
            } else if (questionType == 3) { // 编程题
                // 编程题需要返回包含完整信息的JSON
                String code = answerMap.get("code") != null ? answerMap.get("code").toString() : "";
                String language = answerMap.get("language") != null ? answerMap.get("language").toString() : "C++";
                Boolean submitted = answerMap.get("submitted") != null ? (Boolean) answerMap.get("submitted") : false;
                Boolean isCorrect = answerMap.get("isCorrect") != null ? (Boolean) answerMap.get("isCorrect") : false;

                JSONObject answerJson = new JSONObject();
                answerJson.put("code", code);
                answerJson.put("language", language);
                answerJson.put("submitted", submitted);
                answerJson.put("isCorrect", isCorrect);
                return answerJson.toJSONString();
            }
        }

        return answerObj.toString();
    }

    @Override
    public Result<?> getExamRecordPaperPreview(String recordId) {
        log.info("=== 开始获取考试记录试卷预览 ===");
        log.info("记录ID: {}", recordId);

        try {
            // 1. 获取考试记录
            ExamRecord examRecord = this.getById(recordId);
            if (examRecord == null) {
                log.error("考试记录不存在 - 记录ID: {}", recordId);
                return Result.error("考试记录不存在");
            }

            // 2. 获取试卷信息
            ExamPaper paper = examPaperService.getById(examRecord.getPaperId());
            if (paper == null) {
                log.error("试卷不存在 - 试卷ID: {}", examRecord.getPaperId());
                return Result.error("试卷不存在");
            }

            // 3. 解析试卷内容
            JSONObject paperContent = JSON.parseObject(paper.getContent());
            List<Map<String, Object>> questions = new ArrayList<>();
            paperContent.getJSONArray("questions").forEach(item -> {
                @SuppressWarnings("unchecked")
                Map<String, Object> questionMap = (Map<String, Object>) item;
                questions.add(questionMap);
            });

            // 4. 获取所有题目详情
            List<String> questionIds = questions.stream()
                    .map(q -> q.get("questionId").toString())
                    .collect(Collectors.toList());

            List<ExamQuestion> examQuestions = new ArrayList<>(examQuestionService.listByIds(questionIds));
            Map<String, ExamQuestion> questionMap = examQuestions.stream()
                    .collect(Collectors.toMap(ExamQuestion::getId, q -> q));

            // 5. 解析用户答案
            Map<String, Object> userAnswers = new HashMap<>();
            if (examRecord.getAnswers() != null && !examRecord.getAnswers().isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> parsedAnswers = JSON.parseObject(examRecord.getAnswers(), Map.class);
                userAnswers = parsedAnswers;
            }

            // 6. 解析分数详情
            Map<String, Object> scoreDetails = new HashMap<>();
            if (examRecord.getScoreDetails() != null && !examRecord.getScoreDetails().isEmpty()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> parsedScoreDetails = JSON.parseObject(examRecord.getScoreDetails(), Map.class);
                scoreDetails = parsedScoreDetails;
            }

            log.info("=== 调试信息 ===");
            log.info("用户答案数据: {}", examRecord.getAnswers());
            log.info("分数详情数据: {}", examRecord.getScoreDetails());
            log.info("解析后的用户答案: {}", userAnswers);
            log.info("解析后的分数详情: {}", scoreDetails);
            log.info("试卷预览数据准备 - 题目总数: {}, 用户答案数: {}",
                    examQuestions.size(), userAnswers.size());

            // 7. 构建预览数据
            return buildPaperPreviewData(examRecord, paper, questions, questionMap, userAnswers, scoreDetails);

        } catch (Exception e) {
            log.error("获取考试记录试卷预览失败 - 记录ID: {}", recordId, e);
            return Result.error("获取试卷预览失败: " + e.getMessage());
        }
    }

    /**
     * 构建试卷预览数据
     */
    private Result<?> buildPaperPreviewData(ExamRecord examRecord, ExamPaper paper,
                                           List<Map<String, Object>> questions,
                                           Map<String, ExamQuestion> questionMap,
                                           Map<String, Object> userAnswers,
                                           Map<String, Object> scoreDetails) {

        // 分类题目
        List<Map<String, Object>> singleChoiceList = new ArrayList<>();
        List<Map<String, Object>> judgmentList = new ArrayList<>();
        List<Map<String, Object>> programmingList = new ArrayList<>();

        // 计算考试用时（秒）- 与ExamRecordVO.getDuration()保持一致
        long examDurationSeconds = 0;
        if (examRecord.getStartTime() != null && examRecord.getEndTime() != null) {
            examDurationSeconds = (examRecord.getEndTime().getTime() - examRecord.getStartTime().getTime()) / 1000;
            // 最小为1秒
            examDurationSeconds = examDurationSeconds > 0 ? examDurationSeconds : 1;
        }

        log.info("开始构建试卷预览数据 - 题目总数: {}, 考试用时: {}秒", questions.size(), examDurationSeconds);

        for (Map<String, Object> questionConfig : questions) {
            String questionId = questionConfig.get("questionId").toString();
            int score = Integer.parseInt(questionConfig.get("score").toString());

            ExamQuestion examQuestion = questionMap.get(questionId);
            if (examQuestion == null) {
                log.warn("题目详情不存在，跳过题目: {}", questionId);
                continue;
            }

            // 构建题目基础信息
            Map<String, Object> questionData = new HashMap<>();
            questionData.put("id", examQuestion.getId());
            questionData.put("title", examQuestion.getTitle());
            questionData.put("score", score);
            questionData.put("questionType", examQuestion.getQuestionType());

            // 获取用户答案和评分结果
            String userAnswerKey = getUserAnswerKey(examQuestion.getQuestionType(), questionId);
            Object userAnswerObj = userAnswers.get(userAnswerKey);

            // 获取正确答案
            JSONObject content = JSON.parseObject(examQuestion.getContent());

            // 根据题型处理
            if (examQuestion.getQuestionType() == 1) { // 单选题
                processSingleChoiceForPreview(questionData, content, userAnswerObj, scoreDetails, questionId);
                singleChoiceList.add(questionData);
            } else if (examQuestion.getQuestionType() == 2) { // 判断题
                processJudgmentForPreview(questionData, content, userAnswerObj, scoreDetails, questionId);
                judgmentList.add(questionData);
            } else if (examQuestion.getQuestionType() == 3) { // 编程题
                processProgrammingForPreview(questionData, content, userAnswerObj, scoreDetails, questionId);
                programmingList.add(questionData);
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();

        // 考试记录基本信息
        result.put("recordId", examRecord.getId());
        result.put("startTime", examRecord.getStartTime());
        result.put("endTime", examRecord.getEndTime());
        result.put("score", examRecord.getScore());
        result.put("status", examRecord.getStatus());
        result.put("examDurationSeconds", examDurationSeconds);

        // 试卷基本信息
        result.put("paperId", paper.getId());
        result.put("paperTitle", paper.getTitle());
        result.put("subject", paper.getSubject());
        result.put("level", paper.getLevel());
        result.put("difficulty", paper.getDifficulty());
        result.put("year", paper.getYear());
        result.put("examDuration", paper.getExamDuration());

        // 题目数据
        result.put("singleChoiceQuestions", singleChoiceList);
        result.put("judgmentQuestions", judgmentList);
        result.put("programmingQuestions", programmingList);

        // 分数统计
        result.put("scoreDetails", scoreDetails);

        log.info("试卷预览数据构建完成 - 单选题: {}道, 判断题: {}道, 编程题: {}道",
                singleChoiceList.size(), judgmentList.size(), programmingList.size());

        return Result.ok(result);
    }

    /**
     * 获取用户答案的键名
     */
    private String getUserAnswerKey(Integer questionType, String questionId) {
        if (questionType == 1) {
            return "single_" + questionId;
        } else if (questionType == 2) {
            return "judgment_" + questionId;
        } else if (questionType == 3) {
            return "programming_" + questionId;
        }
        return questionId;
    }

    /**
     * 处理单选题预览数据
     */
    private void processSingleChoiceForPreview(Map<String, Object> questionData, JSONObject content,
                                             Object userAnswerObj, Map<String, Object> scoreDetails, String questionId) {
        // 获取选项
        List<Map<String, Object>> options = new ArrayList<>();
        if (content.containsKey("options")) {
            JSONArray optionsArray = content.getJSONArray("options");
            for (int i = 0; i < optionsArray.size(); i++) {
                Object optionObj = optionsArray.get(i);
                Map<String, Object> optionMap = new HashMap<>();

                if (optionObj instanceof JSONObject) {
                    // 如果是JSONObject格式
                    JSONObject option = (JSONObject) optionObj;
                    optionMap.put("label", option.getString("label"));
                    optionMap.put("content", option.getString("content"));
                } else if (optionObj instanceof String) {
                    // 如果是String格式，尝试解析
                    try {
                        JSONObject option = JSON.parseObject(optionObj.toString());
                        optionMap.put("label", option.getString("label"));
                        optionMap.put("content", option.getString("content"));
                    } catch (Exception e) {
                        // 如果解析失败，使用默认格式
                        log.warn("无法解析选项格式: {}", optionObj);
                        optionMap.put("label", String.valueOf((char)('A' + i)));
                        optionMap.put("content", optionObj.toString());
                    }
                } else {
                    // 其他格式，使用默认处理
                    log.warn("未知的选项格式: {}", optionObj);
                    optionMap.put("label", String.valueOf((char)('A' + i)));
                    optionMap.put("content", optionObj.toString());
                }

                options.add(optionMap);
            }
        }
        questionData.put("options", options);

        // 获取正确答案
        String correctAnswer = content.getString("answer");
        questionData.put("correctAnswer", correctAnswer);

        // 获取解析
        String analysis = content.getString("analysis");
        questionData.put("analysis", analysis);

        // 获取用户答案
        String userAnswer = "";
        if (userAnswerObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> answerMap = (Map<String, Object>) userAnswerObj;
            userAnswer = answerMap.get("answer") != null ? answerMap.get("answer").toString() : "";
        }
        questionData.put("userAnswer", userAnswer);

        // 判断是否正确
        boolean isCorrect = correctAnswer != null && correctAnswer.equals(userAnswer);
        questionData.put("isCorrect", isCorrect);

        // 计算实际得分：正确则获得满分，错误或未作答则0分
        int actualScore = 0;
        int fullScore = (Integer) questionData.get("score");
        if (isCorrect) {
            actualScore = fullScore; // 获得该题的满分
        }
        questionData.put("actualScore", actualScore);

        log.info("单选题得分计算 - 题目ID: {}, 用户答案: {}, 正确答案: {}, 是否正确: {}, 实际得分: {}/{}",
                questionId, userAnswer, correctAnswer, isCorrect, actualScore, fullScore);
    }

    /**
     * 处理判断题预览数据
     */
    private void processJudgmentForPreview(Map<String, Object> questionData, JSONObject content,
                                         Object userAnswerObj, Map<String, Object> scoreDetails, String questionId) {
        // 获取正确答案
        String correctAnswer = content.getString("answer");
        questionData.put("correctAnswer", correctAnswer);

        // 获取解析
        String analysis = content.getString("analysis");
        questionData.put("analysis", analysis);

        // 获取用户答案
        String userAnswer = "";
        if (userAnswerObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> answerMap = (Map<String, Object>) userAnswerObj;
            userAnswer = answerMap.get("answer") != null ? answerMap.get("answer").toString() : "";
        }
        questionData.put("userAnswer", userAnswer);

        // 判断是否正确
        boolean isCorrect = correctAnswer != null && correctAnswer.equals(userAnswer);
        questionData.put("isCorrect", isCorrect);

        // 计算实际得分：正确则获得满分，错误或未作答则0分
        int actualScore = 0;
        int fullScore = (Integer) questionData.get("score");
        if (isCorrect) {
            actualScore = fullScore; // 获得该题的满分
        }
        questionData.put("actualScore", actualScore);

        log.info("判断题得分计算 - 题目ID: {}, 用户答案: {}, 正确答案: {}, 是否正确: {}, 实际得分: {}/{}",
                questionId, userAnswer, correctAnswer, isCorrect, actualScore, fullScore);
    }

    /**
     * 处理编程题预览数据
     */
    private void processProgrammingForPreview(Map<String, Object> questionData, JSONObject content,
                                            Object userAnswerObj, Map<String, Object> scoreDetails, String questionId) {
        // 获取题目描述
        String description = content.getString("description");
        questionData.put("description", description);

        // 获取输入格式（适配数据库字段名）
        String inputFormat = content.getString("input_format");
        questionData.put("inputFormat", inputFormat);

        // 获取输出格式（适配数据库字段名）
        String outputFormat = content.getString("output_format");
        questionData.put("outputFormat", outputFormat);

        // 获取样例（适配数据库字段名）
        List<Map<String, Object>> samples = new ArrayList<>();
        if (content.containsKey("sample_cases")) {
            JSONArray samplesArray = content.getJSONArray("sample_cases");
            for (int i = 0; i < samplesArray.size(); i++) {
                JSONObject sample = samplesArray.getJSONObject(i);
                Map<String, Object> sampleMap = new HashMap<>();
                sampleMap.put("input", sample.getString("input"));
                sampleMap.put("output", sample.getString("output"));
                samples.add(sampleMap);
            }
        }
        questionData.put("samples", samples);

        // 获取提示
        String hint = content.getString("hint");
        questionData.put("hint", hint);

        // 获取时间和内存限制（适配数据库字段名）
        if (content.containsKey("time_limit")) {
            questionData.put("timeLimit", content.getInteger("time_limit"));
        }
        if (content.containsKey("memory_limit")) {
            questionData.put("memoryLimit", content.getInteger("memory_limit"));
        }

        // 获取用户答案
        String userCode = "";
        String language = "C++";
        boolean submitted = false;
        boolean isCorrect = false;

        if (userAnswerObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> answerMap = (Map<String, Object>) userAnswerObj;
            userCode = answerMap.get("code") != null ? answerMap.get("code").toString() : "";
            language = answerMap.get("language") != null ? answerMap.get("language").toString() : "C++";
            submitted = answerMap.get("submitted") != null ? (Boolean) answerMap.get("submitted") : false;
            isCorrect = answerMap.get("isCorrect") != null ? (Boolean) answerMap.get("isCorrect") : false;
        }

        questionData.put("userCode", userCode);
        questionData.put("language", language);
        questionData.put("submitted", submitted);
        questionData.put("isCorrect", isCorrect);

        // 计算实际得分：通过则获得满分，未通过或未提交则0分
        int actualScore = 0;
        if (submitted && isCorrect) {
            actualScore = (Integer) questionData.get("score"); // 获得该题的满分
        }
        questionData.put("actualScore", actualScore);

        log.info("编程题得分计算 - 题目ID: {}, 是否提交: {}, 是否正确: {}, 实际得分: {}/{}",
                questionId, submitted, isCorrect, actualScore, questionData.get("score"));
    }
}