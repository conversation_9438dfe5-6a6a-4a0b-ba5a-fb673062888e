<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.teaching.mapper.TeachingWorkCommentMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  teaching_work_comment 
		WHERE
			 work_id = #{mainId} 	</delete>

	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.teaching.entity.TeachingWorkComment">
		SELECT *
		FROM  teaching_work_comment c
		WHERE
			work_id = #{workId}
	</select>
	
	<select id="getWorkComments" resultType="org.jeecg.modules.teaching.model.WorkCommentModel">
		SELECT c.*, u.username, u.realname, u.avatar
		FROM  teaching_work_comment c
		left join sys_user u on u.id = c.user_id
		WHERE
			 work_id = #{workId}
		order by c.create_time desc
		limit #{offset},#{pageSize}
	</select>
</mapper>
