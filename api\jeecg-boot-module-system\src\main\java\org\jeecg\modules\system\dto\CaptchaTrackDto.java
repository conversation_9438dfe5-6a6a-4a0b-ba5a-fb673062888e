package org.jeecg.modules.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 验证码轨迹DTO
 */
@ApiModel(value = "验证码校验参数", description = "用于校验验证码完成状态的参数对象")
public class CaptchaTrackDto {

    @ApiModelProperty(value = "验证码唯一标识", required = true)
    private String id;
    
    @ApiModelProperty(value = "验证码轨迹数据", required = true)
    private CustomImageCaptchaTrack data;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public CustomImageCaptchaTrack getData() {
        return data;
    }

    public void setData(CustomImageCaptchaTrack data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "CaptchaTrackDto{" +
                "id='" + id + '\'' +
                ", data=" + data +
                '}';
    }
} 