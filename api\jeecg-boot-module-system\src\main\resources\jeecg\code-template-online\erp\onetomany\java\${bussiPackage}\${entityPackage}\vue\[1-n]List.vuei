<#list subTables as sub>
#segment#${sub.entityName}List.vue
<template>
  <a-card :bordered="false" :class="'cust-erp-sub-tab'">
    <!-- 操作按钮区域 -->
    <div class="table-operator" v-if="mainId">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('${sub.ftlDescription}')">导出</a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel">
          <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="uploadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>

    <${sub.entityName?uncap_first}-modal ref="modalForm" @ok="modalFormOk" :mainId="mainId"></${sub.entityName?uncap_first}-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import ${sub.entityName}Modal from './modules/${sub.entityName}Modal'



  export default {
    name: "${sub.entityName}List",
    mixins:[JeecgListMixin],
    components: { ${sub.entityName}Modal },
    props:{
      mainId:{
        type:String,
        default:'',
        required:false
      }
    },
    watch:{
      mainId:{
        immediate: true,
        handler(val) {
          if(!this.mainId){
            this.clearList()
          }else{
            <#list sub.foreignKeys as key>
            this.queryParam['${key?uncap_first}'] = val
            </#list>
            this.loadData(1);
          }
        }
      }
    },
    data () {
      return {
        description: '${tableVo.ftlDescription}管理页面',
        disableMixinCreated:true,
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
    <#assign showColNum=0>
	<#list sub.originalColumns as po>
	<#if po.isShowList =='Y'>
	<#assign showColNum=showColNum+1>
          {
            title:'${po.filedComment}',
            align:"center",
            <#if po.classType=='date'>
            dataIndex: '${po.fieldName}',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
            <#elseif po.fieldDbType=='Blob'>
            dataIndex: '${po.fieldName}String'
            <#elseif po.classType=='umeditor'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'htmlSlot'}
            <#elseif po.classType=='file'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'fileSlot'}
            <#elseif po.classType=='image'>
            dataIndex: '${po.fieldName}',
            scopedSlots: {customRender: 'imgSlot'}
            <#elseif po.classType =='list' || po.classType =='radio' || po.classType =='list_multi' || po.classType =='checkbox' || po.classType =='sel_search' || po.classType =='cat_tree' || po.classType =='sel_depart'>
            dataIndex: '${po.fieldName}_dictText',
			<#else>
            dataIndex: '${po.fieldName}'
			</#if>
          },
     </#if>
     </#list>
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/${entityPackage}/${entityName?uncap_first}/list${sub.entityName}ByMainId",
          delete: "/${entityPackage}/${entityName?uncap_first}/delete${sub.entityName}",
          deleteBatch: "/${entityPackage}/${entityName?uncap_first}/deleteBatch${sub.entityName}",
          exportXlsUrl: "/${entityPackage}/${entityName?uncap_first}/export${sub.entityName}",
          importUrl: "/${entityPackage}/${entityName?uncap_first}/import${sub.entityName}",
        },
        dictOptions:{
         <#list columns as po>
         <#if (po.isQuery=='Y' || po.isShowList=='Y')>
         <#if  po.classType='sel_depart' || po.classType=='list_multi' || po.classType=='list' || po.classType=='radio' || po.classType=='checkbox'>
         ${po.fieldName}:[],
         </#if>
         </#if>
         </#list>
        },

      }
    },
    computed: {
      importExcelUrl(){
        return `${'$'}{window._CONFIG['domianURL']}/${'$'}{this.url.importUrl}/${'$'}{this.mainId}`;
      }
    },
    methods: {
      clearList(){
        this.dataSource=[]
        this.selectedRowKeys=[]
        this.ipagination.current = 1
      },

    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
</#list>