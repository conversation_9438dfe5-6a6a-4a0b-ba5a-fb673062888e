package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;
import org.jeecg.modules.teaching.mapper.TeachingObjectiveQuizDetailMapper;
import org.jeecg.modules.teaching.service.ITeachingObjectiveQuizDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客观题答题详情 Service实现类
 */
@Service
public class TeachingObjectiveQuizDetailServiceImpl extends ServiceImpl<TeachingObjectiveQuizDetailMapper, TeachingObjectiveQuizDetail> implements ITeachingObjectiveQuizDetailService {
    
    @Override
    public List<TeachingObjectiveQuizDetail> listByRecordIdOrderByQuestionCreateTime(String recordId) {
        return baseMapper.listByRecordIdOrderByQuestionCreateTime(recordId);
    }
} 