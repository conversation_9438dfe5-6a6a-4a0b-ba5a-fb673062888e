-- 数据修复SQL脚本
-- 将exam_collection和exam_mistake表中的user_id从用户名改为实际的用户ID

-- 为了安全起见，先创建备份表
CREATE TABLE IF NOT EXISTS `exam_collection_backup` LIKE `exam_collection`;
INSERT INTO `exam_collection_backup` SELECT * FROM `exam_collection`;

CREATE TABLE IF NOT EXISTS `exam_mistake_backup` LIKE `exam_mistake`;
INSERT INTO `exam_mistake_backup` SELECT * FROM `exam_mistake`;

-- 更新exam_collection表中的user_id字段
-- 将原先存储的用户名替换为对应的用户ID
UPDATE `exam_collection` ec
SET ec.user_id = (
    SELECT su.id 
    FROM `sys_user` su 
    WHERE su.username = ec.user_id
)
WHERE EXISTS (
    SELECT 1 
    FROM `sys_user` su 
    WHERE su.username = ec.user_id
);

-- 更新exam_mistake表中的user_id字段
-- 将原先存储的用户名替换为对应的用户ID
UPDATE `exam_mistake` em
SET em.user_id = (
    SELECT su.id 
    FROM `sys_user` su 
    WHERE su.username = em.user_id
)
WHERE EXISTS (
    SELECT 1 
    FROM `sys_user` su 
    WHERE su.username = em.user_id
);

-- 查询没有成功更新的记录，这些记录的用户名可能不存在于sys_user表中
SELECT * FROM `exam_collection` WHERE user_id NOT IN (SELECT id FROM `sys_user`);
SELECT * FROM `exam_mistake` WHERE user_id NOT IN (SELECT id FROM `sys_user`); 