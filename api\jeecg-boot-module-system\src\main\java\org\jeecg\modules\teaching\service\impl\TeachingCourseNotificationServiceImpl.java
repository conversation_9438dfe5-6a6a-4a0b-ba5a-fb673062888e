package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserDepart;
import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.teaching.constant.NotificationConstant;
import org.jeecg.modules.teaching.entity.TeachingCourseNotification;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.entity.TeachingNotificationRead;
import org.jeecg.modules.teaching.mapper.TeachingCourseNotificationMapper;
import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import org.jeecg.modules.teaching.service.ITeachingNotificationReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.constant.WebsocketConst;
import org.jeecg.modules.message.websocket.WebSocket;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 课程通知实现
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
@Slf4j
@Service
public class TeachingCourseNotificationServiceImpl extends ServiceImpl<TeachingCourseNotificationMapper, TeachingCourseNotification> implements ITeachingCourseNotificationService {

    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private ISysUserDepartService sysUserDepartService;
    

    
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private WebSocket webSocket;

    @Autowired
    private ITeachingNotificationReadService teachingNotificationReadService;

    @Override
    @Transactional
    public boolean createNewCourseNotification(TeachingCourseSchedule schedule, boolean notifyTeachers, boolean notifyStudents) {
        try {
            TeachingCourseNotification notification = new TeachingCourseNotification();
            notification.setScheduleId(schedule.getId());
            notification.setNotificationType(NotificationConstant.NOTIFICATION_TYPE_NEW);
            notification.setTitle("新课程通知："+schedule.getScheduleTitle());
            
            // 构建通知内容
            String format = "yyyy-MM-dd HH:mm";
            String startTime = DateFormatUtils.format(schedule.getStartTime(), format);
            String endTime = DateFormatUtils.format(schedule.getEndTime(), format);
            
            StringBuilder content = new StringBuilder();
            content.append("您有一门新的课程安排：").append(schedule.getScheduleTitle())
                   .append("<br>上课时间：").append(startTime).append(" 至 ").append(endTime);
            
            // 获取教室名称
            String classroomName = getClassroomName(schedule);
            if (StringUtils.isNotEmpty(classroomName)) {
                content.append("<br>教室：").append(classroomName);
            }
            
            // 获取教师名称
            String teacherName = getTeacherName(schedule);
            if (StringUtils.isNotEmpty(teacherName)) {
                content.append("<br>授课教师：").append(teacherName);
            }
            
            // 获取班级名称
            String className = getClassName(schedule);
            if (StringUtils.isNotEmpty(className)) {
                content.append("<br>班级：").append(className);
            }
            
            // 如果有描述信息，添加到内容中
            if (StringUtils.isNotEmpty(schedule.getDescription())) {
                content.append("<br>课程简介：").append(schedule.getDescription());
            }
            
            // 获取相关用户IDs和学生信息
            String userIds = getScheduleRelatedUserIds(schedule, notifyTeachers, notifyStudents);
            notification.setReceivers(userIds);
            
            // 新增：添加学生信息到通知内容
            if (notifyStudents && StringUtils.isNotEmpty(schedule.getClassId())) {
                // 获取班级学生信息
                List<String> studentNames = getClassStudentNames(schedule.getClassId());
                if (studentNames != null && !studentNames.isEmpty()) {
                    content.append("<br>学生：").append(String.join("、", studentNames));
                }
            }
            
            notification.setContent(content.toString());
            notification.setNotifyTeachers(notifyTeachers ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setNotifyStudents(notifyStudents ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setIsSent(NotificationConstant.NOTIFICATION_UNSENT);
            notification.setCreateTime(new Date());
            
            // 保存通知
            return save(notification);
        } catch (Exception e) {
            log.error("创建新增课程通知失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean createUpdateCourseNotification(TeachingCourseSchedule newSchedule, TeachingCourseSchedule oldSchedule, boolean notifyTeachers, boolean notifyStudents) {
        try {
            // 新增代码：删除该课程未发送的旧变更通知
            try {
                // 查询与此课程关联的、类型为UPDATE、状态为未发送的通知
                LambdaQueryWrapper<TeachingCourseNotification> updateQuery = new LambdaQueryWrapper<>();
                updateQuery.eq(TeachingCourseNotification::getScheduleId, newSchedule.getId())
                          .eq(TeachingCourseNotification::getNotificationType, NotificationConstant.NOTIFICATION_TYPE_UPDATE)
                          .eq(TeachingCourseNotification::getIsSent, NotificationConstant.NOTIFICATION_UNSENT);
                
                List<TeachingCourseNotification> oldUpdateNotifications = this.list(updateQuery);
                
                // 如果找到旧的变更通知，则删除它们
                if (oldUpdateNotifications != null && !oldUpdateNotifications.isEmpty()) {
                    log.info("发现课程多次变更，删除旧的未发送课程变更通知，课程ID：{}，找到{}条待删除的变更通知", 
                            newSchedule.getId(), oldUpdateNotifications.size());
                    
                    // 删除这些通知
                    List<String> updateNotificationIds = new ArrayList<>();
                    for (TeachingCourseNotification oldNotification : oldUpdateNotifications) {
                        updateNotificationIds.add(oldNotification.getId());
                    }
                    this.removeByIds(updateNotificationIds);
                    
                    log.info("成功删除{}条旧的课程变更通知，确保用户只收到最新的变更信息", updateNotificationIds.size());
                }
            } catch (Exception e) {
                // 仅记录错误，不影响正常流程
                log.error("删除旧的课程变更通知失败，课程ID：" + newSchedule.getId(), e);
            }
            // 新增代码结束
            
            TeachingCourseNotification notification = new TeachingCourseNotification();
            notification.setScheduleId(newSchedule.getId());
            notification.setNotificationType(NotificationConstant.NOTIFICATION_TYPE_UPDATE);
            notification.setTitle("课程变更通知："+newSchedule.getScheduleTitle());
            
            // 构建通知内容，突出显示变更内容
            String format = "yyyy-MM-dd HH:mm";
            String oldStartTime = DateFormatUtils.format(oldSchedule.getStartTime(), format);
            String oldEndTime = DateFormatUtils.format(oldSchedule.getEndTime(), format);
            String newStartTime = DateFormatUtils.format(newSchedule.getStartTime(), format);
            String newEndTime = DateFormatUtils.format(newSchedule.getEndTime(), format);
            
            StringBuilder content = new StringBuilder();
            content.append("您的课程 ").append(newSchedule.getScheduleTitle()).append(" 已变更：<br>");
            
            // 检查时间是否变更
            boolean timeChanged = !oldStartTime.equals(newStartTime) || !oldEndTime.equals(newEndTime);
            if (timeChanged) {
                content.append("原时间：").append(oldStartTime).append(" 至 ").append(oldEndTime)
                       .append("<br>     ↓（变更）")
                       .append("<br>新时间：<span style=\"color: #1890ff;\">").append(newStartTime).append(" 至 ").append(newEndTime).append("</span><br>");
                
                // 新增代码：当课程时间变更时，取消未发送的旧的课程提醒通知
                try {
                    // 查询与此课程关联的、类型为提醒、状态为未发送的通知
                    LambdaQueryWrapper<TeachingCourseNotification> reminderQuery = new LambdaQueryWrapper<>();
                    reminderQuery.eq(TeachingCourseNotification::getScheduleId, newSchedule.getId())
                               .eq(TeachingCourseNotification::getNotificationType, NotificationConstant.NOTIFICATION_TYPE_REMIND)
                               .eq(TeachingCourseNotification::getIsSent, NotificationConstant.NOTIFICATION_UNSENT);
                    
                    List<TeachingCourseNotification> oldReminders = this.list(reminderQuery);
                    
                    // 如果找到旧的提醒通知，则删除它们
                    if (oldReminders != null && !oldReminders.isEmpty()) {
                        log.info("课程时间已变更，删除旧的课程提醒通知，课程ID：{}，找到{}条待删除的提醒通知", 
                                newSchedule.getId(), oldReminders.size());
                        
                        // 删除这些通知
                        List<String> reminderIds = new ArrayList<>();
                        for (TeachingCourseNotification oldReminder : oldReminders) {
                            reminderIds.add(oldReminder.getId());
                        }
                        this.removeByIds(reminderIds);
                        
                        log.info("成功删除{}条旧的课程提醒通知", reminderIds.size());
                    }
                } catch (Exception e) {
                    // 仅记录错误，不影响正常流程
                    log.error("删除旧的课程提醒通知失败，课程ID：" + newSchedule.getId(), e);
                }
                // 新增代码结束
            }
            
            // 检查教室是否变更
            String oldClassroomName = getClassroomName(oldSchedule);
            String newClassroomName = getClassroomName(newSchedule);
            if (!StringUtils.equals(oldSchedule.getClassroomId(), newSchedule.getClassroomId())) {
                content.append("教室变更：").append(oldClassroomName == null ? "未指定" : oldClassroomName)
                       .append(" -> <b>").append(newClassroomName == null ? "未指定" : newClassroomName).append("</b><br>");
            }
            
            // 检查教师是否变更
            String oldTeacherName = getTeacherName(oldSchedule);
            String newTeacherName = getTeacherName(newSchedule);
            if (!StringUtils.equals(oldSchedule.getTeacherId(), newSchedule.getTeacherId())) {
                content.append("教师变更：").append(oldTeacherName == null ? "未指定" : oldTeacherName)
                       .append(" -> <b>").append(newTeacherName == null ? "未指定" : newTeacherName).append("</b><br>");
            }
            
            // 当前课程信息
            content.append("<br>当前课程信息：<br>")
                   .append("课程：").append(newSchedule.getScheduleTitle()).append("<br>");
            
            // 只有在时间发生变更时，上课时间才使用蓝色文字颜色
            if (timeChanged) {
                content.append("上课时间：<span style=\"color: #1890ff;\">").append(newStartTime).append(" 至 ").append(newEndTime).append("</span><br>");
            } else {
                content.append("上课时间：").append(newStartTime).append(" 至 ").append(newEndTime).append("<br>");
            }
            
            if (StringUtils.isNotEmpty(newClassroomName)) {
                content.append("教室：").append(newClassroomName).append("<br>");
            }
            
            if (StringUtils.isNotEmpty(newTeacherName)) {
                content.append("授课教师：").append(newTeacherName).append("<br>");
            }
            
            String className = getClassName(newSchedule);
            if (StringUtils.isNotEmpty(className)) {
                content.append("班级：").append(className);
            }
            
            // 获取相关用户IDs
            String userIds = getScheduleRelatedUserIds(newSchedule, notifyTeachers, notifyStudents);
            notification.setReceivers(userIds);
            
            // 新增：添加学生信息到通知内容
            if (notifyStudents && StringUtils.isNotEmpty(newSchedule.getClassId())) {
                // 获取班级学生信息
                List<String> studentNames = getClassStudentNames(newSchedule.getClassId());
                if (studentNames != null && !studentNames.isEmpty()) {
                    content.append("<br>学生：").append(String.join("、", studentNames));
                }
            }
            
            notification.setContent(content.toString());
            notification.setNotifyTeachers(notifyTeachers ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setNotifyStudents(notifyStudents ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setIsSent(NotificationConstant.NOTIFICATION_UNSENT);
            notification.setCreateTime(new Date());
            
            // 保存通知
            return save(notification);
        } catch (Exception e) {
            log.error("创建课程变更通知失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean createCancelCourseNotification(TeachingCourseSchedule schedule, boolean notifyTeachers, boolean notifyStudents) {
        try {
            TeachingCourseNotification notification = new TeachingCourseNotification();
            notification.setScheduleId(schedule.getId());
            notification.setNotificationType(NotificationConstant.NOTIFICATION_TYPE_CANCEL);
            notification.setTitle("课程取消通知："+schedule.getScheduleTitle());
            
            // 构建通知内容
            String format = "yyyy-MM-dd HH:mm";
            String startTime = DateFormatUtils.format(schedule.getStartTime(), format);
            String endTime = DateFormatUtils.format(schedule.getEndTime(), format);
            
            StringBuilder content = new StringBuilder();
            content.append("您的课程已被取消！")
                   .append("<br>课程：").append(schedule.getScheduleTitle())
                   .append("<br>原定上课时间：").append(startTime).append(" 至 ").append(endTime);
            
            String classroomName = getClassroomName(schedule);
            if (StringUtils.isNotEmpty(classroomName)) {
                content.append("<br>原定教室：").append(classroomName);
            }
            
            String teacherName = getTeacherName(schedule);
            if (StringUtils.isNotEmpty(teacherName)) {
                content.append("<br>授课教师：").append(teacherName);
            }
            
            String className = getClassName(schedule);
            if (StringUtils.isNotEmpty(className)) {
                content.append("<br>班级：").append(className);
            }
            
            // 获取相关用户IDs
            String userIds = getScheduleRelatedUserIds(schedule, notifyTeachers, notifyStudents);
            notification.setReceivers(userIds);
            
            // 新增：添加学生信息到通知内容
            if (notifyStudents && StringUtils.isNotEmpty(schedule.getClassId())) {
                // 获取班级学生信息
                List<String> studentNames = getClassStudentNames(schedule.getClassId());
                if (studentNames != null && !studentNames.isEmpty()) {
                    content.append("<br>学生：").append(String.join("、", studentNames));
                }
            }
            
            notification.setContent(content.toString());
            notification.setNotifyTeachers(notifyTeachers ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setNotifyStudents(notifyStudents ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setIsSent(NotificationConstant.NOTIFICATION_UNSENT);
            notification.setCreateTime(new Date());
            
            // 保存通知
            return save(notification);
        } catch (Exception e) {
            log.error("创建课程取消通知失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean createCourseReminderNotification(TeachingCourseSchedule schedule, Date remindTime, boolean notifyTeachers, boolean notifyStudents) {
        try {
            TeachingCourseNotification notification = new TeachingCourseNotification();
            notification.setScheduleId(schedule.getId());
            notification.setNotificationType(NotificationConstant.NOTIFICATION_TYPE_REMIND);
            notification.setTitle("课程提醒："+schedule.getScheduleTitle());
            
            // 构建通知内容
            String format = "yyyy-MM-dd HH:mm";
            String startTime = DateFormatUtils.format(schedule.getStartTime(), format);
            String endTime = DateFormatUtils.format(schedule.getEndTime(), format);
            
            StringBuilder content = new StringBuilder();
            content.append("您有一门课程即将开始！")
                   .append("<br>课程：").append(schedule.getScheduleTitle())
                   .append("<br>上课时间：").append(startTime).append(" 至 ").append(endTime);
            
            String classroomName = getClassroomName(schedule);
            if (StringUtils.isNotEmpty(classroomName)) {
                content.append("<br>教室：").append(classroomName);
            }
            
            String teacherName = getTeacherName(schedule);
            if (StringUtils.isNotEmpty(teacherName)) {
                content.append("<br>授课教师：").append(teacherName);
            }
            
            String className = getClassName(schedule);
            if (StringUtils.isNotEmpty(className)) {
                content.append("<br>班级：").append(className);
            }
            
            // 获取相关用户IDs
            String userIds = getScheduleRelatedUserIds(schedule, notifyTeachers, notifyStudents);
            notification.setReceivers(userIds);
            
            // 新增：添加学生信息到通知内容
            if (notifyStudents && StringUtils.isNotEmpty(schedule.getClassId())) {
                // 获取班级学生信息
                List<String> studentNames = getClassStudentNames(schedule.getClassId());
                if (studentNames != null && !studentNames.isEmpty()) {
                    content.append("<br>学生：").append(String.join("、", studentNames));
                }
            }
            
            notification.setContent(content.toString());
            notification.setNotifyTeachers(notifyTeachers ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setNotifyStudents(notifyStudents ? NotificationConstant.NOTIFY_YES : NotificationConstant.NOTIFY_NO);
            notification.setIsSent(NotificationConstant.NOTIFICATION_UNSENT);
            notification.setRemindTime(remindTime); // 设置提醒时间
            notification.setCreateTime(new Date());
            
            // 保存通知
            return save(notification);
        } catch (Exception e) {
            log.error("创建课程提醒通知失败", e);
            return false;
        }
    }

    /**
     * 处理所有待发送的通知
     * 
     * 该方法负责查询并处理系统中所有未发送的通知，不包括提醒类型的通知。
     * 提醒类型的通知由专门的processReminderNotifications方法处理。
     * 
     * @return 成功发送的通知数量
     */
    @Override
    @Transactional
    public int processNotifications() {
        // 创建查询条件：查询所有未发送的通知
        LambdaQueryWrapper<TeachingCourseNotification> queryWrapper = new LambdaQueryWrapper<>();
        // 筛选条件1：通知状态为未发送
        queryWrapper.eq(TeachingCourseNotification::getIsSent, NotificationConstant.NOTIFICATION_UNSENT);
        // 筛选条件2：排除提醒类型的通知，因为提醒通知由专门的方法处理
        queryWrapper.ne(TeachingCourseNotification::getNotificationType, NotificationConstant.NOTIFICATION_TYPE_REMIND);
        
        // 执行查询，获取所有符合条件的通知列表
        List<TeachingCourseNotification> notifications = list(queryWrapper);
        // 计数器，记录成功发送的通知数量
        int count = 0;
        
        // 遍历所有待发送的通知
        for (TeachingCourseNotification notification : notifications) {
            // 调用发送通知的方法，尝试发送当前通知
            boolean success = sendNotification(notification);
            // 如果发送成功，增加计数器
            if (success) {
                count++;
            }
        }
        
        // 返回成功发送的通知数量
        return count;
    }

    @Override
    @Transactional
    public int processReminderNotifications() {
        List<TeachingCourseNotification> reminders = getPendingReminders();
        int count = 0;
        
        for (TeachingCourseNotification reminder : reminders) {
            boolean success = sendNotification(reminder);
            if (success) {
                count++;
            }
        }
        
        return count;
    }

    @Override
    public List<TeachingCourseNotification> getPendingReminders() {
        QueryWrapper<TeachingCourseNotification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("notification_type", "REMIND");
        queryWrapper.eq("is_sent", NotificationConstant.NOTIFICATION_UNSENT);
        queryWrapper.lt("remind_time", new Date());
        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public boolean sendNotification(TeachingCourseNotification notification) {
        try {
            // 检查接收人列表是否为空
            if (StringUtils.isEmpty(notification.getReceivers())) {
                log.warn("通知接收人为空，通知ID：{}", notification.getId());
                notification.setIsSent(NotificationConstant.NOTIFICATION_SENT);
                updateById(notification);
                return true;
            }
            
            // 设置发送人
            String senderUsername = "system"; // 默认发送者为system
            if (StringUtils.isEmpty(notification.getSender())) {
                try {
                    // 尝试获取当前登录用户
                LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                if (sysUser != null) {
                    senderUsername = sysUser.getUsername();
                    }
                    // 如果获取失败，使用默认的system发送者（已在方法开始时设置）
                } catch (Exception e) {
                    // 捕获Shiro安全异常，使用默认发送者
                    log.warn("获取当前用户失败，使用默认系统用户发送通知，通知ID：{}", notification.getId());
                    // 不需要设置senderUsername，因为已经默认设置为system
                }
            } else {
                senderUsername = notification.getSender();
            }
            
            // 处理接收人
            String userIds = notification.getReceivers();
            // 确保以逗号结尾，方便处理
            if (!userIds.endsWith(",")) {
                userIds = userIds + ",";
            }
            
            // 获取当前日期时间作为发送时间的基准时间
            Date now = new Date();
            
            // 新增代码：为发送时间添加基于通知类型和创建时间的细微差异
            try {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                
                // 使用通知创建时间的哈希值作为基础偏移量
                if (notification.getCreateTime() != null) {
                    // 获取通知类型对应的基础偏移值
                    int baseOffset = 0;
                    if (NotificationConstant.NOTIFICATION_TYPE_NEW.equals(notification.getNotificationType())) {
                        baseOffset = 0; // 新课程通知
                    } else if (NotificationConstant.NOTIFICATION_TYPE_CANCEL.equals(notification.getNotificationType())) {
                        baseOffset = 100; // 取消课程通知
                    } else if (NotificationConstant.NOTIFICATION_TYPE_UPDATE.equals(notification.getNotificationType())) {
                        baseOffset = 200; // 更新课程通知
                    } else if (NotificationConstant.NOTIFICATION_TYPE_REMIND.equals(notification.getNotificationType())) {
                        baseOffset = 300; // 提醒通知
                    }
                    
                    // 使用通知ID的哈希值生成0-99毫秒的额外偏移，确保同类型通知也有序
                    int additionalOffset = 0;
                    if (notification.getId() != null) {
                        additionalOffset = Math.abs(notification.getId().hashCode() % 100);
                    }
                    
                    // 设置毫秒级偏移
                    int totalMillisOffset = baseOffset + additionalOffset;
                    calendar.set(Calendar.MILLISECOND, totalMillisOffset);
                    
                    log.debug("通知ID：{}，类型：{}，应用毫秒偏移：{}，最终发送时间：{}", 
                            notification.getId(), notification.getNotificationType(), totalMillisOffset, calendar.getTime());
                    log.info("通知ID：{}，类型：{}，应用毫秒偏移：{}，最终发送时间：{}", 
                            notification.getId(), notification.getNotificationType(), totalMillisOffset, calendar.getTime());
                }
                
                // 使用调整后的时间作为发送时间
                now = calendar.getTime();
            } catch (Exception e) {
                // 如果时间偏移处理出错，不影响主流程，继续使用原始时间
                log.info("设置通知发送时间偏移失败，使用原始时间，通知ID：{}", notification.getId(), e);
                log.warn("设置通知发送时间偏移失败，使用原始时间，通知ID：{}", notification.getId(), e);
            }
            // 新增代码结束
            
            // 构建通知消息数据，确保字段名与SysAnnouncement保持一致
            JSONObject obj = new JSONObject();
            // 使用topic命令类型，确保消息显示在通知中心
            obj.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_TOPIC);
            // 设置消息ID
            obj.put(WebsocketConst.MSG_ID, notification.getId());
            // 设置消息标题
            obj.put(WebsocketConst.MSG_TXT, notification.getTitle());
            
            // 兼容系统通知的字段
            obj.put("id", notification.getId());
            obj.put("title", notification.getTitle());
            obj.put("msgContent", notification.getContent());
            obj.put("sender", senderUsername);
            obj.put("sendTime", DateFormatUtils.format(now, "yyyy-MM-dd HH:mm:ss"));
            obj.put("createTime", DateFormatUtils.format(notification.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            obj.put("openType", "announcement"); // 使用默认打开方式
            obj.put("openPage", ""); // 默认空页面
            
            // 添加课程通知分类标识，3表示课程通知
            obj.put("msgCategory", NotificationConstant.MSG_CATEGORY_COURSE); 
            
            // 添加通知优先级 - 根据通知类型设置优先级
            String priority = NotificationConstant.PRIORITY_LOW; // 默认低优先级
            if (NotificationConstant.NOTIFICATION_TYPE_CANCEL.equals(notification.getNotificationType())) {
                priority = NotificationConstant.PRIORITY_HIGH; // 取消课程为高优先级
            } else if (NotificationConstant.NOTIFICATION_TYPE_UPDATE.equals(notification.getNotificationType()) 
                    || NotificationConstant.NOTIFICATION_TYPE_REMIND.equals(notification.getNotificationType())) {
                priority = NotificationConstant.PRIORITY_MEDIUM; // 变更和提醒为中优先级
            }
            obj.put("priority", priority);
            
            // 保持与SysAnnouncement一致的其他字段
            obj.put("msgType", "USER"); // 用户消息类型
            obj.put("sendStatus", "1"); // 已发送状态
            
            // 添加通知类型字段，用于前端区分不同类型的通知
            obj.put("notificationType", notification.getNotificationType());
            
            // 为每个接收者发送WebSocket消息
            String[] userIdArr = userIds.split(",");
            // 使用更安全的方式发送消息，避免空字符串
            List<String> validUserIds = new ArrayList<>();
            for (String userId : userIdArr) {
                if (StringUtils.isNotEmpty(userId)) {
                    validUserIds.add(userId);
                }
            }
            
            // 只有在有有效用户ID时才发送消息
            if (!validUserIds.isEmpty()) {
                String[] validUserIdArray = validUserIds.toArray(new String[0]);
                // 调用webSocket发送消息给多个用户
                webSocket.sendMoreMessage(validUserIdArray, obj.toJSONString());
                
                log.info("成功发送课程通知消息给{}位用户", validUserIds.size());
            }
            
            // 更新通知状态为已发送
            notification.setIsSent(NotificationConstant.NOTIFICATION_SENT);
            notification.setSender(senderUsername);
            notification.setSendTime(now);
            updateById(notification);
            
            return true;
        } catch (Exception e) {
            log.error("发送通知失败，通知ID：" + notification.getId(), e);
            return false;
        }
    }
    
    /**
     * 获取教室名称
     * @param schedule 课程排期
     * @return 教室名称
     */
    private String getClassroomName(TeachingCourseSchedule schedule) {
        // 此处应该通过Dict注解自动生成的字典值获取，或者通过服务查询
        // 这里简单实现，实际项目中应使用缓存或其他方式优化
        if (StringUtils.isEmpty(schedule.getClassroomId())) {
            return null;
        }
        
        // 使用queryTableDictItemsByCode方法替代translateDictFromTable
        try {
            List<DictModel> dictModels = sysBaseAPI.queryTableDictItemsByCode("teaching_classroom", "classroom_name", "id");
            for (DictModel dictModel : dictModels) {
                if (schedule.getClassroomId().equals(dictModel.getValue())) {
                    return dictModel.getText();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取教室名称失败", e);
            return null;
        }
    }
    
    /**
     * 获取教师名称
     * @param schedule 课程排期
     * @return 教师名称
     */
    private String getTeacherName(TeachingCourseSchedule schedule) {
        if (StringUtils.isEmpty(schedule.getTeacherId())) {
            return null;
        }
        
        try {
            List<DictModel> dictModels = sysBaseAPI.queryTableDictItemsByCode("sys_user", "realname", "id");
            for (DictModel dictModel : dictModels) {
                if (schedule.getTeacherId().equals(dictModel.getValue())) {
                    return dictModel.getText();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取教师名称失败", e);
            return null;
        }
    }
    
    /**
     * 获取班级名称
     * @param schedule 课程排期
     * @return 班级名称
     */
    private String getClassName(TeachingCourseSchedule schedule) {
        if (StringUtils.isEmpty(schedule.getClassId())) {
            return null;
        }
        
        try {
            List<DictModel> dictModels = sysBaseAPI.queryTableDictItemsByCode("sys_depart", "depart_name", "id");
            for (DictModel dictModel : dictModels) {
                if (schedule.getClassId().equals(dictModel.getValue())) {
                    return dictModel.getText();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
            return null;
        }
    }

    @Override
    public String getScheduleRelatedUserIds(TeachingCourseSchedule schedule, boolean includeTeacher, boolean includeStudents) {
        List<String> userIds = new ArrayList<>();
        
        // 添加教师ID
        if (includeTeacher && StringUtils.isNotEmpty(schedule.getTeacherId())) {
            userIds.add(schedule.getTeacherId());
        }
        
        // 添加学生IDs
        if (includeStudents && StringUtils.isNotEmpty(schedule.getClassId())) {
            try {
                // 创建查询条件，用于筛选特定班级的用户-部门关联记录
            QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dep_id", schedule.getClassId());
                // 执行查询，获取该班级下所有的用户-部门关联记录列表
            List<SysUserDepart> userDepartList = sysUserDepartService.list(queryWrapper);
            
                if (userDepartList != null && !userDepartList.isEmpty()) {
                    log.debug("班级{}中找到{}个用户", schedule.getClassId(), userDepartList.size());
                    
                    // 遍历班级中的所有用户关联记录
            for (SysUserDepart userDepart : userDepartList) {
                        // 获取用户ID
                String userId = userDepart.getUserId();
                        
                        // 获取用户信息
                        SysUser user = sysUserService.getById(userId);
                        if (user == null) {
                            log.warn("未找到用户信息，用户ID：{}", userId);
                            continue;
                        }
                        
                        // 查询用户的角色代码
                        List<String> userRoles = sysUserService.getRoleById(userId);
                        if (userRoles == null || userRoles.isEmpty()) {
                            log.warn("未找到用户角色代码，用户ID：{}，用户名：{}", userId, user.getUsername());
                            continue;
                        }
                        
                        // 判断用户是否有学生角色
                        boolean isStudent = userRoles.contains("student");
                        log.debug("用户ID：{}，用户名：{}，是否为学生：{}", userId, user.getUsername(), isStudent);
                        
                        // 只有学生角色且未添加过的用户才会被添加到接收者列表
                        if (isStudent && !userIds.contains(userId)) {
                    userIds.add(userId);
                            log.debug("添加学生ID到通知接收者：{}", userId);
                        }
                    }
                } else {
                    log.warn("班级{}中未找到用户", schedule.getClassId());
                }
            } catch (Exception e) {
                log.error("获取班级学生失败", e);
            }
        }
        
        return String.join(",", userIds);
    }

    /**
     * 获取用户的课程通知
     */
    @Override
    public IPage<TeachingCourseNotification> getUserNotifications(
            Page<TeachingCourseNotification> page,
            String userId,
            String notificationType) {
        
        // 构建查询条件
        LambdaQueryWrapper<TeachingCourseNotification> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只查询已发送的通知
        queryWrapper.eq(TeachingCourseNotification::getIsSent, NotificationConstant.NOTIFICATION_SENT);
        
        // LIKE查询，receivers字段包含用户ID
        queryWrapper.like(TeachingCourseNotification::getReceivers, userId);
        
        // 如果指定了通知类型，添加类型过滤条件
        if (StringUtils.isNotEmpty(notificationType)) {
            queryWrapper.eq(TeachingCourseNotification::getNotificationType, notificationType);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(TeachingCourseNotification::getCreateTime);
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
    
    /**
     * 获取用户的最近通知
     */
    @Override
    public IPage<TeachingCourseNotification> getUserRecentNotifications(
            Page<TeachingCourseNotification> page,
            String userId,
            String notificationType) {
        
        // 构建查询条件
        LambdaQueryWrapper<TeachingCourseNotification> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只查询已发送的通知
        queryWrapper.eq(TeachingCourseNotification::getIsSent, NotificationConstant.NOTIFICATION_SENT);
        
        // LIKE查询，receivers字段包含用户ID
        queryWrapper.like(TeachingCourseNotification::getReceivers, userId);
        
        // 如果指定了通知类型，添加类型过滤条件
        if (StringUtils.isNotEmpty(notificationType)) {
            queryWrapper.eq(TeachingCourseNotification::getNotificationType, notificationType);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(TeachingCourseNotification::getCreateTime);
        
        // 执行查询获取所有通知
        List<TeachingCourseNotification> allNotifications = this.list(queryWrapper);
        
        // 过滤出未读通知
            List<TeachingCourseNotification> unreadNotifications = new ArrayList<>();
            
        if (allNotifications != null && !allNotifications.isEmpty()) {
            // 获取通知ID列表
            List<String> notificationIds = allNotifications.stream()
                .map(TeachingCourseNotification::getId)
                .collect(Collectors.toList());
            
            // 查询已读通知ID列表
            List<String> readIds = teachingNotificationReadService.getReadNotificationIds(userId, notificationIds);
            
            // 过滤出未读通知
            for (TeachingCourseNotification notification : allNotifications) {
                if (!readIds.contains(notification.getId())) {
                    unreadNotifications.add(notification);
                }
            }
            }
            
        // 分页处理
        int total = unreadNotifications.size();
        int start = (int)((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int)page.getSize(), total);
        
        List<TeachingCourseNotification> pageRecords;
        if (start < total) {
            pageRecords = unreadNotifications.subList(start, end);
        } else {
            pageRecords = new ArrayList<>();
        }
        
        Page<TeachingCourseNotification> resultPage = new Page<>(page.getCurrent(), page.getSize(), total);
        resultPage.setRecords(pageRecords);
        
        return resultPage;
    }
    
    /**
     * 标记通知为已读
     */
    @Override
    @Transactional
    public boolean markAsRead(String notificationId, String userId) {
        try {
            // 查询通知是否存在
            TeachingCourseNotification notification = this.getById(notificationId);
            if (notification == null) {
                log.warn("通知不存在，ID: {}", notificationId);
                return false;
            }
            
            // 使用新的markReadStatus方法，直接标记为已读
            TeachingNotificationRead readRecord = teachingNotificationReadService.markReadStatus(notificationId, userId, true);
            return readRecord != null;
        } catch (Exception e) {
            log.error("标记通知已读失败", e);
            return false;
        }
    }

    /**
     * 新增：获取班级学生姓名列表
     * @param classId 班级ID
     * @return 学生姓名列表
     */
    private List<String> getClassStudentNames(String classId) {
        List<String> studentNames = new ArrayList<>();
        if (StringUtils.isEmpty(classId)) {
            return studentNames;
        }
        
        try {
            // 创建查询条件，筛选特定班级的用户-部门关联记录
            QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dep_id", classId);
            // 执行查询，获取该班级下所有的用户-部门关联记录列表
            List<SysUserDepart> userDepartList = sysUserDepartService.list(queryWrapper);
            
            if (userDepartList != null && !userDepartList.isEmpty()) {
                log.debug("班级{}中找到{}个用户", classId, userDepartList.size());
                
                // 遍历班级中的所有用户关联记录
                for (SysUserDepart userDepart : userDepartList) {
                    // 获取用户ID
                    String userId = userDepart.getUserId();
                    
                    // 获取用户信息
                    SysUser user = sysUserService.getById(userId);
                    if (user == null) {
                        log.warn("未找到用户信息，用户ID：{}", userId);
                        continue;
                    }
                    
                    // 查询用户的角色代码
                    List<String> userRoles = sysUserService.getRoleById(userId);
                    if (userRoles == null || userRoles.isEmpty()) {
                        log.warn("未找到用户角色代码，用户ID：{}，用户名：{}", userId, user.getUsername());
                        continue;
                    }
                    
                    // 判断用户是否有学生角色
                    boolean isStudent = userRoles.contains("student");
                    
                    // 只添加学生角色的用户姓名
                    if (isStudent && StringUtils.isNotEmpty(user.getRealname())) {
                        studentNames.add(user.getRealname());
                        log.debug("添加学生姓名到通知内容：{}", user.getRealname());
                    }
                }
            } else {
                log.warn("班级{}中未找到用户", classId);
            }
        } catch (Exception e) {
            log.error("获取班级学生姓名失败", e);
        }
        
        return studentNames;
    }

    /**
     * 获取用户的所有通知（不分页）
     */
    @Override
    public List<TeachingCourseNotification> getUserAllNotifications(String userId) {
        // 构建查询条件
        LambdaQueryWrapper<TeachingCourseNotification> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只查询已发送的通知
        queryWrapper.eq(TeachingCourseNotification::getIsSent, NotificationConstant.NOTIFICATION_SENT);
        
        // LIKE查询，receivers字段包含用户ID
        queryWrapper.like(TeachingCourseNotification::getReceivers, userId);
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(TeachingCourseNotification::getCreateTime);
        
        // 执行查询并返回结果
        return this.list(queryWrapper);
    }
} 