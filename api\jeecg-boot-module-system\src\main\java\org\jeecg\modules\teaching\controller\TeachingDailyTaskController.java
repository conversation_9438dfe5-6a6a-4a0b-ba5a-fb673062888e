package org.jeecg.modules.teaching.controller;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.common.controller.BaseController;
import org.jeecg.modules.teaching.service.ITeachingDailyTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.Map;

/**
 * 每日任务控制器
 */
@Slf4j
@RestController
@RequestMapping("/teaching/dailyTask")
public class TeachingDailyTaskController extends BaseController {

    @Autowired
    private ITeachingDailyTaskService dailyTaskService;

    /**
     * 获取用户每日任务
     */
    @GetMapping("/getTasks")
    public Result<?> getTasks() {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        return dailyTaskService.getUserDailyTasks(user.getId());
    }

    /**
     * 完成每日签到
     */
    @PostMapping("/signIn")
    public Result<?> signIn() {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        return dailyTaskService.completeSignIn(user.getId());
    }

    /**
     * 处理评论任务
     */
    @PostMapping("/commentTask")
    public Result<?> commentTask(@RequestBody Map<String, Object> params) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        
        String workId = params.get("workId").toString();
        return dailyTaskService.handleCommentTask(user.getId(), workId);
    }

    /**
     * 处理点赞任务
     */
    @PostMapping("/likeTask")
    public Result<?> likeTask(@RequestBody Map<String, Object> params) {
        LoginUser user = getCurrentUser();
        if (user == null) {
            return Result.error("用户未登录");
        }
        
        String workId = params.get("workId").toString();
        return dailyTaskService.handleLikeTask(user.getId(), workId);
    }
} 