---
alwaysApply: true
---
# 常见问题与解决方案

## Result返回值问题

### 🚨 重要：Result.ok()重载方法陷阱
**现象**: 前端接收到的数据在`message`字段而不是`result`字段
**原因**: Java编译器选择了错误的重载方法

**Result类重载方法说明**:
```java
// 方法1：无参数，返回默认成功消息
public static Result<Object> ok()

// 方法2：String参数，设置message字段 ⚠️ 陷阱所在
public static Result<Object> ok(String msg)

// 方法3：Object参数，设置result字段 ✅ 我们想要的
public static Result<Object> ok(Object data)
```

**问题示例**:
```java
// ❌ 错误：String类型会调用Result.ok(String msg)
String formattedContent = "格式化后的内容";
return Result.ok(formattedContent);  // 数据进入message字段！

// 前端收到：{ success: true, message: "格式化后的内容", result: null }
```

**解决方案**:
```java
// ✅ 方案1：强制类型转换为Object
return Result.ok((Object) formattedContent);

// ✅ 方案2：使用明确的构造方法
Result<String> result = new Result<>();
result.setSuccess(true);
result.setResult(formattedContent);
return result;

// ✅ 方案3：创建包装对象
return Result.ok(Collections.singletonMap("content", formattedContent));
```

**最佳实践**:
- 对于String类型返回值，始终使用`(Object)`强制转换
- 在代码审查时特别注意Result.ok()的使用
- 前端接收数据时检查res.result是否为null

## 数据库相关问题

### 问题：MyBatis-Plus分页查询无效
**现象**: 分页参数设置了但查询返回全部数据
**原因**: 未配置分页插件

**解决方案**:
```java
@Configuration
public class MybatisPlusConfig {
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
```

### 问题：字段映射异常
**现象**: 数据库字段名与实体类属性名不匹配
**解决方案**:
```java
@TableField("question_type")
private String questionType;

// 或者在application.yml中配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
```

### 问题：事务不生效
**现象**: 数据库操作异常时未回滚
**原因**: 
1. 方法不是public
2. 同类内部调用
3. 异常被捕获未抛出

**解决方案**:
```java
@Transactional(rollbackFor = Exception.class)
public void businessMethod() {
    try {
        // 业务逻辑
    } catch (Exception e) {
        log.error("业务异常", e);
        throw e; // 必须重新抛出异常
    }
}
```

## 前端常见问题

### 问题：Axios请求跨域
**现象**: 浏览器控制台显示CORS错误
**解决方案**:
```java
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
```

### 问题：Vue组件数据不响应
**现象**: 数据变化但页面不更新
**原因**: 
1. 直接修改数组索引
2. 添加新属性未使用Vue.set

**解决方案**:
```javascript
// ❌ 错误
this.list[0] = newItem;
this.obj.newProp = value;

// ✅ 正确
this.$set(this.list, 0, newItem);
this.$set(this.obj, 'newProp', value);

// 或使用Vue 3的响应式API
this.list.splice(0, 1, newItem);
```

### 问题：路由跳转参数丢失
**现象**: 页面跳转后参数为空
**解决方案**:
```javascript
// 使用query传参（显示在URL中）
this.$router.push({
  path: '/exam/detail',
  query: { id: examId }
});

// 使用params传参（需要路由配置name）
this.$router.push({
  name: 'ExamDetail',
  params: { id: examId }
});
```

## 文件上传问题

### 问题：文件上传大小限制
**现象**: 上传大文件时报错
**解决方案**:
```yaml
# application.yml
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
```

```nginx
# nginx.conf
client_max_body_size 100M;
```

### 问题：文件编码问题
**现象**: 上传的中文文件名乱码
**解决方案**:
```java
String fileName = new String(file.getOriginalFilename().getBytes("ISO-8859-1"), "UTF-8");
```

## 权限控制问题

### 问题：Shiro权限注解不生效
**现象**: 添加了@RequiresPermissions但未进行权限检查
**原因**: 未开启注解支持

**解决方案**:
```java
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ShiroConfig {
    
    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }
}
```

### 问题：JWT Token过期处理
**现象**: Token过期后用户无感知
**解决方案**:
```javascript
// axios响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // Token过期，跳转登录页
      store.dispatch('user/logout');
      router.push('/login');
    }
    return Promise.reject(error);
  }
);
```

## 性能优化问题

### 问题：查询性能慢
**现象**: 数据库查询耗时长
**解决方案**:
1. 添加索引
```sql
CREATE INDEX idx_subject_level ON exam_question(subject, level);
```

2. 优化查询条件
```java
// ❌ 避免使用函数
queryWrapper.apply("DATE(create_time) = '2023-01-01'");

// ✅ 使用范围查询
queryWrapper.between("create_time", startTime, endTime);
```

3. 使用分页查询
```java
Page<ExamQuestion> page = new Page<>(pageNo, pageSize);
IPage<ExamQuestion> result = examQuestionService.page(page, queryWrapper);
```

### 问题：内存溢出
**现象**: 处理大量数据时OutOfMemoryError
**解决方案**:
```java
// 使用流式处理
@Select("SELECT * FROM exam_question WHERE subject = #{subject}")
@Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
void processQuestions(@Param("subject") String subject, ResultHandler<ExamQuestion> handler);

// 批量处理
List<ExamQuestion> questions = getAllQuestions();
int batchSize = 1000;
for (int i = 0; i < questions.size(); i += batchSize) {
    List<ExamQuestion> batch = questions.subList(i, Math.min(i + batchSize, questions.size()));
    processBatch(batch);
}
```

## HOJ集成问题

### 问题：HOJ服务连接超时
**现象**: 调用HOJ接口时超时
**解决方案**:
```java
@Bean
public RestTemplate hojRestTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    
    HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
    factory.setConnectTimeout(10000);  // 连接超时10秒
    factory.setReadTimeout(60000);     // 读取超时60秒
    
    restTemplate.setRequestFactory(factory);
    return restTemplate;
}
```

### 问题：判题结果状态不更新
**现象**: 提交代码后状态一直是"判题中"
**解决方案**:
```java
// 添加重试机制
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 2000))
public HojJudgeResultVO getJudgeResult(String submissionId) {
    // 查询判题结果
}

// 异步轮询结果
@Async
public CompletableFuture<HojJudgeResultVO> pollJudgeResult(String submissionId) {
    int maxAttempts = 30;
    int attempt = 0;
    
    while (attempt < maxAttempts) {
        HojJudgeResultVO result = getJudgeResult(submissionId);
        if (result.getStatus() != 7) { // 不是"判题中"状态
            return CompletableFuture.completedFuture(result);
        }
        
        try {
            Thread.sleep(2000); // 等待2秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            break;
        }
        attempt++;
    }
    
    throw new RuntimeException("判题超时");
}
```

## 缓存问题

### 问题：Redis连接池耗尽
**现象**: 获取Redis连接时超时
**解决方案**:
```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
```

### 问题：缓存穿透
**现象**: 大量请求查询不存在的数据
**解决方案**:
```java
public ExamQuestion getQuestionById(String id) {
    // 先查缓存
    ExamQuestion question = (ExamQuestion) redisTemplate.opsForValue().get("question:" + id);
    if (question != null) {
        return question;
    }
    
    // 查数据库
    question = examQuestionService.getById(id);
    if (question != null) {
        // 存入缓存
        redisTemplate.opsForValue().set("question:" + id, question, 3600, TimeUnit.SECONDS);
    } else {
        // 缓存空值，防止缓存穿透
        redisTemplate.opsForValue().set("question:" + id, new ExamQuestion(), 300, TimeUnit.SECONDS);
    }
    
    return question;
}
```

## 部署问题

### 问题：Docker容器启动失败
**现象**: 容器启动后立即退出
**解决方案**:
1. 查看容器日志
```bash
docker logs container_name
```

2. 检查配置文件
```bash
docker exec -it container_name sh
```

3. 健康检查
```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/actuator/health || exit 1
```

### 问题：静态资源404
**现象**: 前端页面加载不出来
**解决方案**:
```nginx
location / {
    root /usr/share/nginx/html;
    index index.html;
    try_files $uri $uri/ /index.html;
}
```

## 调试技巧

### 1. 开启SQL日志
```yaml
logging:
  level:
    com.baomidou.mybatisplus: DEBUG
```

### 2. 使用Actuator监控
```yaml
management:
  endpoints:
    web:
      exposure:
        include: "*"
```

### 3. 远程调试
```bash
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar app.jar
```
