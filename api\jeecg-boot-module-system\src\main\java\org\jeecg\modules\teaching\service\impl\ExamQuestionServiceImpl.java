package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.mapper.ExamQuestionMapper;
import org.jeecg.modules.teaching.service.IExamQuestionService;
import org.jeecg.modules.teaching.service.QuestionContentService;
import org.jeecg.modules.teaching.util.QuestionImportUtil;
import org.jeecg.modules.teaching.util.QuestionImportUtil.SimilarQuestion;
import org.jeecg.modules.teaching.util.DuplicateCheckUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

import java.util.stream.Collectors;

/**
 * @Description: 考试题目
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements IExamQuestionService {

    /**
     * 相似度阈值，大于等于此值认为题目相似
     */
    private static final double SIMILARITY_THRESHOLD = 0.8;

    /**
     * 批量处理的大小
     */
    private static final int BATCH_SIZE = 500;

    @Autowired
    private DuplicateCheckUtil duplicateCheckUtil;

    @Autowired
    private QuestionContentService questionContentService;
    
    @Override
    @Transactional
    public Map<String, Object> importQuestionsFromFile(MultipartFile file, String subject, String level, Integer difficulty, String author) {
        // 创建元数据Map，调用新方法
        Map<String, Object> metadata = new HashMap<>();
        if (author != null) {
            metadata.put("author", author);
        }
        // 不再传递其他元数据，只从文件中获取
        return importQuestionsFromFile(file, metadata);
    }
    
    @Override
    @Transactional
    public Map<String, Object> importQuestionsFromFile(MultipartFile file, Map<String, Object> metadata) {
        Map<String, Object> result = new HashMap<>();
        List<ExamQuestion> importedQuestions = new ArrayList<>();
        List<Map<String, Object>> similarQuestions = new ArrayList<>();
        
        try {
            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
            
            // 从文件内容中提取元数据
            Map<String, String> fileMetadata = extractMetadata(content.toString());
            log.info("提取的文件元数据: {}", fileMetadata);
            
            // 获取author参数（如果提供）
            String author = null;
            if (metadata != null && metadata.containsKey("author")) {
                author = (String) metadata.get("author");
            }
            
            // 解析题目
            List<ExamQuestion> parsedQuestions = QuestionImportUtil.parseQuestionText(content.toString());

            // 检测文件内部重复
            Map<String, Object> internalDuplicateResult = checkInternalDuplicates(parsedQuestions);
            boolean hasInternalDuplicates = (Boolean) internalDuplicateResult.get("hasInternalDuplicates");

            log.info("=== 题目导入 - 文件内部重复检测 ===");
            log.info("总题目数: {}", parsedQuestions.size());
            log.info("是否有内部重复: {}", hasInternalDuplicates);

            if (hasInternalDuplicates) {
                int duplicateCount = (Integer) internalDuplicateResult.get("duplicateCount");
                int uniqueQuestions = (Integer) internalDuplicateResult.get("uniqueQuestions");

                log.warn("发现文件内部重复题目！重复数量: {}, 唯一题目数: {}", duplicateCount, uniqueQuestions);

                String errorMessage = String.format(
                    "文件内部存在重复题目！共检测到 %d 道题目，其中 %d 道重复，实际只有 %d 道不同的题目。请检查文件内容，确保每道题目只出现一次。",
                    parsedQuestions.size(), duplicateCount, uniqueQuestions
                );

                throw new RuntimeException(errorMessage);
            }

            // 设置题目的元数据信息 - 优先使用文件中的元数据
            for (ExamQuestion question : parsedQuestions) {
                // 只使用文件中的元数据，除了author
                if (StringUtils.isBlank(question.getSubject()) && fileMetadata.containsKey("subject")) {
                    question.setSubject(fileMetadata.get("subject"));
                }
                
                // 级别处理
                if (StringUtils.isBlank(question.getLevel()) && fileMetadata.containsKey("level")) {
                    String level = fileMetadata.get("level");
                    question.setLevel(standardizeLevel(level));
                } else if (question.getLevel() != null) {
                    // 如果题目本身有级别，也对题目本身的级别进行标准化
                    question.setLevel(standardizeLevel(question.getLevel()));
                }
                
                // 难度处理
                if (question.getDifficulty() == null) {
                    if (fileMetadata.containsKey("difficulty")) {
                        try {
                            question.setDifficulty(Integer.parseInt(fileMetadata.get("difficulty")));
                        } catch (NumberFormatException e) {
                            question.setDifficulty(1); // 默认难度为简单
                        }
                    } else {
                        question.setDifficulty(1); // 默认难度为简单
                    }
                }
                
                // 作者处理 - 优先使用参数提供的作者
                if (author != null) {
                    question.setAuthor(author);
                } else if (StringUtils.isBlank(question.getAuthor()) && fileMetadata.containsKey("author")) {
                    question.setAuthor(fileMetadata.get("author"));
                }
                
                // 数据验证 - 确保必填字段有值
                if (StringUtils.isBlank(question.getSubject())) {
                    throw new RuntimeException("导入失败：题目缺少科目信息，请确保文件中包含完整元数据");
                }
                
                if (StringUtils.isBlank(question.getLevel())) {
                    throw new RuntimeException("导入失败：题目缺少级别信息，请确保文件中包含完整元数据");
                }
                
                // 进行更严格的重复检测
                Map<String, Object> duplicateCheckResult = checkDuplicateBeforeAdd(question);

                if (duplicateCheckResult.containsKey("exactDuplicate") && (Boolean) duplicateCheckResult.get("exactDuplicate")) {
                    // 发现精确重复，跳过此题目
                    log.warn("跳过重复题目：{} (科目：{}，级别：{}，类型：{})",
                            question.getTitle(), question.getSubject(), question.getLevel(), question.getQuestionType());
                    continue;
                }

                if (duplicateCheckResult.containsKey("similarQuestions")) {
                    @SuppressWarnings("unchecked")
                    List<SimilarQuestion> similar = (List<SimilarQuestion>) duplicateCheckResult.get("similarQuestions");
                    if (!similar.isEmpty()) {
                        // 有相似题目，记录相似信息
                        Map<String, Object> similarInfo = new HashMap<>();
                        similarInfo.put("newQuestion", question);
                        similarInfo.put("similarQuestions", similar);
                        similarQuestions.add(similarInfo);
                        continue;
                    }
                }

                // 没有重复或相似题目，可以导入
                importedQuestions.add(question);
            }
            
            // 优化：分批保存导入的题目，提高大批量导入性能
            if (!importedQuestions.isEmpty()) {
                int totalQuestions = importedQuestions.size();
                if (totalQuestions <= BATCH_SIZE) {
                    // 数量不多时直接批量保存
                    this.saveBatch(importedQuestions);
                } else {
                    // 数量较多时分批处理
                    for (int i = 0; i < totalQuestions; i += BATCH_SIZE) {
                        int endIndex = Math.min(i + BATCH_SIZE, totalQuestions);
                        List<ExamQuestion> batch = importedQuestions.subList(i, endIndex);
                        this.saveBatch(batch);
                    }
                }
            }
            
            result.put("imported", importedQuestions);
            result.put("similar", similarQuestions);
            result.put("total", parsedQuestions.size());
            result.put("success", importedQuestions.size());
            result.put("similar_count", similarQuestions.size());
            
        } catch (IOException e) {
            throw new RuntimeException("导入题目失败: " + e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public List<SimilarQuestion> checkSimilarQuestions(ExamQuestion question) {
        // 根据题目类型查询现有题目
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("question_type", question.getQuestionType());

        // 如果指定了科目和级别，优先按科目和级别筛选
        if (StringUtils.isNotBlank(question.getSubject())) {
            queryWrapper.eq("subject", question.getSubject());
        }

        if (StringUtils.isNotBlank(question.getLevel())) {
            queryWrapper.eq("level", question.getLevel());
        }

        List<ExamQuestion> existingQuestions = this.list(queryWrapper);

        // 计算相似度并筛选相似题目
        return QuestionImportUtil.findSimilarQuestions(question, existingQuestions, SIMILARITY_THRESHOLD);
    }

    @Override
    public Map<String, Object> checkDuplicateBeforeAdd(ExamQuestion question) {
        Map<String, Object> result = new HashMap<>();

        // 1. 使用数据库工具进行精确重复检测
        Map<String, Object> exactDuplicateResult = duplicateCheckUtil.checkQuestionExactDuplicate(question);

        if ((Boolean) exactDuplicateResult.getOrDefault("hasDuplicate", false)) {
            result.put("exactDuplicate", true);
            result.put("exactCount", exactDuplicateResult.get("duplicateCount"));
            result.put("duplicateQuestions", exactDuplicateResult.get("duplicateQuestions"));
            return result;
        }

        // 2. 相似度检测：使用降低的阈值进行更严格的相似度检测
        double strictThreshold = 0.6; // 降低阈值，提高检测敏感度
        List<SimilarQuestion> similarQuestions = checkSimilarQuestionsWithThreshold(question, strictThreshold);

        if (!similarQuestions.isEmpty()) {
            result.put("similarQuestions", similarQuestions);
        }

        result.put("exactDuplicate", false);
        return result;
    }

    /**
     * 使用指定阈值检查相似题目
     * @param question 待检查的题目
     * @param threshold 相似度阈值
     * @return 相似题目列表
     */
    private List<SimilarQuestion> checkSimilarQuestionsWithThreshold(ExamQuestion question, double threshold) {
        // 根据题目类型查询现有题目
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("question_type", question.getQuestionType());

        // 如果指定了科目和级别，优先按科目和级别筛选
        if (StringUtils.isNotBlank(question.getSubject())) {
            queryWrapper.eq("subject", question.getSubject());
        }

        if (StringUtils.isNotBlank(question.getLevel())) {
            queryWrapper.eq("level", question.getLevel());
        }

        List<ExamQuestion> existingQuestions = this.list(queryWrapper);

        // 计算相似度并筛选相似题目
        return QuestionImportUtil.findSimilarQuestions(question, existingQuestions, threshold);
    }

    @Override
    public List<ExamQuestion> getQuestionsByCondition(String title, String subject, String level, Integer questionType, Integer difficulty) {
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.isNotBlank(title)) {
            // 使用参数绑定方式处理特殊字符，并增强模糊查询能力
            // 直接使用数据库函数进行模糊匹配，避免特殊字符转义问题
            queryWrapper.apply("INSTR(LOWER(title), LOWER({0})) > 0", title);
            
            // 可选：使用参数数组的方式处理特殊字符
            // queryWrapper.like("title", title);
        }
        
        if (StringUtils.isNotBlank(subject)) {
            queryWrapper.eq("subject", subject);
        }
        
        if (StringUtils.isNotBlank(level)) {
            // 使用标准化的级别进行查询
            queryWrapper.eq("level", standardizeLevel(level));
        }
        
        if (questionType != null) {
            queryWrapper.eq("question_type", questionType);
        }
        
        if (difficulty != null) {
            queryWrapper.eq("difficulty", difficulty);
        }
        
        return this.list(queryWrapper);
    }
    
    /**
     * 从文本内容中提取元数据
     * @param content 文本内容
     * @return 元数据键值对
     */
    private Map<String, String> extractMetadata(String content) {
        Map<String, String> metadata = new HashMap<>();
        
        if (content == null || content.isEmpty()) {
            return metadata;
        }
        
        try (BufferedReader reader = new BufferedReader(new StringReader(content))) {
            String line;
            boolean inMetadata = false;
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                if (line.equals("【元数据】")) {
                    inMetadata = true;
                    continue;
                }
                
                if (inMetadata) {
                    // 检测元数据部分结束
                    if (line.isEmpty() || line.startsWith("【一、") || line.startsWith("【二、") || line.startsWith("【三、")) {
                        break;
                    }
                    
                    // 解析元数据行
                    if (line.startsWith("【科目】")) {
                        String value = line.substring("【科目】".length()).trim();
                        if (!value.contains("请填写") && !value.isEmpty()) {
                            metadata.put("subject", value);
                        }
                    } else if (line.startsWith("【级别】")) {
                        String value = line.substring("【级别】".length()).trim();
                        if (!value.contains("请填写") && !value.isEmpty()) {
                            metadata.put("level", value);
                        }
                    } else if (line.startsWith("【难度】")) {
                        String value = line.substring("【难度】".length()).trim();
                        if (!value.contains("请填写") && !value.isEmpty()) {
                            // 提取数字难度值
                            if (value.startsWith("1")) {
                                metadata.put("difficulty", "1");
                            } else if (value.startsWith("2")) {
                                metadata.put("difficulty", "2");
                            } else if (value.startsWith("3")) {
                                metadata.put("difficulty", "3");
                            }
                        }
                    } else if (line.startsWith("【作者】")) {
                        String value = line.substring("【作者】".length()).trim();
                        if (!value.contains("请填写") && !value.isEmpty()) {
                            metadata.put("author", value);
                        }
                    }
                }
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return metadata;
    }
    

    
    /**
     * 将各种格式的级别标准化为"X级"格式
     * @param level 原始级别文本，如"6"、"六"、"6级"、"六级"等
     * @return 标准化的级别文本，如"六级"
     */
    private static String standardizeLevel(String level) {
        if (level == null || level.isEmpty()) {
            return level;
        }
        
        // 如果已经是"X级"格式，检查是否需要转换数字部分
        if (level.endsWith("级")) {
            String prefix = level.substring(0, level.length() - 1);
            // 如果前缀是数字，转换为中文
            try {
                int num = Integer.parseInt(prefix);
                return numberToChinese(num) + "级";
            } catch (NumberFormatException e) {
                // 前缀不是数字，可能已经是中文
                return level;
            }
        }
        
        // 如果是纯数字，转换为"X级"格式
        try {
            int num = Integer.parseInt(level);
            return numberToChinese(num) + "级";
        } catch (NumberFormatException e) {
            // 不是纯数字，检查是否是单个中文数字
            switch (level) {
                case "一": return "一级";
                case "二": return "二级";
                case "三": return "三级";
                case "四": return "四级";
                case "五": return "五级";
                case "六": return "六级";
                case "七": return "七级";
                case "八": return "八级";
                case "九": return "九级";
                default: return level; // 无法识别，返回原始值
            }
        }
    }
    
    /**
     * 将数字转换为中文数字
     * @param num 数字
     * @return 中文数字
     */
    private static String numberToChinese(int num) {
        if (num <= 0 || num > 9) {
            return String.valueOf(num); // 超出范围，返回原始数字
        }

        switch (num) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "七";
            case 8: return "八";
            case 9: return "九";
            default: return String.valueOf(num);
        }
    }

    @Override
    public Map<String, Object> previewQuestionsFromFile(MultipartFile file, Map<String, Object> metadata) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }

            // 从文件内容中提取元数据
            Map<String, String> fileMetadata = extractMetadata(content.toString());
            log.info("预览模式 - 提取的文件元数据: {}", fileMetadata);

            // 解析题目
            List<ExamQuestion> parsedQuestions = QuestionImportUtil.parseQuestionText(content.toString());

            if (parsedQuestions.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件中没有找到有效的题目");
                return result;
            }

            // 首先检测文件内部重复
            Map<String, Object> internalDuplicateResult = checkInternalDuplicates(parsedQuestions);
            boolean hasInternalDuplicates = (Boolean) internalDuplicateResult.get("hasInternalDuplicates");

            log.info("=== 题目预览 - 文件内部重复检测 ===");
            log.info("总题目数: {}", parsedQuestions.size());
            log.info("是否有内部重复: {}", hasInternalDuplicates);

            if (hasInternalDuplicates) {
                int duplicateCount = (Integer) internalDuplicateResult.get("duplicateCount");
                int uniqueQuestions = (Integer) internalDuplicateResult.get("uniqueQuestions");

                log.warn("预览模式发现文件内部重复题目！重复数量: {}, 唯一题目数: {}", duplicateCount, uniqueQuestions);

                result.put("success", false);
                result.put("hasInternalDuplicates", true);
                result.put("internalDuplicateInfo", internalDuplicateResult);
                result.put("message", "检测到文件内部存在重复题目！请检查文件内容，确保每道题目只出现一次。");
                return result;
            }

            // 设置题目的元数据信息进行检测
            String author = null;
            if (metadata != null && metadata.containsKey("author")) {
                author = (String) metadata.get("author");
            }

            for (ExamQuestion question : parsedQuestions) {
                // 设置元数据用于检测
                if (StringUtils.isBlank(question.getSubject()) && fileMetadata.containsKey("subject")) {
                    question.setSubject(fileMetadata.get("subject"));
                }

                if (StringUtils.isBlank(question.getLevel()) && fileMetadata.containsKey("level")) {
                    String level = fileMetadata.get("level");
                    question.setLevel(standardizeLevel(level));
                } else if (question.getLevel() != null) {
                    question.setLevel(standardizeLevel(question.getLevel()));
                }

                if (question.getDifficulty() == null) {
                    if (fileMetadata.containsKey("difficulty")) {
                        try {
                            question.setDifficulty(Integer.parseInt(fileMetadata.get("difficulty")));
                        } catch (NumberFormatException e) {
                            question.setDifficulty(1);
                        }
                    } else {
                        question.setDifficulty(1);
                    }
                }

                if (author != null) {
                    question.setAuthor(author);
                } else if (StringUtils.isBlank(question.getAuthor()) && fileMetadata.containsKey("author")) {
                    question.setAuthor(fileMetadata.get("author"));
                }
            }

            // 统计信息
            int totalQuestions = parsedQuestions.size();
            int newQuestionsCount = 0;
            int duplicateQuestionsCount = 0;
            int similarQuestionsCount = 0;

            // 检测每道题目的状态（但不保存）
            for (ExamQuestion question : parsedQuestions) {
                Map<String, Object> duplicateCheckResult = checkDuplicateBeforeAdd(question);

                if (duplicateCheckResult.containsKey("exactDuplicate") && (Boolean) duplicateCheckResult.get("exactDuplicate")) {
                    duplicateQuestionsCount++;
                } else if (duplicateCheckResult.containsKey("similarQuestions")) {
                    @SuppressWarnings("unchecked")
                    List<SimilarQuestion> similar = (List<SimilarQuestion>) duplicateCheckResult.get("similarQuestions");
                    if (!similar.isEmpty()) {
                        similarQuestionsCount++;
                    } else {
                        newQuestionsCount++;
                    }
                } else {
                    newQuestionsCount++;
                }
            }

            result.put("success", true);
            result.put("totalQuestions", totalQuestions);
            result.put("newQuestions", newQuestionsCount);
            result.put("duplicateQuestions", duplicateQuestionsCount);
            result.put("similarQuestions", similarQuestionsCount);
            result.put("fileMetadata", fileMetadata);

            String message = String.format("检测完成！共发现 %d 道题目，其中新题目 %d 道，重复题目 %d 道，相似题目 %d 道。",
                totalQuestions, newQuestionsCount, duplicateQuestionsCount, similarQuestionsCount);
            result.put("message", message);

        } catch (IOException e) {
            result.put("success", false);
            result.put("message", "文件读取失败: " + e.getMessage());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "预览失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public String autoFormatTemplate(MultipartFile file, String subject, String level, Integer difficulty) throws Exception {
        log.info("=== 开始自动格式化 ===");
        log.info("文件名: {}, 科目: {}, 级别: {}, 难度: {}", file.getOriginalFilename(), subject, level, difficulty);

        // 读取文件内容
        String content;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            content = reader.lines().collect(Collectors.joining("\n"));
        }

        log.info("读取到的文件内容长度: {}", content != null ? content.length() : 0);
        if (content != null && content.length() > 0) {
            log.info("文件内容前100字符: {}", content.substring(0, Math.min(100, content.length())));
        } else {
            log.info("文件内容为空");
        }

        if (StringUtils.isBlank(content)) {
            throw new RuntimeException("文件内容为空");
        }

        // 构建格式化后的内容
        StringBuilder formattedContent = new StringBuilder();

        // 添加元数据
        formattedContent.append("【元数据】\n");
        formattedContent.append("【科目】").append(subject).append("\n");
        formattedContent.append("【级别】").append(level).append("\n");
        formattedContent.append("【难度】").append(getDifficultyText(difficulty)).append("\n\n");

        // 分析并格式化题目内容
        if (content == null) {
            content = "";
        }
        String[] lines = content.split("\n");

        // 状态变量
        String currentSection = "";
        int singleChoiceCount = 0;
        int judgmentCount = 0;
        int programmingCount = 0;
        boolean waitingForQuestionContent = false;
        boolean afterDataRange = false;  // 标记是否刚处理完数据范围内容

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // 处理空行
            if (StringUtils.isBlank(line)) {
                // 如果在编程题部分且刚处理完数据范围，空行后可能是新编程题
                if ("编程题".equals(currentSection) && afterDataRange) {
                    waitingForQuestionContent = true;
                    afterDataRange = false;
                }
                continue;
            }

            // 检测题型标识
            if (line.equals("单选题")) {
                currentSection = "单选题";
                formattedContent.append("【一、单选题】\n\n");
                singleChoiceCount = 0;
                waitingForQuestionContent = true;
                continue;
            } else if (line.equals("判断题")) {
                currentSection = "判断题";
                formattedContent.append("【二、判断题】\n\n");
                judgmentCount = 0;
                waitingForQuestionContent = true;
                continue;
            } else if (line.equals("编程题")) {
                currentSection = "编程题";
                formattedContent.append("【三、编程题】\n\n");
                programmingCount = 0;
                waitingForQuestionContent = true;
                afterDataRange = false;
                continue;
            }

            // 处理不同题型的内容
            if ("单选题".equals(currentSection)) {
                if (waitingForQuestionContent && isQuestionLine(line)) {
                    // 题目内容
                    singleChoiceCount++;
                    formattedContent.append("【").append(singleChoiceCount).append(".】").append(line).append("\n");
                    waitingForQuestionContent = false;
                } else if (isOption(line)) {
                    // 选项格式化：只包裹选项标识符，不包裹内容
                    String formattedOption = formatOption(line);
                    formattedContent.append(formattedOption).append("\n");
                } else if (isAnswer(line)) {
                    // 答案
                    String answer = extractAnswer(line);
                    formattedContent.append("【答案】").append(answer).append("\n");
                } else if (isExplanation(line)) {
                    // 解析
                    String explanation = extractExplanation(line);
                    formattedContent.append("【解析】").append(explanation).append("\n\n");
                    waitingForQuestionContent = true;
                }
            } else if ("判断题".equals(currentSection)) {
                if (waitingForQuestionContent && isQuestionLine(line)) {
                    // 题目内容
                    judgmentCount++;
                    formattedContent.append("【").append(judgmentCount).append(".】").append(line).append("\n");
                    waitingForQuestionContent = false;
                } else if (isAnswer(line)) {
                    // 答案
                    String answer = extractAnswer(line);
                    formattedContent.append("【答案】").append(answer).append("\n");
                } else if (isExplanation(line)) {
                    // 解析
                    String explanation = extractExplanation(line);
                    formattedContent.append("【解析】").append(explanation).append("\n\n");
                    waitingForQuestionContent = true;
                }
            } else if ("编程题".equals(currentSection)) {
                // 检测是否是新的编程题开始
                if (isProgrammingQuestionTitle(line, waitingForQuestionContent)) {
                    // 新编程题标题
                    programmingCount++;
                    // 如果不是第一道编程题，在前面添加空行
                    if (programmingCount > 1) {
                        formattedContent.append("\n");
                    }
                    formattedContent.append("【").append(programmingCount).append(".】").append(line).append("\n");
                    waitingForQuestionContent = false;
                    afterDataRange = false;
                } else if (line.startsWith("- 时间限制：") || line.startsWith("-时间限制：")) {
                    // 时间内存限制
                    formattedContent.append("【").append(line).append("】\n");
                } else if (line.equals("题目描述")) {
                    formattedContent.append("【题目描述】\n");
                } else if (line.equals("输入格式")) {
                    formattedContent.append("【输入格式】\n");
                } else if (line.equals("输出格式")) {
                    formattedContent.append("【输出格式】\n");
                } else if (line.equals("输入样例") || line.startsWith("输入样例")) {
                    // 处理输入样例，可能有编号
                    if (line.equals("输入样例")) {
                        formattedContent.append("【输入样例 1】\n");
                    } else {
                        // 提取样例编号，如"输入样例1" -> "【输入样例 1】"
                        String sampleNumber = line.replace("输入样例", "").trim();
                        if (sampleNumber.isEmpty()) {
                            sampleNumber = "1";
                        }
                        formattedContent.append("【输入样例 ").append(sampleNumber).append("】\n");
                    }
                } else if (line.equals("输出样例") || line.startsWith("输出样例")) {
                    // 处理输出样例，可能有编号
                    if (line.equals("输出样例")) {
                        formattedContent.append("【输出样例 1】\n");
                    } else {
                        // 提取样例编号，如"输出样例 1" -> "【输出样例 1】"
                        String sampleNumber = line.replace("输出样例", "").trim();
                        if (sampleNumber.isEmpty()) {
                            sampleNumber = "1";
                        }
                        formattedContent.append("【输出样例 ").append(sampleNumber).append("】\n");
                    }
                } else if (line.equals("数据范围")) {
                    formattedContent.append("【数据范围】\n");
                } else if (line.startsWith("对于所有测试点") || line.contains("保证")) {
                    // 数据范围的具体内容，不编号
                    formattedContent.append(line).append("\n");
                    // 标记刚处理完数据范围内容
                    afterDataRange = true;
                } else {
                    // 普通内容，不编号
                    formattedContent.append(line).append("\n");
                }
            }
        }

        String result = formattedContent.toString();
        log.info("=== 格式化完成 ===");
        log.info("格式化结果长度: {}", result.length());
        if (result.length() > 0) {
            log.info("格式化结果前200字符: {}", result.substring(0, Math.min(200, result.length())));
        } else {
            log.info("格式化结果为空");
        }

        return result;
    }

    /**
     * 获取难度文本
     */
    private String getDifficultyText(Integer difficulty) {
        switch (difficulty) {
            case 1: return "简单";
            case 2: return "中等";
            case 3: return "困难";
            default: return "中等";
        }
    }

    /**
     * 判断是否为编程题标题
     * @param line 当前行内容
     * @param waitingForQuestionContent 是否正在等待新题目内容（即刚处理完数据范围且遇到空行后）
     */
    private boolean isProgrammingQuestionTitle(String line, boolean waitingForQuestionContent) {
        // 如果正在等待新题目内容，且当前行符合题目标题特征，则认为是新编程题
        if (waitingForQuestionContent) {
            return isPotentialProgrammingTitle(line);
        }

        // 如果不在等待状态，则不是新题目标题
        return false;
    }

    /**
     * 判断一行内容是否可能是编程题标题
     */
    private boolean isPotentialProgrammingTitle(String line) {
        // 排除已知的格式标识符
        if (line.equals("题目描述") || line.equals("输入格式") || line.equals("输出格式") ||
            line.equals("输入样例") || line.equals("输出样例") || line.equals("数据范围") ||
            line.startsWith("- 时间限制：") || line.startsWith("-时间限制：") ||
            line.startsWith("【") || line.startsWith("输入样例") || line.startsWith("输出样例")) {
            return false;
        }

        // 排除纯数字行（通常是样例数据）
        if (line.trim().matches("^\\d+$")) {
            return false;
        }

        // 排除数据范围描述行
        if (line.startsWith("对于所有测试点") || line.contains("保证") || line.contains("≤") || line.contains("≥")) {
            return false;
        }

        // 排除题目描述内容（通常包含具体的描述性语句）
        if (line.contains("行，") || line.contains("个整数") || line.contains("表示") ||
            line.contains("需要") || line.contains("计算") || line.contains("输出") ||
            line.contains("输入") || line.contains("第一行") || line.contains("第二行") ||
            line.contains("第三行") || line.contains("每行") || line.contains("共") ||
            line.contains("接下来") || line.contains("一个正整数") || line.contains("一个整数")) {
            return false;
        }

        // 编程题标题通常是简短的中文名称，不包含复杂的描述
        // 长度在2-20字符之间，包含中文，不包含标点符号（除了可能的中文标点）
        if (line.length() >= 2 && line.length() <= 20 && containsChinese(line) &&
            !line.contains("，") && !line.contains("。") && !line.contains("；") &&
            !line.contains("：") && !line.contains("？") && !line.contains("！")) {
            return true;
        }

        return false;
    }

    /**
     * 判断字符串是否包含中文
     */
    private boolean containsChinese(String str) {
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为题目行（包含问号或者是编程题标题）
     */
    private boolean isQuestionLine(String line) {
        // 包含问号的是题目
        if (line.contains("？") || line.contains("?")) {
            return true;
        }
        // 不包含特殊标识符的普通文本可能是编程题标题
        return !isOption(line) && !isAnswer(line) && !isExplanation(line);
    }

    /**
     * 判断是否为选项
     */
    private boolean isOption(String line) {
        return line.matches("^[A-D][.、]\\s*.*") || line.matches("^[ABCD][:：]\\s*.*");
    }

    /**
     * 判断是否为答案
     */
    private boolean isAnswer(String line) {
        return line.startsWith("答案：") || line.startsWith("答案:") || line.toLowerCase().startsWith("answer:");
    }

    /**
     * 判断是否为解析
     */
    private boolean isExplanation(String line) {
        return line.startsWith("解析：") || line.startsWith("解析:") || line.toLowerCase().startsWith("explanation:");
    }

    /**
     * 提取答案内容
     */
    private String extractAnswer(String line) {
        if (line.startsWith("答案：")) {
            return line.substring(3).trim();
        } else if (line.startsWith("答案:")) {
            return line.substring(3).trim();
        } else if (line.toLowerCase().startsWith("answer:")) {
            return line.substring(7).trim();
        }
        return line;
    }

    /**
     * 提取解析内容
     */
    private String extractExplanation(String line) {
        if (line.startsWith("解析：")) {
            return line.substring(3).trim();
        } else if (line.startsWith("解析:")) {
            return line.substring(3).trim();
        } else if (line.toLowerCase().startsWith("explanation:")) {
            return line.substring(12).trim();
        }
        return line;
    }

    /**
     * 格式化选项：只包裹选项标识符部分
     */
    private String formatOption(String line) {
        // 匹配 "A. 内容" 或 "A、内容" 格式
        if (line.matches("^[A-D][.、]\\s*.*")) {
            // 找到选项标识符的结束位置
            int separatorIndex = -1;
            if (line.contains(". ")) {
                separatorIndex = line.indexOf(". ") + 2;
            } else if (line.contains(".")) {
                separatorIndex = line.indexOf(".") + 1;
            } else if (line.contains("、")) {
                separatorIndex = line.indexOf("、") + 1;
            }

            if (separatorIndex > 0) {
                String optionLabel = line.substring(0, separatorIndex);
                String optionContent = line.substring(separatorIndex).trim();
                return "【" + optionLabel + "】" + optionContent;
            }
        }

        // 匹配 "A: 内容" 或 "A：内容" 格式
        if (line.matches("^[ABCD][:：]\\s*.*")) {
            int separatorIndex = -1;
            if (line.contains(": ")) {
                separatorIndex = line.indexOf(": ") + 2;
            } else if (line.contains(":")) {
                separatorIndex = line.indexOf(":") + 1;
            } else if (line.contains("：")) {
                separatorIndex = line.indexOf("：") + 1;
            }

            if (separatorIndex > 0) {
                String optionLabel = line.substring(0, separatorIndex);
                String optionContent = line.substring(separatorIndex).trim();
                return "【" + optionLabel + "】" + optionContent;
            }
        }

        // 如果格式不匹配，返回原始内容
        return line;
    }

    /**
     * 检测文件内部重复题目
     * @param questions 题目列表
     * @return 检测结果
     */
    private Map<String, Object> checkInternalDuplicates(List<ExamQuestion> questions) {
        Map<String, Object> result = new HashMap<>();
        Map<String, List<ExamQuestion>> duplicateGroups = new HashMap<>();
        Set<String> processedKeys = new HashSet<>();
        boolean hasInternalDuplicates = false;

        for (int i = 0; i < questions.size(); i++) {
            ExamQuestion question1 = questions.get(i);
            String key1 = generateQuestionKey(question1);

            if (processedKeys.contains(key1)) {
                continue; // 已经处理过的题目
            }

            List<ExamQuestion> duplicateGroup = new ArrayList<>();
            duplicateGroup.add(question1);

            // 查找与当前题目重复的其他题目
            for (int j = i + 1; j < questions.size(); j++) {
                ExamQuestion question2 = questions.get(j);
                String key2 = generateQuestionKey(question2);

                if (key1.equals(key2)) {
                    duplicateGroup.add(question2);
                    hasInternalDuplicates = true;
                }
            }

            if (duplicateGroup.size() > 1) {
                duplicateGroups.put(key1, duplicateGroup);
            }

            processedKeys.add(key1);
        }

        result.put("hasInternalDuplicates", hasInternalDuplicates);
        result.put("duplicateGroups", duplicateGroups);
        result.put("totalQuestions", questions.size());
        result.put("uniqueQuestions", processedKeys.size());
        result.put("duplicateCount", questions.size() - processedKeys.size());

        return result;
    }

    /**
     * 生成题目的唯一标识键
     */
    private String generateQuestionKey(ExamQuestion question) {
        return String.format("%s|%s|%s|%d",
            question.getTitle() != null ? question.getTitle().trim() : "",
            question.getSubject() != null ? question.getSubject() : "",
            question.getLevel() != null ? question.getLevel() : "",
            question.getQuestionType() != null ? question.getQuestionType() : 0);
    }

    /**
     * 重写save方法，集成内容处理
     */
    @Override
    public boolean save(ExamQuestion entity) {
        // 保存前处理内容
        questionContentService.processQuestionBeforeSave(entity);
        return super.save(entity);
    }

    /**
     * 重写updateById方法，集成内容处理
     */
    @Override
    public boolean updateById(ExamQuestion entity) {
        // 更新前处理内容
        questionContentService.processQuestionBeforeSave(entity);
        return super.updateById(entity);
    }

    /**
     * 重写getById方法，集成内容处理
     */
    @Override
    public ExamQuestion getById(java.io.Serializable id) {
        ExamQuestion question = super.getById(id);
        if (question != null) {
            // 读取后处理内容
            questionContentService.processQuestionAfterLoad(question);
        }
        return question;
    }

    /**
     * 重写list方法，集成内容处理
     */
    @Override
    public List<ExamQuestion> list() {
        List<ExamQuestion> questions = super.list();
        if (questions != null && !questions.isEmpty()) {
            // 批量处理内容
            questions.forEach(questionContentService::processQuestionAfterLoad);
        }
        return questions;
    }
}