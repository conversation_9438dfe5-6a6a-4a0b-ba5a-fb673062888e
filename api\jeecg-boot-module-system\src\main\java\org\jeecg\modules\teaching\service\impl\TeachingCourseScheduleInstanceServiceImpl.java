package org.jeecg.modules.teaching.service.impl;

import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance;
import org.jeecg.modules.teaching.mapper.TeachingCourseScheduleInstanceMapper;
import org.jeecg.modules.teaching.mapper.TeachingCourseScheduleMapper;
import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleInstanceService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 单次课程实例
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
@Service
public class TeachingCourseScheduleInstanceServiceImpl extends ServiceImpl<TeachingCourseScheduleInstanceMapper, TeachingCourseScheduleInstance> implements ITeachingCourseScheduleInstanceService {

    private static final Logger log = LoggerFactory.getLogger(TeachingCourseScheduleInstanceServiceImpl.class);

    @Autowired
    private TeachingCourseScheduleInstanceMapper teachingCourseScheduleInstanceMapper;
    
    @Autowired
    private TeachingCourseScheduleMapper teachingCourseScheduleMapper;
    

    
    @Autowired
    private ITeachingCourseNotificationService teachingCourseNotificationService;

    @Override
    public TeachingCourseScheduleInstance getInstanceByParentIdAndDate(String parentId, String instanceDate) {
        return teachingCourseScheduleInstanceMapper.selectByParentIdAndDate(parentId, instanceDate);
    }

    @Override
    @Transactional
    public Result<?> addOrUpdateInstance(TeachingCourseScheduleInstance instance, boolean sendNotification) {
        if (StringUtils.isEmpty(instance.getParentId())) {
            return Result.error("父课程ID不能为空");
        }
        
        // 查询父课程信息
        TeachingCourseSchedule parentSchedule = teachingCourseScheduleMapper.selectById(instance.getParentId());
        if (parentSchedule == null) {
            return Result.error("父课程不存在");
        }
        
        boolean isNew = StringUtils.isEmpty(instance.getId());
        TeachingCourseScheduleInstance oldInstance = null;
        
        if (!isNew) {
            oldInstance = getById(instance.getId());
            if (oldInstance == null) {
                return Result.error("要编辑的课程实例不存在");
            }
            
            // 已存在的实例，记录详细日志
            log.info("编辑已修改的单次课程实例, ID={}, 实例日期={}", 
                instance.getId(), 
                instance.getInstanceDate() != null ? 
                    DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd") : 
                    "未设置日期");
            log.info("这是多次编辑已修改的课程实例，不需要更新父课程的deleted_instances字段");
            
            // 检查是否修改了时间相关字段，如果修改了，需要重置reminder_created字段
            boolean timeChanged = false;
            if (oldInstance.getStartTime() != null && instance.getStartTime() != null) {
                // 比较时间是否变更（精确到分钟级别）
                String oldStartTimeStr = DateFormatUtils.format(oldInstance.getStartTime(), "yyyy-MM-dd HH:mm");
                String newStartTimeStr = DateFormatUtils.format(instance.getStartTime(), "yyyy-MM-dd HH:mm");
                timeChanged = !oldStartTimeStr.equals(newStartTimeStr);
                
                // 检查结束时间是否变更
                if (!timeChanged && oldInstance.getEndTime() != null && instance.getEndTime() != null) {
                    String oldEndTimeStr = DateFormatUtils.format(oldInstance.getEndTime(), "yyyy-MM-dd HH:mm");
                    String newEndTimeStr = DateFormatUtils.format(instance.getEndTime(), "yyyy-MM-dd HH:mm");
                    timeChanged = !oldEndTimeStr.equals(newEndTimeStr);
                }
            }
            
            if (timeChanged) {
                log.info("检测到课程时间变更，重置reminder_created字段为0，以便系统重新创建提醒通知");
                log.info("原始时间: 开始={}, 结束={}, 新时间: 开始={}, 结束={}", 
                    oldInstance.getStartTime() != null ? DateFormatUtils.format(oldInstance.getStartTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                    oldInstance.getEndTime() != null ? DateFormatUtils.format(oldInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                    instance.getStartTime() != null ? DateFormatUtils.format(instance.getStartTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                    instance.getEndTime() != null ? DateFormatUtils.format(instance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "null");
                instance.setReminderCreated(0);
            } else if (instance.getReminderCreated() != null && instance.getReminderCreated() == 1) {
                log.info("课程时间未变更，且reminder_created=1，保持不变");
            }
        } else {
            // 这是首次修改，记录详细日志
            log.info("首次修改单次课程实例, parentId={}, 实例日期={}", 
                instance.getParentId(), 
                instance.getInstanceDate() != null ? 
                    DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd") : 
                    "未设置日期");
            
            // 首次创建实例时，设置reminder_created为0
            instance.setReminderCreated(0);
            log.info("首次创建实例，设置reminder_created=0");
            
            // 对于首次修改的情况，从父课程创建一个临时的原始课程实例作为oldInstance
            // 这样可以保证在通知系统中能够显示正确的变更信息
            if (instance.getInstanceDate() != null) {
                // 创建一个临时的原始课程实例
                oldInstance = createTemporaryOriginalInstance(parentSchedule, instance.getInstanceDate());
                log.info("已创建临时原始课程实例，用于生成变更通知");
            }
        }
        
        // 设置或更新instance_date字段
        if (instance.getInstanceDate() == null && instance.getStartTime() != null) {
            instance.setInstanceDate(DateUtils.truncate(instance.getStartTime(), java.util.Calendar.DATE));
        }
        
        // 保存或更新实例
        boolean success = saveOrUpdate(instance);
        if (!success) {
            return Result.error("保存课程实例失败");
        }
        
        // 只有在第一次修改单次课程实例时才更新父课程的deletedInstances字段
        if (isNew && instance.getInstanceDate() != null) {
            // 将实例日期添加到父课程的deletedInstances中
            String instanceDateStr = DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd");
            List<String> deletedInstances = new ArrayList<>();
            
            // 如果已有删除记录，解析现有记录
            if (StringUtils.isNotBlank(parentSchedule.getDeletedInstances())) {
                deletedInstances.addAll(Arrays.asList(parentSchedule.getDeletedInstances().split(",")));
            }
            
            // 如果日期记录不在列表中，添加到列表
            if (!deletedInstances.contains(instanceDateStr)) {
                deletedInstances.add(instanceDateStr);
                log.info("首次修改单次课程，将日期{}添加到父课程的deleted_instances中", instanceDateStr);
                
                // 更新到数据库
                parentSchedule.setDeletedInstances(String.join(",", deletedInstances));
                teachingCourseScheduleMapper.updateById(parentSchedule);
            } else {
                log.info("日期{}已存在于父课程的deleted_instances中，无需重复添加", instanceDateStr);
            }
        } else if (!isNew) {
            log.info("多次编辑单次课程实例，无需更新父课程的deleted_instances字段");
        }
        
        // 发送通知
        if (sendNotification) {
            try {
                // 修改为统一处理通知逻辑，不再区分isNew，因为我们已经为首次修改创建了临时的原始实例
                teachingCourseNotificationService.createUpdateCourseNotification(
                    convertInstanceToSchedule(instance), 
                    convertInstanceToSchedule(oldInstance), 
                    instance.getNotifyTeachers() == 1,
                    instance.getNotifyStudents() == 1
                );
                
                log.info("已发送课程变更通知, 实例ID={}, isNew={}", instance.getId(), isNew);
            } catch (Exception e) {
                log.error("发送课程变更通知失败", e);
            }
        }
        
        Result<TeachingCourseScheduleInstance> result = new Result<>();
        result.setSuccess(true);
        result.setMessage("保存成功");
        result.setResult(instance);
        return result;
    }
    
    /**
     * 添加或更新单次课程实例，处理拖拽场景
     * 
     * @param instance 课程实例
     * @param originalDate 原始日期 (格式: yyyy-MM-dd)，用于拖拽场景
     * @param sendNotification 是否发送通知
     * @return 处理结果
     */
    @Override
    @Transactional
    public Result<?> addOrUpdateInstanceWithOriginalDate(TeachingCourseScheduleInstance instance, String originalDate, boolean sendNotification) {
        if (StringUtils.isEmpty(instance.getParentId())) {
            return Result.error("父课程ID不能为空");
        }
        
        if (StringUtils.isEmpty(originalDate)) {
            return Result.error("原始日期不能为空");
        }
        
        // 记录详细日志
        log.info("进入addOrUpdateInstanceWithOriginalDate方法, parentId={}, originalDate={}, 新日期={}", 
                 instance.getParentId(), originalDate, 
                 instance.getInstanceDate() != null ? DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd") : "null");
        
        // 查询父课程信息
        TeachingCourseSchedule parentSchedule = teachingCourseScheduleMapper.selectById(instance.getParentId());
        if (parentSchedule == null) {
            return Result.error("父课程不存在");
        }
        
        // 检查是否存在原始日期的实例
        TeachingCourseScheduleInstance existingOriginalInstance = getInstanceByParentIdAndDate(
            instance.getParentId(), originalDate);
        
        // 创建一个临时的原始课程实例，用于生成变更通知
        TeachingCourseScheduleInstance oldInstance = null;
        
        // 如果存在原始日期的实例，用它作为oldInstance并删除它
        if (existingOriginalInstance != null) {
            log.info("发现原始日期的已修改实例，准备删除, instanceId={}", existingOriginalInstance.getId());
            oldInstance = existingOriginalInstance;
            removeById(existingOriginalInstance.getId());
        } else {
            // 如果不存在原始日期的实例，则创建一个临时的原始实例用于通知
            try {
                Date originalDateObj = DateUtils.parseDate(originalDate, "yyyy-MM-dd");
                oldInstance = createTemporaryOriginalInstance(parentSchedule, originalDateObj);
                log.info("已创建临时原始课程实例，用于生成拖拽变更通知");
            } catch (ParseException e) {
                log.error("解析原始日期失败: {}", originalDate, e);
            }
        }
        
        boolean isNew = StringUtils.isEmpty(instance.getId());
        
        if (!isNew) {
            TeachingCourseScheduleInstance existingInstance = getById(instance.getId());
            if (existingInstance == null) {
                return Result.error("要编辑的课程实例不存在");
            }
            
            // 检查是否修改了时间相关字段
            boolean timeChanged = false;
            if (existingInstance.getStartTime() != null && instance.getStartTime() != null) {
                // 比较时间是否变更（精确到分钟级别）
                String oldStartTimeStr = DateFormatUtils.format(existingInstance.getStartTime(), "yyyy-MM-dd HH:mm");
                String newStartTimeStr = DateFormatUtils.format(instance.getStartTime(), "yyyy-MM-dd HH:mm");
                timeChanged = !oldStartTimeStr.equals(newStartTimeStr);
                
                // 检查结束时间是否变更
                if (!timeChanged && existingInstance.getEndTime() != null && instance.getEndTime() != null) {
                    String oldEndTimeStr = DateFormatUtils.format(existingInstance.getEndTime(), "yyyy-MM-dd HH:mm");
                    String newEndTimeStr = DateFormatUtils.format(instance.getEndTime(), "yyyy-MM-dd HH:mm");
                    timeChanged = !oldEndTimeStr.equals(newEndTimeStr);
                }
            }
            
            if (timeChanged) {
                log.info("检测到课程时间变更，重置reminder_created字段为0，以便系统重新创建提醒通知");
                log.info("原始时间: 开始={}, 结束={}, 新时间: 开始={}, 结束={}", 
                    existingInstance.getStartTime() != null ? DateFormatUtils.format(existingInstance.getStartTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                    existingInstance.getEndTime() != null ? DateFormatUtils.format(existingInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                    instance.getStartTime() != null ? DateFormatUtils.format(instance.getStartTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                    instance.getEndTime() != null ? DateFormatUtils.format(instance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "null");
                instance.setReminderCreated(0);
            } else if (instance.getReminderCreated() != null && instance.getReminderCreated() == 1) {
                log.info("课程时间未变更，且reminder_created=1，保持不变");
            }
        } else {
            // 拖拽场景下的新建实例，设置reminder_created为0
            log.info("拖拽场景下创建新实例，设置reminder_created为0");
            instance.setReminderCreated(0);
        }
        
        // 设置或更新instance_date字段
        if (instance.getInstanceDate() == null && instance.getStartTime() != null) {
            instance.setInstanceDate(DateUtils.truncate(instance.getStartTime(), java.util.Calendar.DATE));
            log.info("根据startTime设置instanceDate={}", 
                     instance.getInstanceDate() != null ? DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd") : "null");
        }
        
        // 保存或更新实例
        boolean success = saveOrUpdate(instance);
        if (!success) {
            return Result.error("保存课程实例失败");
        }
        
        log.info("实例保存成功，实例ID={}, reminder_created={}", instance.getId(), instance.getReminderCreated());
        
        // 对于拖拽场景，判断是否需要添加原始日期到deleted_instances字段
        // 只有在首次修改单次课程时才需要添加，多次修改已修改的单次课程不需要重复添加
        // 首次修改判断标准：existingOriginalInstance为空且原始日期不在父课程的deleted_instances中
        
        // 判断原始日期是否已在父课程的deleted_instances中
        boolean isOriginalDateInDeletedInstances = false;
        if (StringUtils.isNotBlank(parentSchedule.getDeletedInstances())) {
            List<String> deletedInstances = Arrays.asList(parentSchedule.getDeletedInstances().split(","));
            isOriginalDateInDeletedInstances = deletedInstances.contains(originalDate);
            log.info("原始日期{}{}在父课程的deleted_instances列表中", originalDate, isOriginalDateInDeletedInstances ? "已经" : "不");
        }
        
        // 只有在首次修改的情况下才将原始日期添加到deleted_instances列表
        // 判断条件：existingOriginalInstance为空（表示这个日期之前没有被修改过）且原始日期不在deleted_instances中
        if (existingOriginalInstance == null && !isOriginalDateInDeletedInstances) {
            log.info("首次修改单次课程，将原始日期{}添加到父课程的deleted_instances中", originalDate);
            
            List<String> deletedInstances = new ArrayList<>();
            // 如果已有删除记录，解析现有记录
            if (StringUtils.isNotBlank(parentSchedule.getDeletedInstances())) {
                deletedInstances.addAll(Arrays.asList(parentSchedule.getDeletedInstances().split(",")));
                log.info("现有的deleted_instances列表: {}", deletedInstances);
            }
            
            // 添加原始日期到列表
            deletedInstances.add(originalDate);
            log.info("将原始日期{}添加到deleted_instances", originalDate);
            
            // 更新到数据库
            parentSchedule.setDeletedInstances(String.join(",", deletedInstances));
            int updateResult = teachingCourseScheduleMapper.updateById(parentSchedule);
            log.info("更新父课程deleted_instances结果: {}", updateResult > 0 ? "成功" : "失败");
        } else {
            // 多次修改单次课程，无需更新父课程的deleted_instances字段
            log.info("多次修改单次课程，无需更新父课程的deleted_instances字段");
        }
        
        // 发送通知
        if (sendNotification) {
            try {
                log.info("准备发送课程更新通知");
                // 更新通知处理 - 相当于UPDATE通知
                teachingCourseNotificationService.createUpdateCourseNotification(
                    convertInstanceToSchedule(instance), 
                    convertInstanceToSchedule(oldInstance), 
                    instance.getNotifyTeachers() == 1,
                    instance.getNotifyStudents() == 1
                );
                log.info("课程更新通知发送完成");
            } catch (Exception e) {
                log.error("发送通知失败", e);
            }
        }
        
        Result<TeachingCourseScheduleInstance> result = new Result<>();
        result.setSuccess(true);
        result.setMessage("保存成功");
        result.setResult(instance);
        return result;
    }

    @Override
    @Transactional
    public Result<?> deleteInstance(String id, boolean sendNotification) {
        // 查询实例信息
        TeachingCourseScheduleInstance instance = getById(id);
        if (instance == null) {
            return Result.error("课程实例不存在");
        }
        
        // 查询父课程信息
        TeachingCourseSchedule parentSchedule = teachingCourseScheduleMapper.selectById(instance.getParentId());
        if (parentSchedule == null) {
            return Result.error("父课程不存在");
        }
        
        // 记录详细日志
        String instanceDateStr = DateFormatUtils.format(instance.getInstanceDate(), "yyyy-MM-dd");
        log.info("删除已修改的单次课程实例, ID={}, 实例日期={}", id, instanceDateStr);
        
        // 注意：此处不需要将实例日期添加到父课程的deleted_instances中
        // 因为根据核心需求，删除"已修改的单次课程"时仅需直接从实例表中删除记录
        
        // 发送通知
        if (sendNotification) {
            // 取消通知处理 - 相当于CANCEL通知
            teachingCourseNotificationService.createCancelCourseNotification(
                convertInstanceToSchedule(instance), 
                instance.getNotifyTeachers() == 1,
                instance.getNotifyStudents() == 1
            );
        }
        
        // 删除实例
        boolean success = removeById(id);
        if (!success) {
            return Result.error("删除课程实例失败");
        }
        
        Result<?> result = new Result<>();
        result.setSuccess(true);
        result.setMessage("删除成功");
        return result;
    }
    
    /**
     * 将课程实例转换为课程排期对象(用于通知系统处理)
     */
    private TeachingCourseSchedule convertInstanceToSchedule(TeachingCourseScheduleInstance instance) {
        if (instance == null) return null;
        
        TeachingCourseSchedule schedule = new TeachingCourseSchedule();
        schedule.setId(instance.getId());
        schedule.setCourseId(instance.getCourseId());
        schedule.setClassroomId(instance.getClassroomId());
        schedule.setTeacherId(instance.getTeacherId());
        schedule.setClassId(instance.getClassId());
        schedule.setScheduleTitle(instance.getScheduleTitle());
        schedule.setStudentNames(instance.getStudentNames());
        schedule.setStartTime(instance.getStartTime());
        schedule.setEndTime(instance.getEndTime());
        schedule.setColor(instance.getColor());
        schedule.setDescription(instance.getDescription());
        schedule.setStatus(instance.getStatus());
        schedule.setNotifyBeforeMinutes(instance.getNotifyBeforeMinutes());
        schedule.setNotifyTeachers(instance.getNotifyTeachers());
        schedule.setNotifyStudents(instance.getNotifyStudents());
        schedule.setCreateBy(instance.getCreateBy());
        schedule.setCreateTime(instance.getCreateTime());
        schedule.setUpdateBy(instance.getUpdateBy());
        schedule.setUpdateTime(instance.getUpdateTime());
        // 设置为单次课程
        schedule.setRepeatType(0);
        
        return schedule;
    }

    /**
     * 根据父课程和日期创建临时原始课程实例
     * 用于首次修改单次课程时，生成正确的变更通知
     * 
     * @param parentSchedule 父课程
     * @param instanceDate 实例日期
     * @return 临时原始课程实例
     */
    private TeachingCourseScheduleInstance createTemporaryOriginalInstance(TeachingCourseSchedule parentSchedule, Date instanceDate) {
        try {
            if (parentSchedule == null || instanceDate == null) {
                log.warn("无法创建临时原始课程实例，父课程或实例日期为空");
                return null;
            }
            
            // 实例日期字符串，用于日志记录
            String instanceDateStr = DateFormatUtils.format(instanceDate, "yyyy-MM-dd");
            log.info("创建临时原始课程实例, 父课程ID={}, 实例日期={}", parentSchedule.getId(), instanceDateStr);
            
            // 创建临时实例对象
            TeachingCourseScheduleInstance tempInstance = new TeachingCourseScheduleInstance();
            
            // 生成临时ID，格式：父课程ID_TEMP_yyyyMMdd
            String tempId = parentSchedule.getId() + "_TEMP_" + DateFormatUtils.format(instanceDate, "yyyyMMdd");
            tempInstance.setId(tempId);
            
            // 设置父课程ID和实例日期
            tempInstance.setParentId(parentSchedule.getId());
            tempInstance.setInstanceDate(instanceDate);
            
            // 复制父课程的基本信息
            tempInstance.setScheduleTitle(parentSchedule.getScheduleTitle());
            tempInstance.setCourseId(parentSchedule.getCourseId());
            tempInstance.setClassroomId(parentSchedule.getClassroomId());
            tempInstance.setTeacherId(parentSchedule.getTeacherId());
            tempInstance.setClassId(parentSchedule.getClassId());
            tempInstance.setStudentNames(parentSchedule.getStudentNames());
            tempInstance.setColor(parentSchedule.getColor());
            tempInstance.setDescription(parentSchedule.getDescription());
            tempInstance.setStatus(parentSchedule.getStatus());
            tempInstance.setNotifyBeforeMinutes(parentSchedule.getNotifyBeforeMinutes());
            tempInstance.setNotifyTeachers(parentSchedule.getNotifyTeachers());
            tempInstance.setNotifyStudents(parentSchedule.getNotifyStudents());
            
            // 计算该实例日期的动态开始和结束时间
            // 从父课程时间复制时分秒，使用实例日期的年月日
            java.util.Calendar baseCal = java.util.Calendar.getInstance();
            java.util.Calendar instanceCal = java.util.Calendar.getInstance();
            
            if (parentSchedule.getStartTime() != null) {
                baseCal.setTime(parentSchedule.getStartTime());
                instanceCal.setTime(instanceDate);
                instanceCal.set(java.util.Calendar.HOUR_OF_DAY, baseCal.get(java.util.Calendar.HOUR_OF_DAY));
                instanceCal.set(java.util.Calendar.MINUTE, baseCal.get(java.util.Calendar.MINUTE));
                instanceCal.set(java.util.Calendar.SECOND, baseCal.get(java.util.Calendar.SECOND));
                tempInstance.setStartTime(instanceCal.getTime());
            }
            
            if (parentSchedule.getEndTime() != null) {
                baseCal.setTime(parentSchedule.getEndTime());
                instanceCal.setTime(instanceDate);
                instanceCal.set(java.util.Calendar.HOUR_OF_DAY, baseCal.get(java.util.Calendar.HOUR_OF_DAY));
                instanceCal.set(java.util.Calendar.MINUTE, baseCal.get(java.util.Calendar.MINUTE));
                instanceCal.set(java.util.Calendar.SECOND, baseCal.get(java.util.Calendar.SECOND));
                tempInstance.setEndTime(instanceCal.getTime());
            }
            
            log.info("临时原始课程实例创建成功, 临时ID={}, 开始时间={}, 结束时间={}", 
                tempId, 
                tempInstance.getStartTime() != null ? DateFormatUtils.format(tempInstance.getStartTime(), "yyyy-MM-dd HH:mm:ss") : "null",
                tempInstance.getEndTime() != null ? DateFormatUtils.format(tempInstance.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "null");
            
            return tempInstance;
        } catch (Exception e) {
            log.error("创建临时原始课程实例失败", e);
            return null;
        }
    }
} 