# HOJ判题服务集成规范

## HOJ系统架构

### 服务组件
- **HOJ主后端** (`HOJ/hoj-springboot/DataBackup`): 业务逻辑处理
- **HOJ判题服务** (`HOJ/hoj-springboot/JudgeServer`): 专职判题引擎
- **HOJ前端** (`HOJ/hoj-vue`): 用户界面
- **Teaching系统**: 教学管理平台

### 集成架构
```
Teaching系统 → HOJ主后端 → HOJ判题服务
     ↓           ↓           ↓
   用户界面    业务处理    代码执行
```

## 判题服务接口规范

### 1. 测试连接接口
```java
// Teaching系统调用
@GetMapping("/test-connection")
public Result<String> testConnection() {
    return teachingJudgeService.testHojConnection();
}

// 实现逻辑
public Result<String> testHojConnection() {
    try {
        String url = hojConfig.getBaseUrl() + "/api/test-connection";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        return Result.ok("HOJ连接正常: " + response.getBody());
    } catch (Exception e) {
        return Result.error("HOJ连接失败: " + e.getMessage());
    }
}
```

### 2. 代码测试接口
```java
// 请求DTO
@Data
public class HojTestJudgeReqDTO {
    private String language;        // 编程语言
    private String code;           // 用户代码
    private String input;          // 测试输入
    private String expectedOutput; // 期望输出
    private Integer timeLimit;     // 时间限制(ms)
    private Integer memoryLimit;   // 内存限制(KB)
}

// 控制器接口
@PostMapping("/test-judge")
public Result<String> testJudge(@RequestBody @Valid HojTestJudgeReqDTO request) {
    return teachingJudgeService.submitTestJudge(request);
}
```

### 3. 代码提交接口
```java
@PostMapping("/submit-test")
public Result<String> submitTest(@RequestBody @Valid HojSubmitReqDTO request) {
    return teachingJudgeService.submitCode(request);
}
```

### 4. 结果查询接口
```java
@GetMapping("/result/{submissionId}")
public Result<HojJudgeResultVO> getJudgeResult(@PathVariable String submissionId) {
    return teachingJudgeService.getJudgeResult(submissionId);
}
```

## 数据传输对象规范

### 判题请求DTO
```java
@Data
@ApiModel("HOJ测试判题请求")
public class HojTestJudgeReqDTO {
    
    @NotBlank(message = "编程语言不能为空")
    @ApiModelProperty("编程语言(C++, Java, Python等)")
    private String language;
    
    @NotBlank(message = "代码不能为空")
    @ApiModelProperty("用户代码")
    private String code;
    
    @ApiModelProperty("测试输入")
    private String input;
    
    @ApiModelProperty("期望输出")
    private String expectedOutput;
    
    @ApiModelProperty(value = "时间限制(ms)", example = "1000")
    private Integer timeLimit = 1000;
    
    @ApiModelProperty(value = "内存限制(KB)", example = "65536")
    private Integer memoryLimit = 65536;
}
```

### 判题结果VO
```java
@Data
@ApiModel("HOJ判题结果")
public class HojJudgeResultVO {
    
    @ApiModelProperty("提交ID")
    private String submissionId;
    
    @ApiModelProperty("判题状态")
    private Integer status;
    
    @ApiModelProperty("状态描述")
    private String statusMsg;
    
    @ApiModelProperty("运行时间(ms)")
    private Integer time;
    
    @ApiModelProperty("内存使用(KB)")
    private Integer memory;
    
    @ApiModelProperty("编译信息")
    private String compileInfo;
    
    @ApiModelProperty("运行错误信息")
    private String errorInfo;
    
    @ApiModelProperty("用户输出")
    private String userOutput;
    
    @ApiModelProperty("期望输出")
    private String expectedOutput;
}
```

## 判题状态码规范

### HOJ判题状态
```java
public enum JudgeStatus {
    PENDING(5, "Pending"),                    // 等待判题
    COMPILING(6, "Compiling"),               // 编译中
    JUDGING(7, "Judging"),                   // 判题中
    ACCEPTED(0, "Accepted"),                 // 通过
    WRONG_ANSWER(-1, "Wrong Answer"),        // 答案错误
    COMPILE_ERROR(-2, "Compile Error"),      // 编译错误
    PRESENTATION_ERROR(-3, "Presentation Error"), // 格式错误
    RUNTIME_ERROR(3, "Runtime Error"),       // 运行时错误
    TIME_LIMIT_EXCEEDED(1, "Time Limit Exceeded"), // 超时
    MEMORY_LIMIT_EXCEEDED(2, "Memory Limit Exceeded"), // 内存超限
    SYSTEM_ERROR(4, "System Error");         // 系统错误
    
    private final int code;
    private final String message;
}
```

### 状态处理逻辑
```java
public String getStatusDescription(Integer status) {
    switch (status) {
        case 0: return "通过";
        case -1: return "答案错误";
        case -2: return "编译错误";
        case -3: return "格式错误";
        case 1: return "时间超限";
        case 2: return "内存超限";
        case 3: return "运行错误";
        case 4: return "系统错误";
        case 5: return "等待判题";
        case 6: return "编译中";
        case 7: return "判题中";
        default: return "未知状态";
    }
}
```

## 配置管理规范

### HOJ服务配置
```yaml
# application.yml
hoj:
  integration:
    enabled: true
    base-url: http://localhost:6688
    timeout: 30000
    retry-count: 3
    auth:
      token: your-hoj-auth-token
```

### 配置类
```java
@Data
@Component
@ConfigurationProperties(prefix = "hoj.integration")
public class HojIntegrationConfig {
    
    private boolean enabled = true;
    private String baseUrl = "http://localhost:6688";
    private int timeout = 30000;
    private int retryCount = 3;
    private Auth auth = new Auth();
    
    @Data
    public static class Auth {
        private String token;
    }
}
```

## 错误处理规范

### 异常定义
```java
public class HojIntegrationException extends RuntimeException {
    private final String errorCode;
    
    public HojIntegrationException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public HojIntegrationException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
}
```

### 异常处理
```java
@ExceptionHandler(HojIntegrationException.class)
public Result<String> handleHojIntegrationException(HojIntegrationException e) {
    log.error("HOJ集成异常: {}", e.getMessage(), e);
    return Result.error("判题服务异常: " + e.getMessage());
}
```

## 服务实现规范

### RestTemplate配置
```java
@Configuration
public class HojRestTemplateConfig {
    
    @Bean("hojRestTemplate")
    public RestTemplate hojRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 设置超时时间
        HttpComponentsClientHttpRequestFactory factory = 
            new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(30000);
        restTemplate.setRequestFactory(factory);
        
        // 添加拦截器
        restTemplate.getInterceptors().add(new HojAuthInterceptor());
        
        return restTemplate;
    }
}
```

### 服务实现模板
```java
@Service
@Slf4j
public class TeachingJudgeServiceImpl implements ITeachingJudgeService {
    
    @Autowired
    @Qualifier("hojRestTemplate")
    private RestTemplate restTemplate;
    
    @Autowired
    private HojIntegrationConfig hojConfig;
    
    @Override
    public Result<String> submitTestJudge(HojTestJudgeReqDTO request) {
        try {
            log.info("提交测试判题请求: {}", request);
            
            String url = hojConfig.getBaseUrl() + "/api/judge/test";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + hojConfig.getAuth().getToken());
            
            HttpEntity<HojTestJudgeReqDTO> entity = new HttpEntity<>(request, headers);
            ResponseEntity<CommonResult> response = restTemplate.postForEntity(
                url, entity, CommonResult.class);
            
            if (response.getStatusCode().is2xxSuccessful() && 
                response.getBody() != null && 
                response.getBody().getStatus() == 200) {
                
                String submissionId = (String) response.getBody().getData();
                log.info("测试判题提交成功，提交ID: {}", submissionId);
                return Result.ok(submissionId);
            } else {
                String errorMsg = response.getBody() != null ? 
                    response.getBody().getMsg() : "未知错误";
                log.error("测试判题提交失败: {}", errorMsg);
                return Result.error("判题提交失败: " + errorMsg);
            }
            
        } catch (Exception e) {
            log.error("测试判题提交异常", e);
            return Result.error("判题服务异常: " + e.getMessage());
        }
    }
}
```

## 前端集成规范

### API接口定义
```javascript
// api/judge.js
export const testJudge = (data) => {
  return axios({
    url: '/teaching/judge/test-judge',
    method: 'post',
    data
  })
}

export const getJudgeResult = (submissionId) => {
  return axios({
    url: `/teaching/judge/result/${submissionId}`,
    method: 'get'
  })
}
```

### 判题状态处理
```javascript
// utils/judgeStatus.js
export const JUDGE_STATUS = {
  0: { name: 'Accepted', color: 'green', icon: 'check-circle' },
  [-1]: { name: 'Wrong Answer', color: 'red', icon: 'close-circle' },
  [-2]: { name: 'Compile Error', color: 'orange', icon: 'warning' },
  5: { name: 'Pending', color: 'blue', icon: 'clock-circle' },
  7: { name: 'Judging', color: 'blue', icon: 'loading' }
}

export const getStatusInfo = (status) => {
  return JUDGE_STATUS[status] || { name: 'Unknown', color: 'gray', icon: 'question' }
}
```

## 监控和日志规范

### 日志记录
```java
// 关键操作日志
log.info("HOJ判题请求 - 语言: {}, 代码长度: {}", language, code.length());
log.info("HOJ判题结果 - 提交ID: {}, 状态: {}, 耗时: {}ms", submissionId, status, time);
log.error("HOJ服务异常 - URL: {}, 错误: {}", url, e.getMessage(), e);
```

### 性能监控
```java
@Component
public class HojMetrics {
    
    private final Counter judgeRequestCounter = Counter.build()
        .name("hoj_judge_requests_total")
        .help("Total HOJ judge requests")
        .register();
    
    private final Histogram judgeLatency = Histogram.build()
        .name("hoj_judge_duration_seconds")
        .help("HOJ judge request latency")
        .register();
}
```
