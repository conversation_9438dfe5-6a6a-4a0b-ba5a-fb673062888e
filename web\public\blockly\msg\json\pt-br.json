{"@metadata": {"authors": ["Almondega", "Amgauna", "<PERSON><PERSON><PERSON><PERSON>", "Cainamarques", "Caçador de Palavras", "<PERSON><PERSON><PERSON>", "EVinente", "<PERSON>", "Eduardoad<PERSON>", "<PERSON><PERSON><PERSON>", "Fasouzafreitas", "<PERSON>", "<PERSON><PERSON>", "Luk3", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Mordecaista", "Prilopes", "<PERSON> codi<PERSON>", "<PERSON><PERSON>", "TheGabrielZaum", "Trigonometria87", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "WikiUser22222"]}, "VARIABLES_DEFAULT_NAME": "item", "UNNAMED_KEY": "<PERSON><PERSON> tí<PERSON>lo", "TODAY": "Hoje", "DUPLICATE_BLOCK": "Duplicar", "ADD_COMMENT": "<PERSON><PERSON><PERSON><PERSON>", "REMOVE_COMMENT": "Remover coment<PERSON><PERSON>", "DUPLICATE_COMMENT": "<PERSON>p<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "EXTERNAL_INPUTS": "Entradas externas", "INLINE_INPUTS": "Entradas incorporadas", "DELETE_BLOCK": "Deletar bloco", "DELETE_X_BLOCKS": "Deletar %1 blocos", "DELETE_ALL_BLOCKS": "Deletar todos os blocos %1?", "CLEAN_UP": "Limpar blocos", "COLLAPSE_BLOCK": "Colapsar Bloco", "COLLAPSE_ALL": "Colapsar Blocos", "EXPAND_BLOCK": "Expandir bloco", "EXPAND_ALL": "Expandir blocos", "DISABLE_BLOCK": "Desabilitar bloco", "ENABLE_BLOCK": "Habilitar bloco", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "<PERSON><PERSON><PERSON>", "REDO": "<PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "<PERSON>dar valor:", "RENAME_VARIABLE": "Renomear variável...", "RENAME_VARIABLE_TITLE": "Renomear todas as variáveis '%1' para:", "NEW_VARIABLE": "<PERSON><PERSON><PERSON> vari<PERSON>...", "NEW_STRING_VARIABLE": "Criar variável de segmentos de texto...", "NEW_NUMBER_VARIABLE": "<PERSON>riar vari<PERSON>vel numérica...", "NEW_COLOUR_VARIABLE": "Criar variável colorida...", "NEW_VARIABLE_TYPE_TITLE": "Tipo da nova variável:", "NEW_VARIABLE_TITLE": "Nome da nova variável:", "VARIABLE_ALREADY_EXISTS": "A variável chamada '%1' já existe.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Já existe uma variável chamada '%1' para outra do tipo: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Deletar %1 usos da variável '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Não se pode deletar a variável '%1' porque é parte da definição da função '%2'", "DELETE_VARIABLE": "Deletar a variável '%1'", "COLOUR_PICKER_HELPURL": "https://pt.wikipedia.org/wiki/Cor", "COLOUR_PICKER_TOOLTIP": "Escolher uma cor da palheta de cores.", "COLOUR_RANDOM_TITLE": "cor aleat<PERSON><PERSON>", "COLOUR_RANDOM_TOOLTIP": "Escolher cor de forma aleatória.", "COLOUR_RGB_TITLE": "colorir com", "COLOUR_RGB_RED": "verm<PERSON><PERSON>", "COLOUR_RGB_GREEN": "verde", "COLOUR_RGB_BLUE": "azul", "COLOUR_RGB_TOOLTIP": "Criar uma cor com a quantidade especificada de vermelho, verde e azul. Todos os valores devem estar entre 0 e 100.", "COLOUR_BLEND_TITLE": "misturar", "COLOUR_BLEND_COLOUR1": "cor 1", "COLOUR_BLEND_COLOUR2": "cor 2", "COLOUR_BLEND_RATIO": "proporção", "COLOUR_BLEND_TOOLTIP": "Mistura duas cores em uma dada proporção (0,0 - 1,0).", "CONTROLS_REPEAT_HELPURL": "https://pt.wikipedia.org/wiki/Estrutura_de_repeti%C3%A7%C3%A3o#Repeti.C3.A7.C3.A3o_com_vari.C3.A1vel_de_controle", "CONTROLS_REPEAT_TITLE": "repita %1 vezes", "CONTROLS_REPEAT_INPUT_DO": "faça", "CONTROLS_REPEAT_TOOLTIP": "Faça algumas instruções várias vezes.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "repita enquanto", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "repita até", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Enquanto um valor for verdadeiro, então faça algumas instruções.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Enquanto um valor for falso, então faça algumas instruções.", "CONTROLS_FOR_TOOLTIP": "Faça com que a variável '%1' assuma os valores do número inicial ao número final, contando de acordo com o intervalo especificado e execute os blocos especificados.", "CONTROLS_FOR_TITLE": "contar com %1 de %2 até %3 por %4", "CONTROLS_FOREACH_TITLE": "para cada item %1 na lista %2", "CONTROLS_FOREACH_TOOLTIP": "Para cada item em uma lista, atribua o item à variável '%1' e então realize algumas instruções.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "encerra o laço", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "continua com a próxima iteração do laço", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Encerra o laço.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Ignora o resto deste laço, e continua com a próxima iteração.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Atenção: Este bloco só pode ser usado dentro de um laço.", "CONTROLS_IF_TOOLTIP_1": "Se um valor for verdade<PERSON>, então realize algumas instruções.", "CONTROLS_IF_TOOLTIP_2": "Se um valor for verda<PERSON><PERSON>, então realize o primeiro bloco de instruções. <PERSON>ão, realize o segundo bloco de instruções.", "CONTROLS_IF_TOOLTIP_3": "Se o primeiro valor é verdadeiro, então realize o primeiro bloco de instruções.  <PERSON>ão, se o segundo valor é verdadeiro, realize o segundo bloco de instruções.", "CONTROLS_IF_TOOLTIP_4": "Se o primeiro valor for verdade<PERSON>, então realize o primeiro bloco de instruções. <PERSON><PERSON>, se o segundo valor é verdadeiro, realize o segundo bloco de instruções. Se nenhum dos blocos for verdadeiro, realize o último bloco de instruções.", "CONTROLS_IF_MSG_IF": "se", "CONTROLS_IF_MSG_ELSEIF": "senão se", "CONTROLS_IF_MSG_ELSE": "senão", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remova ou reordene seções para reconfigurar este bloco.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Acrescente uma condição para o bloco se.", "CONTROLS_IF_ELSE_TOOLTIP": "Acrescente uma condição final para o bloco se.", "IOS_OK": "Ok", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "Erro", "IOS_PROCEDURES_INPUTS": "ENTRADAS", "IOS_PROCEDURES_ADD_INPUT": "+ Adicionar entrada", "IOS_PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON><PERSON>", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Essa função tem entradas duplicadas.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "Renomear", "IOS_VARIABLES_DELETE_BUTTON": "Deletar", "IOS_VARIABLES_VARIABLE_NAME": "<PERSON>me variavel", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Você não pode usar um nome de variável vazio.", "LOGIC_COMPARE_HELPURL": "https://pt.wikipedia.org/wiki/Inequa%C3%A7%C3%A3o", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON><PERSON> verda<PERSON>iro se ambas as entradas forem iguais.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON><PERSON> verdadeiro se ambas as entradas forem diferentes.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON><PERSON>na verdadeiro se a primeira entrada for menor que a segunda entrada.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON><PERSON>na verdadeiro se a primeira entrada for menor ou igual à segunda entrada.", "LOGIC_COMPARE_TOOLTIP_GT": "<PERSON><PERSON>na verdadeiro se a primeira entrada for maior que a segunda entrada.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON><PERSON>na verdadeiro se a primeira entrada for maior ou igual à segunda entrada.", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON><PERSON> verdade<PERSON> se ambas as entradas forem verdade<PERSON>.", "LOGIC_OPERATION_AND": "e", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON><PERSON><PERSON> verdadeiro se uma das estradas for verdadeira.", "LOGIC_OPERATION_OR": "ou", "LOGIC_NEGATE_TITLE": "não %1", "LOGIC_NEGATE_TOOLTIP": "Retorna verdadeiro se a entrada for falsa.  Retorna falsa se a entrada for verdadeira.", "LOGIC_BOOLEAN_TRUE": "<PERSON><PERSON><PERSON><PERSON>", "LOGIC_BOOLEAN_FALSE": "falso", "LOGIC_BOOLEAN_TOOLTIP": "<PERSON><PERSON><PERSON> verda<PERSON> ou falso.", "LOGIC_NULL": "nulo", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON><PERSON> nulo.", "LOGIC_TERNARY_CONDITION": "teste", "LOGIC_TERNARY_IF_TRUE": "se verdadeiro", "LOGIC_TERNARY_IF_FALSE": "se falso", "LOGIC_TERNARY_TOOLTIP": "Avalia a condição em \"teste\". Se a condição for verdadeira retorna o valor \"se verdadeiro\", senão retorna o valor \"se falso\".", "MATH_NUMBER_HELPURL": "https://pt.wikipedia.org/wiki/N%C3%BAmero", "MATH_NUMBER_TOOLTIP": "Um número.", "MATH_ARITHMETIC_HELPURL": "https://pt.wikipedia.org/wiki/Aritm%C3%A9tica", "MATH_ARITHMETIC_TOOLTIP_ADD": "Retorna a soma dos dois números.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Retorna a diferença entre os dois números.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Retorna o produto dos dois números.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Retorna o quociente da divisão dos dois números.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Retorna o primeiro número elevado à potência do segundo número.", "MATH_SINGLE_HELPURL": "https://pt.wikipedia.org/wiki/Raiz_quadrada", "MATH_SINGLE_OP_ROOT": "raiz quadrada", "MATH_SINGLE_TOOLTIP_ROOT": "Retorna a raiz quadrada de um número.", "MATH_SINGLE_OP_ABSOLUTE": "absoluto", "MATH_SINGLE_TOOLTIP_ABS": "Retorna o valor absoluto de um número.", "MATH_SINGLE_TOOLTIP_NEG": "Retorna o oposto de um número.", "MATH_SINGLE_TOOLTIP_LN": "Retorna o logaritmo natural de um número.", "MATH_SINGLE_TOOLTIP_LOG10": "Retorna o logaritmo em base 10 de um número.", "MATH_SINGLE_TOOLTIP_EXP": "Retorna o número e elevado à potência de um número.", "MATH_SINGLE_TOOLTIP_POW10": "Retorna 10 elevado à potência de um número.", "MATH_TRIG_HELPURL": "https://pt.wikipedia.org/wiki/Fun%C3%A7%C3%A3o_trigonom%C3%A9trica", "MATH_TRIG_TOOLTIP_SIN": "Retorna o seno de um grau (não radiano).", "MATH_TRIG_TOOLTIP_COS": "Retorna o cosseno de um grau (não radiano).", "MATH_TRIG_TOOLTIP_TAN": "Retorna a tangente de um grau (não radiano).", "MATH_TRIG_TOOLTIP_ASIN": "Retorna o arco seno de um número.", "MATH_TRIG_TOOLTIP_ACOS": "Retorna o arco cosseno de um número.", "MATH_TRIG_TOOLTIP_ATAN": "Retorna o arco tangente de um número.", "MATH_CONSTANT_HELPURL": "https://pt.wikipedia.org/wiki/Anexo:Lista_de_constantes_matem%C3%A1ticas", "MATH_CONSTANT_TOOLTIP": "Retorna uma das constantes comuns: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), ou ∞ (infinito).", "MATH_IS_EVEN": "é par", "MATH_IS_ODD": "<PERSON> ímpar", "MATH_IS_PRIME": "é primo", "MATH_IS_WHOLE": "é inteiro", "MATH_IS_POSITIVE": "é positivo", "MATH_IS_NEGATIVE": "é negativo", "MATH_IS_DIVISIBLE_BY": "é divisível por", "MATH_IS_TOOLTIP": "Verifica se um número é par, ímpar, inteiro, positivo, negativo, ou se é divisível por outro número.  Retorna verdadeiro ou falso.", "MATH_CHANGE_HELPURL": "https://pt.wikipedia.org/wiki/Adi%C3%A7%C3%A3o", "MATH_CHANGE_TITLE": "alterar %1 por %2", "MATH_CHANGE_TOOLTIP": "Soma um número à variável \"%1\".", "MATH_ROUND_HELPURL": "https://pt.wikipedia.org/wiki/Arredondamento", "MATH_ROUND_TOOLTIP": "Arredonda um número para cima ou para baixo.", "MATH_ROUND_OPERATOR_ROUND": "a<PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "arredonda para cima", "MATH_ROUND_OPERATOR_ROUNDDOWN": "arredonda para baixo", "MATH_ONLIST_OPERATOR_SUM": "soma de uma lista", "MATH_ONLIST_TOOLTIP_SUM": "Retorna a soma de todos os números na lista.", "MATH_ONLIST_OPERATOR_MIN": "menor da lista", "MATH_ONLIST_TOOLTIP_MIN": "Retorna o menor número da lista.", "MATH_ONLIST_OPERATOR_MAX": "ma<PERSON> da lista", "MATH_ONLIST_TOOLTIP_MAX": "Retorna o maior número da lista.", "MATH_ONLIST_OPERATOR_AVERAGE": "média da <PERSON>a", "MATH_ONLIST_TOOLTIP_AVERAGE": "Retorna a média aritmética dos números da lista.", "MATH_ONLIST_OPERATOR_MEDIAN": "mediana da lista", "MATH_ONLIST_TOOLTIP_MEDIAN": "Retorna a mediana dos números da lista.", "MATH_ONLIST_OPERATOR_MODE": "moda da lista", "MATH_ONLIST_TOOLTIP_MODE": "Retorna uma lista do(s) item(ns) mais comum(ns) da lista.", "MATH_ONLIST_OPERATOR_STD_DEV": "<PERSON><PERSON> da <PERSON>a", "MATH_ONLIST_TOOLTIP_STD_DEV": "Retorna o desvio padrão dos números da lista.", "MATH_ONLIST_OPERATOR_RANDOM": "item aleatório da lista", "MATH_ONLIST_TOOLTIP_RANDOM": "Retorna um elemento aleatório da lista.", "MATH_MODULO_HELPURL": "https://pt.wikipedia.org/wiki/Opera%C3%A7%C3%A3o_m%C3%B3dulo", "MATH_MODULO_TITLE": "resto da divisão de %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Retorna o resto da divisão de dois números.", "MATH_CONSTRAIN_TITLE": "restringe %1 inferior %2 superior %3", "MATH_CONSTRAIN_TOOLTIP": "Restringe um número entre os limites especificados (inclusivo).", "MATH_RANDOM_INT_HELPURL": "https://pt.wikipedia.org/wiki/Gerador_de_n%C3%BAmeros_pseudoaleat%C3%B3rios", "MATH_RANDOM_INT_TITLE": "inteiro aleatório entre %1 e %2", "MATH_RANDOM_INT_TOOLTIP": "Retorna um número inteiro entre os dois limites informados, inclusivo.", "MATH_RANDOM_FLOAT_HELPURL": "https://pt.wikipedia.org/wiki/Gerador_de_n%C3%BAmeros_pseudoaleat%C3%B3rios", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "fração aleatória", "MATH_RANDOM_FLOAT_TOOLTIP": "Retorna uma fração aleatória entre 0.0 (inclusivo) e 1.0 (exclusivo).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 de X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Retorne o arco tangente do ponto (X, Y) em graus de -180 a 180.", "TEXT_TEXT_HELPURL": "https://pt.wikipedia.org/wiki/<PERSON><PERSON>_de_caracteres", "TEXT_TEXT_TOOLTIP": "<PERSON>a letra, pala<PERSON>ra ou linha de texto.", "TEXT_JOIN_TITLE_CREATEWITH": "criar texto com", "TEXT_JOIN_TOOLTIP": "Criar um pedaço de texto juntando qualquer número de itens.", "TEXT_CREATE_JOIN_TITLE_JOIN": "unir", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remove ou reordena seções para reconfigurar este bloco de texto.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Acrescentar um item ao texto.", "TEXT_APPEND_TITLE": "para %1 anexar texto %2", "TEXT_APPEND_TOOLTIP": "Acrescentar um pedaço de texto à variável \"%1\".", "TEXT_LENGTH_TITLE": "tamanho de %1", "TEXT_LENGTH_TOOLTIP": "Retorna o número de letras (incluindo espaços) no texto fornecido.", "TEXT_ISEMPTY_TITLE": "%1 é vazio", "TEXT_ISEMPTY_TOOLTIP": "Retorna verdadeiro se o texto fornecido for vazio.", "TEXT_INDEXOF_TOOLTIP": "Retorna a posição da primeira/última ocorrência do primeiro texto no segundo texto.  Retorna %1 se o texto não for encontrado.", "TEXT_INDEXOF_TITLE": "no texto %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "encontre a primeira ocorrência do item", "TEXT_INDEXOF_OPERATOR_LAST": "encontre a última ocorrência do texto", "TEXT_CHARAT_TITLE": "no texto %1 %2", "TEXT_CHARAT_FROM_START": "obter letra nº", "TEXT_CHARAT_FROM_END": "obter letra # a partir do final", "TEXT_CHARAT_FIRST": "obter primeira letra", "TEXT_CHARAT_LAST": "obter última letra", "TEXT_CHARAT_RANDOM": "obter letra aleatória", "TEXT_CHARAT_TOOLTIP": "Retorna a letra na posição especificada.", "TEXT_GET_SUBSTRING_TOOLTIP": "Retorna o trecho de texto especificado.", "TEXT_GET_SUBSTRING_HELPURL": "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "no texto", "TEXT_GET_SUBSTRING_START_FROM_START": "obter trecho de letra nº", "TEXT_GET_SUBSTRING_START_FROM_END": "obter trecho de letra nº a partir do final", "TEXT_GET_SUBSTRING_START_FIRST": "obter trecho de primeira letra", "TEXT_GET_SUBSTRING_END_FROM_START": "até letra nº", "TEXT_GET_SUBSTRING_END_FROM_END": "até letra nº a partir do final", "TEXT_GET_SUBSTRING_END_LAST": "até última letra", "TEXT_CHANGECASE_HELPURL": "https://github.com/google/blockly/wiki/Text#adjusting-text-case", "TEXT_CHANGECASE_TOOLTIP": "Retorna uma cópia do texto em um formato diferente.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "para MAIÚSCULAS", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "para minúsculas", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "para Nomes Próprios", "TEXT_TRIM_HELPURL": "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces", "TEXT_TRIM_TOOLTIP": "Retorna uma cópia do texto com os espaços removidos de uma ou ambas extremidades.", "TEXT_TRIM_OPERATOR_BOTH": "remover espaços de ambos os lados de", "TEXT_TRIM_OPERATOR_LEFT": "remover espaços à esquerda de", "TEXT_TRIM_OPERATOR_RIGHT": "remover espaços à direita de", "TEXT_PRINT_HELPURL": "https://github.com/google/blockly/wiki/Text#printing-text", "TEXT_PRINT_TITLE": "imprime %1", "TEXT_PRINT_TOOLTIP": "Imprime o texto, número ou valor especificado.", "TEXT_PROMPT_HELPURL": "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user", "TEXT_PROMPT_TYPE_TEXT": "Pede um texto com uma mensagem", "TEXT_PROMPT_TYPE_NUMBER": "Pede um número com uma mensagem", "TEXT_PROMPT_TOOLTIP_NUMBER": "Pede ao usuário um número.", "TEXT_PROMPT_TOOLTIP_TEXT": "Pede ao usuário um texto.", "TEXT_COUNT_MESSAGE0": "Contar %1 em %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Calcule quantas vezes algum texto aparece centro de algum outro texto.", "TEXT_REPLACE_MESSAGE0": "substituir %1 por %2 em %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Texto#substituindo-substrings", "TEXT_REPLACE_TOOLTIP": "Substitua todas as ocorrências de algum texto dentro de algum outro texto.", "TEXT_REVERSE_MESSAGE0": "inverter %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Texto#invertendo-texto", "TEXT_REVERSE_TOOLTIP": "Inverter a ordem dos caracteres no texto.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "criar lista vazia", "LISTS_CREATE_EMPTY_TOOLTIP": "Retorna uma lista, de taman<PERSON> 0, contendo nenhum registro", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "Cria uma lista com a quantidade de itens informada.", "LISTS_CREATE_WITH_INPUT_WITH": "criar lista com", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, remove ou reordena seções para reconfigurar este bloco de lista.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Acrescenta um item à lista.", "LISTS_REPEAT_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_REPEAT_TOOLTIP": "Cria uma lista consistindo no valor informado repetido o número de vezes especificado.", "LISTS_REPEAT_TITLE": "criar lista com item %1 repetido %2 vezes", "LISTS_LENGTH_HELPURL": "https://github.com/google/blockly/wiki/Lists#length-of", "LISTS_LENGTH_TITLE": "tamanho de %1", "LISTS_LENGTH_TOOLTIP": "Retorna o tamanho de uma lista.", "LISTS_ISEMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#is-empty", "LISTS_ISEMPTY_TITLE": "%1 é vazia", "LISTS_ISEMPTY_TOOLTIP": "Retorna ao verdadeiro se a lista estiver vazia.", "LISTS_INLIST": "na lista", "LISTS_INDEX_OF_FIRST": "encontre a primeira ocorrência do item", "LISTS_INDEX_OF_LAST": "encontre a última ocorrência do item", "LISTS_INDEX_OF_TOOLTIP": "Retorna o índice da primeira/última ocorrência do item na lista.  Retorna %1 se o item não for encontrado.", "LISTS_GET_INDEX_GET": "obter", "LISTS_GET_INDEX_GET_REMOVE": "obter e remover", "LISTS_GET_INDEX_REMOVE": "remover", "LISTS_GET_INDEX_FROM_START": "nº", "LISTS_GET_INDEX_FROM_END": "nº a partir do final", "LISTS_GET_INDEX_FIRST": "primeiro", "LISTS_GET_INDEX_LAST": "último", "LISTS_GET_INDEX_RANDOM": "aleatório", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 é o primeiro item.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 é o último item.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Retorna o item da lista na posição especificada.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Retorna o primeiro item em uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Retorna o último item em uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Retorna um item aleatório de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Remove e retorna o item na posição especificada em uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Remove e retorna o primeiro item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Remove e retorna o último item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Remove e retorna um item aleatório de uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Remove o item na posição especificada em uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Remove o primeiro item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Remove o último item de uma lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Remove um item aleatório de uma lista.", "LISTS_SET_INDEX_SET": "definir", "LISTS_SET_INDEX_INSERT": "inserir em", "LISTS_SET_INDEX_INPUT_TO": "como", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Define o item da posição especificada de uma lista.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Define o primeiro item de uma lista.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Define o último item de uma lista.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Define um item aleatório de uma lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Insere o item na posição especificada em uma lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Insere o item no início de uma lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Insere o item no final de uma lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Insere o item em uma posição qualquer de uma lista.", "LISTS_GET_SUBLIST_HELPURL": "https://github.com/google/blockly/wiki/Lists#getting-a-sublist", "LISTS_GET_SUBLIST_START_FROM_START": "obtém sublista de nº", "LISTS_GET_SUBLIST_START_FROM_END": "obtém sublista de nº a partir do final", "LISTS_GET_SUBLIST_START_FIRST": "obtém sublista a partir do primeiro", "LISTS_GET_SUBLIST_END_FROM_START": "até nº", "LISTS_GET_SUBLIST_END_FROM_END": "até nº a partir do final", "LISTS_GET_SUBLIST_END_LAST": "até <PERSON>", "LISTS_GET_SUBLIST_TOOLTIP": "Cria uma cópia da porção especificada de uma lista.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "ordenar %1 %2 %3", "LISTS_SORT_TOOLTIP": "Ordenar uma cópia de uma lista.", "LISTS_SORT_ORDER_ASCENDING": "ascendente", "LISTS_SORT_ORDER_DESCENDING": "descendente", "LISTS_SORT_TYPE_NUMERIC": "numérico", "LISTS_SORT_TYPE_TEXT": "alfabético", "LISTS_SORT_TYPE_IGNORECASE": "alfab<PERSON><PERSON><PERSON>, ignorar ma<PERSON>/minúscula", "LISTS_SPLIT_HELPURL": "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists", "LISTS_SPLIT_LIST_FROM_TEXT": "Fazer uma lista a partir do texto", "LISTS_SPLIT_TEXT_FROM_LIST": "fazer um texto a partir da lista", "LISTS_SPLIT_WITH_DELIMITER": "com delimitador", "LISTS_SPLIT_TOOLTIP_SPLIT": "Dividir o texto em uma lista de textos, separando-o em cada delimitador.", "LISTS_SPLIT_TOOLTIP_JOIN": "Juntar uma lista de textos em um único texto, separado por um delimitador.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Listas#invertendo-uma-lista", "LISTS_REVERSE_MESSAGE0": "inverter %1", "LISTS_REVERSE_TOOLTIP": "Inverter uma cópia da lista.", "VARIABLES_GET_HELPURL": "https://github.com/google/blockly/wiki/Variables#get", "VARIABLES_GET_TOOLTIP": "Retorna o valor desta variável.", "VARIABLES_GET_CREATE_SET": "Criar \"definir %1\"", "VARIABLES_SET_HELPURL": "https://github.com/google/blockly/wiki/Variables#set", "VARIABLES_SET": "definir %1 para %2", "VARIABLES_SET_TOOLTIP": "Define esta variável para o valor da entrada.", "VARIABLES_SET_CREATE_GET": "Criar \"obter %1\"", "PROCEDURES_DEFNORETURN_HELPURL": "https://pt.wikipedia.org/wiki/M%C3%A9todo_(programa%C3%A7%C3%A3o)", "PROCEDURES_DEFNORETURN_TITLE": "para", "PROCEDURES_DEFNORETURN_PROCEDURE": "faça algo", "PROCEDURES_BEFORE_PARAMS": "com:", "PROCEDURES_CALL_BEFORE_PARAMS": "com:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Cria uma função que não tem retorno.", "PROCEDURES_DEFNORETURN_COMMENT": "Descreva esta função...", "PROCEDURES_DEFRETURN_HELPURL": "https://pt.wikipedia.org/wiki/M%C3%A9todo_(programa%C3%A7%C3%A3o)", "PROCEDURES_DEFRETURN_RETURN": "retorna", "PROCEDURES_DEFRETURN_TOOLTIP": "Cria uma função que possui um valor de retorno.", "PROCEDURES_ALLOW_STATEMENTS": "permitir declarações", "PROCEDURES_DEF_DUPLICATE_WARNING": "Atenção: Esta função tem parâmetros duplicados.", "PROCEDURES_CALLNORETURN_HELPURL": "https://pt.wikipedia.org/wiki/M%C3%A9todo_(programa%C3%A7%C3%A3o)", "PROCEDURES_CALLNORETURN_TOOLTIP": "Executa a função definida pelo usuário \"%1\".", "PROCEDURES_CALLRETURN_HELPURL": "https://pt.wikipedia.org/wiki/Sub-rotina", "PROCEDURES_CALLRETURN_TOOLTIP": "Executa a função definida pelo usuário \"%1\" e usa seu retorno.", "PROCEDURES_MUTATORCONTAINER_TITLE": "entradas", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, remove, ou reordena as entradas para esta função.", "PROCEDURES_MUTATORARG_TITLE": "nome da entrada:", "PROCEDURES_MUTATORARG_TOOLTIP": "Adiciona uma entrada para esta função", "PROCEDURES_HIGHLIGHT_DEF": "Destacar definição da função", "PROCEDURES_CREATE_DO": "Criar \"%1\"", "PROCEDURES_IFRETURN_TOOLTIP": "Se um valor é verdadeiro, então retorna um valor.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Atenção: Este bloco só pode ser utilizado dentro da definição de uma função.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Diz algo...", "WORKSPACE_ARIA_LABEL": "Espaço de trabalho do Blockly", "COLLAPSED_WARNINGS_WARNING": "Bloqueios recolhidos contêm avisos.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}