<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.teaching.mapper.TeachingCourseNotificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.teaching.entity.TeachingCourseNotification">
        <id column="id" property="id" />
        <result column="schedule_id" property="scheduleId" />
        <result column="notification_type" property="notificationType" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="sender" property="sender" />
        <result column="receivers" property="receivers" />
        <result column="announcement_id" property="announcementId" />
        <result column="send_time" property="sendTime" />
        <result column="is_sent" property="isSent" />
        <result column="remind_time" property="remindTime" />
        <result column="notify_teachers" property="notifyTeachers" />
        <result column="notify_students" property="notifyStudents" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 查询待发送的课程提醒通知 -->
    <select id="selectPendingReminders" resultMap="BaseResultMap">
        SELECT * FROM teaching_course_notification
        WHERE notification_type = #{notificationType}
        AND is_sent = 0
        AND remind_time &lt;= #{currentTime}
        ORDER BY remind_time ASC
    </select>

</mapper> 