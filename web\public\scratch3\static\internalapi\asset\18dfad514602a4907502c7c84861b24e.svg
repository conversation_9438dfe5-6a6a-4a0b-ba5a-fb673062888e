<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100.60353" height="260.52857" viewBox="0,0,100.60353,260.52857"><g transform="translate(-198.19823,-49.73572)"><g data-paper-data="{&quot;isPaintingLayer&quot;:true}" fill-rule="evenodd" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" style="mix-blend-mode: normal"><path d="M223.97887,111.03515c0.75814,1.81017 0.73539,3.85512 -0.06282,5.64781l-14.92547,34.98681c-1.99219,4.15518 -6.77344,2.63402 -6.77344,2.63402c-2.65359,-1.07282 -4.74938,-4.09914 -3.76922,-6.80521l11.33156,-36.02761c2.85281,-5.94055 10.13625,-4.33933 10.13625,-4.33933c1.83382,0.67997 3.305,2.09334 4.06314,3.90351z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#705347" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M261.26558,107.09961c0,0 7.33922,-1.34503 9.96891,4.71561l10.00078,36.4119c0.87656,2.7381 -1.33078,5.68436 -3.98437,6.6611c0,0 -4.83703,1.34503 -6.66984,-2.88221l-13.66641,-35.50721c-0.73262,-1.82171 -0.68035,-3.86712 0.14433,-5.64867c0.82469,-1.78155 2.34792,-3.13963 4.2066,-3.75054z" id="Path" fill="#705347" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><g id="Group-15" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M264.1423,107.84418c6.27141,1.37706 -12.27984,16.01227 -25.46016,16.01227c-13.18031,0 -29.78719,-15.45184 -23.46,-16.5727c7.92094,-1.39307 20.98172,-6.7812 24.99,-6.7812c5.34703,0.01601 16.05703,5.6123 23.93016,7.34163z" id="Path" fill="#705347"/><path d="M247.20074,106.81139c0.30281,2.39383 -5.41078,4.49144 -7.80141,4.49144c-1.93641,-0.02402 -6.63797,-1.44911 -6.93281,-3.37859l0.58172,-16.07632h14.06484z" id="Path" fill="#705347"/><path d="M232.6737,102.55213l0.37453,-10.72021h14.10469v4.80368c-5.30719,0.68052 -11.77781,3.51469 -14.47922,5.91653z" id="Path" fill="#49352c"/></g><g id="Group-12" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M261.21777,126.20225l3.825,33.80991h-49.73297l3.05203,-34.13816l-6.90891,-18.12589l9.85734,-2.33779c1.86469,4.45942 9.25172,7.78196 18.09703,7.78196c8.84531,0 16.24031,-3.32255 18.14484,-7.78196l9.80953,1.53718z" id="Path" fill="#3259e4"/><g id="Group-11"><path d="M221.31433,121.96701c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M254.02605,149.82836c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M248.74277,119.94946c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M242.08886,133.73601c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M228.67745,138.03532c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M259.60417,114.65741c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M228.67745,117.09928c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M219.4337,131.2221c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M220.37402,108.78891c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M256.20949,130.94187c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M239.35558,123.87247c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M220.8362,148.24314c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M238.4073,147.70673c0,-0.55228 0.44772,-1 1,-1c0.55228,0 1,0.44772 1,1c0,0.55228 -0.44772,1 -1,1c-0.55228,0 -1,-0.44772 -1,-1z" id="Oval" fill="#fff49a"/><path d="M243.47933,115.97041c0.20025,0.23656 0.1717,0.59142 -0.06375,0.79261c-0.23545,0.20119 -0.58866,0.17251 -0.78891,-0.06405c-0.41092,-0.52549 -1.10345,-0.73933 -1.73719,-0.53641c-0.62431,0.27139 -0.99701,0.9226 -0.91641,1.60123c0.10611,1.63332 -1.10287,3.05397 -2.72531,3.20245h-0.1275c-1.37284,-0.01066 -2.61681,-0.81477 -3.19547,-2.06558c-0.09529,-0.18554 -0.08002,-0.40891 0.03962,-0.57962c0.11964,-0.17071 0.32381,-0.26045 0.52979,-0.23287c0.20598,0.02758 0.37963,0.16792 0.45059,0.36415c0.4254,0.88153 1.32849,1.4278 2.30297,1.39307c0.99614,-0.16027 1.69508,-1.07316 1.59375,-2.08159c-0.08218,-1.163 0.59961,-2.24404 1.68141,-2.66604c1.07031,-0.3481 2.2436,-0.00177 2.95641,0.87267z" id="Path" fill="#ffffff"/><path d="M254.01922,137.21031c-0.14866,-0.00603 -0.28854,-0.07234 -0.38771,-0.18377c-0.41293,-0.52257 -1.1051,-0.73311 -1.73719,-0.5284c-0.62431,0.27139 -0.99701,0.9226 -0.91641,1.60123c0.11044,1.63477 -1.10078,3.05805 -2.72531,3.20245h-0.14344c-1.38542,0.00632 -2.64822,-0.79679 -3.23531,-2.05758c-0.10047,-0.27785 0.03114,-0.58632 0.3007,-0.70481c0.26957,-0.11849 0.58438,-0.00624 0.7193,0.25647c0.43084,0.87552 1.33137,1.41713 2.30297,1.38506c0.51254,-0.05735 0.97974,-0.32193 1.29396,-0.73278c0.31422,-0.41085 0.44836,-0.93254 0.37151,-1.44489c-0.07166,-1.14122 0.59336,-2.19991 1.64953,-2.62601c1.0767,-0.35361 2.25892,-0.00333 2.97234,0.88067c0.10133,0.11283 0.15191,0.26262 0.13985,0.41411c-0.01206,0.15149 -0.08571,0.29129 -0.2036,0.3865c-0.10779,0.10304 -0.25254,0.15779 -0.4012,0.15175z" id="Path" fill="#ffffff"/><path d="M261.7198,124.569l-0.37453,1.1689c-1.28768,-0.46186 -2.12052,-1.71861 -2.04797,-3.09037c0.08456,-0.67717 -0.2854,-1.32928 -0.90844,-1.60123c-0.63612,-0.20664 -1.333,0.00756 -1.74516,0.53641c-0.12491,0.16705 -0.33194,0.25043 -0.53711,0.21629c-0.20517,-0.03413 -0.37441,-0.1801 -0.43905,-0.37869c-0.06465,-0.19859 -0.01397,-0.41682 0.13148,-0.56616c0.71477,-0.87684 1.89336,-1.22062 2.96438,-0.86466c1.06707,0.4175 1.74567,1.47733 1.68141,2.62601c-0.06301,0.86577 0.45958,1.66645 1.275,1.9535z" id="Path" fill="#ffffff"/><path d="M229.47027,126.49848c-0.12954,0.15303 -0.33049,0.225 -0.52717,0.1888c-0.19667,-0.03619 -0.35919,-0.17506 -0.42633,-0.36428c-0.06714,-0.18922 -0.0287,-0.40006 0.10084,-0.55308c0.71659,-0.89497 1.91077,-1.24916 2.99625,-0.88868c1.05785,0.42408 1.72373,1.48415 1.64953,2.62601c-0.06018,0.50889 0.08708,1.02061 0.40828,1.41871c0.3212,0.39811 0.78903,0.64875 1.29703,0.6949c0.96903,0.04566 1.86945,-0.50156 2.27906,-1.38506c0.15779,-0.20117 0.43125,-0.26927 0.66429,-0.16542c0.23304,0.10385 0.36635,0.35322 0.32383,0.60576c-0.58817,1.2572 -1.84468,2.0614 -3.22734,2.06558h-0.14344c-1.62881,-0.14033 -2.84696,-1.56343 -2.74125,-3.20245c0.08456,-0.67717 -0.2854,-1.32928 -0.90844,-1.60123c-0.63612,-0.20664 -1.333,0.00756 -1.74516,0.53641z" id="Path" fill="#ffffff"/><path d="M216.52902,106.5552l1.24312,-0.33626c-0.23206,1.04323 -0.92007,1.92567 -1.87266,2.40184c-0.07036,0.03107 -0.14627,0.04742 -0.22312,0.04804c-0.22552,-0.00078 -0.42903,-0.13605 -0.51797,-0.34426c-0.05838,-0.13693 -0.0601,-0.29157 -0.00477,-0.42978c0.05532,-0.1382 0.16313,-0.24859 0.29962,-0.30679c0.45367,-0.22794 0.82853,-0.58782 1.07578,-1.03279z" id="Path" fill="#ffffff"/><path d="M245.34402,125.39363c-0.3543,-1.07604 -0.01213,-2.26016 0.86062,-2.97828c0.11328,-0.09962 0.26183,-0.14902 0.41189,-0.13696c0.15007,0.01206 0.28891,0.08455 0.38498,0.201c0.20216,0.23886 0.17364,0.59708 -0.06375,0.80061c-0.52304,0.41284 -0.73588,1.10862 -0.53391,1.74534c0.27217,0.6254 0.91885,0.99898 1.59375,0.92071c1.62427,-0.10229 3.03569,1.11014 3.1875,2.7381c0.05602,1.44383 -0.75385,2.78104 -2.05594,3.3946c-0.07006,0.03198 -0.14617,0.04837 -0.22313,0.04804c-0.22391,0.00028 -0.42563,-0.13588 -0.51,-0.34426c-0.11794,-0.28307 0.00904,-0.60909 0.28688,-0.73656c0.88496,-0.41375 1.43068,-1.32649 1.37859,-2.30577c-0.05381,-0.50406 -0.30697,-0.96525 -0.70259,-1.27991c-0.39562,-0.31467 -0.90049,-0.45641 -1.40116,-0.39337c-1.13993,0.06955 -2.19492,-0.60584 -2.61375,-1.67328z" id="Path" fill="#ffffff"/><path d="M228.05183,111.09468c0.33469,0.1361 0.67734,0.27221 1.03594,0.40031c-0.49722,1.20492 -1.70035,1.96041 -2.99625,1.88144c-0.67401,-0.08496 -1.32307,0.28674 -1.59375,0.9127c-0.20923,0.63577 -0.00261,1.3352 0.51797,1.75334c0.17679,0.15325 0.23988,0.40091 0.15808,0.62064c-0.08179,0.21973 -0.29115,0.36503 -0.52465,0.36411c-0.13246,0.00237 -0.26078,-0.04633 -0.35859,-0.1361c-0.87275,-0.71812 -1.21492,-1.90225 -0.86062,-2.97828c0.42349,-1.06261 1.4765,-1.7335 2.61375,-1.66528c0.84829,0.09827 1.66172,-0.36873 2.00812,-1.15288z" id="Path" fill="#ffffff"/><path d="M239.28777,139.29228c-0.13557,1.63856 -1.55467,2.86471 -3.1875,2.75411c-0.67545,-0.08098 -1.32362,0.29346 -1.59375,0.92071c-0.20375,0.63505 0.00581,1.33047 0.52594,1.74534c0.18209,0.15089 0.24994,0.4006 0.16942,0.62353c-0.08052,0.22292 -0.29198,0.37079 -0.52802,0.36924c-0.13392,-0.00282 -0.26301,-0.05075 -0.36656,-0.1361c-0.87017,-0.72333 -1.21151,-1.90775 -0.86062,-2.98629c0.44421,-1.03741 1.4924,-1.67955 2.61375,-1.60123c0.50865,0.06282 1.02095,-0.08384 1.42024,-0.40658c0.3993,-0.32274 0.65159,-0.7941 0.69944,-1.30673c0.02964,-0.9715 -0.50969,-1.87043 -1.37859,-2.29776c-0.26148,-0.13555 -0.3732,-0.45184 -0.25527,-0.72267c0.11793,-0.27083 0.42497,-0.40306 0.70152,-0.30211c1.28241,0.6081 2.08366,1.92252 2.04,3.34656z" id="Path" fill="#ffffff"/><path d="M216.26605,116.25063c0.00401,0.63517 -0.16106,1.25986 -0.47813,1.80939l-0.62953,-1.94549c-0.0283,-1.09803 0.62659,-2.09774 1.64156,-2.50592c1.07332,-0.36205 2.25761,-0.01754 2.97234,0.86466c0.20025,0.23656 0.1717,0.59142 -0.06375,0.79261c-0.23545,0.20119 -0.58866,0.17251 -0.78891,-0.06405c-0.40583,-0.51226 -1.08118,-0.7247 -1.70531,-0.53641c-0.6307,0.25902 -1.01659,0.9041 -0.94828,1.58521z" id="Path" fill="#ffffff"/><path d="M216.34574,148.14707l0.11156,-1.20893c0.775,-0.33776 1.24263,-1.14131 1.15547,-1.98552c-0.0654,-1.14194 0.60151,-2.19854 1.6575,-2.62601c1.07101,-0.35596 2.2496,-0.01218 2.96437,0.86466c0.14545,0.14934 0.19612,0.36757 0.13148,0.56616c-0.06465,0.19859 -0.23388,0.34456 -0.43905,0.37869c-0.20517,0.03413 -0.4122,-0.04924 -0.53711,-0.21629c-0.41216,-0.52885 -1.10903,-0.74305 -1.74516,-0.53641c-0.62118,0.27402 -0.99028,0.9246 -0.90844,1.60123c0.09565,1.50442 -0.92249,2.85126 -2.39062,3.16242z" id="Path" fill="#ffffff"/><path d="M258.36612,146.47213c0.32018,0.40022 0.7887,0.65228 1.29774,0.69817c0.97187,0.04899 1.87631,-0.49876 2.28703,-1.38506c0.12464,-0.25485 0.41544,-0.38054 0.68531,-0.29623c0.27784,0.12747 0.40482,0.45349 0.28688,0.73656c-0.58195,1.24426 -1.81928,2.04608 -3.1875,2.06558h-0.13547c-1.632,-0.13623 -2.85475,-1.56057 -2.74922,-3.20245c0.08456,-0.67717 -0.2854,-1.32928 -0.90844,-1.60123c-0.63374,-0.20292 -1.32627,0.01092 -1.73719,0.53641c-0.20258,0.2385 -0.55913,0.26716 -0.79687,0.06405c-0.11591,-0.09652 -0.18806,-0.23602 -0.20007,-0.38679c-0.01201,-0.15077 0.03716,-0.30002 0.13632,-0.41383c0.71477,-0.87684 1.89336,-1.22062 2.96437,-0.86466c1.05764,0.42547 1.72541,1.48342 1.6575,2.62601c-0.06485,0.50933 0.07942,1.02323 0.39961,1.42345z" id="Path" fill="#ffffff"/><path d="M235.70183,151.11734c-0.58195,1.24426 -1.81928,2.04608 -3.1875,2.06558h-0.13547c-1.632,-0.13623 -2.85475,-1.56057 -2.74922,-3.20245c0.08456,-0.67717 -0.2854,-1.32928 -0.90844,-1.60123c-0.63208,-0.20471 -1.32426,0.00583 -1.73719,0.5284c-0.20253,0.21827 -0.53978,0.23882 -0.76707,0.04673c-0.2273,-0.19208 -0.26492,-0.52942 -0.08558,-0.76729c0.71788,-0.89322 1.91417,-1.24165 2.99625,-0.87267c1.05931,0.42346 1.72794,1.48277 1.6575,2.62601c-0.06036,0.50511 0.0837,1.01342 0.39984,1.41082c0.31614,0.3974 0.77788,0.65062 1.28157,0.7028c0.95765,0.0325 1.8421,-0.51263 2.24719,-1.38506c0.127,-0.28354 0.45718,-0.41195 0.74109,-0.28822c0.26265,0.14177 0.37072,0.464 0.24703,0.73656z" id="Path" fill="#ffffff"/><path d="M263.2737,106.92348c-0.02323,-0.19925 -0.07699,-0.3937 -0.15938,-0.57644l1.22719,0.20816c0.04205,0.10688 0.07667,0.21656 0.10359,0.32825c0.05818,1.44439 -0.75237,2.7827 -2.05594,3.3946c-0.06754,0.0311 -0.14087,0.04747 -0.21516,0.04804c-0.22363,0.00061 -0.42663,-0.13118 -0.51797,-0.33626c-0.05838,-0.13693 -0.0601,-0.29157 -0.00477,-0.42978c0.05532,-0.1382 0.16313,-0.24859 0.29962,-0.30679c0.85374,-0.45272 1.36946,-1.36103 1.32281,-2.32979z" id="Path" fill="#ffffff"/><path d="M251.43214,111.06265c-0.16711,0.01254 -0.33492,0.01254 -0.50203,0c0.59868,-0.255 1.18384,-0.54093 1.75313,-0.85666c1.09425,0.37353 1.8681,1.35792 1.97625,2.51393c0.05818,1.44439 -0.75237,2.7827 -2.05594,3.3946c-0.06754,0.0311 -0.14087,0.04747 -0.21516,0.04804c-0.23942,0.00286 -0.45543,-0.14395 -0.54188,-0.36828c-0.05838,-0.13693 -0.0601,-0.29157 -0.00477,-0.42978c0.05532,-0.1382 0.16313,-0.24859 0.29962,-0.30679c0.87286,-0.4313 1.41255,-1.33709 1.37859,-2.31377c-0.12091,-1.03992 -1.05125,-1.78912 -2.08781,-1.68129z" id="Path" fill="#ffffff"/><path d="M221.98761,136.69829c-0.20197,0.63671 0.01087,1.33249 0.53391,1.74534c0.23739,0.20353 0.26591,0.56175 0.06375,0.80061c-0.10596,0.12862 -0.26414,0.2022 -0.43031,0.20015c-0.13246,0.00237 -0.26078,-0.04633 -0.35859,-0.1361c-0.87275,-0.71812 -1.21492,-1.90225 -0.86062,-2.97828c0.42548,-1.06094 1.47715,-1.73098 2.61375,-1.66528c0.50362,0.0607 1.01045,-0.08381 1.40726,-0.40124c0.39681,-0.31743 0.65052,-0.78133 0.70446,-1.28805c0.01845,-0.97137 -0.53435,-1.86251 -1.41047,-2.27374c-0.13565,-0.06113 -0.24139,-0.17414 -0.29376,-0.31394c-0.05237,-0.1398 -0.04702,-0.29481 0.01485,-0.43063c0.05778,-0.13512 0.16681,-0.24152 0.30292,-0.29562c0.13611,-0.0541 0.28808,-0.05144 0.42223,0.0074c1.29808,0.60961 2.10759,1.93991 2.05594,3.37859c-0.13968,1.63757 -1.55474,2.86378 -3.1875,2.76212c-0.66258,-0.08128 -1.30113,0.27838 -1.57781,0.88868z" id="Path" fill="#ffffff"/><path d="M262.15808,134.68873l0.11953,1.11285h-0.07172c-0.67347,-0.08223 -1.32101,0.2886 -1.59375,0.9127c-0.20197,0.63671 0.01087,1.33249 0.53391,1.74534c0.11591,0.09652 0.18806,0.23602 0.20007,0.38679c0.01201,0.15077 -0.03716,0.30002 -0.13632,0.41383c-0.10419,0.12645 -0.25896,0.19979 -0.42234,0.20015c-0.13492,0.0028 -0.26589,-0.04582 -0.36656,-0.1361c-0.87275,-0.71812 -1.21492,-1.90225 -0.86063,-2.97828c0.41824,-1.05861 1.46632,-1.72723 2.59781,-1.65727z" id="Path" fill="#ffffff"/><path d="M250.10136,151.64575c0.05641,1.44371 -0.74921,2.78222 -2.04797,3.40261c-0.28613,0.11372 -0.61092,-0.0161 -0.74109,-0.29623c-0.05838,-0.13693 -0.0601,-0.29157 -0.00477,-0.42978c0.05532,-0.1382 0.16313,-0.24859 0.29962,-0.30679c0.87247,-0.43481 1.40923,-1.34407 1.37062,-2.32178c-0.12469,-1.04711 -1.06913,-1.79547 -2.11172,-1.67328c-1.13589,0.07199 -2.18963,-0.59614 -2.61375,-1.65727c-0.35323,-1.0785 -0.0115,-2.26425 0.86062,-2.98629c0.23775,-0.20311 0.59429,-0.17445 0.79688,0.06405c0.09916,0.11381 0.14832,0.26305 0.13632,0.41383c-0.01201,0.15077 -0.08416,0.29027 -0.20007,0.38679c-0.51661,0.42077 -0.72776,1.11419 -0.53391,1.75334c0.27274,0.6241 0.92028,0.99493 1.59375,0.9127c1.62869,-0.11115 3.04767,1.10473 3.19547,2.7381z" id="Path" fill="#ffffff"/><path d="M260.60417,105.93072c0.04102,0.10459 0.06262,0.21584 0.06375,0.32825c0,0.52176 -0.42099,0.94472 -0.94031,0.94472c-0.51932,0 -0.94031,-0.42297 -0.94031,-0.94472c0.00229,-0.21778 0.08135,-0.4277 0.22313,-0.59245z" id="Path" fill="#fff49a"/></g></g><path d="M225.90027,239.72925c0,0 -10.51875,-1.07282 -10.51875,-6.14071l-7.61812,-39.76647c0.96422,-9.68742 16.28016,-9.47926 16.28016,-9.47926c5.04422,0 16.32,1.15288 16.32,6.21276l-4.56609,43.04098c0.03188,5.05988 -4.86094,6.1327 -9.89719,6.1327z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#00bcbd" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M254.77902,229.1051l-14.18437,-38.71767c-1.25906,-4.89975 8.415,-11.45678 13.29984,-12.72175c0,0 12.91734,-3.09837 19.21266,7.83l2.54203,41.95215c-0.23109,7.92607 -8.45484,8.50252 -8.45484,8.50252c-4.88484,1.25696 -11.12438,-1.94549 -12.41531,-6.84525z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#00bcbd" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><g id="Group-16" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M273.10714,185.49569c0.39844,4.4354 -26.9025,5.09991 -32.72766,5.05187c-6.09609,0 -26.56781,5.07589 -32.60016,3.2665c-1.29094,-12.23337 2.62172,-29.14233 7.905,-38.94985c-0.2495,-1.49173 -0.16512,-3.02057 0.24703,-4.47543h48.41016c0.15141,1.32902 0.45422,4.61153 0.45422,4.61153c5.81719,11.60089 7.89703,25.78776 8.31141,30.49537z" id="Path" fill="#00bcbd"/><path d="M220.51339,154.64805v13.68248c-0.01749,5.07762 -4.11015,9.18949 -9.16406,9.20706h-2.805c0.04781,-0.24819 0.08766,-0.48837 0.13547,-0.72856h1.99219c5.05572,-0.0132 9.15093,-4.12762 9.16406,-9.20706v-13.68248c0,-0.2642 0,-0.53641 0,-0.80061h0.54188c0.08568,0.50528 0.13099,1.01663 0.13547,1.52917z" id="Path" fill="#269b9e"/><path d="M259.12995,153.11888h0.54188c0,0.2642 0,0.53641 0,0.80061v13.68248c0.01314,5.07944 4.10834,9.19386 9.16406,9.20706h2.89266c0.04781,0.24018 0.08766,0.48037 0.13547,0.72856h-3.66562c-5.06012,-0.0132 -9.15889,-4.1312 -9.17203,-9.21506v-13.67448c-0.01001,-0.51179 0.02465,-1.02347 0.10359,-1.52917z" id="Path" fill="#269b9e"/><path d="M242.12464,181.31649v0.62448h-2.55797c-0.84188,-0.00441 -1.52204,-0.69133 -1.52203,-1.53718v-27.34095h0.40641l0.45422,27.1488c0,0.44217 0.35677,0.80061 0.79688,0.80061z" id="Path" fill="#269b9e"/><path d="M242.13261,180.0355l-0.46219,-3.07436v-23.84227h0.46219z" id="Path" fill="#269b9e"/></g><path d="M265.43324,155.48069h-50.32266l0.32672,-2.99429h49.56562z" id="Path" fill="#fff49a" stroke="#f8b900" stroke-width="0.768" stroke-linecap="round" stroke-linejoin="round"/><path d="M221.97167,74.3225c0.69223,-0.36819 1.50842,-0.42032 2.24149,-0.14318c0.73307,0.27715 1.31257,0.85694 1.59148,1.59229l1.08375,3.56273c0.51,1.76135 0.43031,2.67405 -0.07172,3.07436c-1.21125,0.72055 -2.9325,0.07206 -3.83297,-1.44911l-1.53797,-2.59399c-0.93234,-1.48914 -0.67734,-3.32255 0.52594,-4.0431z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#49352c" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M258.66968,78.6156l-1.53797,2.59399c-0.90047,1.52117 -2.62172,2.16966 -3.83297,1.44911c-0.50203,-0.40031 -0.58172,-1.31301 -0.07172,-3.07436l1.08375,-3.56273c0.27891,-0.73535 0.85841,-1.31514 1.59148,-1.59229c0.73307,-0.27714 1.54926,-0.22501 2.24149,0.14318c1.20328,0.72055 1.45828,2.55396 0.52594,4.0431z" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" id="Path" fill="#49352c" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M241.39949,96.19526h-2.47031c-2.87672,0 -11.73,-5.7404 -12.83766,-12.37748l-2.03203,-13.38626c0,-6.80521 9.12422,-12.37748 15.89766,-12.37748h0.41438c6.77344,0 15.9375,5.60429 15.9375,12.37748l-2.09578,13.38626c-1.09172,6.06064 -9.45891,12.37748 -12.81375,12.37748z" id="Path" fill="#705347" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M237.31152,72.22488c0.00003,0.31526 -0.25202,0.57206 -0.56578,0.57644h-5.95266c-0.31687,0 -0.57375,-0.25808 -0.57375,-0.57644c0,-0.31836 0.25688,-0.57644 0.57375,-0.57644l5.98453,-0.27221c0.31078,0 0.53391,0.5284 0.53391,0.84865z" id="Path" fill="#412d20" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M249.78753,71.81351c0.09897,0.11902 0.14603,0.27308 0.13055,0.42739c-0.02952,0.31674 -0.30577,0.55158 -0.62156,0.5284l-5.98453,0.2562c-0.31405,-0.02554 -0.54885,-0.30075 -0.52594,-0.61647c0.03187,-0.32025 0.29484,-0.80061 0.60563,-0.80061h6.00047c0.15389,0.01214 0.29642,0.08607 0.39539,0.20509z" id="Path" fill="#412d20" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M241.67839,84.65842l-1.09172,0.54442c-0.5623,0.28029 -1.2227,0.28029 -1.785,0l-1.09969,-0.54442" id="Path" fill="none" stroke="#523a30" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/><path d="M225.66417,79.66259c-0.64547,-4.18721 -3.23531,-12.19334 -3.23531,-12.19334c-0.61769,-2.82527 0.04176,-5.78032 1.80094,-8.07018c-0.09563,-5.32408 3.98438,-5.90052 3.98438,-5.90052c7.02844,-7.06141 14.79797,-2.01755 14.79797,-2.01755c9.19594,-2.16166 11.22,3.83494 11.22,3.83494c10.12828,0 10.21594,11.2166 8.44688,13.05801c-1.76906,1.84141 -1.24313,5.82046 0.11156,9.23908c1.84078,4.64356 -2.20734,6.10067 -2.20734,6.10067c-1.76109,0.48837 -0.96422,3.1384 -0.96422,3.1384c0.26297,5.6123 -4.78125,4.85972 -4.78125,4.85972c-2.03203,0.16012 -1.23516,3.45865 -1.23516,3.45865c-2.35875,-3.20245 -0.33469,-4.80368 -0.33469,-4.80368c0.97994,-0.47316 1.44466,-1.61449 1.07578,-2.64202c-2.60578,-6.69313 0.79688,-13.6985 0.79688,-13.6985c2.39063,-7.91006 -1.52203,-7.5658 -1.52203,-7.5658c-3.63375,1.87344 -7.61812,-3.60276 -7.61812,-3.60276c-6.03234,-3.89098 -9.00469,1.4411 -9.00469,1.4411c-1.41047,3.45865 -6.01641,3.02632 -6.01641,3.02632c-5.57016,1.99353 -4.95656,8.44647 -4.95656,8.44647c0.37453,1.41709 -0.33469,3.86696 -0.35859,3.89098z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#412d20" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><g id="Group-3" fill="#00bcbd" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M229.63761,68.78225l-0.70125,0.86466l-4.45453,-3.14641l0.45422,-0.56844z" id="Path"/><path d="M228.37058,70.34345l-0.70125,0.87267l-4.07203,-3.69883l0.43031,-0.53641z" id="Path"/></g><g id="Group-20" fill="#705347" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M203.44433,142.94308c2.918,-0.13273 5.48614,1.91776 6.01641,4.80368l2.13563,33.39359c-0.06423,1.25675 -1.00681,2.29106 -2.24719,2.46589c-1.60354,0.25154 -3.12151,-0.8051 -3.45047,-2.40184l-7.69781,-32.21669c-0.09563,-2.85819 2.39063,-5.82847 5.24344,-6.04463z" id="Path"/><g id="Group-2"><path d="M208.45195,177.53198c1.31085,0.22274 2.35264,1.22848 2.62644,2.53555c0.90844,3.37058 -1.48219,8.0942 -1.48219,8.0942c-0.40636,0.84637 -1.03098,1.56791 -1.80891,2.0896c0.07884,1.23563 0.05218,2.47578 -0.07969,3.70684c1.19392,0.72485 2.32663,1.5469 3.38672,2.45788c2.36672,0.96874 1.46625,2.4819 1.46625,2.4819c-6.10406,-1.8254 -7.25156,-3.09837 -7.25156,-3.09837c-4.86094,-6.93331 -3.06,-9.93561 -3.06,-9.93561l2.89266,-6.80521c0.68639,-1.14391 1.99943,-1.74951 3.31028,-1.52677z" id="Path"/><path d="M211.74777,193.62191c-0.79687,0.16813 -2.11172,-1.33702 -2.31094,-2.45788l-2.04,-11.40874c0.43394,-1.02124 1.5081,-1.6105 2.59781,-1.42509c1.37745,1.14539 2.2059,2.82463 2.27906,4.61954l-0.39844,7.42969c-0.31694,0.9095 -0.24185,1.91061 0.20719,2.76212c0.13547,0.18414 -0.09563,0.43233 -0.33469,0.48037z" id="Path"/></g></g><g fill="#705347" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M270.31808,148.1871l14.74219,-29.6227c0.66599,-1.49244 2.38064,-2.19702 3.89672,-1.60123c1.15492,0.46982 1.82117,1.69142 1.59375,2.92224l-9.5625,32.02454c-1.17623,2.67966 -4.13278,4.08473 -6.94078,3.29853c-2.72531,-0.79261 -4.47047,-4.25126 -3.72938,-7.02138z" id="Path"/><g id="Group"><path d="M290.69417,114.61737l5.25141,-8.80675c0.31002,-0.51396 0.97208,-0.6847 1.49016,-0.38429c0.51053,0.3084 0.68074,0.97105 0.3825,1.48914l-4.62187,9.04693c-0.31875,0.5204 -1.4025,0.56043 -1.9125,0.2562c-0.57354,-0.30516 -0.8274,-0.99449 -0.58969,-1.60123z" id="Path"/><path d="M288.78964,111.97535l5.07609,-9.18304c0.31492,-0.56631 1.02056,-0.779 1.59375,-0.48037c0.56019,0.31919 0.77093,1.02494 0.47812,1.60123l-4.49437,9.48727c-0.31875,0.57644 -1.49813,0.49638 -2.07188,0.18414c-0.58375,-0.30031 -0.8376,-1.00255 -0.58172,-1.60923z" id="Path"/><path d="M291.02089,118.78857l6.13594,-8.18227c0.26839,-0.41477 0.81861,-0.53603 1.23516,-0.27221c0.41283,0.26965 0.53353,0.82245 0.27094,1.24095l-5.57812,8.64663c-0.27096,0.41197 -0.82067,0.52956 -1.23516,0.2642c-0.41438,-0.27221 -1.10766,-1.28098 -0.82875,-1.6973z" id="Path"/><path d="M286.55042,110.95857l4.36687,-8.53454c0.24886,-0.49243 0.84558,-0.69227 1.33875,-0.44834c0.4891,0.24681 0.68838,0.84386 0.44625,1.33702l-3.98438,8.75071c-0.24703,0.48837 -1.22719,0.47236 -1.71328,0.23218c-0.49211,-0.2439 -0.69521,-0.84173 -0.45422,-1.33702z" id="Path"/><path d="M282.86886,106.86744c0.79688,-0.16813 2.00016,1.20092 2.20734,2.24172l1.87266,9.45525c-0.38353,0.95966 -1.37255,1.52936 -2.39062,1.37706c-0.44625,-0.24018 -1.79297,-0.68052 -1.96031,-3.11439l0.22312,-6.92531c0.28025,-0.85353 0.19061,-1.78609 -0.24703,-2.56997c-0.11953,-0.17613 0.09562,-0.41632 0.29484,-0.46436z" id="Path"/><path d="M285.68183,121.8309c-2.53406,-1.38506 -3.90469,-4.80368 -2.52609,-7.38166l0.62156,-1.15288c1.39166,-2.54855 4.56885,-3.49224 7.11609,-2.11362l0.86859,0.47236c2.53665,1.39819 3.47593,4.59029 2.10375,7.14948l-0.62156,1.15288c-1.37859,2.54595 -4.99641,3.2585 -7.56234,1.87344z" id="Path"/></g></g><g><path d="M244.79417,88.14109c-0.37453,1.60123 -2.31891,3.21046 -4.70156,3.21046c-2.38266,0 -4.35891,-1.60123 -4.7175,-3.20245c-0.05578,-0.2562 0.11156,-0.50439 0.58969,-0.50439c2.69772,0.71534 5.534,0.71534 8.23172,0c0.37453,0 0.65344,0.24018 0.59766,0.49638z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#966a57" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M235.51855,87.86888c0.13303,-0.09857 0.29767,-0.1442 0.46219,-0.1281c2.69501,0.71539 5.52874,0.71539 8.22375,0c0.16458,-0.0051 0.3256,0.04883 0.45422,0.15212c-0.48609,0.94472 -2.33484,1.84942 -4.56609,1.84942c-2.23922,0 -4.09594,-0.89669 -4.57406,-1.87344z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#dbdbdb" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M244.79417,88.14109c-0.35063,1.60123 -2.32688,3.25049 -4.70156,3.25049c-2.37469,0 -4.35891,-1.64926 -4.7175,-3.25049c0.00402,-0.14042 0.06437,-0.27325 0.16734,-0.36828c0.11212,-0.10878 0.26817,-0.15906 0.42234,-0.1361c1.33812,0.41921 2.7268,0.65354 4.12781,0.69653c1.39357,-0.05172 2.77389,-0.29138 4.10391,-0.71255c0.36656,0.02402 0.65344,0.2642 0.59766,0.5204z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="none" stroke="#523a30" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></g><g stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M249.27027,74.38742c0.07328,0.0859 0.10609,0.1994 0.08999,0.31137c-0.28719,1.44033 -1.54612,2.47692 -3.0082,2.47692c-1.46208,0 -2.72102,-1.03659 -3.0082,-2.47692c-0.01609,-0.11197 0.01671,-0.22547 0.08999,-0.31137c0.07328,-0.0859 0.17991,-0.13583 0.29251,-0.13697h5.25141c0.1126,0.00115 0.21922,0.05108 0.29251,0.13697z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#ffffff"/><path d="M245.09151,76.48965c-0.33522,-0.33679 -0.52259,-0.79418 -0.52046,-1.27047c-0.00157,-0.34427 0.09824,-0.68132 0.28687,-0.96874h2.98031c0.3623,0.54689 0.39479,1.24997 0.08448,1.82824c-0.31031,0.57827 -0.91279,0.9374 -1.56667,0.93388c-0.47406,0.00214 -0.92932,-0.18612 -1.26454,-0.5229z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#472f1b"/></g><g stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M236.68762,74.38742c0.07328,0.0859 0.10609,0.1994 0.08999,0.31137c-0.28719,1.44033 -1.54612,2.47692 -3.0082,2.47692c-1.46208,0 -2.72102,-1.03659 -3.0082,-2.47692c-0.01609,-0.11197 0.01671,-0.22547 0.08999,-0.31137c0.07328,-0.0859 0.17991,-0.13583 0.29251,-0.13697h5.25141c0.1126,0.00115 0.21922,0.05108 0.29251,0.13697z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#ffffff"/><path d="M232.50885,76.48965c-0.33522,-0.33679 -0.52259,-0.79418 -0.52046,-1.27047c0.00209,-0.34374 0.1016,-0.67976 0.28688,-0.96874h2.98828c0.18512,0.29201 0.28454,0.63054 0.28687,0.97675c0,0.98603 -0.7956,1.78537 -1.77703,1.78537c-0.47406,0.00214 -0.92932,-0.18612 -1.26454,-0.5229z" data-paper-data="{&quot;origPos&quot;:null}" id="Path" fill="#472f1b"/></g><g><g id="Group-18" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M262.43699,287.15759c0.05823,1.1603 -0.38813,2.28878 -1.2232,3.09242c-0.83506,0.80364 -1.97643,1.20315 -3.12774,1.09479h-0.06375c-1.87266,-0.18414 -4.11984,-1.74534 -4.31906,-3.62678l0.79688,-56.19506c0.11156,-3.07436 6.98859,-5.39614 9.96891,-5.13193c2.98031,0.2642 9.21188,3.62678 8.89313,6.61307z" id="Path" fill="#705347"/><path d="M265.32964,274.53191c-0.52594,2.58598 -3.315,3.21847 -5.70563,2.97828c0,0 -5.50641,-0.76859 -6.05625,-3.90699l-0.20719,-48.83742c0.10359,-3.09037 5.60203,-7.3016 11.77781,-6.29282c2.92453,0.48037 11.64234,3.53071 10.35938,10.22383z" id="Path" fill="#00bcbd"/></g><g id="Group-8"><path d="M251.76683,294.89151c-0.31078,-0.29623 -0.42234,-0.72055 -0.42234,-1.67328c0,0 1.88859,-9.71945 1.86469,-10.9684c1.70456,0.78831 3.65496,0.84612 5.40281,0.16012c0.48609,-0.16012 0.66141,0.68853 0.66141,0.68853c1.51406,0.48037 11.0925,9.95163 12.05672,12.10528c0.499,0.24605 0.9026,0.65154 1.1475,1.15288c3.98438,0.37629 7.07625,2.60199 6.74953,4.80368v0.74457c0.04781,0.31224 -1.02,1.84942 -2.39062,2.36181c-0.74906,0.28021 -7.92094,1.39307 -10.53469,-1.31301c0,0 -1.87266,-2.20169 -3.33094,-2.81015c-0.65344,-0.27221 -9.76172,-4.51546 -10.28766,-4.73163c-0.32773,-0.13578 -0.63381,-0.31921 -0.90844,-0.54442z" id="Path" fill="#ffc724" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M269.45745,302.84961c6.63797,0.9127 8.34328,0.6485 9.76969,-0.9127c-0.35859,2.04957 -0.30281,3.21847 -3.88875,3.45064c-2.90062,0.18414 -10.04063,0.64049 -11.87344,-3.85896c-0.26297,-0.6565 -10.25578,-5.83647 -10.78172,-6.06064c-1.03594,-0.51239 -1.54594,-0.79261 -1.33875,-2.22571l0.36656,-2.40184l13.42734,8.19028c0.35063,3.31454 3.54609,3.71485 4.31906,3.81893z" id="Path" fill="#8967e3" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M268.54105,295.70813l-9.26766,-12.60966c1.11563,-1.96951 2.78109,-3.34656 4.51828,-2.23371c1.32281,0.84865 0.24703,3.93101 0.24703,3.93101c2.50219,2.89021 4.24734,5.86049 7.29141,10.40798z" id="Path" fill="#8967e3" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M265.95917,292.20145l1.32281,-3.12239l-4.76531,-1.5612l1.52203,-2.72209l-3.4425,-0.03202l5.00438,1.9615l-1.41047,3.06635l4.93266,2.06558l-0.58172,3.85095l2.78906,-0.50439z" id="Path" fill="none" stroke="#00bcbd" stroke-width="0.768" stroke-linecap="round" stroke-linejoin="round"/><path d="M273.90402,296.5728c0,0 4.66172,1.25696 3.98438,2.84218c0,0 -2.30297,3.84294 -10.25578,-1.06482c0,0 0.90844,-1.20092 3.1875,-0.45635c0,0 3.37078,0.72856 3.98438,0c-0.03188,0.00801 0.40641,-0.48037 -0.90047,-1.32101z" id="Path" fill="#fe7e5b" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M257.41667,284.9479l6.12797,7.91807c0,0 1.96031,3.42663 -0.92438,2.18567c-1.17503,-0.58429 -2.0118,-1.68549 -2.26313,-2.97828c0,0 -2.74922,-7.10945 -3.91266,-3.05034c0,0 -0.08766,3.04233 -1.44234,2.29776c0,0 -1.53,-0.28021 -1.23516,-4.5715c-0.00797,0 1.22719,-4.91577 3.64969,-1.80138z" id="Path" fill="#fe7e5b" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/></g></g><g><g id="Group-19" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"><path d="M235.06433,234.78946l-5.65781,56.04294c-0.37503,1.84654 -1.98122,3.1802 -3.85688,3.20245h-0.05578c-2.43047,0.02402 -5.07609,-2.14564 -4.74141,-4.54748l-4.60594,-54.92209c-0.05578,-3.0023 6.85312,-5.46018 9.84141,-5.46018c3.06,0 9.23578,2.57798 9.07641,5.68436z" id="Path" fill="#705347"/><path d="M236.3473,228.68879l-5.18766,48.10086c-0.43031,3.34656 -6.20766,3.24248 -6.20766,3.24248c-2.43047,0.02402 -5.9925,-2.05758 -6.26344,-4.54748l-4.35891,-47.38031c-0.04781,-3.0023 5.51438,-6.95733 11.62641,-6.95733c5.92875,0 10.55063,4.48344 10.39125,7.54178z" id="Path" fill="#00bcbd"/></g><g id="Group-9"><path d="M230.60183,300.19958c-0.26822,0.21131 -0.56294,0.38628 -0.87656,0.5204c-0.69942,0.28444 -1.35221,0.67309 -1.93641,1.15288c-0.58309,0.52302 -1.11662,1.09926 -1.59375,1.72132c-2.20734,2.59399 -1.23516,4.36334 -10.88531,6.12469c-1.99094,-0.04999 -3.9779,-0.20499 -5.95266,-0.46436c-0.90606,-0.38956 -1.50139,-1.27578 -1.52203,-2.26574v-1.47313c0.77297,-1.72132 2.76516,-4.90776 4.47047,-5.83647c1.35469,-3.41862 5.61797,-10.02368 7.7775,-13.02598c7.905,-0.81663 3.70547,3.64279 10.46297,0.72055c-0.01594,1.1769 0.44625,11.20859 0.44625,11.20859c0,0.9127 -0.11156,1.31301 -0.40641,1.60123z" id="Path" fill="#ffc724" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M224.31449,300.73598l6.55828,-5.54825l0.13547,3.41862c0,1.52117 -0.28688,1.64126 -1.29094,2.13764c-0.70551,0.28547 -1.36379,0.67689 -1.95234,1.16089c-0.58534,0.52364 -1.11906,1.10276 -1.59375,1.72933c-2.20734,2.58598 -1.59375,5.13994 -10.86141,6.47696c-2.1675,0.31224 -8.08031,0.49638 -7.50656,-3.11439c3.33094,3.47466 11.15625,0.88067 14.27203,-0.66451c1.50609,-0.74457 2.78109,-3.62678 2.23922,-5.59629z" id="Path" fill="#8967e3" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M213.28574,297.66164c0.92437,-2.51393 5.06016,-8.92684 6.80531,-11.05647c2.70937,-3.33856 5.51438,-1.48914 6.5025,1.60123l-6.53438,9.69543z" id="Path" fill="#8967e3" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M213.28574,297.65363l6.77344,0.2562l-5.28328,-3.2585l9.10031,-2.40184l-5.9925,-2.38583l8.31937,-1.07282l-7.06031,-0.80061l5.94469,2.44988l-8.48672,1.33702l5.57813,2.97828l-8.89313,2.89822" id="Path" fill="none" stroke="#00bcbd" stroke-width="0.768" stroke-linecap="round" stroke-linejoin="round"/><path d="M213.0387,301.99296c0.11156,2.20969 4.83703,-0.05604 4.83703,-0.05604c2.36672,-1.40908 3.54609,0 3.54609,0c-5.76937,6.47696 -11.95312,3.45865 -11.95312,3.45865c-1.13156,-1.19291 2.61375,-4.29129 2.61375,-4.29129c1.57781,-1.08883 0.93234,0.9127 0.95625,0.88868z" id="Path" fill="#fe7e5b" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M229.76511,291.37681c0.66938,3.42663 -0.53391,3.81092 -0.53391,3.81092c-1.02,0.74457 -1.4025,-1.70531 -1.4025,-1.70531c-1.34672,-3.16242 -2.84484,2.85819 -2.84484,2.85819c-0.08607,1.08627 -0.67665,2.06826 -1.59375,2.65003c-2.19938,1.29699 -0.96422,-1.67328 -0.96422,-1.67328l4.16766,-7.02138c1.64156,-2.80215 3.13969,1.08083 3.17156,1.08083z" id="Path" fill="#fe7e5b" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter"/></g></g></g></g></svg>