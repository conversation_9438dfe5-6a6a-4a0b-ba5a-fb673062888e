-- ----------------------------
-- Table structure for teaching_gift
-- ----------------------------
DROP TABLE IF EXISTS `teaching_gift`;
CREATE TABLE `teaching_gift` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `gift_name` varchar(100) NOT NULL COMMENT '礼物名称',
  `gift_img` varchar(255) NOT NULL COMMENT '礼物图片路径',
  `coin_count` int(11) NOT NULL COMMENT '所需金币数量',
  `gift_type` tinyint(1) DEFAULT '0' COMMENT '礼物类型：0-普通，1-热门，2-珍稀',
  `gift_order` int(11) DEFAULT '0' COMMENT '排序号',
  `gift_row` tinyint(1) DEFAULT '1' COMMENT '所在行号(1-5)',
  `gift_desc` varchar(255) DEFAULT NULL COMMENT '礼物描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_gift_type` (`gift_type`),
  KEY `idx_gift_row` (`gift_row`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='礼物配置表';

-- ----------------------------
-- Records of teaching_gift 初始数据
-- ----------------------------
INSERT INTO `teaching_gift` VALUES ('1', '陀螺', 'temp/陀螺.png', 50, 1, 0, 1, '经典玩具陀螺，可以旋转很长时间', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('2', '积木', 'temp/积木.png', 50, 0, 1, 1, '彩色积木套装，培养创造力', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('3', '拼图', 'temp/拼图.png', 50, 0, 2, 1, '100片拼图，提高专注力和观察力', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('4', '魔方', 'temp/魔方.png', 100, 0, 0, 2, '经典三阶魔方，锻炼逻辑思维', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('5', '棋盘', 'temp/棋盘.png', 100, 0, 1, 2, '多功能棋盘，含国际象棋和中国象棋', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('6', '跳绳', 'temp/跳绳.png', 100, 0, 2, 2, '可调节长度的跳绳，锻炼身体', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('7', '铅笔盒', 'temp/铅笔盒.png', 200, 0, 0, 3, '多功能铅笔盒，含尺子计算器等', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('8', '笔记本', 'temp/笔记本.png', 200, 0, 1, 3, 'A5大小的彩色笔记本，精美封面', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('9', '书签', 'temp/书签.png', 200, 0, 2, 3, '精美金属书签，多种款式', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('10', '科学实验盒', 'temp/科学实验盒.png', 500, 0, 0, 4, '儿童科学实验套装，激发科学兴趣', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('11', '水彩笔', 'temp/水彩笔.png', 500, 0, 1, 4, '24色水彩笔套装，丰富多彩', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('12', '画板', 'temp/画板.png', 500, 0, 2, 4, '儿童画板，磁性擦除，反复使用', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('13', '编程机器人', 'temp/编程机器人.png', 1000, 2, 0, 5, '儿童编程机器人，可编程控制，培养编程思维', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('14', '平板电脑', 'temp/平板电脑.png', 1000, 2, 1, 5, '学习专用平板电脑，预装教育应用', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00');
INSERT INTO `teaching_gift` VALUES ('15', '编程书籍', 'temp/编程书籍.png', 1000, 2, 2, 5, '少儿编程入门书籍，图文并茂', 1, 'admin', '2023-06-01 10:00:00', 'admin', '2023-06-01 10:00:00'); 