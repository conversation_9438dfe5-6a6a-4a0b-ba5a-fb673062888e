package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客观题答题记录
 */
@Data
@TableName("teaching_objective_quiz_record")
public class TeachingObjectiveQuizRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    @Excel(name = "课程ID", width = 15)
    private String courseId;
    
    @Excel(name = "单元ID", width = 15)
    private String unitId;
    
    @Excel(name = "用户ID", width = 15)
    private String userId;
    
    @Excel(name = "部门/班级ID", width = 15)
    private String departId;
    
    @Excel(name = "正确数量", width = 15)
    private Integer correctCount;
    
    @Excel(name = "题目总数", width = 15)
    private Integer totalCount;
    
    @Excel(name = "正确率", width = 15)
    private BigDecimal correctRate;
    
    @Excel(name = "答题时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date quizTime;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    private String createBy;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    private String updateBy;
} 