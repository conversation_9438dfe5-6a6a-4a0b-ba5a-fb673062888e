{"@metadata": {"authors": ["<PERSON><PERSON>", "Festina90", "JaapDeKleine", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "KlaasZ4usV", "Lemondoge", "Marcelhospers", "McDut<PERSON><PERSON>", "MedShot", "Optilete", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "아라"]}, "VARIABLES_DEFAULT_NAME": "item", "UNNAMED_KEY": "zonder naam", "TODAY": "Vandaag", "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "ADD_COMMENT": "Opmerking toevoegen", "REMOVE_COMMENT": "Opmerking verwijderen", "DUPLICATE_COMMENT": "Opmerking dupliceren", "EXTERNAL_INPUTS": "Externe invoer", "INLINE_INPUTS": "Inline invoer", "DELETE_BLOCK": "Blok verwijderen", "DELETE_X_BLOCKS": "%1 blokken verwijderen", "DELETE_ALL_BLOCKS": "Alle %1 blokken verwijderen?", "CLEAN_UP": "Blokken opschonen", "COLLAPSE_BLOCK": "Blok samenvouwen", "COLLAPSE_ALL": "Blokken samenvouwen", "EXPAND_BLOCK": "Blok uitvouwen", "EXPAND_ALL": "Blokken uitvouwen", "DISABLE_BLOCK": "Blok uitschakelen", "ENABLE_BLOCK": "Blok inschakelen", "HELP": "<PERSON><PERSON><PERSON>", "UNDO": "Ongedaan maken", "REDO": "Opnieuw", "CHANGE_VALUE_TITLE": "Waarde wijzigen:", "RENAME_VARIABLE": "Variabele hernoemen...", "RENAME_VARIABLE_TITLE": "Alle variabelen \"%1\" hernoemen naar:", "NEW_VARIABLE": "Variabele maken...", "NEW_STRING_VARIABLE": "<PERSON><PERSON><PERSON><PERSON>", "NEW_NUMBER_VARIABLE": "Creëer numeriek variabele", "NEW_COLOUR_VARIABLE": "<PERSON><PERSON><PERSON><PERSON>", "NEW_VARIABLE_TYPE_TITLE": "<PERSON><PERSON><PERSON> so<PERSON> variabele", "NEW_VARIABLE_TITLE": "Nieuwe variabelenaam:", "VARIABLE_ALREADY_EXISTS": "<PERSON><PERSON> bestaa<PERSON> al een variabele met de naam \"%1\".", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "<PERSON><PERSON> variabel<PERSON> met de naam '%1' bestaat al voor een ander soort variabele: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "%1 g<PERSON><PERSON><PERSON><PERSON> van de variabele \"%2\" verwijderen?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "De variabele \"%1\" kan niet verwijderd worden omdat die onderdeel uitmaakt van de definitie van de functie \"%2\"", "DELETE_VARIABLE": "Verwijder de variabele \"%1\"", "COLOUR_PICKER_HELPURL": "https://nl.wikipedia.org/wiki/<PERSON><PERSON>ur", "COLOUR_PICKER_TOOLTIP": "<PERSON>es een kleur in het palet.", "COLOUR_RANDOM_TITLE": "<PERSON><PERSON><PERSON><PERSON> kleur", "COLOUR_RANDOM_TOOLTIP": "<PERSON>es een willek<PERSON>ige kleur.", "COLOUR_RGB_HELPURL": "https://www.december.com/html/spec/colorpercompact.html", "COLOUR_RGB_TITLE": "<PERSON><PERSON><PERSON> met", "COLOUR_RGB_RED": "rood", "COLOUR_RGB_GREEN": "groen", "COLOUR_RGB_BLUE": "blauw", "COLOUR_RGB_TOOLTIP": "Maak een kleur met de opgegeven hoeve<PERSON>heid rood, groen en blauw.  Alle waarden moeten tussen 0 en 100 liggen.", "COLOUR_BLEND_HELPURL": "https://meyerweb.com/eric/tools/color-blend/#:::rgbp", "COLOUR_BLEND_TITLE": "mengen", "COLOUR_BLEND_COLOUR1": "kleur 1", "COLOUR_BLEND_COLOUR2": "kleur 2", "COLOUR_BLEND_RATIO": "verhouding", "COLOUR_BLEND_TOOLTIP": "<PERSON><PERSON> twee kleuren samen met een be<PERSON><PERSON><PERSON> verhou<PERSON> (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://nl.wikipedia.org/wiki/Repetitie_(informatica)#For_en_Foreach", "CONTROLS_REPEAT_TITLE": "%1 keer herhalen", "CONTROLS_REPEAT_INPUT_DO": "voer uit", "CONTROLS_REPEAT_TOOLTIP": "Voer een aantal opdrachten meerdere keren uit.", "CONTROLS_WHILEUNTIL_HELPURL": "https://github.com/google/blockly/wiki/Loops#repeat", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "<PERSON><PERSON>n zolang", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "<PERSON><PERSON>n to<PERSON>t", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "<PERSON><PERSON><PERSON><PERSON><PERSON> een waarde waar is de volgende opdrachten uitvoeren.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Ter<PERSON><PERSON><PERSON> een waarde onwaar is de volgende opdrachten uitvoeren.", "CONTROLS_FOR_HELPURL": "https://github.com/google/blockly/wiki/Loops#count-with", "CONTROLS_FOR_TOOLTIP": "Laat de variabele \"%1\" de waarden aannemen van het beginnummer tot het laatste nummer, tell<PERSON><PERSON> met het opgegeven interval, en met uitvo<PERSON> van de opgegeven blokken.", "CONTROLS_FOR_TITLE": "rekenen met %1 van %2 tot %3 in stappen van %4", "CONTROLS_FOREACH_TITLE": "voor ieder item %1 in lijst %2", "CONTROLS_FOREACH_TOOLTIP": "Voor ieder item in een lijst, stel de variabele \"%1\" in op het item en voer daarna opdrachten uit.", "CONTROLS_FLOW_STATEMENTS_HELPURL": "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "uit lus breken", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "<PERSON><PERSON><PERSON> met de volgende iteratie van de lus", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "<PERSON>it de bovenliggende lus breken.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "De <PERSON> van deze lus <PERSON><PERSON>an en door<PERSON> met de volgende herhaling.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Waarschuwing: dit blok mag alleen gebruikt worden in een lus.", "CONTROLS_IF_HELPURL": "https://github.com/google/blockly/wiki/IfElse", "CONTROLS_IF_TOOLTIP_1": "Als een waarde waar is, voer dan opdrachten uit.", "CONTROLS_IF_TOOLTIP_2": "Als een waarde waar is, voert dan het eerste blok met opdrachten uit. Voer andere het tweede blok met opdrachten uit.", "CONTROLS_IF_TOOLTIP_3": "Als de eerste waarde waar is, voer dan het eerste blok met opdrachten uit. <PERSON><PERSON><PERSON> and<PERSON>, als de tweede waarde waar is, het tweede blok met opdrachten uit.", "CONTROLS_IF_TOOLTIP_4": "Als de eerste waarde \"waar\" is, voer dan het eerste blok uit. Voer anders wanneer de tweede waarde \"waar\" is, het tweede blok uit. Als geen van beide waarden waar zijn, voer dan het laatste blok uit.", "CONTROLS_IF_MSG_IF": "als", "CONTROLS_IF_MSG_ELSEIF": "anders als", "CONTROLS_IF_MSG_ELSE": "anders", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON> stuk<PERSON>, ver<PERSON><PERSON><PERSON> of wijzig de volgorde om dit \"als\"-blok te wijzigen.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Voeg een voorwaarde toe aan het als-blok.", "CONTROLS_IF_ELSE_TOOLTIP": "Voeg een la<PERSON>, vang-alles conditie toe aan het als-statement.", "IOS_OK": "OK", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "Fout", "IOS_PROCEDURES_INPUTS": "INVOER", "IOS_PROCEDURES_ADD_INPUT": "+ Invoer toevoegen", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Statements toestaan", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Deze functie heeft dubbele invoeren.", "IOS_VARIABLES_ADD_VARIABLE": "+ Varia<PERSON><PERSON>n", "IOS_VARIABLES_ADD_BUTTON": "Toevoegen", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "Verwijderen", "IOS_VARIABLES_VARIABLE_NAME": "Variabelenaam", "IOS_VARIABLES_EMPTY_NAME_ERROR": "U kunt geen lege variabelenaam geb<PERSON>iken.", "LOGIC_COMPARE_HELPURL": "https://nl.wikipedia.org/wiki/Ongelijkheid_(wiskunde)", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON>ft \"waar\", als beide waarden gelijk aan el<PERSON>ar zijn.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON>ft \"waar\" terug als de waarden niet gelijk zijn aan el<PERSON>ar.", "LOGIC_COMPARE_TOOLTIP_LT": "Geeft \"waar\" als de eerste invoer kleiner is dan de tweede invoer.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON><PERSON> \"waar\" terug als de eerste invoer kleiner of gelijk is aan de tweede invoer.", "LOGIC_COMPARE_TOOLTIP_GT": "Geeft \"waar\" terug als de eerste invoer meer is dan de tweede invoer.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON>ft \"waar\" terug als de eerste invoer groter is of gelijk aan de tweede invoer.", "LOGIC_OPERATION_HELPURL": "https://github.com/google/blockly/wiki/Logic#logical-operations", "LOGIC_OPERATION_TOOLTIP_AND": "Geeft waar als beide waarden waar zijn.", "LOGIC_OPERATION_AND": "en", "LOGIC_OPERATION_TOOLTIP_OR": "Geeft \"waar\" terug als in ieder geval één van de waarden waar is.", "LOGIC_OPERATION_OR": "of", "LOGIC_NEGATE_HELPURL": "https://github.com/google/blockly/wiki/Logic#not", "LOGIC_NEGATE_TITLE": "niet %1", "LOGIC_NEGATE_TOOLTIP": "Geeft \"waar\" terug als de invoer \"onwaar\" is. Geeft \"onwaar\" als de invoer \"waar\" is.", "LOGIC_BOOLEAN_HELPURL": "https://github.com/google/blockly/wiki/Logic#values", "LOGIC_BOOLEAN_TRUE": "waar", "LOGIC_BOOLEAN_FALSE": "on<PERSON><PERSON>", "LOGIC_BOOLEAN_TOOLTIP": "Geeft \"waar\" of \"onwaar\" terug.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "niets", "LOGIC_NULL_TOOLTIP": "Geeft niets terug.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "als waar", "LOGIC_TERNARY_IF_FALSE": "als onwaar", "LOGIC_TERNARY_TOOLTIP": "Test de voorwaarde in \"test\". Als de voorwaarde \"waar\" is, geef de waarde van \"als waar\" terug; geef and<PERSON> de waarde van \"als onwaar\" terug.", "MATH_NUMBER_HELPURL": "https://nl.wikipedia.org/wiki/Getal_%28wiskunde%29", "MATH_NUMBER_TOOLTIP": "<PERSON>en getal.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "asin", "MATH_TRIG_ACOS": "acos", "MATH_TRIG_ATAN": "arctan", "MATH_ARITHMETIC_HELPURL": "https://nl.wikipedia.org/wiki/Rekenen", "MATH_ARITHMETIC_TOOLTIP_ADD": "Geeft de som van 2 getallen.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Geeft het verschil van de twee getallen.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Geeft het product terug van de twee getallen.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "<PERSON><PERSON> de gedeelde waarde van twee getallen.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Geeft het eerste getal tot de macht van het tweede getal.", "MATH_SINGLE_HELPURL": "https://nl.wikipedia.org/wiki/Vierkantswortel", "MATH_SINGLE_OP_ROOT": "wortel", "MATH_SINGLE_TOOLTIP_ROOT": "<PERSON><PERSON> de wortel van een getal.", "MATH_SINGLE_OP_ABSOLUTE": "absoluut", "MATH_SINGLE_TOOLTIP_ABS": "Geeft de absolute waarde van een getal.", "MATH_SINGLE_TOOLTIP_NEG": "Geeft de negatief van een getal.", "MATH_SINGLE_TOOLTIP_LN": "Geeft het natuurlijk logaritme van een getal.", "MATH_SINGLE_TOOLTIP_LOG10": "Geeft het logaritme basis 10 van een getal.", "MATH_SINGLE_TOOLTIP_EXP": "Geeft e tot de macht van een getal.", "MATH_SINGLE_TOOLTIP_POW10": "Geeft 10 tot de macht van een getal.", "MATH_TRIG_HELPURL": "https://nl.wikipedia.org/wiki/Goniometrische_functie", "MATH_TRIG_TOOLTIP_SIN": "Geeft de sinus van een graad (geen radialen).", "MATH_TRIG_TOOLTIP_COS": "Geeft de cosinus van een graad (geen radialen).", "MATH_TRIG_TOOLTIP_TAN": "Geeft de tangens van een graad (geen radialen).", "MATH_TRIG_TOOLTIP_ASIN": "<PERSON><PERSON> de arcsinus van een getal.", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON> de arccosinus van een getal.", "MATH_TRIG_TOOLTIP_ATAN": "Geeft de arctangens van een getal.", "MATH_CONSTANT_HELPURL": "https://nl.wikipedia.org/wiki/Wiskundige_constante", "MATH_CONSTANT_TOOLTIP": "Geeft een van de vaak voorkomende constante waardes:  π (3.141…), e (2.718…), φ (1.618…), √2 (1.414…), √½ (0.707…), of ∞ (one<PERSON>ig).", "MATH_IS_EVEN": "is even", "MATH_IS_ODD": "is oneven", "MATH_IS_PRIME": "is priemgetal", "MATH_IS_WHOLE": "is geheel getal", "MATH_IS_POSITIVE": "is positief", "MATH_IS_NEGATIVE": "is negatief", "MATH_IS_DIVISIBLE_BY": "is de<PERSON><PERSON><PERSON> door", "MATH_IS_TOOLTIP": "Test of een getal even, oneven, een priemgetal, geheel, positief of negatief is, of deelbaar is door een bepaald getal. Geeft \"waar\" of \"onwaar\".", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "%1 wijzigen met %2", "MATH_CHANGE_TOOLTIP": "Voegt een getal toe aan variabele \"%1\".", "MATH_ROUND_HELPURL": "https://nl.wikipedia.org/wiki/Afronden", "MATH_ROUND_TOOLTIP": "<PERSON><PERSON> een getal af omhoog of naar beneden.", "MATH_ROUND_OPERATOR_ROUND": "afronden", "MATH_ROUND_OPERATOR_ROUNDUP": "omhoog afronden", "MATH_ROUND_OPERATOR_ROUNDDOWN": "naar beneden afronden", "MATH_ONLIST_OPERATOR_SUM": "som van lijst", "MATH_ONLIST_TOOLTIP_SUM": "<PERSON>ft de som van alle getallen in de lijst.", "MATH_ONLIST_OPERATOR_MIN": "laagste uit lijst", "MATH_ONLIST_TOOLTIP_MIN": "Geeft het kleinste getal uit een lijst.", "MATH_ONLIST_OPERATOR_MAX": "hoogste uit lijst", "MATH_ONLIST_TOOLTIP_MAX": "Geeft het grootste getal in een lijst.", "MATH_ONLIST_OPERATOR_AVERAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON> van lij<PERSON>", "MATH_ONLIST_TOOLTIP_AVERAGE": "Geeft het gemiddelde terug van de numerieke waardes in een lijst.", "MATH_ONLIST_OPERATOR_MEDIAN": "<PERSON><PERSON> van lij<PERSON>", "MATH_ONLIST_TOOLTIP_MEDIAN": "Geeft de mediaan in de lijst.", "MATH_ONLIST_OPERATOR_MODE": "modi van lijst", "MATH_ONLIST_TOOLTIP_MODE": "Geeft een lijst van de meest voorkomende onderdelen in de lijst.", "MATH_ONLIST_OPERATOR_STD_DEV": "standaardd<PERSON><PERSON><PERSON> van lij<PERSON>", "MATH_ONLIST_TOOLTIP_STD_DEV": "Geeft de standaardafwijking van de lijst.", "MATH_ONLIST_OPERATOR_RANDOM": "willekeurige item van lijst", "MATH_ONLIST_TOOLTIP_RANDOM": "Geeft een willekeurig item uit de lijst terug.", "MATH_MODULO_HELPURL": "https://nl.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_rekenen", "MATH_MODULO_TITLE": "restgetal van %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Geeft het restgetal van het resultaat van de deling van de twee getallen.", "MATH_CONSTRAIN_TITLE": "beperk %1 van minimaal %2 tot maximaal %3", "MATH_CONSTRAIN_TOOLTIP": "Beperk een getal tussen de twee opgegeven limieten (inclusief).", "MATH_RANDOM_INT_HELPURL": "https://nl.wikipedia.org/wiki/Toevalsgenerator", "MATH_RANDOM_INT_TITLE": "willek<PERSON><PERSON> geheel getal van %1 tot %2", "MATH_RANDOM_INT_TOOLTIP": "Geeft een willekeurig getal tussen de 2 opgegeven limieten in, inclusief.", "MATH_RANDOM_FLOAT_HELPURL": "https://nl.wikipedia.org/wiki/Toevalsgenerator", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "willek<PERSON><PERSON> fractie", "MATH_RANDOM_FLOAT_TOOLTIP": "Geeft een willekeurige fractie tussen 0.0 (inclusief) en 1.0 (exclusief).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 van X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "<PERSON><PERSON><PERSON> van punt (X, Y) terug in graden tussen -180 naar 180.", "TEXT_TEXT_HELPURL": "https://nl.wikipedia.org/wiki/String_%28informatica%29", "TEXT_TEXT_TOOLTIP": "Een letter, woord of een regel tekst.", "TEXT_JOIN_HELPURL": "https://github.com/google/blockly/wiki/Text#text-creation", "TEXT_JOIN_TITLE_CREATEWITH": "maak tekst met", "TEXT_JOIN_TOOLTIP": "Maakt een stuk tekst door één of meer items samen te voegen.", "TEXT_CREATE_JOIN_TITLE_JOIN": "samenvoegen", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, ver<PERSON><PERSON><PERSON><PERSON> of volgorde wijzigen van secties om dit tekstblok opnieuw in te stellen.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Voegt een item aan de tekst toe.", "TEXT_APPEND_HELPURL": "https://github.com/google/blockly/wiki/Text#text-modification", "TEXT_APPEND_TITLE": "voor%1 voeg tekst toe van %2", "TEXT_APPEND_TOOLTIP": "Voeg tekst toe aan de variabele \"%1\".", "TEXT_LENGTH_HELPURL": "https://github.com/google/blockly/wiki/Text#text-modification", "TEXT_LENGTH_TITLE": "lengte van %1", "TEXT_LENGTH_TOOLTIP": "Geeft het aantal tekens terug (inclusief spaties) in de opgegeven tekst.", "TEXT_ISEMPTY_HELPURL": "https://github.com/google/blockly/wiki/Text#checking-for-empty-text", "TEXT_ISEMPTY_TITLE": "%1 is leeg", "TEXT_ISEMPTY_TOOLTIP": "Geeft \"waar\" terug, als de opgegeven tekst leeg is.", "TEXT_INDEXOF_HELPURL": "https://github.com/google/blockly/wiki/Text#finding-text", "TEXT_INDEXOF_TOOLTIP": "Geeft de index terug van het eerste of laatste voorkomen van de eerste tekst in de tweede tekst. Geeft %1 terug als de tekst niet gevonden is.", "TEXT_INDEXOF_TITLE": "in tekst %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "zoek eerste voorkomen van tekst", "TEXT_INDEXOF_OPERATOR_LAST": "zoek het laatste voorkomen van tekst", "TEXT_CHARAT_HELPURL": "https://github.com/google/blockly/wiki/Text#extracting-text", "TEXT_CHARAT_TITLE": "in tekst %1 %2", "TEXT_CHARAT_FROM_START": "haal letter # op", "TEXT_CHARAT_FROM_END": "haal letter # op vanaf einde", "TEXT_CHARAT_FIRST": "haal eerste letter op", "TEXT_CHARAT_LAST": "haal laatste letter op", "TEXT_CHARAT_RANDOM": "haal willekeurige letter op", "TEXT_CHARAT_TOOLTIP": "Geeft de letter op de opgegeven positie terug.", "TEXT_GET_SUBSTRING_TOOLTIP": "Geeft het opgegeven onderdeel van de tekst terug.", "TEXT_GET_SUBSTRING_HELPURL": "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "in tekst", "TEXT_GET_SUBSTRING_START_FROM_START": "haal subtekst op vanaf letter #", "TEXT_GET_SUBSTRING_START_FROM_END": "haal subtekst op vanaf letter # vanaf einde", "TEXT_GET_SUBSTRING_START_FIRST": "haal subtekst op van eerste letter", "TEXT_GET_SUBSTRING_END_FROM_START": "naar letter #", "TEXT_GET_SUBSTRING_END_FROM_END": "van letter # tot einde", "TEXT_GET_SUBSTRING_END_LAST": "naar laatste letter", "TEXT_CHANGECASE_HELPURL": "https://github.com/google/blockly/wiki/Text#adjusting-text-case", "TEXT_CHANGECASE_TOOLTIP": "<PERSON><PERSON> een kop<PERSON> van <PERSON> met veranderde hoofdletters terug.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "naar HOOFDLETTERS", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "naar kleine letters", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "naar <PERSON><PERSON><PERSON>", "TEXT_TRIM_HELPURL": "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces", "TEXT_TRIM_TOOLTIP": "Geeft een kopie van <PERSON> met verwijder<PERSON> spat<PERSON> van <PERSON>én of beide kanten.", "TEXT_TRIM_OPERATOR_BOTH": "spaties van beide kanten afhalen van", "TEXT_TRIM_OPERATOR_LEFT": "spaties van de linkerkant verwijderen van", "TEXT_TRIM_OPERATOR_RIGHT": "spaties van de rechterkant verwijderen van", "TEXT_PRINT_HELPURL": "https://github.com/google/blockly/wiki/Text#printing-text", "TEXT_PRINT_TITLE": "tekst weergeven: %1", "TEXT_PRINT_TOOLTIP": "<PERSON><PERSON><PERSON> de op<PERSON>ven tekst, getal of een andere waarde af.", "TEXT_PROMPT_HELPURL": "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user", "TEXT_PROMPT_TYPE_TEXT": "vraagt om invoer met bericht", "TEXT_PROMPT_TYPE_NUMBER": "vra<PERSON><PERSON> de gebruiker om een getal met de tekst", "TEXT_PROMPT_TOOLTIP_NUMBER": "<PERSON><PERSON><PERSON><PERSON> de gebruiker om een getal in te voeren.", "TEXT_PROMPT_TOOLTIP_TEXT": "Vraagt de gebruiker om invoer.", "TEXT_COUNT_MESSAGE0": "%1 in %2 tellen", "TEXT_COUNT_TOOLTIP": "Tel hoe vaak bepaalde tekst voorkomt in andere tekst.", "TEXT_REPLACE_MESSAGE0": "vervang %1 door %2 in %3", "TEXT_REPLACE_TOOLTIP": "Vervang alle voorkomens van tekst in een andere tekst.", "TEXT_REVERSE_MESSAGE0": "%1 omkeren", "TEXT_REVERSE_TOOLTIP": "<PERSON><PERSON> de <PERSON> van de te<PERSON> in de tekst om.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "maak een lege lijst", "LISTS_CREATE_EMPTY_TOOLTIP": "Geeft een lijst terug met lengte 0, zonder items", "LISTS_CREATE_WITH_TOOLTIP": "Maak een lij<PERSON> met een will<PERSON><PERSON>ig aantal items.", "LISTS_CREATE_WITH_INPUT_WITH": "maak een lijst met", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lijst", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON>g stukken toe, verwijder ze of wijzig de volgorde om dit lijstblok aan te passen.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "<PERSON>oeg iets toe aan de lijst.", "LISTS_REPEAT_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_REPEAT_TOOLTIP": "Maakt een lijst die bestaat uit de opgegeven waarde, het opgegeven aantal keer herhaald.", "LISTS_REPEAT_TITLE": "<PERSON>ak lijst met item %1, %2 keer herhaald", "LISTS_LENGTH_HELPURL": "https://github.com/google/blockly/wiki/Lists#length-of", "LISTS_LENGTH_TITLE": "lengte van %1", "LISTS_LENGTH_TOOLTIP": "Geeft de lengte van een lijst terug.", "LISTS_ISEMPTY_TITLE": "%1 is leeg", "LISTS_ISEMPTY_TOOLTIP": "Geeft waar terug als de lijst leeg is.", "LISTS_INLIST": "in lijst", "LISTS_INDEX_OF_HELPURL": "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list", "LISTS_INDEX_OF_FIRST": "zoek eerste voorkomen van item", "LISTS_INDEX_OF_LAST": "zoek laatste voorkomen van item", "LISTS_INDEX_OF_TOOLTIP": "Geeft de index terug van het eerste of laatste voorkomen van een item in de lijst. Geeft %1 terug als het item niet is gevonden.", "LISTS_GET_INDEX_GET": "haal op", "LISTS_GET_INDEX_GET_REMOVE": "haal op en verwijder", "LISTS_GET_INDEX_REMOVE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# van einde", "LISTS_GET_INDEX_FIRST": "eerste", "LISTS_GET_INDEX_LAST": "laatste", "LISTS_GET_INDEX_RANDOM": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_INDEX_FROM_START_TOOLTIP": "Item %1 is het eerste item.", "LISTS_INDEX_FROM_END_TOOLTIP": "Item %1 is het laatste item.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Geeft het item op de opgegeven positie in een lijst.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Geeft het eerste item in een lijst terug.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Geeft het laatste item in een lijst terug.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Geeft een willekeurig item uit een lijst.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Geeft het item op de opgegeven positie in een lijst terug en verwijdert het.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Geeft het laatste item in een lijst terug en verwijdert het.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Geeft het laatste item uit een lijst terug en verwijdert het.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Geeft een willekeurig item in een lijst terug en verwijdert het.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Verwijdert het item op de opgegeven positie in een lijst.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Verwijdert het eerste item in een lijst.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Verwijdert het laatste item uit een lijst.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Verwijdert een willekeurig item uit een lijst.", "LISTS_SET_INDEX_HELPURL": "https://github.com/google/blockly/wiki/Lists#in-list--set", "LISTS_SET_INDEX_SET": "stel in", "LISTS_SET_INDEX_INSERT": "tussenvoegen op", "LISTS_SET_INDEX_INPUT_TO": "als", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Zet het item op de opgegeven positie in de lijst.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Stelt het eerste item in een lijst in.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Stelt het laatste item van een lijst in.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "<PERSON><PERSON>t een willekeurig item uit de lijst in.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Voegt het item op een opgegeven positie in een lijst in.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Voegt het item toe aan het begin van de lijst.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Voeg het item aan het einde van een lijst toe.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Voegt het item op een willekeurige positie in de lijst in.", "LISTS_GET_SUBLIST_HELPURL": "https://github.com/google/blockly/wiki/Lists#getting-a-sublist", "LISTS_GET_SUBLIST_START_FROM_START": "haal sublijst op vanaf positie", "LISTS_GET_SUBLIST_START_FROM_END": "haal sublijst op van positie vanaf einde", "LISTS_GET_SUBLIST_START_FIRST": "haal sublijst op vanaf e<PERSON>te", "LISTS_GET_SUBLIST_END_FROM_START": "naar item", "LISTS_GET_SUBLIST_END_FROM_END": "naar # vanaf einde", "LISTS_GET_SUBLIST_END_LAST": "naar laatste", "LISTS_GET_SUBLIST_TOOLTIP": "Maakt een kopie van het opgegeven de<PERSON> van de lijst.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sorteer %1 %2 %3", "LISTS_SORT_TOOLTIP": "So<PERSON><PERSON> een kopie van een lijst.", "LISTS_SORT_ORDER_ASCENDING": "oplopend", "LISTS_SORT_ORDER_DESCENDING": "aflopend", "LISTS_SORT_TYPE_NUMERIC": "numeriek", "LISTS_SORT_TYPE_TEXT": "alfabetisch", "LISTS_SORT_TYPE_IGNORECASE": "al<PERSON><PERSON><PERSON>, negeer hoofd-/kleine letters", "LISTS_SPLIT_LIST_FROM_TEXT": "lijst maken van te<PERSON>t", "LISTS_SPLIT_TEXT_FROM_LIST": "tekst maken van lijst", "LISTS_SPLIT_WITH_DELIMITER": "met scheidingsteken", "LISTS_SPLIT_TOOLTIP_SPLIT": "Tekst splitsen in een lijst van teksten op basis van een scheidingsteken.", "LISTS_SPLIT_TOOLTIP_JOIN": "<PERSON><PERSON><PERSON> van tekstdelen samenvoegen in één stuk tekst, waar<PERSON><PERSON> de tekstdelen gescheiden zijn door een scheidingsteken.", "LISTS_REVERSE_MESSAGE0": "%1 omkeren", "LISTS_REVERSE_TOOLTIP": "<PERSON><PERSON> een kopie van een lijst om.", "VARIABLES_GET_HELPURL": "https://github.com/google/blockly/wiki/Variables#get", "VARIABLES_GET_TOOLTIP": "<PERSON><PERSON> de waarde van deze variabele.", "VARIABLES_GET_CREATE_SET": "Maak \"verander %1\"", "VARIABLES_SET_HELPURL": "https://github.com/google/blockly/wiki/Variables#set", "VARIABLES_SET": "stel %1 in op %2", "VARIABLES_SET_TOOLTIP": "<PERSON><PERSON><PERSON> de waarde van de variabele naar de waarde van de in<PERSON>.", "VARIABLES_SET_CREATE_GET": "Maak 'opvragen van %1'", "PROCEDURES_DEFNORETURN_HELPURL": "https://nl.wikipedia.org/wiki/Subprogramma", "PROCEDURES_DEFNORETURN_TITLE": "om", "PROCEDURES_DEFNORETURN_PROCEDURE": "doe iets", "PROCEDURES_BEFORE_PARAMS": "met:", "PROCEDURES_CALL_BEFORE_PARAMS": "met:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Maakt een functie zonder uitvoer.", "PROCEDURES_DEFNORETURN_COMMENT": "Deze functie beschrijven...", "PROCEDURES_DEFRETURN_HELPURL": "https://nl.wikipedia.org/wiki/Subprogramma", "PROCEDURES_DEFRETURN_RETURN": "uitvoeren", "PROCEDURES_DEFRETURN_TOOLTIP": "<PERSON><PERSON>t een functie met een uit<PERSON>er.", "PROCEDURES_ALLOW_STATEMENTS": "statements <PERSON><PERSON><PERSON>", "PROCEDURES_DEF_DUPLICATE_WARNING": "Waarschuwing: deze functie heeft parameters met dezelfde naam.", "PROCEDURES_CALLNORETURN_HELPURL": "https://nl.wikipedia.org/wiki/Subprogramma", "PROCEDURES_CALLNORETURN_TOOLTIP": "Voer de door de gebruiker gedefinieerde functie \"%1\" uit.", "PROCEDURES_CALLRETURN_HELPURL": "https://nl.wikipedia.org/wiki/Subprogramma", "PROCEDURES_CALLRETURN_TOOLTIP": "Voer de door de gebruiker gedefinieerde functie \"%1\" uit en gebruik de uitvoer.", "PROCEDURES_MUTATORCONTAINER_TITLE": "ingangen", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON> van deze functie toe<PERSON>n, verwij<PERSON><PERSON> of herordenen.", "PROCEDURES_MUTATORARG_TITLE": "invoernaam:", "PROCEDURES_MUTATORARG_TOOLTIP": "<PERSON>en invoer aan de functie toevoegen.", "PROCEDURES_HIGHLIGHT_DEF": "A<PERSON><PERSON><PERSON><PERSON> functiedefini<PERSON>", "PROCEDURES_CREATE_DO": "Maak \"%1\"", "PROCEDURES_IFRETURN_TOOLTIP": "Als de eerste waarde \"waar\" is, geef dan de tweede waarde terug.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Waarschuwing: dit blok mag alleen gebruikt worden binnen de definitie van een functie.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Zeg iets...", "WORKSPACE_ARIA_LABEL": "<PERSON><PERSON>", "COLLAPSED_WARNINGS_WARNING": "Samengevouwen blokken bevatten waarschuwingen.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}