package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingGiftExchange;

/**
 * 礼品兑换记录服务接口
 */
public interface ITeachingGiftExchangeService extends IService<TeachingGiftExchange> {

    /**
     * 确认领取礼品
     *
     * @param id 兑换记录ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<?> receiveGift(String id, String userId);

    /**
     * 获取用户礼品兑换记录
     *
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 兑换记录
     */
    Result<?> getUserExchangeRecords(String userId, Integer pageNo, Integer pageSize);
} 