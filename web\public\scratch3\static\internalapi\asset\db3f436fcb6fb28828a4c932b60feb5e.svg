<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="97.09954" height="249.27846" viewBox="0,0,97.09954,249.27846"><g transform="translate(-191.46447,-53.10006)"><g data-paper-data="{&quot;isPaintingLayer&quot;:true}" fill-rule="nonzero" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" style="mix-blend-mode: normal"><path d="M238.46695,53.10074c19.45932,-0.12903 21.93265,18.15225 23.10409,21.98205c1.17143,3.82981 -0.32088,6.42279 0.2593,10.60789c0.58018,3.90407 3.29874,3.47224 4.36699,7.78772c0.78337,6.10126 -0.62823,4.88415 -0.4367,8.6509c0.19153,3.76674 3.29221,4.82261 2.64698,11.78273c-1.68021,18.12457 -13.69239,13.22976 -28.41222,14.70164c-27.49327,1.80651 -30.03795,-10.67505 -28.98825,-18.20206c0.89957,-6.45063 2.83517,-5.20158 3.43774,-11.58017c0.29095,-3.07993 -2.05441,-5.15948 -1.63401,-8.92234c0.44797,-4.00966 2.63685,-4.50628 4.17049,-8.24044c1.09572,-2.66791 0.38435,-5.83054 1.17907,-8.60218c1.67822,-5.85284 5.39839,-19.86691 20.30651,-19.96576z" data-paper-data="{&quot;origPos&quot;:null}" fill="#000000" stroke="none" stroke-width="0" stroke-linecap="round" stroke-linejoin="miter"/><g data-paper-data="{&quot;origPos&quot;:null}" stroke="none" stroke-width="0" stroke-linejoin="miter"><g data-paper-data="{&quot;origPos&quot;:null}" stroke-linecap="butt"><path d="M252.70872,109.61796c-2.7387,5.42219 -6.08129,9.07964 -13.56464,8.97223c-6.39634,-0.10368 -10.24727,-3.35719 -13.46098,-8.50646c2.69544,-0.99899 6.46861,-2.45103 6.46861,-2.45103l0.62581,-8.48494l12.75262,-0.19372l0.75492,8.14028c0,0 5.75886,2.1849 6.42364,2.52366z" data-paper-data="{&quot;origPos&quot;:null}" fill="#b5875c"/><path d="M232.86767,99.0024l12.75262,-0.19373l0.682,8.05167c-3.45884,0.04183 -11.36949,-0.29185 -13.89775,-2.36361z" data-paper-data="{&quot;origPos&quot;:null}" fill="#755135"/></g><path d="M239.64753,104.44134c-9.98927,0.08693 -14.88442,-8.11044 -16.53019,-16.80004c-2.54655,-0.98415 -4.0628,-6.01908 -3.11222,-7.80689c1.04992,-2.17738 2.43129,-0.32573 2.39577,-0.86121c-0.26448,-7.97607 2.56636,-19.81665 18.46458,-19.98419c12.97953,0.54584 15.11735,13.8985 15.01463,19.71612c0,0 1.19566,-1.04299 2.43823,-0.00503c1.32912,2.17179 -0.89791,7.28394 -3.15382,8.16924c-1.14141,9.26825 -5.16881,17.60543 -15.51697,17.572z" data-paper-data="{&quot;origPos&quot;:null}" fill="#b5875c" stroke-linecap="round"/></g><g data-paper-data="{&quot;origPos&quot;:null}" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"><path d="M219.07256,135.78684c-1.57762,4.39801 -5.60293,15.7869 -6.76119,19.43621c-1.16854,2.50969 -4.03179,4.16342 -7.10269,3.18088c-3.10937,-0.99484 -4.74351,-2.64261 -4.67488,-4.85765c0.51142,-1.79521 1.59436,-9.96654 2.01308,-11.98457c2.05906,-9.87821 5.17972,-20.11532 13.00154,-27.87268c2.96339,-2.93863 5.21579,-2.64254 7.89033,-3.02228" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#b5875c"/><path d="M219.19427,137.69548c-0.63549,1.57549 -1.50184,3.37364 -2.4295,5.21973c-2.58637,-0.83789 -5.2511,-1.66068 -7.73014,-2.55188c-2.17576,-0.78219 -4.51955,-1.86961 -6.9601,-2.92658c1.82141,-8.53884 4.24631,-17.25915 11.66907,-23.74538c3.70805,-3.23986 6.55673,-2.94809 9.93057,-3.3979" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#9ed094"/></g><g data-paper-data="{&quot;origPos&quot;:null}" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"><path d="M256.58975,110.66674c2.67455,0.37974 4.92695,0.08363 7.89032,3.02228c7.82182,7.75736 10.94249,17.99447 13.00154,27.87268c0.41873,2.01801 1.50166,10.18935 2.01308,11.98457c0.06861,2.21505 -1.56552,3.8628 -4.67488,4.85765c-3.0709,0.98254 -5.93415,-0.67118 -7.10269,-3.18088c-1.15826,-3.64931 -5.18358,-15.0382 -6.76119,-19.43621" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#b5875c"/><path d="M254.70919,109.80502c3.51008,0.44983 6.47381,0.15804 10.33159,3.3979c7.72249,6.48624 10.24531,15.20655 12.14028,23.74538c-2.5391,1.05698 -4.97754,2.14441 -7.24116,2.92658c-2.57917,0.89122 -5.35149,1.714 -8.0423,2.55189c-0.96512,-1.84609 -1.86643,-3.64424 -2.5276,-5.21973" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#9ed094"/></g><path d="M265.50216,237.98854c-0.52872,12.3918 -13.65378,9.67107 -16.99664,3.5912c-4.09745,-7.4523 -6.57537,-17.37024 -7.83173,-35.08342c5.13387,-0.15819 23.6464,-1.20703 26.21337,-1.26081c0.98862,17.2412 -1.385,32.75304 -1.385,32.75304z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#b5875c" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M237.25535,206.10074c-0.22118,18.95612 -6.10723,33.1193 -6.10723,33.1193c-2.64577,12.10021 -15.10055,7.04445 -17.34881,0.45499c-2.75576,-8.07692 -4.03621,-19.064 -3.4632,-33.56504c0.00606,-0.15318 0.01234,-0.30487 0.01888,-0.45508c0.77088,-0.00897 26.93057,0.16385 26.90036,0.44583z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#b5875c" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M226.9367,74.32605c2.63056,-1.4245 6.17648,-0.92917 8.19889,0.57674" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="miter"/><path d="M243.69278,74.98194c2.0224,-1.50592 5.56834,-2.00125 8.19889,-0.57674" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="miter"/><path d="M223.081,64.71192c5.46081,-9.89893 16.64325,-13.09988 26.28295,-6.67018c7.86282,5.49201 8.15752,13.71675 6.75868,20.51388c-0.25394,-0.08608 -16.53213,-6.57363 -23.60609,-18.15197c-4.24325,10.78867 -10.11408,18.35573 -10.11408,18.35573c0,0 -2.62197,-8.63217 0.67852,-14.04745z" data-paper-data="{&quot;origPos&quot;:null}" fill="#000000" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="round"/><path d="M222.75089,89.58429c-0.10927,1.77963 -0.8312,3.18412 -1.6125,3.13705c-0.78128,-0.04707 -1.32605,-1.52787 -1.21678,-3.3075c0.10927,-1.77963 0.8312,-3.18412 1.61249,-3.13705c0.78128,0.04707 1.32605,1.52788 1.21678,3.30751z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#d6a04f" stroke-width="1.5" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M258.33733,88.83753c0.10085,1.78016 -0.45081,3.26117 -1.23216,3.30797c-0.78136,0.0468 -1.49649,-1.35836 -1.59735,-3.13851c-0.10085,-1.78016 0.45081,-3.26118 1.23216,-3.30798c0.78136,-0.0468 1.49649,1.35837 1.59735,3.13851z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#d6a04f" stroke-width="1.5" stroke-linecap="butt" stroke-linejoin="miter"/><g data-paper-data="{&quot;origPos&quot;:null}" stroke-linejoin="miter"><path d="M248.39044,82.85929c-2.38508,0.2863 -4.51864,-0.69558 -4.76546,-2.19307c-0.24681,-1.4975 1.48658,-2.94354 3.87167,-3.22983c2.38508,-0.2863 4.60814,0.79327 4.85495,2.29077c0.2468,1.4975 -1.5761,2.84585 -3.96117,3.13214z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#ffffff" stroke="none" stroke-width="0" stroke-linecap="butt"/><path d="M247.26591,80.90987c-0.67696,-1.44972 0.1211,-3.12227 1.59487,-3.53205c3.17821,-0.29769 4.97869,2.82619 2.37029,4.56493c-1.44185,0.95338 -3.30015,0.39127 -3.96516,-1.03288z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#000000" stroke="none" stroke-width="1" stroke-linecap="butt"/><path d="M243.70446,78.6001c2.37421,-1.55315 5.95394,-1.61091 8.18773,-0.61276" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#000000" stroke-width="1.5" stroke-linecap="round"/></g><g stroke-linejoin="miter"><path d="M230.14035,82.74122c-2.39352,-0.22926 -4.17363,-1.63337 -3.97599,-3.13616c0.19764,-1.5028 2.29819,-2.53521 4.69173,-2.30595c2.39352,0.22926 4.23182,1.74699 4.03419,3.24979c-0.19763,1.5028 -2.35641,2.42159 -4.74994,2.19232z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#ffffff" stroke="none" stroke-width="0" stroke-linecap="butt"/><path d="M229.61962,80.61431c-0.2329,-1.54789 1.02779,-2.99832 2.57468,-3.08222c3.16503,0.38668 3.9979,3.79465 0.96453,4.92472c-1.67452,0.61717 -3.31042,-0.32194 -3.53922,-1.8425z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#000000" stroke="none" stroke-width="1" stroke-linecap="butt"/><path d="M226.52031,78.13657c2.63056,-1.21567 6.17648,-0.79295 8.19889,0.49219" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="none" stroke="#000000" stroke-width="1.5" stroke-linecap="round"/></g><g fill="none" stroke="#755135" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="miter"><path d="M236.62719,87.52336c0,0 1.74711,-0.42738 2.11344,0.46228" data-paper-data="{&quot;origPos&quot;:null}"/><path d="M240.84971,88.02053c0.23248,-0.87222 1.72598,-0.64544 1.72598,-0.64544" data-paper-data="{&quot;origPos&quot;:null}"/></g><g stroke-linejoin="miter"><path d="M260.55458,287.12034c-1.56509,0.22207 -4.69867,-0.70379 -4.69867,-0.70379c0,0 -7.05421,-18.48132 -9.10011,-32.22836c-1.47855,-9.0165 -0.38463,-15.29983 2.39658,-20.31029c3.14983,-6.05355 10.91032,-6.07496 14.39148,-0.14496c2.24787,3.82913 2.76526,11.40372 2.84867,16.58959c0.21937,13.63724 -0.7183,34.70087 -0.7183,34.70087c0,0 -2.85301,1.77531 -5.11965,2.09694z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#b5875c" stroke="none" stroke-width="0" stroke-linecap="butt"/><g stroke-linecap="round"><path d="M274.2504,291.25879c3.01588,2.10251 12.02167,0.77505 8.2084,6.25711c-2.60642,3.74706 -7.40936,3.17549 -12.07979,2.68346c-3.94594,-0.4157 -4.44145,-4.46944 -8.40794,-4.36865c-4.20759,0.10693 -8.17187,0.84036 -10.65896,-1.80892c-0.88408,-0.94173 2.67061,-10.41852 2.67061,-10.41852c0,0 3.64671,1.6945 7.45034,1.79782c1.26788,0.03444 6.0695,-1.43058 6.0695,-1.43058c0,0 3.73196,5.18576 6.74784,7.28828z" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#ffffff" stroke="none" stroke-width="0"/><path d="M251.15062,293.6599c3.09631,1.7955 2.54382,2.31085 10.61651,1.5075c2.90519,0.1938 4.64283,3.28097 8.29941,4.31332c9.68357,1.64216 12.06182,-2.11795 13.38323,-3.62242c0.00511,1.12758 -0.23446,2.30896 -0.71446,2.99901c-2.60642,3.74706 -7.26869,3.324 -11.93914,2.83198c-6.66973,-1.35792 -5.31687,-4.2038 -9.28337,-4.10301c-4.20759,0.10693 -5.87146,0.90965 -8.66997,-0.45255c-2.04272,-0.99431 -1.96783,-1.7254 -1.69222,-3.47382z" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="#a5a5a5" stroke="none" stroke-width="0"/><path d="M271.44471,292.92535l2.29152,-2.17032" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M271.68314,288.71482l-2.16759,2.17369" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M267.46585,288.72425l2.28817,-2.0464" data-paper-data="{&quot;origPos&quot;:null,&quot;index&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/></g></g><g stroke-linejoin="miter"><path d="M217.84459,286.46114c-1.57887,-0.065 -4.50506,-1.54399 -4.50506,-1.54399c0,0 -3.77512,-19.46739 -3.43157,-33.36788c0.09077,-9.14191 2.24535,-15.12776 5.84271,-19.55516c4.13917,-5.3872 11.7829,-4.00223 14.19302,2.46479c1.55627,4.17588 0.76654,11.72454 -0.04072,16.84358c-2.12289,13.4615 -6.65852,34.02243 -6.65852,34.02243c0,0 -3.11322,1.23035 -5.39986,1.13622z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#b5875c" stroke="none" stroke-width="0" stroke-linecap="butt"/><g stroke-linecap="round"><path d="M231.36908,291.39024c2.94508,2.20059 11.98968,1.17009 7.99814,6.52376c-2.72827,3.65928 -7.50982,2.93002 -12.16155,2.28462c-3.93013,-0.54529 -4.29201,-4.61313 -8.25968,-4.64288c-4.20882,-0.03154 -8.19511,0.57108 -10.5937,-2.15857c-0.85262,-0.9703 3.01189,-10.32502 3.01189,-10.32502c0,0 4.24289,2.03831 6.45504,2.09305c2.73495,0.06767 7.04544,-1.28124 7.04544,-1.28124c0,0 3.55934,5.30569 6.50442,7.50628z" data-paper-data="{&quot;origPos&quot;:null}" fill="#ffffff" stroke="none" stroke-width="0"/><path d="M209.77326,296.75764c-2.00891,-1.06096 -1.91001,-1.78921 -1.57702,-3.5276c3.03556,1.89639 2.46643,2.39328 10.56117,1.85593c2.89724,0.28927 4.5324,3.43192 8.15304,4.584c9.6243,1.95982 12.12497,-1.72002 13.49514,-3.18021c-0.032,1.12712 -0.31029,2.29998 -0.81272,2.97387c-2.72827,3.65928 -7.37411,3.08309 -12.02584,2.43769c-6.62145,-1.57659 -5.17571,-4.37643 -9.14339,-4.40617c-4.20882,-0.03154 -5.89817,0.716 -8.65039,-0.73753z" data-paper-data="{&quot;origPos&quot;:null}" fill="#a5a5a5" stroke="none" stroke-width="0"/><path d="M230.86525,291.06963l-2.36168,2.09378" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M226.6424,291.06413l2.23793,-2.10123" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M227.01918,286.86361l-2.35424,1.97002" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/><path d="M225.38549,289.60717" data-paper-data="{&quot;origPos&quot;:null}" fill="none" stroke="#000000" stroke-width="1.5"/></g></g><g stroke-linejoin="miter"><path d="M237.66193,92.17172c0.75754,0.08768 1.46693,0.39715 2.02845,0.88497c0.56331,-0.48899 1.2752,-0.79856 2.03509,-0.88497c1.05619,0.15095 2.08273,0.44919 3.04599,0.88497c0,0 -0.17444,2.74839 -2.20289,3.63336c-1.62205,0.65744 -3.3718,0.74466 -4.99386,0.08722c-1.2705,-0.58751 -2.24427,-1.68689 -2.96541,-2.83562c0,0 2.03508,-1.76993 3.05263,-1.76993z" data-paper-data="{&quot;origPos&quot;:null}" fill="#991414" stroke="none" stroke-width="1" stroke-linecap="butt"/><path d="M245.31073,93.4554c-2.89814,1.49671 -7.71721,1.97414 -10.9134,0.1542" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="none" stroke="#700f0f" stroke-width="1.5" stroke-linecap="round"/></g><path d="M206.80236,224.1819c0,0 0.3065,-27.35354 1.65617,-34.5797c4.04903,-21.67848 12.14909,-31.25536 12.14909,-31.25536l39.42941,-0.0022c0,0 8.28841,18.7566 9.63549,28.00116c2.82047,19.35609 2.07027,38.62432 2.07027,38.62432c0,0 -19.20999,6.20681 -32.29022,6.20681c-15.74165,0 -32.65018,-6.99501 -32.65018,-6.99501z" data-paper-data="{&quot;origPos&quot;:null}" fill="#254978" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M217.52689,111.37706c0,0 6.26957,-1.96786 10.76451,-2.88179c1.87634,4.23195 3.17632,7.76569 11.07876,7.93524c6.7427,0.14465 9.41265,-3.50685 10.68982,-8.0229c1.34999,0.29651 10.81797,2.28581 10.81797,2.28581c0,0 2.65008,13.67691 2.71344,22.33412c0.0536,7.32341 -2.10223,24.57852 -2.10223,24.57852c0,0 -11.77026,6.30594 -21.73016,5.89172c-11.2431,-0.46759 -22.38096,-5.54339 -22.38096,-5.54339c0,0 -1.77652,-14.39997 -2.52956,-24.72911c-0.75304,-10.32914 2.67841,-21.84822 2.67841,-21.84822z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0}" fill="#b9dcb2" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M197.93365,174.23868c0,0 -0.97745,-13.7029 1.98192,-19.49248c0.40465,-1.27902 3.47975,-5.89857 7.69236,-4.74461c4.67522,0.92547 4.38849,6.45652 4.37979,6.50695c-1.25377,7.22331 -6.68928,17.55883 -7.01399,19.69244c0.50107,2.11446 0.98271,4.63479 1.32239,6.59027c0.42906,1.89547 -0.69327,5.25967 -0.80504,7.18452c-0.03352,0.54658 -0.68,1.78945 -1.80518,0.36256c-0.84825,-1.36472 -0.10231,-3.09554 -0.50593,-4.51913c-0.98685,2.5817 -1.48856,3.24783 -1.86428,6.0283c-0.33514,2.48011 1.23038,5.76132 -0.91891,5.95424c-0.81045,0.22129 -1.33584,-2.75063 -1.40225,-4.79807c-0.07131,-2.19784 0.07911,-4.21814 0.25711,-5.71949c-0.59552,0.86954 -0.72634,2.6919 -0.96308,4.37191c-0.07892,0.55999 0.13164,3.25851 -0.12984,3.75997c-0.34834,0.86539 -1.25756,0.69093 -1.65607,-0.15253c-0.50037,-1.05913 -0.47661,-3.53275 -0.37995,-4.70015c0.11523,-1.39166 0.39807,-3.10241 0.67581,-4.47851c-0.50262,0.1447 -0.8415,2.36143 -1.21574,3.71794c-0.16405,0.59467 0.13169,3.29344 -1.06518,3.59537c-1.41222,0.07599 -0.77301,-3.12751 -0.83657,-4.74832c0.16774,-1.62143 0.80992,-4.32415 0.80992,-4.32415c0,0 -0.85029,2.2222 -1.18977,3.33223c-0.34707,1.13487 -0.06996,2.49122 -1.07165,2.47414c-1.13237,0.10745 -0.66929,-3.08153 -0.6813,-3.48934c0.15095,-1.26392 1.48712,-4.33513 1.48712,-4.33513z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#b5875c" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/><path d="M286.99312,182.30758c0,0 1.33618,3.07121 1.48714,4.33513c-0.01201,0.40782 0.45108,3.59681 -0.68129,3.48935c-1.00169,0.01708 -0.72458,-1.33926 -1.07165,-2.47413c-0.33948,-1.11003 -1.18977,-3.33223 -1.18977,-3.33223c0,0 0.64217,2.70271 0.80992,4.32415c-0.06355,1.6208 0.57566,4.8243 -0.83657,4.74832c-1.19687,-0.30193 -0.90112,-3.0007 -1.06518,-3.59537c-0.37425,-1.35651 -0.71312,-3.57324 -1.21574,-3.71794c0.27775,1.37611 0.56058,3.08687 0.67582,4.47851c0.09665,1.1674 0.12042,3.64101 -0.37994,4.70015c-0.39849,0.84347 -1.30773,1.01793 -1.65607,0.15253c-0.26147,-0.50146 -0.05092,-3.19996 -0.12984,-3.75997c-0.23674,-1.68 -0.36756,-3.50238 -0.96308,-4.37191c0.17799,1.50136 0.32842,3.52165 0.25711,5.71949c-0.06641,2.04745 -0.59181,5.01936 -1.40225,4.79807c-2.14928,-0.19293 -0.58379,-3.47412 -0.91892,-5.95423c-0.37572,-2.78048 -0.87744,-3.44662 -1.86428,-6.02831c-0.40363,1.42359 0.34231,3.1544 -0.50593,4.51913c-1.12519,1.42688 -1.77166,0.18401 -1.80518,-0.36256c-0.11176,-1.92484 -1.2341,-5.28906 -0.80504,-7.18452c0.33968,-1.95547 0.82132,-4.4758 1.32239,-6.59026c-0.32471,-2.1336 -5.76023,-12.46912 -7.01399,-19.69244c-0.0087,-0.05043 -0.29542,-5.58147 4.3798,-6.50694c4.21262,-1.15397 7.2877,3.46558 7.69235,4.7446c2.95935,5.78958 1.98192,19.49248 1.98192,19.49248z" data-paper-data="{&quot;origPos&quot;:null,&quot;origRot&quot;:0,&quot;index&quot;:null}" fill="#b5875c" stroke="none" stroke-width="0" stroke-linecap="butt" stroke-linejoin="miter"/></g></g></svg>