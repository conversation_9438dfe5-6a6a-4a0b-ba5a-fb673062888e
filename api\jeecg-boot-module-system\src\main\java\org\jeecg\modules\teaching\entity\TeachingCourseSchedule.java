package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 课程排期
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@TableName("teaching_course_schedule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="teaching_course_schedule对象", description="课程排期")
public class TeachingCourseSchedule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**课程标题*/
    @Excel(name = "课程标题", width = 15)
    @ApiModelProperty(value = "课程标题")
    @TableField("schedule_title")
    private String scheduleTitle;
    
    /**班级ID*/
    @Excel(name = "班级", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @ApiModelProperty(value = "班级ID")
    @TableField("class_id")
    private String classId;
    
    /**课程ID*/
    @Excel(name = "课程", width = 15, dictTable = "teaching_course", dicText = "course_name", dicCode = "id")
    @Dict(dictTable = "teaching_course", dicText = "course_name", dicCode = "id")
    @ApiModelProperty(value = "课程ID")
    private String courseId;
    
    /**教师ID*/
    @Excel(name = "教师", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "id")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "id")
    @ApiModelProperty(value = "教师ID")
    private String teacherId;
    
    /**教室ID*/
    @Excel(name = "教室", width = 15, dictTable = "teaching_classroom", dicText = "classroom_name", dicCode = "id")
    @Dict(dictTable = "teaching_classroom", dicText = "classroom_name", dicCode = "id")
    @ApiModelProperty(value = "教室ID")
    @TableField("classroom_id")
    private String classroomId;
    
    /**学生名单*/
    @ApiModelProperty(value = "学生名单")
    @TableField(exist = true)
    private String studentNames;
    
    /**开始时间*/
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    /**结束时间*/
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    /**重复类型*/
    @Excel(name = "重复类型", width = 15)
    @ApiModelProperty(value = "重复类型：0-不重复，1-每天，2-每周")
    @TableField("repeat_type")
    private Integer repeatType;
    
    /**重复结束日期*/
    @Excel(name = "重复结束日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "重复结束日期")
    @TableField("repeat_end_date")
    private Date repeatEndDate;
    
    /**重复星期*/
    @Excel(name = "重复星期", width = 15)
    @ApiModelProperty(value = "重复星期")
    @TableField("week_days")
    private String weekdays;
    
    /**删除的重复实例*/
    @Excel(name = "删除的重复实例", width = 15)
    @ApiModelProperty(value = "删除的重复实例，格式：yyyy-MM-dd,yyyy-MM-dd")
    @TableField("deleted_instances")
    private String deletedInstances;
    
    /**显示颜色*/
    @Excel(name = "显示颜色", width = 15)
    @ApiModelProperty(value = "显示颜色")
    private String color;
    
    /**状态*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态：0-取消，1-正常")
    private Integer status;
    
    /**提前多少分钟提醒*/
    @Excel(name = "提前提醒时间", width = 15)
    @ApiModelProperty(value = "提前多少分钟提醒（默认15分钟）")
    @TableField("notify_before_minutes")
    private Integer notifyBeforeMinutes;
    
    /**是否通知教师*/
    @Excel(name = "是否通知教师", width = 15)
    @ApiModelProperty(value = "是否通知教师：0-不通知，1-通知")
    @TableField("notify_teachers")
    private Integer notifyTeachers;
    
    /**是否通知学生*/
    @Excel(name = "是否通知学生", width = 15)
    @ApiModelProperty(value = "是否通知学生：0-不通知，1-通知")
    @TableField("notify_students")
    private Integer notifyStudents;
    
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String description;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**是否已创建提醒通知*/
    @Excel(name = "是否已创建提醒", width = 15)
    @ApiModelProperty(value = "是否已创建提醒通知：0-未创建，1-已创建")
    @TableField("reminder_created")
    private Integer reminderCreated;
} 