package org.jeecg.modules.teaching.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;

import java.util.stream.Collectors;

/**
 * 题目导入工具类
 * 用于解析文本文件中的题目
 */
@Slf4j
public class QuestionImportUtil {
    
    /**
     * 解析导入的题目文本内容
     * @param content 文本内容
     * @return 题目列表
     */
    public static List<ExamQuestion> parseQuestionText(String content) {
        return parseQuestionText(content, null, null, null);
    }
    
    /**
     * 解析导入的题目文本内容，带元数据
     * @param content 文本内容
     * @param subject 所属科目
     * @param level 题目级别
     * @param difficulty 难度
     * @return 题目列表
     */
    public static List<ExamQuestion> parseQuestionText(String content, String subject, String level, Integer difficulty) {
        List<ExamQuestion> questions = new ArrayList<>();
        if (StringUtils.isBlank(content)) {
            return questions;
        }
        
        try (BufferedReader reader = new BufferedReader(new StringReader(content))) {
            String line;
            StringBuilder currentQuestionText = new StringBuilder();
            int questionType = 0; // 1:单选题, 2:判断题, 3:编程题
            boolean inMetadata = false; // 标记是否在元数据部分
            boolean questionStarted = false; // 标记是否已经开始解析题目
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // 跳过空行
                if (line.isEmpty()) {
                    continue;
                }
                
                // 检测元数据部分
                if (line.equals("【元数据】")) {
                    inMetadata = true;
                    continue;
                }
                
                // 如果是元数据部分，跳过
                if (inMetadata) {
                    if (line.startsWith("【一、") || line.startsWith("【二、") || line.startsWith("【三、")) {
                        inMetadata = false;
                    } else {
                        continue;  // 跳过元数据行
                    }
                }
                
                // 检测题目类型
                // 修改：合并题型检测逻辑，先处理当前未完成解析的题目
                if (line.startsWith("【一、单选题】") || line.startsWith("【二、判断题】") || line.startsWith("【三、编程题】")) {
                    // 先处理当前可能存在的题目
                    if (questionStarted && currentQuestionText.length() > 0 && questionType > 0) {
                        // 添加日志以便调试
                        log.debug("切换题型前处理题目，当前题型：{}", questionType);
                        ExamQuestion question = parseQuestion(currentQuestionText.toString(), questionType, subject, level, difficulty);
                        if (question != null) {
                            questions.add(question);
                        }
                    }
                    
                    // 设置新的题型
                    if (line.startsWith("【一、单选题】")) {
                        questionType = 1;
                        log.debug("切换到单选题");
                    } else if (line.startsWith("【二、判断题】")) {
                        questionType = 2;
                        log.debug("切换到判断题");
                    } else if (line.startsWith("【三、编程题】")) {
                        questionType = 3;
                        log.debug("切换到编程题");
                    }
                    
                    // 清空文本准备新题型解析
                    currentQuestionText.setLength(0);
                    questionStarted = false;
                    continue;
                }
                
                // 如果遇到新题目的开始标记
                if (line.matches("【\\d+\\.】.*")) {
                    // 如果当前有题目内容，先处理它
                    if (questionStarted && currentQuestionText.length() > 0) {
                    ExamQuestion question = parseQuestion(currentQuestionText.toString(), questionType, subject, level, difficulty);
                    if (question != null) {
                        questions.add(question);
                    }
                    currentQuestionText.setLength(0);
                    }
                    questionStarted = true;
                }
                
                // 只有在已经识别了题型后才累加题目文本
                if (questionType > 0) {
                    currentQuestionText.append(line).append("\n");
                }
            }
            
            // 处理最后一个题目
            if (questionStarted && currentQuestionText.length() > 0) {
                ExamQuestion question = parseQuestion(currentQuestionText.toString(), questionType, subject, level, difficulty);
                if (question != null) {
                    questions.add(question);
                }
            }
            
            // 添加日志输出解析结果
            log.debug("解析完成，共解析{}个题目", questions.size());
        } catch (IOException e) {
            log.error("解析题目文本内容失败", e);
        }
        
        return questions;
    }
    

    
    /**
     * 解析单个题目，带元数据
     * @param questionText 题目文本
     * @param questionType 题目类型
     * @param subject 所属科目
     * @param level 题目级别
     * @param difficulty 难度
     * @return 题目对象
     */
    private static ExamQuestion parseQuestion(String questionText, int questionType, String subject, String level, Integer difficulty) {
        if (StringUtils.isBlank(questionText) || questionType <= 0) {
            return null;
        }
        
        ExamQuestion question = new ExamQuestion();
        question.setQuestionType(questionType);
        
        // 设置元数据
        if (subject != null) {
            question.setSubject(subject);
        }
        if (level != null) {
            // 注意: 此处直接设置level，级别的标准化处理在ExamQuestionServiceImpl中进行
            // 在ExamQuestionServiceImpl.importQuestionsFromFile方法中会对所有题目的level进行标准化处理
            question.setLevel(level);
        }
        if (difficulty != null) {
            question.setDifficulty(difficulty);
        }
        
        // 提取题干（题号和题目内容）
        Pattern titlePattern = Pattern.compile("【(\\d+)\\.】(.+?)(?=【|$)", Pattern.DOTALL);
        Matcher titleMatcher = titlePattern.matcher(questionText);
        if (titleMatcher.find()) {
            String titleContent = titleMatcher.group(2).trim();
            question.setTitle(titleContent);
        } else {
            return null; // 未找到题干
        }
        
        JSONObject contentJson = new JSONObject();
        
        if (questionType == 1) { // 单选题
            // 解析选项
            List<String> options = new ArrayList<>();
            Pattern optionPattern = Pattern.compile("【([A-Z])\\. 】(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher optionMatcher = optionPattern.matcher(questionText);
            while (optionMatcher.find()) {
                options.add(optionMatcher.group(2).trim());
            }
            
            // 解析答案
            String answer = "";
            Pattern answerPattern = Pattern.compile("【答案】(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher answerMatcher = answerPattern.matcher(questionText);
            if (answerMatcher.find()) {
                answer = answerMatcher.group(1).trim();
            }
            
            // 解析解析
            String analysis = "";
            Pattern analysisPattern = Pattern.compile("【解析】(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher analysisMatcher = analysisPattern.matcher(questionText);
            if (analysisMatcher.find()) {
                analysis = analysisMatcher.group(1).trim();
            }
            
            contentJson.put("options", options);
            contentJson.put("answer", answer);
            contentJson.put("analysis", analysis);
            
        } else if (questionType == 2) { // 判断题
            // 解析答案
            String answer = "";
            Pattern answerPattern = Pattern.compile("【答案】(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher answerMatcher = answerPattern.matcher(questionText);
            if (answerMatcher.find()) {
                String answerText = answerMatcher.group(1).trim();
                answer = "正确".equals(answerText) ? "T" : "F";
            }
            
            // 解析解析
            String analysis = "";
            Pattern analysisPattern = Pattern.compile("【解析】(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher analysisMatcher = analysisPattern.matcher(questionText);
            if (analysisMatcher.find()) {
                analysis = analysisMatcher.group(1).trim();
            }
            
            contentJson.put("answer", answer);
            contentJson.put("analysis", analysis);
            
        } else if (questionType == 3) { // 编程题
            Map<String, Object> programContent = new HashMap<>();
            
            // 解析时间和内存限制
            Pattern limitPattern = Pattern.compile("【- 时间限制：(.+?)s\\s*-\\s*内存限制：(.+?)】", Pattern.DOTALL);
            Matcher limitMatcher = limitPattern.matcher(questionText);
            if (limitMatcher.find()) {
                String timeLimit = limitMatcher.group(1).trim();
                String memoryLimit = limitMatcher.group(2).trim();
                programContent.put("time_limit", (int)(Double.parseDouble(timeLimit) * 1000)); // 转换为毫秒
                // 修复内存限制解析，保留小数点，正确处理浮点数值
                double memoryValue = Double.parseDouble(memoryLimit.replaceAll("[^0-9.]", ""));
                programContent.put("memory_limit", (int)memoryValue); // 转换为整数MB
                programContent.put("stack_limit", 8); // 默认栈限制为8MB(8192KB)
            } else {
                // 尝试使用另一种格式匹配（多行格式）
                Pattern multiLineLimitPattern = Pattern.compile("【-\\s*时间限制：(.+?)s[\\s\\S]*?-\\s*内存限制：(.+?)】", Pattern.DOTALL);
                Matcher multiLineLimitMatcher = multiLineLimitPattern.matcher(questionText);
                if (multiLineLimitMatcher.find()) {
                    String timeLimit = multiLineLimitMatcher.group(1).trim();
                    String memoryLimit = multiLineLimitMatcher.group(2).trim();
                    programContent.put("time_limit", (int)(Double.parseDouble(timeLimit) * 1000)); // 转换为毫秒
                    // 修复内存限制解析，保留小数点，正确处理浮点数值
                    double memoryValue = Double.parseDouble(memoryLimit.replaceAll("[^0-9.]", ""));
                    programContent.put("memory_limit", (int)memoryValue); // 转换为整数MB
                    programContent.put("stack_limit", 8); // 默认栈限制为8MB(8192KB)
                } else {
                    // 如果无法匹配任何限制格式，则使用默认值
                    programContent.put("time_limit", 1000); // 默认1秒
                    programContent.put("memory_limit", 512); // 默认512MB
                    programContent.put("stack_limit", 8); // 默认8MB(8192KB)
                    log.warn("无法解析时间和内存限制，使用默认值");
                }
            }
            
            // 解析题目描述
            Pattern descPattern = Pattern.compile("【题目描述】\\s*(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher descMatcher = descPattern.matcher(questionText);
            if (descMatcher.find()) {
                programContent.put("description", descMatcher.group(1).trim());
            }
            
            // 解析输入格式
            Pattern inputPattern = Pattern.compile("【输入格式】\\s*(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher inputMatcher = inputPattern.matcher(questionText);
            if (inputMatcher.find()) {
                programContent.put("input_format", inputMatcher.group(1).trim());
            }
            
            // 解析输出格式
            Pattern outputPattern = Pattern.compile("【输出格式】\\s*(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher outputMatcher = outputPattern.matcher(questionText);
            if (outputMatcher.find()) {
                programContent.put("output_format", outputMatcher.group(1).trim());
            }
            
            // 解析样例
            List<Map<String, String>> sampleCases = new ArrayList<>();
            Pattern sampleInputPattern = Pattern.compile("【输入样例\\s*(\\d+)】\\s*(.+?)(?=【输出样例|$)", Pattern.DOTALL);
            Pattern sampleOutputPattern = Pattern.compile("【输出样例\\s*(\\d+)】\\s*(.+?)(?=【|$)", Pattern.DOTALL);
            
            Matcher sampleInputMatcher = sampleInputPattern.matcher(questionText);
            Matcher sampleOutputMatcher = sampleOutputPattern.matcher(questionText);
            
            // 使用Map保存输入样例，以便后续匹配输出样例
            Map<String, String> inputSamples = new HashMap<>();
            while (sampleInputMatcher.find()) {
                String sampleNum = sampleInputMatcher.group(1).trim();
                String sampleInput = sampleInputMatcher.group(2).trim();
                inputSamples.put(sampleNum, sampleInput);
            }
            
            // 匹配对应的输出样例
            Map<String, String> outputSamples = new HashMap<>();
            while (sampleOutputMatcher.find()) {
                String sampleNum = sampleOutputMatcher.group(1).trim();
                String sampleOutput = sampleOutputMatcher.group(2).trim();
                outputSamples.put(sampleNum, sampleOutput);
            }
            
            // 组合输入输出样例对
            for (String sampleNum : inputSamples.keySet()) {
                if (outputSamples.containsKey(sampleNum)) {
                    Map<String, String> sampleCase = new HashMap<>();
                    sampleCase.put("input", inputSamples.get(sampleNum));
                    sampleCase.put("output", outputSamples.get(sampleNum));
                    sampleCases.add(sampleCase);
                    log.debug("成功解析样例{}: 输入长度={}, 输出长度={}", sampleNum, 
                            inputSamples.get(sampleNum).length(),
                            outputSamples.get(sampleNum).length());
                } else {
                    log.warn("样例{}缺少对应的输出样例", sampleNum);
                }
            }
            
            // 如果没有成功解析任何样例，尝试使用另一种格式的正则表达式
            if (sampleCases.isEmpty()) {
                log.debug("使用备用正则表达式尝试解析样例");
                // 尝试更宽松的匹配模式
                Pattern altInputPattern = Pattern.compile("【输入样例.*?】\\s*(.+?)(?=【输出样例|$)", Pattern.DOTALL);
                Pattern altOutputPattern = Pattern.compile("【输出样例.*?】\\s*(.+?)(?=【|$)", Pattern.DOTALL);
                
                Matcher altInputMatcher = altInputPattern.matcher(questionText);
                Matcher altOutputMatcher = altOutputPattern.matcher(questionText);
                
                while (altInputMatcher.find() && altOutputMatcher.find()) {
                Map<String, String> sampleCase = new HashMap<>();
                    sampleCase.put("input", altInputMatcher.group(1).trim());
                    sampleCase.put("output", altOutputMatcher.group(1).trim());
                sampleCases.add(sampleCase);
                    log.debug("使用备用正则表达式成功解析样例: 输入长度={}, 输出长度={}", 
                            altInputMatcher.group(1).trim().length(),
                            altOutputMatcher.group(1).trim().length());
                }
            }
            
            programContent.put("sample_cases", sampleCases);
            
            // 在日志中输出解析结果
            log.debug("共解析到{}个样例", sampleCases.size());
            
            // 解析数据范围/提示
            Pattern hintPattern = Pattern.compile("【数据范围】\\s*(.+?)(?=【|$)", Pattern.DOTALL);
            Matcher hintMatcher = hintPattern.matcher(questionText);
            if (hintMatcher.find()) {
                programContent.put("hint", hintMatcher.group(1).trim());
            }
            
            contentJson = JSONObject.parseObject(JSON.toJSONString(programContent));
        }
        
        question.setContent(contentJson.toJSONString());
        return question;
    }
    
    /**
     * 计算两个题目的相似度
     * @param q1 题目1
     * @param q2 题目2
     * @return 相似度，范围[0, 1]
     */
    private static double calculateSimilarity(ExamQuestion q1, ExamQuestion q2) {
        // 优化：使用缓存避免重复计算
        String key1 = q1.getId() + "-" + q2.getId();
        String key2 = q2.getId() + "-" + q1.getId();

        // 使用本地缓存优化，避免重复计算
        // 注意：在实际生产环境中，可以考虑使用更高级的缓存方案
        Map<String, Double> similarityCache = SIMILARITY_CACHE.get();
        if (similarityCache.containsKey(key1)) {
            return similarityCache.get(key1);
        }
        if (similarityCache.containsKey(key2)) {
            return similarityCache.get(key2);
        }

        // 1. 首先进行精确匹配检测
        if (isExactMatch(q1, q2)) {
            similarityCache.put(key1, 1.0);
            return 1.0;
        }

        // 2. 计算标题相似度（使用改进的算法）
        double titleSimilarity = calculateEnhancedTextSimilarity(
                q1.getTitle() == null ? "" : q1.getTitle(),
                q2.getTitle() == null ? "" : q2.getTitle()
        );

        // 3. 根据题目类型计算内容相似度
        double contentSimilarity = 0;
        if (q1.getQuestionType().equals(q2.getQuestionType())) {
            if (q1.getQuestionType() == 1) { // 单选题
                // 比较选项和答案
                contentSimilarity = compareChoiceQuestions(q1.getContent(), q2.getContent());
            } else if (q1.getQuestionType() == 2) { // 判断题
                // 比较答案
                contentSimilarity = compareJudgmentQuestions(q1.getContent(), q2.getContent());
            } else if (q1.getQuestionType() == 3) { // 编程题
                // 比较描述、输入输出格式等
                contentSimilarity = compareProgrammingQuestions(q1.getContent(), q2.getContent());
            }
        }

        // 4. 计算综合相似度，标题占比70%，内容占比30%（提高标题权重）
        double similarity = titleSimilarity * 0.7 + contentSimilarity * 0.3;

        // 5. 如果标题相似度很高，提升整体相似度
        if (titleSimilarity > 0.9) {
            similarity = Math.max(similarity, titleSimilarity * 0.95);
        }

        // 缓存计算结果
        similarityCache.put(key1, similarity);

        return similarity;
    }

    /**
     * 检查两个题目是否精确匹配
     * @param q1 题目1
     * @param q2 题目2
     * @return 是否精确匹配
     */
    private static boolean isExactMatch(ExamQuestion q1, ExamQuestion q2) {
        // 检查标题是否完全相同（忽略空格和标点符号）
        String title1 = normalizeText(q1.getTitle());
        String title2 = normalizeText(q2.getTitle());

        if (title1.equals(title2)) {
            return true;
        }

        // 检查标题是否只是格式不同（如"1+1=?"和"1 + 1 = ?"）
        String simplifiedTitle1 = title1.replaceAll("\\s+", "").replaceAll("[\\p{Punct}]", "");
        String simplifiedTitle2 = title2.replaceAll("\\s+", "").replaceAll("[\\p{Punct}]", "");

        return simplifiedTitle1.equals(simplifiedTitle2);
    }

    /**
     * 标准化文本（去除多余空格、统一标点符号等）
     * @param text 原始文本
     * @return 标准化后的文本
     */
    private static String normalizeText(String text) {
        if (text == null) {
            return "";
        }

        return text.trim()
                   .replaceAll("\\s+", " ")  // 多个空格替换为单个空格
                   .replaceAll("？", "?")    // 统一问号
                   .replaceAll("：", ":")    // 统一冒号
                   .replaceAll("，", ",")    // 统一逗号
                   .replaceAll("。", ".")    // 统一句号
                   .toLowerCase();           // 转为小写
    }
    
    // 使用ThreadLocal缓存相似度计算结果，避免重复计算
    private static final ThreadLocal<Map<String, Double>> SIMILARITY_CACHE = ThreadLocal.withInitial(HashMap::new);
    
    /**
     * 表示一个有相似度的题目
     */
    @Data
    public static class SimilarQuestion {
        private ExamQuestion question;
        private double similarity;
        
        public SimilarQuestion(ExamQuestion question, double similarity) {
            this.question = question;
            this.similarity = similarity;
        }
    }
    
    /**
     * 查找相似题目
     * @param question 待检查的题目
     * @param existingQuestions 已存在的题目列表
     * @param threshold 相似度阈值
     * @return 相似题目列表
     */
    public static List<SimilarQuestion> findSimilarQuestions(ExamQuestion question, List<ExamQuestion> existingQuestions, double threshold) {
        if (existingQuestions == null || existingQuestions.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 优化：使用并行流加速相似度计算
        return existingQuestions.parallelStream()
            .map(existingQuestion -> {
                double similarity = calculateSimilarity(question, existingQuestion);
                return new SimilarQuestion(existingQuestion, similarity);
            })
            .filter(similarQuestion -> similarQuestion.getSimilarity() >= threshold)
            .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity())) // 按相似度降序排序
            .collect(Collectors.toList());
    }
    
    /**
     * 计算两个文本的增强相似度（结合多种算法）
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度(0-1)
     */
    private static double calculateEnhancedTextSimilarity(String text1, String text2) {
        if (StringUtils.isBlank(text1) || StringUtils.isBlank(text2)) {
            return 0.0;
        }

        // 标准化文本
        String normalizedText1 = normalizeText(text1);
        String normalizedText2 = normalizeText(text2);

        // 1. 精确匹配检查
        if (normalizedText1.equals(normalizedText2)) {
            return 1.0;
        }

        // 2. 计算编辑距离相似度
        double editDistanceSimilarity = calculateEditDistanceSimilarity(normalizedText1, normalizedText2);

        // 3. 计算余弦相似度
        double cosineSimilarity = calculateCosineSimilarity(normalizedText1, normalizedText2);

        // 4. 计算Jaccard相似度（基于字符n-gram）
        double jaccardSimilarity = calculateJaccardSimilarity(normalizedText1, normalizedText2);

        // 5. 综合多种相似度算法，权重分配
        return editDistanceSimilarity * 0.4 + cosineSimilarity * 0.3 + jaccardSimilarity * 0.3;
    }

    /**
     * 计算基于编辑距离的相似度
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度(0-1)
     */
    private static double calculateEditDistanceSimilarity(String text1, String text2) {
        int maxLength = Math.max(text1.length(), text2.length());
        if (maxLength == 0) {
            return 1.0;
        }

        int editDistance = calculateEditDistance(text1, text2);
        return 1.0 - (double) editDistance / maxLength;
    }

    /**
     * 计算编辑距离（Levenshtein距离）
     * @param text1 文本1
     * @param text2 文本2
     * @return 编辑距离
     */
    private static int calculateEditDistance(String text1, String text2) {
        int m = text1.length();
        int n = text2.length();

        int[][] dp = new int[m + 1][n + 1];

        // 初始化
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }

        // 动态规划计算编辑距离
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (text1.charAt(i - 1) == text2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }

        return dp[m][n];
    }

    /**
     * 计算Jaccard相似度（基于字符2-gram）
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度(0-1)
     */
    private static double calculateJaccardSimilarity(String text1, String text2) {
        Set<String> grams1 = generateCharacterNGrams(text1, 2);
        Set<String> grams2 = generateCharacterNGrams(text2, 2);

        if (grams1.isEmpty() && grams2.isEmpty()) {
            return 1.0;
        }

        Set<String> intersection = new HashSet<>(grams1);
        intersection.retainAll(grams2);

        Set<String> union = new HashSet<>(grams1);
        union.addAll(grams2);

        return (double) intersection.size() / union.size();
    }

    /**
     * 生成字符n-gram集合
     * @param text 文本
     * @param n n-gram的长度
     * @return n-gram集合
     */
    private static Set<String> generateCharacterNGrams(String text, int n) {
        Set<String> grams = new HashSet<>();
        if (text.length() < n) {
            grams.add(text);
            return grams;
        }

        for (int i = 0; i <= text.length() - n; i++) {
            grams.add(text.substring(i, i + n));
        }

        return grams;
    }

    /**
     * 计算两个文本的相似度（基于余弦相似度）
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度(0-1)
     */
    private static double calculateCosineSimilarity(String text1, String text2) {
        if (StringUtils.isBlank(text1) || StringUtils.isBlank(text2)) {
            return 0.0;
        }

        // 将文本转换为字符数组
        char[] chars1 = text1.toCharArray();
        char[] chars2 = text2.toCharArray();

        // 构建向量空间模型
        Map<Character, Integer> vector1 = new HashMap<>();
        Map<Character, Integer> vector2 = new HashMap<>();

        // 统计字符频率
        for (char c : chars1) {
            vector1.put(c, vector1.getOrDefault(c, 0) + 1);
        }

        for (char c : chars2) {
            vector2.put(c, vector2.getOrDefault(c, 0) + 1);
        }

        // 计算向量点积
        double dotProduct = 0.0;
        for (Character key : vector1.keySet()) {
            if (vector2.containsKey(key)) {
                dotProduct += vector1.get(key) * vector2.get(key);
            }
        }

        // 计算向量模长
        double magnitude1 = 0.0;
        double magnitude2 = 0.0;

        for (Integer count : vector1.values()) {
            magnitude1 += Math.pow(count, 2);
        }
        magnitude1 = Math.sqrt(magnitude1);

        for (Integer count : vector2.values()) {
            magnitude2 += Math.pow(count, 2);
        }
        magnitude2 = Math.sqrt(magnitude2);

        // 计算余弦相似度
        if (magnitude1 > 0 && magnitude2 > 0) {
            return dotProduct / (magnitude1 * magnitude2);
        } else {
            return 0.0;
        }
    }
    
    /**
     * 比较两个单选题的相似度
     * @param content1 题目1的内容
     * @param content2 题目2的内容
     * @return 相似度
     */
    private static double compareChoiceQuestions(String content1, String content2) {
        try {
            // 解析JSON内容
            JSONObject json1 = JSONObject.parseObject(content1);
            JSONObject json2 = JSONObject.parseObject(content2);
            
            // 比较选项
            List<String> options1 = json1.getJSONArray("options").toJavaList(String.class);
            List<String> options2 = json2.getJSONArray("options").toJavaList(String.class);
            
            double optionsSimilarity = 0;
            if (options1.size() == options2.size()) {
                int matchCount = 0;
                for (int i = 0; i < options1.size(); i++) {
                    double optionSimilarity = calculateCosineSimilarity(options1.get(i), options2.get(i));
                    if (optionSimilarity > 0.8) {
                        matchCount++;
                    }
                }
                optionsSimilarity = (double) matchCount / options1.size();
            }
            
            // 比较答案
            String answer1 = json1.getString("answer");
            String answer2 = json2.getString("answer");
            double answerSimilarity = answer1.equals(answer2) ? 1.0 : 0.0;
            
            // 比较解析
            String analysis1 = json1.getString("analysis");
            String analysis2 = json2.getString("analysis");
            double analysisSimilarity = calculateCosineSimilarity(
                    analysis1 == null ? "" : analysis1, 
                    analysis2 == null ? "" : analysis2
            );
            
            // 选项占60%，答案占30%，解析占10%
            return optionsSimilarity * 0.6 + answerSimilarity * 0.3 + analysisSimilarity * 0.1;
        } catch (Exception e) {
            // 解析失败，返回基本文本相似度
            return calculateCosineSimilarity(content1, content2);
        }
    }
    
    /**
     * 比较两个判断题的相似度
     * @param content1 题目1的内容
     * @param content2 题目2的内容
     * @return 相似度
     */
    private static double compareJudgmentQuestions(String content1, String content2) {
        try {
            // 解析JSON内容
            JSONObject json1 = JSONObject.parseObject(content1);
            JSONObject json2 = JSONObject.parseObject(content2);
            
            // 比较答案
            String answer1 = json1.getString("answer");
            String answer2 = json2.getString("answer");
            double answerSimilarity = answer1.equals(answer2) ? 1.0 : 0.0;
            
            // 比较解析
            String analysis1 = json1.getString("analysis");
            String analysis2 = json2.getString("analysis");
            double analysisSimilarity = calculateCosineSimilarity(
                    analysis1 == null ? "" : analysis1, 
                    analysis2 == null ? "" : analysis2
            );
            
            // 答案占70%，解析占30%
            return answerSimilarity * 0.7 + analysisSimilarity * 0.3;
        } catch (Exception e) {
            // 解析失败，返回基本文本相似度
            return calculateCosineSimilarity(content1, content2);
        }
    }
    
    /**
     * 比较两个编程题的相似度
     * @param content1 题目1的内容
     * @param content2 题目2的内容
     * @return 相似度
     */
    private static double compareProgrammingQuestions(String content1, String content2) {
        try {
            // 解析JSON内容
            JSONObject json1 = JSONObject.parseObject(content1);
            JSONObject json2 = JSONObject.parseObject(content2);
            
            // 比较题目描述
            String desc1 = json1.getString("description");
            String desc2 = json2.getString("description");
            double descSimilarity = calculateCosineSimilarity(
                    desc1 == null ? "" : desc1, 
                    desc2 == null ? "" : desc2
            );
            
            // 比较输入格式
            String input1 = json1.getString("input_format");
            String input2 = json2.getString("input_format");
            double inputSimilarity = calculateCosineSimilarity(
                    input1 == null ? "" : input1, 
                    input2 == null ? "" : input2
            );
            
            // 比较输出格式
            String output1 = json1.getString("output_format");
            String output2 = json2.getString("output_format");
            double outputSimilarity = calculateCosineSimilarity(
                    output1 == null ? "" : output1, 
                    output2 == null ? "" : output2
            );
            
            // 比较样例
            double sampleSimilarity = 0;
            JSONArray samples1 = json1.getJSONArray("sample_cases");
            JSONArray samples2 = json2.getJSONArray("sample_cases");
            if (samples1 != null && samples2 != null && samples1.size() > 0 && samples2.size() > 0) {
                int minSize = Math.min(samples1.size(), samples2.size());
                double totalSimilarity = 0;
                for (int i = 0; i < minSize; i++) {
                    JSONObject sample1 = samples1.getJSONObject(i);
                    JSONObject sample2 = samples2.getJSONObject(i);
                    
                    String sampleInput1 = sample1.getString("input");
                    String sampleInput2 = sample2.getString("input");
                    double sampleInputSimilarity = calculateCosineSimilarity(
                            sampleInput1 == null ? "" : sampleInput1, 
                            sampleInput2 == null ? "" : sampleInput2
                    );
                    
                    String sampleOutput1 = sample1.getString("output");
                    String sampleOutput2 = sample2.getString("output");
                    double sampleOutputSimilarity = calculateCosineSimilarity(
                            sampleOutput1 == null ? "" : sampleOutput1, 
                            sampleOutput2 == null ? "" : sampleOutput2
                    );
                    
                    totalSimilarity += (sampleInputSimilarity + sampleOutputSimilarity) / 2;
                }
                sampleSimilarity = totalSimilarity / minSize;
            }
            
            // 描述占40%，输入输出格式占30%，样例占30%
            return descSimilarity * 0.4 + (inputSimilarity + outputSimilarity) * 0.15 + sampleSimilarity * 0.3;
        } catch (Exception e) {
            // 解析失败，返回基本文本相似度
            return calculateCosineSimilarity(content1, content2);
        }
    }
} 