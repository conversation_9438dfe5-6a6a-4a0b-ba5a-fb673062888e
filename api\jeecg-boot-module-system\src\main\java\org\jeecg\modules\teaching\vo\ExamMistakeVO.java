package org.jeecg.modules.teaching.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 错题记录VO
 * @Author: jeecg-boot
 * @Date:   2023-06-18
 * @Version: V1.0
 */
@Data
@ApiModel(value="ExamMistakeVO对象", description="错题记录视图对象")
public class ExamMistakeVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 错题记录信息
    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "题目ID")
    private String questionId;
    
    @ApiModelProperty(value = "上次错误答案")
    private String lastAnswer;
    
    @ApiModelProperty(value = "错误次数")
    private Integer mistakeCount;
    
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近一次答错时间")
    private Date lastMistakeTime;
    
    // 题目信息
    @ApiModelProperty(value = "题目标题")
    private String questionTitle;
    
    @ApiModelProperty(value = "题目类型 (1:单选题, 2:判断题, 3:编程题)")
    private Integer questionType;
    
    @ApiModelProperty(value = "所属科目 (Scratch, Python, C++)")
    private String subject;
    
    @ApiModelProperty(value = "题目级别 (Scratch:1-4, Python/C++:1-8)")
    private String level;
    
    @ApiModelProperty(value = "难度 (1:简单, 2:中等, 3:困难)")
    private Integer difficulty;
    
    @ApiModelProperty(value = "题目内容 (JSON格式)")
    private String content;
} 