package org.jeecg.modules.teaching.service;

import org.jeecg.modules.teaching.entity.ExamPaper;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date:   2023-06-20
 * @Version: V1.0
 */
public interface IExamPaperService extends IService<ExamPaper> {

    /**
     * 手动选题创建试卷
     * @param examPaper 试卷基本信息
     * @param questionIds 选中的题目ID列表
     * @param scores 每道题对应的分数
     * @return 创建的试卷ID
     */
    String createPaperWithQuestions(ExamPaper examPaper, List<String> questionIds, List<Integer> scores);
    
    /**
     * 从文本文件导入试卷
     * @param file 文本文件
     * @param author 作者
     * @return 导入结果，包含成功导入的试卷信息和题目信息
     */
    Map<String, Object> importPaperFromFile(MultipartFile file, String author);
    
    /**
     * 从文本文件导入试卷（带元数据）
     * @param file 文本文件
     * @param author 作者
     * @param metadata 额外的元数据参数
     * @return 导入结果，包含成功导入的试卷信息和题目信息
     */
    Map<String, Object> importPaperFromFile(MultipartFile file, String author, Map<String, Object> metadata);

    /**
     * 从文本文件导入试卷（支持引用模式）
     * @param file 文本文件
     * @param author 作者
     * @param metadata 额外的元数据参数
     * @param allowReference 是否允许引用重复题目（true=完整模式，false=新题模式）
     * @return 导入结果，包含成功导入的试卷信息和题目信息
     */
    Map<String, Object> importPaperFromFile(MultipartFile file, String author, Map<String, Object> metadata, boolean allowReference);

    /**
     * 预览试卷导入（只检测不实际导入）
     * @param file 文本文件
     * @param author 作者
     * @param metadata 额外的元数据参数
     * @return 预览结果，包含检测统计信息
     */
    Map<String, Object> previewPaperFromFile(MultipartFile file, String author, Map<String, Object> metadata);
    
    /**
     * 导出试卷到文本文件
     * @param paperId 试卷ID
     * @return 试卷内容文本
     */
    String exportPaperToText(String paperId);
    
    /**
     * 获取试卷的所有题目
     * @param paperId 试卷ID
     * @return 题目列表
     */
    List<Map<String, Object>> getPaperQuestions(String paperId);

    /**
     * 自动格式化试卷模板
     * @param file 原始文件
     * @param subject 科目
     * @param level 级别
     * @param difficulty 难度
     * @param type 类型
     * @param year 年份
     * @return 格式化后的内容
     * @throws Exception 格式化异常
     */
    String autoFormatPaperTemplate(MultipartFile file, String subject, String level, Integer difficulty, String type, String year) throws Exception;
}