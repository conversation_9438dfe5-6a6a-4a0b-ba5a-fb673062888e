{"@metadata": {"authors": ["아라"]}, "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "ADD_COMMENT": "Dagdag komento", "EXTERNAL_INPUTS": "Panlabas na Inputs", "INLINE_INPUTS": "Inline na Inputs", "DELETE_BLOCK": "bura<PERSON> ang bloke", "DELETE_X_BLOCKS": "burahin %1 ng bloke", "COLLAPSE_BLOCK": "bloke", "COLLAPSE_ALL": "bloke", "EXPAND_BLOCK": "Palawakin ang Block", "EXPAND_ALL": "Palawakin ang Blocks", "DISABLE_BLOCK": "Ipangwalang bisa ang Block", "ENABLE_BLOCK": "Bigyan ng bisa ang Block", "HELP": "<PERSON><PERSON>", "CHANGE_VALUE_TITLE": "pagbago ng value:", "COLOUR_PICKER_TOOLTIP": "pagpili ng kulay sa paleta.", "COLOUR_RANDOM_TITLE": "iba i<PERSON> kulay", "COLOUR_RANDOM_TOOLTIP": "pagpili ng iba't ibang kulay.", "COLOUR_RGB_TITLE": "kulayan ng", "COLOUR_RGB_RED": "pula", "COLOUR_RGB_GREEN": "berde", "COLOUR_RGB_BLUE": "asul", "COLOUR_RGB_TOOLTIP": "gumawa ng kulay ng may espisipikong dami ng kulay pula, berde, at asul. lahat ng halaga ay dapat sa pagitan ng 0 at 100.", "COLOUR_BLEND_TITLE": "halo", "COLOUR_BLEND_COLOUR1": "kulay 1", "COLOUR_BLEND_COLOUR2": "kulay 2", "COLOUR_BLEND_RATIO": "proporsyon", "COLOUR_BLEND_TOOLTIP": "Paghalo ng dalawang kulay kasama ng ibinigay na proporsyon (0.0 - 1.0).", "CONTROLS_REPEAT_TITLE": "ulitin %1 beses", "CONTROLS_REPEAT_INPUT_DO": "gawin", "CONTROLS_REPEAT_TOOLTIP": "gumawa ng ilang pangungusap ng ilang ulit.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "ulitin habang", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "ulitin hanggang", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Habang ang value ay true, gagawin ang ibang statements.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Habang ang value ay false, gagawin ang ibang statements.", "CONTROLS_FOR_TOOLTIP": "Magkaroon ng mga variable na \"%1\" na tanggalin ng mga halaga mula sa simulang numero hanggang sa dulong numero, at bilangin sa pamamagitan ng tinukoy na agwat, at gawin ang mga tinukoy na mga blocks.", "CONTROLS_FOR_TITLE": "bilangin %1 mula %2 hanggang %3 ng %4", "CONTROLS_FOREACH_TITLE": "sa bawat bagay %1 sa listahan %2", "CONTROLS_FOREACH_TOOLTIP": "Para sa bawat item sa isang list, i-set ang variable ng '%1' sa mga item, at pagkatapos ay gumawa ng ilang mga statements.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "putulin ang paulit ulit", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "Magpatuloy sa susunod na pag-ulit ng loop", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Hatiin ang nilalaman ng loop.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Laktawan ang natitirang bahagi ng loop, at magpatuloy sa susunod na pag-ulit.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Babala: Ang block ito ay maaari lamang magamit sa loob ng loop.", "CONTROLS_IF_TOOLTIP_1": "kung ang value ay true,  gagawin ang do statements.", "CONTROLS_IF_TOOLTIP_2": "Kung ang value ay true, gagawin ang unang block ng do statements. <PERSON> hindi, gagawin ang pangalawang block ng statement.", "CONTROLS_IF_TOOLTIP_3": "Kung ang unang value ay true, gagawin ang first block ng statement. Kung hindi, kung ang second value ay true, gagawin ang second block ng statement.", "CONTROLS_IF_TOOLTIP_4": "Kung ang first value ay true, gagawin ang first block ng statement. Kung hindi true ang second value, gagawin ang second block ng statement. Kung wala sa mga values ay true, gagawin ang last block ng statements.", "CONTROLS_IF_MSG_IF": "kung", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON>, remove o kaya mag reorder ng sections para maayos ang if block.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Mag dagdag ng condition sa if block.", "CONTROLS_IF_ELSE_TOOLTIP": "Mag Add ng final, kunin lahat ng condition sa if block.", "LOGIC_COMPARE_TOOLTIP_EQ": "Nag babalik ng true kung ang pinasok ay parehong magkatumbas.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Return true if both inputs are not equal to each other.", "LOGIC_COMPARE_TOOLTIP_LT": "Nag babalik ng true kung ang unang pinasok ay maliit kaysa sa pangalawang pinasok.", "LOGIC_COMPARE_TOOLTIP_LTE": "Nag babalik ng true kung ang unang pinasok ay maliit sa o katumbas sa pangalawang pinasok.", "LOGIC_COMPARE_TOOLTIP_GT": "Nagbabalik ng true kung ang unang pinasok ay mas malaki kaysa pangalawang pinasok.", "LOGIC_COMPARE_TOOLTIP_GTE": "Nag babalik ng true kung ang unang pinasok ay mas malaki or katumbas ng pangalawang pinasok.", "LOGIC_OPERATION_AND": "at", "LOGIC_OPERATION_OR": "o", "LOGIC_BOOLEAN_TRUE": "tama", "LOGIC_BOOLEAN_FALSE": "mali", "LOGIC_BOOLEAN_TOOLTIP": "Nag babalik ng true or false.", "LOGIC_NULL": "blangko", "LOGIC_TERNARY_IF_TRUE": "kung tama", "LOGIC_TERNARY_IF_FALSE": "kung mali", "MATH_IS_POSITIVE": "ay positibo", "MATH_IS_NEGATIVE": "ay negatibo", "MATH_CHANGE_TITLE": "baguhin %1 by %2", "LISTS_CREATE_EMPTY_TITLE": "Gumawa ng walang laman na list", "LISTS_CREATE_EMPTY_TOOLTIP": "Ibabalik ang list, na may haba na 0, nag lalaman ng walang data records", "LISTS_CREATE_WITH_TOOLTIP": "Gumawa ng list na may kahit anong number ng items.", "LISTS_CREATE_WITH_INPUT_WITH": "gumawa ng list kasama", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "Magdagdag, mag tanggal or mag ayos ng sections para muling maayos ang listahan ng block.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Mag dagdag ng item sa list.", "LISTS_REPEAT_TOOLTIP": "Pag gawa ng list na binubuo ng binigay na value at inulit na tinuloy na bilang ng beses.", "LISTS_REPEAT_TITLE": "pag gawa ng list kasama ng item %1 inuulit %2 beses", "LISTS_LENGTH_TITLE": "haba ng %1", "LISTS_LENGTH_TOOLTIP": "Pag balik ng haba ng list.", "LISTS_ISEMPTY_TITLE": "%1 ay walang laman", "LISTS_ISEMPTY_TOOLTIP": "Nagbabalik ng true kung ang list ay walang laman.", "LISTS_INLIST": "sa list", "LISTS_INDEX_OF_FIRST": "<PERSON><PERSON>in ang unang pangyayari ng item", "LISTS_INDEX_OF_LAST": "hanapin ang huling pangyayari ng item", "LISTS_INDEX_OF_TOOLTIP": "Pagbalik ng index ng una/huli pangyayari ng item sa list. Pagbalik ng %1 kung ang item ay hindi makita.", "LISTS_GET_INDEX_GET": "kunin", "LISTS_GET_INDEX_GET_REMOVE": "kunin at tanggalin", "LISTS_GET_INDEX_REMOVE": "<PERSON><PERSON><PERSON>n", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# mula katapusan", "LISTS_GET_INDEX_FIRST": "Una", "LISTS_GET_INDEX_LAST": "huli", "LISTS_GET_INDEX_RANDOM": "nang hindi pinipili", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 ay ang unang item.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 ay ang huling item.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Ibalik ang item sa itinakdang posisyon sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Ibalik ang unang item sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Ibalik ang huling item sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Nag babalik ng hindi pinipiling item sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Nag tatanggal at nag babalik ng mga items sa tinukoy na posisyon sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Nag tatanggal at nag babalik ng mga unang item sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Nag tatanggal at nag babalik ng huling item sa list.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Nag tatanggal at nag babalik ng mga hindi pinipiling item sa list.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Nag tatanggal ng item sa tinukoy na posisyon sa list.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Nag tatanggal ng unang item sa list.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Nag tatanggal ng huling item sa list.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Nag tatanggal ng item mula sa walang pinipiling list.", "LISTS_SET_INDEX_INSERT": "isingit sa", "LISTS_SET_INDEX_INPUT_TO": "gaya ng", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "<PERSON><PERSON> set ng item sa tinukoy na position sa isang list.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "<PERSON>g set ng unang item sa isang list.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "<PERSON><PERSON> set sa huling item sa isang list.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "<PERSON><PERSON> set ng walang pinipiling item sa isang list.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Nag singit ng item sa tinukoy na posistion sa list.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Nag singit ng item sa simula ng list.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Idagdag ang item sa huli ng isang list.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Isingit ang item ng walang pinipili sa isang list.", "LISTS_GET_SUBLIST_START_FROM_START": "pag kuha ng sub-list mula #", "LISTS_GET_SUBLIST_START_FROM_END": "pag kuha ng sub-list mula sa # mula huli", "LISTS_GET_SUBLIST_START_FIRST": "pag kuha ng sub-list mula sa una", "LISTS_GET_SUBLIST_END_FROM_START": "mula #", "LISTS_GET_SUBLIST_END_FROM_END": "mula # hanggang huli", "LISTS_GET_SUBLIST_END_LAST": "<PERSON><PERSON>g huli", "LISTS_GET_SUBLIST_TOOLTIP": "Gumagawa ng kopya ng tinukoy na bahagi ng list."}