package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserDepart;

import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.ISysConfigService;
import org.jeecg.modules.teaching.constant.RoleConstant;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleService;
import org.jeecg.modules.teaching.vo.CourseScheduleExcelVO;
import org.jeecg.modules.teaching.vo.CourseScheduleVO;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;

import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import java.util.Calendar;
import org.jeecg.modules.teaching.entity.TeachingCourseScheduleInstance;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleInstanceService;

/**
 * @Description: 课程排期
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Api(tags="课程排期")
@RestController
@RequestMapping("/teaching/coursesScheduling")
@Slf4j
public class TeachingCourseScheduleController extends JeecgController<TeachingCourseSchedule, ITeachingCourseScheduleService> {
    @Autowired
    private ITeachingCourseScheduleService teachingCourseScheduleService;

    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private ISysUserDepartService sysUserDepartService;

    @Autowired
    private ITeachingCourseNotificationService teachingCourseNotificationService;

    @Autowired
    private ISysConfigService sysConfigService;
    
    @Autowired
    private ITeachingCourseScheduleInstanceService teachingCourseScheduleInstanceService;
    
    private static final String CONFIG_KEY_NOTIFY_BEFORE_MINUTES = "courseNotifyBeforeMinutes";

    /**
     * 获取系统默认通知提前时间(分钟)
     * 
     * @return 默认通知提前时间，如果配置不存在或无效则返回15分钟
     */
    private Integer getDefaultNotifyBeforeMinutes() {
        try {
            // 从系统配置中获取默认通知时间
            String configValue = sysConfigService.getConfigItem(CONFIG_KEY_NOTIFY_BEFORE_MINUTES);
            if (configValue != null && !configValue.isEmpty()) {
                return Integer.parseInt(configValue);
            }
        } catch (Exception e) {
            log.error("获取默认通知时间配置异常", e);
        }
        // 如果获取失败，返回15分钟作为默认值
        return 15;
    }

    /**
     * 分页列表查询
     *
     * @param teachingCourseSchedule
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "课程排期-分页列表查询")
    @ApiOperation(value="课程排期-分页列表查询", notes="课程排期-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingCourseSchedule teachingCourseSchedule,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<TeachingCourseSchedule> queryWrapper = QueryGenerator.initQueryWrapper(teachingCourseSchedule, req.getParameterMap());
        
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);
        
        if (user != null) {
            Set<String> roleSet = sysUserService.getUserRolesSet(username);
            
            // 根据用户角色应用不同的数据过滤策略
            if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
                // 教师只能查看自己的课程排期
                queryWrapper.eq("teacher_id", user.getId());
            } else if (roleSet.contains(RoleConstant.STUDENT)) {
                // 学生只能查看与自己所在班级相关的课程排期
                String studentId = user.getId();
                // 查询学生所在班级
                List<String> classIds = new ArrayList<>();
                
                // 查询学生的部门关联
                QueryWrapper<SysUserDepart> userDepartQuery = new QueryWrapper<>();
                userDepartQuery.eq("user_id", studentId);
                // 获取学生所在的班级ID列表
                List<SysUserDepart> userDepartList = sysUserDepartService.list(userDepartQuery);
                
                if (userDepartList != null && !userDepartList.isEmpty()) {
                    for (SysUserDepart userDepart : userDepartList) {
                        classIds.add(userDepart.getDepId());
                    }
                    queryWrapper.in("class_id", classIds);
                } else {
                    // 如果学生不属于任何班级，则返回空结果
                    return Result.ok(new Page<CourseScheduleVO>(pageNo, pageSize));
                }
            }
            // 管理员可以查看所有数据，不需要额外过滤
        }
        
        Page<CourseScheduleVO> page = new Page<CourseScheduleVO>(pageNo, pageSize);
        IPage<CourseScheduleVO> pageList = teachingCourseScheduleService.getScheduleList(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 添加
     *
     * @param teachingCourseSchedule
     * @return
     */
    @AutoLog(value = "课程排期-添加")
    @ApiOperation(value="课程排期-添加", notes="课程排期-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TeachingCourseSchedule teachingCourseSchedule, 
                          HttpServletRequest req,
                          @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);
        
        // 处理通知字段类型转换（布尔值或字符串转为Integer）
        // 增强类型转换的稳定性，确保处理所有可能的输入类型
        try {
            if (teachingCourseSchedule.getNotifyTeachers() != null) {
                Object value = teachingCourseSchedule.getNotifyTeachers();
                
                // 检查值类型并转换为整数 1/0
                if (value instanceof Boolean) {
                    teachingCourseSchedule.setNotifyTeachers(((Boolean) value) ? 1 : 0);
                } else if (value instanceof Integer) {
                    // 如果已经是Integer，确保值为1或0
                    int intValue = (Integer) value;
                    teachingCourseSchedule.setNotifyTeachers(intValue > 0 ? 1 : 0);
                } else if (value instanceof String) {
                    // 处理字符串类型，"1", "true" 表示真，其他表示假
                    String strValue = (String) value;
                    boolean boolValue = "1".equals(strValue) || "true".equalsIgnoreCase(strValue);
                    teachingCourseSchedule.setNotifyTeachers(boolValue ? 1 : 0);
                }
            } else {
                // 未设置时使用默认值1（通知）
                teachingCourseSchedule.setNotifyTeachers(1);
            }
            
            if (teachingCourseSchedule.getNotifyStudents() != null) {
                Object value = teachingCourseSchedule.getNotifyStudents();
                
                // 检查值类型并转换为整数 1/0
                if (value instanceof Boolean) {
                    teachingCourseSchedule.setNotifyStudents(((Boolean) value) ? 1 : 0);
                } else if (value instanceof Integer) {
                    // 如果已经是Integer，确保值为1或0
                    int intValue = (Integer) value;
                    teachingCourseSchedule.setNotifyStudents(intValue > 0 ? 1 : 0);
                } else if (value instanceof String) {
                    // 处理字符串类型，"1", "true" 表示真，其他表示假
                    String strValue = (String) value;
                    boolean boolValue = "1".equals(strValue) || "true".equalsIgnoreCase(strValue);
                    teachingCourseSchedule.setNotifyStudents(boolValue ? 1 : 0);
                }
            } else {
                // 未设置时使用默认值1（通知）
                teachingCourseSchedule.setNotifyStudents(1);
            }
            
            // 记录转换结果
            log.info("转换后的通知设置：notifyTeachers={}, notifyStudents={}", 
                     teachingCourseSchedule.getNotifyTeachers(), 
                     teachingCourseSchedule.getNotifyStudents());
                     
        } catch (Exception e) {
            log.error("转换通知字段类型时出错", e);
            // 设置默认值
            teachingCourseSchedule.setNotifyTeachers(1);
            teachingCourseSchedule.setNotifyStudents(1);
        }
        
        if (user != null) {
            Set<String> roleSet = sysUserService.getUserRolesSet(username);
            
            // 教师只能添加自己的课程
            if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
                teachingCourseSchedule.setTeacherId(user.getId());
                log.info("教师 {} 正在添加课程排期", user.getId());
            }
        }

         // 如果是不重复类型的课程，明确设置repeatEndDate和weekdays为null
         if (teachingCourseSchedule.getRepeatType() != null && teachingCourseSchedule.getRepeatType() == 0) {
            log.info("添加课程时，检测到重复类型为0(不重复)，明确将repeatEndDate和weekdays设置为null");
            teachingCourseSchedule.setRepeatEndDate(null);
            teachingCourseSchedule.setWeekdays(null);
        }else if (teachingCourseSchedule.getRepeatType() != null && teachingCourseSchedule.getRepeatType() == 1) {
            // 如果是每天重复类型的课程，明确设置weekdays为null
            log.info("添加课程时，检测到重复类型为1(每天)，明确将weekdays设置为null");
            teachingCourseSchedule.setWeekdays(null);
        }
        
        // 保存课程排期
        boolean success = teachingCourseScheduleService.save(teachingCourseSchedule);
        
        // 创建课程通知
        if (success && sendNotification) {
            try {
                // 通知教师和学生
                boolean notifyTeachers = true;
                boolean notifyStudents = true;
                
                // 获取通知设置参数
                // 如果课程中指定了通知参数，使用课程的参数，否则使用默认值
                Integer notifyBeforeMinutes = teachingCourseSchedule.getNotifyBeforeMinutes();
                if (notifyBeforeMinutes == null) {
                    // 使用系统配置的默认值
                    notifyBeforeMinutes = getDefaultNotifyBeforeMinutes();
                }
                
                // 如果课程中指定了通知对象设置，使用课程的设置
                if (teachingCourseSchedule.getNotifyTeachers() != null) {
                    notifyTeachers = teachingCourseSchedule.getNotifyTeachers() == 1;
                }
                
                if (teachingCourseSchedule.getNotifyStudents() != null) {
                    notifyStudents = teachingCourseSchedule.getNotifyStudents() == 1;
                }
                
                // 调用创建新课程通知的方法
                teachingCourseNotificationService.createNewCourseNotification(teachingCourseSchedule, notifyTeachers, notifyStudents);
                
                // 获取当前系统时间
                Date now = new Date();
                // 获取课程的开始时间
                Date startTime = teachingCourseSchedule.getStartTime();
                // 计算当前时间与课程开始时间的时间差（毫秒）
                long diffInMillies = startTime.getTime() - now.getTime();
                
                // 尝试使用最高级别日志确保输出
                log.info("【日志测试】当前时间: {}", now);
                log.info("【日志测试】课程开始时间: {}", startTime);
                log.info("【日志测试】时间差(毫秒): {}", diffInMillies);
                
                // 修复Bug：使用分钟作为单位避免整数除法精度损失
                long diffInMinutes = diffInMillies / (60 * 1000);
                log.info("【日志测试】时间差(分钟): {}", diffInMinutes);
                log.info("【日志测试】提前通知时间(分钟): {}", notifyBeforeMinutes);
                
                // 判断课程是否在24小时内开始且尚未开始（时间差为正值）
                // 修复Bug：改用分钟单位判断，24小时等于1440分钟
                if (diffInMinutes <= (24 * 60) && diffInMinutes > 0) {
                    // 根据配置的提前通知时间（分钟）计算实际的提醒时间点
                    // 课程开始时间减去提前通知的分钟数
                    Date remindTime = new Date(startTime.getTime() - notifyBeforeMinutes * 60 * 1000);
                    
                    // 信息日志
                    log.info("计算的提醒时间: {}", remindTime);
                    log.info("是否早于当前时间: {}", remindTime.before(now));
                    
                    // 检查计算出的提醒时间是否已经过去
                    // 如果提醒时间已过（早于当前时间），则将提醒时间设置为当前时间后5分钟
                    if (remindTime.before(now)) {
                        // 计算调整后的时间（当前时间后5分钟，并确保分钟为偶数，秒数为00）
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(now);
                        calendar.add(Calendar.MINUTE, 5); // 先加5分钟
                        
                        // 调整分钟为偶数
                        int minute = calendar.get(Calendar.MINUTE);
                        if (minute % 2 != 0) {
                            calendar.add(Calendar.MINUTE, 1); // 如果是奇数，加1变成偶数
                        }
                        
                        // 调整秒数为00
                        calendar.set(Calendar.SECOND, 0);
                        calendar.set(Calendar.MILLISECOND, 0);
                        
                        remindTime = calendar.getTime();
                        log.info("提醒时间已过，调整为当前时间后（偶数分钟，秒数00）: {}", remindTime);
                    }
                    
                    // 信息日志
                    log.info("最终提醒时间: {}", remindTime);
                    log.info("通知教师: {}, 通知学生: {}", notifyTeachers, notifyStudents);
                    
                    // 调用服务创建课程提醒通知
                    // 参数：课程排期对象、提醒时间、是否通知教师、是否通知学生
                    teachingCourseNotificationService.createCourseReminderNotification(
                        teachingCourseSchedule, remindTime, notifyTeachers, notifyStudents);
                    
                    // 新增代码：标记课程已创建提醒通知，防止CourseUpcomingReminderJob再次创建提醒
                    teachingCourseScheduleService.markCourseReminderCreated(teachingCourseSchedule.getId());
                    
                    log.info("课程提醒通知创建完成，并已标记reminder_created=1");
                } else {
                    log.info("课程不在24小时内开始或已开始，不创建提醒通知");
                }
            } catch (Exception e) {
                log.error("创建课程通知失败", e);
            }
        }
        
        return Result.ok("添加成功！");
    }
    
    /**
     * 编辑
     *
     * @param teachingCourseSchedule
     * @return
     */
    @AutoLog(value = "课程排期-编辑")
    @ApiOperation(value="课程排期-编辑", notes="课程排期-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody TeachingCourseSchedule teachingCourseSchedule, 
                           HttpServletRequest req,
                           @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);
        
        // 处理通知字段类型转换（布尔值或字符串转为Integer）
        // 增强类型转换的稳定性，确保处理所有可能的输入类型
        try {
            if (teachingCourseSchedule.getNotifyTeachers() != null) {
                Object value = teachingCourseSchedule.getNotifyTeachers();
                
                // 检查值类型并转换为整数 1/0
                if (value instanceof Boolean) {
                    teachingCourseSchedule.setNotifyTeachers(((Boolean) value) ? 1 : 0);
                } else if (value instanceof Integer) {
                    // 如果已经是Integer，确保值为1或0
                    int intValue = (Integer) value;
                    teachingCourseSchedule.setNotifyTeachers(intValue > 0 ? 1 : 0);
                } else if (value instanceof String) {
                    // 处理字符串类型，"1", "true" 表示真，其他表示假
                    String strValue = (String) value;
                    boolean boolValue = "1".equals(strValue) || "true".equalsIgnoreCase(strValue);
                    teachingCourseSchedule.setNotifyTeachers(boolValue ? 1 : 0);
                }
            } else {
                // 未设置时使用默认值1（通知）
                teachingCourseSchedule.setNotifyTeachers(1);
            }
            
            if (teachingCourseSchedule.getNotifyStudents() != null) {
                Object value = teachingCourseSchedule.getNotifyStudents();
                
                // 检查值类型并转换为整数 1/0
                if (value instanceof Boolean) {
                    teachingCourseSchedule.setNotifyStudents(((Boolean) value) ? 1 : 0);
                } else if (value instanceof Integer) {
                    // 如果已经是Integer，确保值为1或0
                    int intValue = (Integer) value;
                    teachingCourseSchedule.setNotifyStudents(intValue > 0 ? 1 : 0);
                } else if (value instanceof String) {
                    // 处理字符串类型，"1", "true" 表示真，其他表示假
                    String strValue = (String) value;
                    boolean boolValue = "1".equals(strValue) || "true".equalsIgnoreCase(strValue);
                    teachingCourseSchedule.setNotifyStudents(boolValue ? 1 : 0);
                }
            } else {
                // 未设置时使用默认值1（通知）
                teachingCourseSchedule.setNotifyStudents(1);
            }
            
            // 记录转换结果
            log.info("编辑时转换后的通知设置：notifyTeachers={}, notifyStudents={}", 
                     teachingCourseSchedule.getNotifyTeachers(), 
                     teachingCourseSchedule.getNotifyStudents());
                     
        } catch (Exception e) {
            log.error("编辑时转换通知字段类型时出错", e);
            // 设置默认值
            teachingCourseSchedule.setNotifyTeachers(1);
            teachingCourseSchedule.setNotifyStudents(1);
        }
        
        // 保存修改前的课程信息，用于比较变更
        TeachingCourseSchedule oldSchedule = teachingCourseScheduleService.getById(teachingCourseSchedule.getId());
        if (oldSchedule == null) {
            return Result.error("未找到原课程排期信息");
        }
        
        if (user != null) {
            Set<String> roleSet = sysUserService.getUserRolesSet(username);
            
            // 教师只能编辑自己的课程
            if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
                // 从请求属性中获取当前教师ID（由拦截器设置）
                String currentTeacherId = (String) req.getAttribute("currentTeacherId");
                
                // 如果拦截器没有设置，则使用用户ID
                if (currentTeacherId == null) {
                    currentTeacherId = user.getId();
                }
                
                // 检查原始课程排期是否属于该教师
                if (!teachingCourseScheduleService.isTeacherOfSchedule(teachingCourseSchedule.getId(), currentTeacherId)) {
                    return Result.error("您没有权限修改此课程排期！");
                }
                
                // 强制设置教师ID为当前用户ID，防止修改为其他教师
                teachingCourseSchedule.setTeacherId(currentTeacherId);
                
                // 记录日志
                log.info("教师 {} 正在修改课程排期 {}", currentTeacherId, teachingCourseSchedule.getId());
            }
        }

        // 针对不重复类型，使用UpdateWrapper明确更新repeatEndDate为null
        boolean success;
        if (teachingCourseSchedule.getRepeatType() != null && teachingCourseSchedule.getRepeatType() == 0) {
            log.info("检测到重复类型为0(不重复)，明确将repeatEndDate和weekdays设置为null");
            
            // 先更新实体对象
            success = teachingCourseScheduleService.updateById(teachingCourseSchedule);
            
            // 然后使用UpdateWrapper明确指定更新repeatEndDate和weekdays为null
            UpdateWrapper<TeachingCourseSchedule> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", teachingCourseSchedule.getId());
            updateWrapper.set("repeat_end_date", null);
            updateWrapper.set("week_days", null);
            
            // 执行更新
            teachingCourseScheduleService.update(updateWrapper);
            
            log.info("已使用UpdateWrapper将repeatEndDate和weekdays明确更新为null");
        } else if (teachingCourseSchedule.getRepeatType() != null && teachingCourseSchedule.getRepeatType() == 1) {
            // 针对每天重复类型，只将weekdays设置为null
            log.info("检测到重复类型为1(每天)，明确将weekdays设置为null");
            
            // 先更新实体对象
            success = teachingCourseScheduleService.updateById(teachingCourseSchedule);
            
            // 然后使用UpdateWrapper明确指定更新weekdays为null
            UpdateWrapper<TeachingCourseSchedule> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", teachingCourseSchedule.getId());
            updateWrapper.set("week_days", null);
            
            // 执行更新
            teachingCourseScheduleService.update(updateWrapper);
            
            log.info("已使用UpdateWrapper将weekdays明确更新为null");
        } else {
            // 对于其他重复类型，使用常规更新方法
            success = teachingCourseScheduleService.updateById(teachingCourseSchedule);
        }

        // 创建课程变更通知
        if (success && sendNotification) {
            try {
                // 通知教师和学生
                boolean notifyTeachers = true;
                boolean notifyStudents = true;
                
                // 获取通知设置参数
                // 如果课程中指定了通知参数，使用课程的参数，否则使用默认值
                Integer notifyBeforeMinutes = teachingCourseSchedule.getNotifyBeforeMinutes();
                if (notifyBeforeMinutes == null) {
                    // 使用系统配置的默认值
                    notifyBeforeMinutes = getDefaultNotifyBeforeMinutes();
                }
                
                // 如果课程中指定了通知对象设置，使用课程的设置
                if (teachingCourseSchedule.getNotifyTeachers() != null) {
                    notifyTeachers = teachingCourseSchedule.getNotifyTeachers() == 1;
                }
                
                if (teachingCourseSchedule.getNotifyStudents() != null) {
                    notifyStudents = teachingCourseSchedule.getNotifyStudents() == 1;
                }
                
                // 检查关键字段是否有变化
                boolean hasSignificantChanges = hasSignificantChanges(oldSchedule, teachingCourseSchedule);
                
                if (hasSignificantChanges) {
                    // 调用创建课程变更通知的方法
                    teachingCourseNotificationService.createUpdateCourseNotification(
                        teachingCourseSchedule, oldSchedule, notifyTeachers, notifyStudents);
                }
                
                // 检查课程时间是否有变化，如果有，更新提醒时间
                boolean timeChanged = !oldSchedule.getStartTime().equals(teachingCourseSchedule.getStartTime());
                if (timeChanged) {
                    // 如果课程在24小时内开始，创建提醒通知
                    Date now = new Date();
                    Date startTime = teachingCourseSchedule.getStartTime();
                    long diffInMillies = startTime.getTime() - now.getTime();
                    long diffInMinutes = diffInMillies / (60 * 1000);
                    
                    if (diffInMinutes <= (24 * 60) && diffInMinutes > 0) {
                        // 课程即将开始，创建提醒通知（使用配置的提醒时间）
                        Date remindTime = new Date(startTime.getTime() - notifyBeforeMinutes * 60 * 1000);
                        // 如果提醒时间已过，则设置为当前时间之后5分钟
                        if (remindTime.before(now)) {
                            // 计算调整后的时间（当前时间后5分钟，并确保分钟为偶数，秒数为00）
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(now);
                            calendar.add(Calendar.MINUTE, 5); // 先加5分钟
                            
                            // 调整分钟为偶数
                            int minute = calendar.get(Calendar.MINUTE);
                            if (minute % 2 != 0) {
                                calendar.add(Calendar.MINUTE, 1); // 如果是奇数，加1变成偶数
                            }
                            
                            // 调整秒数为00
                            calendar.set(Calendar.SECOND, 0);
                            calendar.set(Calendar.MILLISECOND, 0);
                            
                            remindTime = calendar.getTime();
                            log.info("提醒时间已过，调整为当前时间后（偶数分钟，秒数00）: {}", remindTime);
                        }
                        
                        teachingCourseNotificationService.createCourseReminderNotification(
                            teachingCourseSchedule, remindTime, notifyTeachers, notifyStudents);
                        // 新增代码：标记课程已创建提醒通知，防止CourseUpcomingReminderJob再次创建提醒
                        teachingCourseScheduleService.markCourseReminderCreated(teachingCourseSchedule.getId());
                        log.info("课程提醒通知创建完成，并已标记reminder_created=1");   
                    }
                }
            } catch (Exception e) {
                log.error("创建课程变更通知失败", e);
            }
        }
        
        return Result.ok("编辑成功!");
    }
    
    /**
     * 检查课程是否有重要变更
     * 
     * @param oldSchedule 原课程信息
     * @param newSchedule 新课程信息
     * @return 是否有重要变更
     */
    private boolean hasSignificantChanges(TeachingCourseSchedule oldSchedule, TeachingCourseSchedule newSchedule) {
        // 检查时间变更
        if (!oldSchedule.getStartTime().equals(newSchedule.getStartTime()) || 
            !oldSchedule.getEndTime().equals(newSchedule.getEndTime())) {
            return true;
        }
        
        // 检查教室变更
        if (!StringUtils.equals(oldSchedule.getClassroomId(), newSchedule.getClassroomId())) {
            return true;
        }
        
        // 检查班级变更
        if (!StringUtils.equals(oldSchedule.getClassId(), newSchedule.getClassId())) {
            return true;
        }
        
        // 检查教师变更
        if (!StringUtils.equals(oldSchedule.getTeacherId(), newSchedule.getTeacherId())) {
            return true;
        }
        
        // 检查标题变更
        if (!StringUtils.equals(oldSchedule.getScheduleTitle(), newSchedule.getScheduleTitle())) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "课程排期-通过id删除")
    @ApiOperation(value="课程排期-通过id删除", notes="课程排期-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id, 
                             HttpServletRequest req,
                             @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);
        
        // 获取待删除的课程信息
        TeachingCourseSchedule schedule = teachingCourseScheduleService.getById(id);
        if (schedule == null) {
            return Result.error("未找到课程排期信息");
        }
        
        if (user != null) {
            Set<String> roleSet = sysUserService.getUserRolesSet(username);
            
            // 教师只能删除自己的课程
            if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
                // 检查原始课程排期是否属于该教师
                if (!teachingCourseScheduleService.isTeacherOfSchedule(id, user.getId())) {
                    return Result.error("您没有权限删除此课程排期！");
                }
            }
        }
        
        // 创建取消课程通知
        if (sendNotification) {
            try {
                // 通知教师和学生
                boolean notifyTeachers = true;
                boolean notifyStudents = true;
                
                // 调用创建取消课程通知的方法
                teachingCourseNotificationService.createCancelCourseNotification(
                    schedule, notifyTeachers, notifyStudents);
            } catch (Exception e) {
                log.error("创建课程取消通知失败", e);
            }
        }
        
        // 删除课程排期
        teachingCourseScheduleService.removeById(id);
        return Result.ok("删除成功!");
    }
    
    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "课程排期-批量删除")
    @ApiOperation(value="课程排期-批量删除", notes="课程排期-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids, HttpServletRequest req) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);
        
        if (user != null) {
            Set<String> roleSet = sysUserService.getUserRolesSet(username);
            
            // 教师只能删除自己的课程
            if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
                // 检查所有要删除的课程排期是否都属于该教师
                String[] idArray = ids.split(",");
                for (String id : idArray) {
                    if (!teachingCourseScheduleService.isTeacherOfSchedule(id, user.getId())) {
                        return Result.error("您没有权限删除ID为 " + id + " 的课程排期！");
                    }
                }
            }
        }
        
        this.teachingCourseScheduleService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }
    
    /**
     * 检查时间冲突
     *
     * @param teachingCourseSchedule
     * @return
     */
    @AutoLog(value = "课程排期-检查时间冲突")
    @ApiOperation(value="课程排期-检查时间冲突", notes="课程排期-检查时间冲突")
    @GetMapping(value = "/checkConflict")
    public Result<?> checkConflict(TeachingCourseSchedule teachingCourseSchedule, 
                                 @RequestParam(name="weekdays", required=false) String weekdays) {
        try {
            // 处理weekdays参数 - 直接使用传入的字符串形式参数
            if (weekdays != null && !weekdays.isEmpty()) {
                teachingCourseSchedule.setWeekdays(weekdays);
                log.info("接收到weekdays参数: {}", weekdays);
            } else {
                log.info("未接收到weekdays参数或参数为空");
            }
            
            // 校验关键参数
            if (teachingCourseSchedule.getStartTime() == null || teachingCourseSchedule.getEndTime() == null) {
                return Result.error("开始时间或结束时间不能为空");
            }
            
            // 记录完整的请求参数用于调试
            log.info("检查冲突请求参数: {}", teachingCourseSchedule);
            
        return teachingCourseScheduleService.checkConflict(teachingCourseSchedule);
        } catch (Exception e) {
            log.error("检查时间冲突时发生异常", e);
            return Result.error("检查时间冲突失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载Excel导入模板
     *
     * @param request
     * @param response
     */
    @AutoLog(value = "课程排期-下载导入模板")
    @ApiOperation(value="课程排期-下载导入模板", notes="课程排期-下载导入模板")
    @RequestMapping(value = "/downloadTemplate", method = RequestMethod.GET)
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        String templatePath = teachingCourseScheduleService.generateTemplate();
        if (templatePath == null) {
            try {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("生成模板失败");
            } catch (IOException e) {
                log.error("响应写入失败", e);
            }
            return;
        }
        
        java.io.InputStream inputStream = null;
        java.io.OutputStream outputStream = null;
        File file = null;
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=course_schedule_template.xlsx");
            
            // 读取模板文件并写入响应流
            file = new File(templatePath);
            inputStream = new java.io.FileInputStream(file);
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            outputStream.flush();
        } catch (IOException e) {
            log.error("下载Excel模板失败", e);
            try {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("下载模板失败");
            } catch (IOException ex) {
                log.error("响应写入失败", ex);
            }
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭输出流失败", e);
                }
            }
            // 删除临时文件
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }
    
    /**
     * 导入Excel
     *
     * @param request
     * @param response
     * @return
     */
    @AutoLog(value = "课程排期-导入Excel")
    @ApiOperation(value="课程排期-导入Excel", notes="课程排期-导入Excel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        
        // 错误信息
        List<String> errorMessages = new ArrayList<>();
        
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            
            // 使用try-with-resources自动关闭输入流
            try (InputStream inputStream = file.getInputStream()) {
                List<CourseScheduleExcelVO> list = ExcelImportUtil.importExcel(inputStream, CourseScheduleExcelVO.class, params);
                return teachingCourseScheduleService.importScheduleList(list);
            } catch (Exception e) {
                log.error("导入Excel异常", e);
                errorMessages.add("导入Excel失败: " + e.getMessage());
            }
        }
        
        if (errorMessages.size() > 0) {
            return Result.error("导入失败: " + String.join(", ", errorMessages));
        }
        
        return Result.error("导入失败，请检查文件格式");
    }

    @AutoLog(value = "课程排期-导出模板")
    @ApiOperation(value="课程排期-导出模板", notes="课程排期-导出模板")
    @GetMapping(value = "/exportTemplate")
    public Result<?> exportTemplate(HttpServletResponse response) {
        try {
            String templatePath = teachingCourseScheduleService.generateTemplate();
            if (templatePath != null) {
                // 返回模板路径
                Map<String, String> result = new HashMap<>();
                result.put("templateUrl", templatePath);
                return Result.ok(result);
            } else {
                return Result.error("生成模板失败");
            }
        } catch (Exception e) {
            log.error("导出模板失败", e);
            return Result.error("导出模板失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取已删除的单次课程记录
     */
    @AutoLog(value = "课程排期-获取已删除的单次课程记录")
    @ApiOperation(value="课程排期-获取已删除的单次课程记录", notes="获取所有被标记为删除的单次重复课程记录")
    @GetMapping(value = "/getDeletedInstances")
    public Result<?> getDeletedInstances() {
        Map<String, List<String>> deletedInstances = teachingCourseScheduleService.getDeletedInstances();
        return Result.ok(deletedInstances);
    }
    
    /**
     * 编辑单次课程实例
     * 
     * @param teachingCourseSchedule 课程排期对象
     * @param req HttpServletRequest
     * @param instanceDate 实例日期 (格式: yyyy-MM-dd)
     * @param sendNotification 是否发送通知
     * @return Result 处理结果
     */
    @AutoLog(value = "课程排期-编辑单次课程")
    @ApiOperation(value="课程排期-编辑单次课程", notes="课程排期-编辑单次课程")
    @PutMapping(value = "/editSingleInstance")
    public Result<?> editSingleInstance(@RequestBody TeachingCourseSchedule teachingCourseSchedule, 
                                      HttpServletRequest req, 
                                      @RequestParam(name="instanceDate", required=true) String instanceDate,
                                      @RequestParam(name="originalDate", required=false) String originalDate,
                                      @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        // 检查原始排期是否存在
        if (StringUtils.isEmpty(teachingCourseSchedule.getId())) {
            return Result.error("课程ID不能为空");
        }
        
        TeachingCourseSchedule originalSchedule = teachingCourseScheduleService.getById(teachingCourseSchedule.getId());
        if (originalSchedule == null) {
            return Result.error("要编辑的课程排期不存在");
        }
        
        try {
            // 查询是否已存在已修改的单次课程实例
            TeachingCourseScheduleInstance existingInstance = null;
            
            // 如果请求中包含instanceId，尝试直接通过ID获取实例
            String instanceId = req.getParameter("instanceId");
            if (StringUtils.isNotEmpty(instanceId)) {
                log.info("通过实例ID获取单次课程实例: {}", instanceId);
                existingInstance = teachingCourseScheduleInstanceService.getById(instanceId);
                if (existingInstance != null) {
                    log.info("找到已修改的单次课程实例(通过ID): {}", existingInstance.getId());
                }
            }
            
            // 如果没有找到实例，尝试通过parentId和instanceDate获取
            if (existingInstance == null) {
                existingInstance = teachingCourseScheduleInstanceService
                .getInstanceByParentIdAndDate(teachingCourseSchedule.getId(), instanceDate);
                if (existingInstance != null) {
                    log.info("找到已修改的单次课程实例(通过日期): {}", existingInstance.getId());
                }
            }
            
            // 不存在则创建新的单次课程实例
            if (existingInstance == null) {
                existingInstance = new TeachingCourseScheduleInstance();
                
                // 从父课程复制基本信息
                existingInstance.setParentId(originalSchedule.getId());
                // 确保设置课程ID
                existingInstance.setCourseId(teachingCourseSchedule.getCourseId() != null ? 
                                            teachingCourseSchedule.getCourseId() : 
                                            originalSchedule.getCourseId());
                existingInstance.setTeacherId(teachingCourseSchedule.getTeacherId() != null ?
                                             teachingCourseSchedule.getTeacherId() :
                                             originalSchedule.getTeacherId());
                existingInstance.setClassId(teachingCourseSchedule.getClassId() != null ?
                                           teachingCourseSchedule.getClassId() :
                                           originalSchedule.getClassId());
                existingInstance.setStudentNames(teachingCourseSchedule.getStudentNames() != null ?
                                               teachingCourseSchedule.getStudentNames() :
                                               originalSchedule.getStudentNames());
                existingInstance.setStatus(originalSchedule.getStatus());
                existingInstance.setNotifyBeforeMinutes(teachingCourseSchedule.getNotifyBeforeMinutes() != null ?
                                                      teachingCourseSchedule.getNotifyBeforeMinutes() :
                                                      originalSchedule.getNotifyBeforeMinutes());
                existingInstance.setNotifyTeachers(teachingCourseSchedule.getNotifyTeachers() != null ?
                                                 teachingCourseSchedule.getNotifyTeachers() :
                                                 originalSchedule.getNotifyTeachers());
                existingInstance.setNotifyStudents(teachingCourseSchedule.getNotifyStudents() != null ?
                                                 teachingCourseSchedule.getNotifyStudents() :
                                                 originalSchedule.getNotifyStudents());
                
                // 设置创建者信息
                existingInstance.setCreateBy(user.getUsername());
                existingInstance.setCreateTime(new Date());
                
                log.info("创建新的单次课程实例");
            } else {
                // 确保更新课程ID
                if (existingInstance.getCourseId() == null) {
                    existingInstance.setCourseId(teachingCourseSchedule.getCourseId() != null ? 
                                               teachingCourseSchedule.getCourseId() : 
                                               originalSchedule.getCourseId());
                }
                
                // 更新修改者信息
                existingInstance.setUpdateBy(user.getUsername());
                existingInstance.setUpdateTime(new Date());
                
                log.info("更新现有的单次课程实例: {}", existingInstance.getId());
            }
            
            // 设置实例日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date instanceDateObj = sdf.parse(instanceDate);
            existingInstance.setInstanceDate(instanceDateObj);
            
            // 更新可编辑信息
            existingInstance.setClassroomId(teachingCourseSchedule.getClassroomId());
            existingInstance.setScheduleTitle(teachingCourseSchedule.getScheduleTitle());
            existingInstance.setStartTime(teachingCourseSchedule.getStartTime());
            existingInstance.setEndTime(teachingCourseSchedule.getEndTime());
            existingInstance.setColor(teachingCourseSchedule.getColor());
            existingInstance.setDescription(teachingCourseSchedule.getDescription());
            existingInstance.setNotifyBeforeMinutes(teachingCourseSchedule.getNotifyBeforeMinutes());
            existingInstance.setNotifyTeachers(teachingCourseSchedule.getNotifyTeachers());
            existingInstance.setNotifyStudents(teachingCourseSchedule.getNotifyStudents());
            
            // 在调用addOrUpdateInstance之前记录日志
            log.info("编辑单次课程实例参数: parentId={}, courseId={}, instanceDate={}, originalDate={}", 
                     existingInstance.getParentId(), 
                     existingInstance.getCourseId(), 
                     instanceDate,
                     originalDate);
            
            // 新增：如果是拖拽场景（originalDate不为空且与instanceDate不同），则传递originalDate
            if (StringUtils.isNotEmpty(originalDate) && !originalDate.equals(instanceDate)) {
                log.info("检测到拖拽场景，原始日期: {}, 新日期: {}", originalDate, instanceDate);
                return teachingCourseScheduleInstanceService.addOrUpdateInstanceWithOriginalDate(existingInstance, originalDate, sendNotification);
            } else {
                // 原有逻辑，非拖拽场景
            return teachingCourseScheduleInstanceService.addOrUpdateInstance(existingInstance, sendNotification);
            }
        } catch (Exception e) {
            log.error("编辑单次课程实例异常", e);
            return Result.error("编辑单次课程实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取单次课程实例
     * 
     * @param id 父课程ID
     * @param instanceDate 实例日期 (格式: yyyy-MM-dd)
     * @param req HttpServletRequest
     * @return Result 处理结果
     */
    @AutoLog(value = "课程排期-获取单次课程实例")
    @ApiOperation(value="课程排期-获取单次课程实例", notes="课程排期-获取单次课程实例")
    @GetMapping(value = "/getSingleInstance")
    public Result<?> getSingleInstance(@RequestParam(name="id", required=true) String id,
                                     @RequestParam(name="instanceDate", required=true) String instanceDate,
                                     HttpServletRequest req) {
        // 获取原始课程排期
        TeachingCourseSchedule originalSchedule = teachingCourseScheduleService.getById(id);
        if (originalSchedule == null) {
            return Result.error("课程排期不存在");
        }
        
        // 查询是否存在已修改的单次课程实例
        TeachingCourseScheduleInstance instance = teachingCourseScheduleInstanceService
            .getInstanceByParentIdAndDate(id, instanceDate);
        
        // 存在已修改的单次课程实例，直接返回
        if (instance != null) {
            return Result.ok(instance);
        }
        
        // 不存在则检查是否在删除列表中
        if (originalSchedule.getDeletedInstances() != null && 
            originalSchedule.getDeletedInstances().contains(instanceDate)) {
            return Result.error("该课程实例已被删除");
        }
        
        // 返回原始课程排期
        return Result.ok(originalSchedule);
    }
    
    /**
     * 删除单次课程记录
     */
    @AutoLog(value = "课程排期-删除单次课程记录")
    @ApiOperation(value="课程排期-删除单次课程记录", notes="标记重复课程中的单次课程为已删除状态")
    @PostMapping(value = "/deleteSingleInstanceOriginal")
    public Result<?> deleteSingleInstanceOriginal(@RequestParam(name="parentId", required=true) String parentId,
                                           @RequestParam(name="instanceDate", required=true) String instanceDate,
                                           HttpServletRequest req,
                                           @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        // 获取父课程信息
        TeachingCourseSchedule parentSchedule = teachingCourseScheduleService.getById(parentId);
        if (parentSchedule == null) {
            return Result.error("未找到父课程排期信息");
        }
        
        if (user != null) {
           Set<String> roleSet = sysUserService.getUserRolesSet(username);
            
            // 教师只能删除自己的课程
            if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
                // 检查原始课程排期是否属于该教师
                if (!teachingCourseScheduleService.isTeacherOfSchedule(parentId, user.getId())) {
                    return Result.error("您没有权限删除此课程排期！");
                }
            }
        }
        
        // 删除单次课程实例
        boolean success = teachingCourseScheduleService.deleteSingleInstance(parentId, instanceDate);
        
        // 如果删除成功且需要发送通知
        if (success && sendNotification) {
            try {
                // 通知教师和学生
                boolean notifyTeachers = true;
                boolean notifyStudents = true;
                
                // 获取通知设置参数
                // 如果课程中指定了通知参数，使用课程的参数，否则使用默认值
                Integer notifyBeforeMinutes = parentSchedule.getNotifyBeforeMinutes();
                if (notifyBeforeMinutes == null) {
                    // 使用系统配置的默认值
                    notifyBeforeMinutes = getDefaultNotifyBeforeMinutes();
                }
                
                // 如果课程中指定了通知对象设置，使用课程的设置
                if (parentSchedule.getNotifyTeachers() != null) {
                    notifyTeachers = parentSchedule.getNotifyTeachers() == 1;
                }
                
                if (parentSchedule.getNotifyStudents() != null) {
                    notifyStudents = parentSchedule.getNotifyStudents() == 1;
                }
                
                // 克隆父课程信息，修改日期为删除的实例日期
                TeachingCourseSchedule instanceSchedule = new TeachingCourseSchedule();
                instanceSchedule.setId(parentSchedule.getId());
                instanceSchedule.setScheduleTitle(parentSchedule.getScheduleTitle());
                instanceSchedule.setTeacherId(parentSchedule.getTeacherId());
                instanceSchedule.setClassId(parentSchedule.getClassId());
                instanceSchedule.setClassroomId(parentSchedule.getClassroomId());
                
                // 解析删除的实例日期
                try {
                    // 解析日期
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date instanceDateTime = dateFormat.parse(instanceDate);
                    
                    // 获取父课程的时间
                    Date parentStartTime = parentSchedule.getStartTime();
                    Date parentEndTime = parentSchedule.getEndTime();
                    
                    // 创建实例的开始和结束时间
                    Calendar cal = Calendar.getInstance();
                    
                    // 设置开始时间
                    cal.setTime(instanceDateTime);
                    Calendar startCal = Calendar.getInstance();
                    startCal.setTime(parentStartTime);
                    cal.set(Calendar.HOUR_OF_DAY, startCal.get(Calendar.HOUR_OF_DAY));
                    cal.set(Calendar.MINUTE, startCal.get(Calendar.MINUTE));
                    cal.set(Calendar.SECOND, startCal.get(Calendar.SECOND));
                    instanceSchedule.setStartTime(cal.getTime());
                    
                    // 设置结束时间
                    cal.setTime(instanceDateTime);
                    Calendar endCal = Calendar.getInstance();
                    endCal.setTime(parentEndTime);
                    cal.set(Calendar.HOUR_OF_DAY, endCal.get(Calendar.HOUR_OF_DAY));
                    cal.set(Calendar.MINUTE, endCal.get(Calendar.MINUTE));
                    cal.set(Calendar.SECOND, endCal.get(Calendar.SECOND));
                    instanceSchedule.setEndTime(cal.getTime());
                    
                    // 创建取消课程通知
                    teachingCourseNotificationService.createCancelCourseNotification(
                        instanceSchedule, notifyTeachers, notifyStudents);
                } catch (ParseException e) {
                    log.error("解析日期失败", e);
                }
            } catch (Exception e) {
                log.error("创建课程取消通知失败", e);
            }
        }
        
        if (success) {
            return Result.ok("删除单次课程成功！");
        } else {
            return Result.error("删除单次课程失败！");
        }
    }

    /**
     * 删除单次课程实例
     * 
     * @param parentId 父课程ID
     * @param instanceDate 实例日期 (格式: yyyy-MM-dd)
     * @param req HttpServletRequest
     * @param sendNotification 是否发送通知
     * @return Result 处理结果
     */
    @AutoLog(value = "课程排期-删除单次课程")
    @ApiOperation(value="课程排期-删除单次课程", notes="课程排期-删除单次课程")
    @PostMapping(value = "/deleteSingleInstance")
    public Result<?> deleteSingleInstance(@RequestParam(name="parentId", required=true) String parentId,
                                        @RequestParam(name="instanceDate", required=true) String instanceDate,
                                        HttpServletRequest req,
                                        @RequestParam(name="sendNotification", defaultValue="true") boolean sendNotification) {
        // 添加日志记录接收到的参数
        log.info("deleteSingleInstance 接收到的参数: parentId={}, instanceDate={}, sendNotification={}", 
                parentId, instanceDate, sendNotification);
                
        // 获取当前用户
        String token = req.getHeader("X-Access-Token");
        String username = JwtUtil.getUsername(token);
        SysUser user = sysUserService.getUserByName(username);

        // 检查权限
        Set<String> roleSet = sysUserService.getUserRolesSet(username);
        // 只有管理员和教师可以删除课程
        if (!roleSet.contains(RoleConstant.ADMIN) && !roleSet.contains(RoleConstant.TEACHER)) {
            return Result.error("您没有权限删除课程排期！");
        }
        
        // 查询父课程信息
        TeachingCourseSchedule parentSchedule = teachingCourseScheduleService.getById(parentId);
        if (parentSchedule == null) {
            return Result.error("未找到父课程排期信息");
        }
        
        // 如果是教师角色，检查是否是自己的课程
        if (roleSet.contains(RoleConstant.TEACHER) && !roleSet.contains(RoleConstant.ADMIN)) {
            if (!teachingCourseScheduleService.isTeacherOfSchedule(parentId, user.getId())) {
                return Result.error("您没有权限删除此课程排期！");
            }
        }
        
        // 检查是否存在已修改的单次课程实例
        TeachingCourseScheduleInstance existingInstance = teachingCourseScheduleInstanceService
            .getInstanceByParentIdAndDate(parentId, instanceDate);
        
        // 处理两种不同的删除场景，分别使用不同的逻辑
        if (existingInstance != null) {
            // 已修改的单次课程实例，直接从数据库中删除
            // 根据核心需求，对已修改的单次课程进行删除时，只需要直接从实例表中删除该条记录
            // 不需要在父课程的deleted_instances字段中记录日期
            String instanceId = existingInstance.getId();
            log.info("删除已修改的单次课程实例, instanceId={}, 日期={}", instanceId, instanceDate);
            return teachingCourseScheduleInstanceService.deleteInstance(instanceId, sendNotification);
        } else {
            // 未修改的单次课程，将日期添加到父课程的deleted_instances中
            // 根据核心需求，删除未修改的单次课程时，只需要将该课程的日期记录到父课程的deleted_instances字段中
            log.info("删除未修改的单次课程, parentId={}, 日期={}", parentId, instanceDate);
            boolean success = teachingCourseScheduleService.deleteSingleInstance(parentId, instanceDate);
            
            // 如果删除成功且需要发送通知
            if (success && sendNotification) {
                try {
                    // 通知教师和学生
                    boolean notifyTeachers = true;
                    boolean notifyStudents = true;
                    
                    // 设置通知参数
                    if (parentSchedule.getNotifyTeachers() != null) {
                        notifyTeachers = parentSchedule.getNotifyTeachers() == 1;
                    }
                    
                    if (parentSchedule.getNotifyStudents() != null) {
                        notifyStudents = parentSchedule.getNotifyStudents() == 1;
                    }
                    
                    // 克隆父课程信息，修改日期为删除的实例日期
                    TeachingCourseSchedule instanceSchedule = new TeachingCourseSchedule();
                    instanceSchedule.setId(parentSchedule.getId());
                    instanceSchedule.setScheduleTitle(parentSchedule.getScheduleTitle());
                    instanceSchedule.setTeacherId(parentSchedule.getTeacherId());
                    instanceSchedule.setClassId(parentSchedule.getClassId());
                    instanceSchedule.setClassroomId(parentSchedule.getClassroomId());
                    
                    // 解析删除的实例日期
                    try {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        Date instanceDateTime = dateFormat.parse(instanceDate);
                        
                        // 获取父课程的时间
                        Date parentStartTime = parentSchedule.getStartTime();
                        Date parentEndTime = parentSchedule.getEndTime();
                        
                        // 创建实例的开始和结束时间
                        Calendar cal = Calendar.getInstance();
                        
                        // 设置开始时间
                        cal.setTime(instanceDateTime);
                        Calendar startCal = Calendar.getInstance();
                        startCal.setTime(parentStartTime);
                        cal.set(Calendar.HOUR_OF_DAY, startCal.get(Calendar.HOUR_OF_DAY));
                        cal.set(Calendar.MINUTE, startCal.get(Calendar.MINUTE));
                        cal.set(Calendar.SECOND, startCal.get(Calendar.SECOND));
                        instanceSchedule.setStartTime(cal.getTime());
                        
                        // 设置结束时间
                        cal.setTime(instanceDateTime);
                        Calendar endCal = Calendar.getInstance();
                        endCal.setTime(parentEndTime);
                        cal.set(Calendar.HOUR_OF_DAY, endCal.get(Calendar.HOUR_OF_DAY));
                        cal.set(Calendar.MINUTE, endCal.get(Calendar.MINUTE));
                        cal.set(Calendar.SECOND, endCal.get(Calendar.SECOND));
                        instanceSchedule.setEndTime(cal.getTime());
                        
                        // 创建取消课程通知
                        teachingCourseNotificationService.createCancelCourseNotification(
                            instanceSchedule, notifyTeachers, notifyStudents);
                    } catch (ParseException e) {
                        log.error("解析日期失败", e);
                    }
                } catch (Exception e) {
                    log.error("创建课程取消通知失败", e);
                }
            }
            
            if (success) {
                return Result.ok("删除单次课程成功！");
            } else {
                return Result.error("删除单次课程失败！");
            }
        }
        }
        
    /**
     * 获取指定父课程的所有单次课程实例
     * 
     * @param parentId 父课程ID
     * @return 单次课程实例列表
     */
    @AutoLog(value = "课程排期-获取单次课程实例列表")
    @ApiOperation(value="课程排期-获取单次课程实例列表", notes="课程排期-获取单次课程实例列表")
    @GetMapping(value = "/listInstances")
    public Result<?> listInstances(@RequestParam(name="parentId", required=true) String parentId) {
        // 查询条件
        QueryWrapper<TeachingCourseScheduleInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        
        // 获取实例列表
        List<TeachingCourseScheduleInstance> instances = teachingCourseScheduleInstanceService.list(queryWrapper);
        
        return Result.ok(instances);
    }
} 