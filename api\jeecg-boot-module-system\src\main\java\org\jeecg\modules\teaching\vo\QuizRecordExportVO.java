package org.jeecg.modules.teaching.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客观题答题记录导出VO
 */
@Data
public class QuizRecordExportVO {
    
    @Excel(name = "学生", width = 15)
    private String studentName;
    
    @Excel(name = "班级", width = 15)
    private String departName;
    
    @Excel(name = "课程", width = 15)
    private String courseName;
    
    @Excel(name = "单元", width = 15)
    private String unitName;
    
    @Excel(name = "题目总数", width = 10)
    private Integer totalCount;
    
    @Excel(name = "正确数", width = 10)
    private Integer correctCount;
    
    @Excel(name = "正确率", width = 10)
    private BigDecimal correctRate;
    
    @Excel(name = "答题时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date quizTime;
    
    @Excel(name = "做题详情", width = 50)
    private String quizDetail;
    
    // 原始记录ID，不导出
    private String recordId;
} 