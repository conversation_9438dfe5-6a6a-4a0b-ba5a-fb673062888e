package org.jeecg.modules.system.enumeration;

/**
 * 验证码类型枚举
 */
public class CaptchaType {

    /**
     * 根据类型编码获取验证码类型
     * @param typeCode 类型编码
     * @return 验证码类型
     */
    public static String getCaptchaTypeByTypeCode(Integer typeCode) {
        if(typeCode == null) {
            return "SLIDER";
        }
        switch (typeCode) {
            case 1:
                return "SLIDER";
            case 2:
                return "ROTATE";
            case 3:
                return "CONCAT";
            case 4:
                return "WORD_IMAGE_CLICK";
            default:
                return "SLIDER";
        }
    }
} 