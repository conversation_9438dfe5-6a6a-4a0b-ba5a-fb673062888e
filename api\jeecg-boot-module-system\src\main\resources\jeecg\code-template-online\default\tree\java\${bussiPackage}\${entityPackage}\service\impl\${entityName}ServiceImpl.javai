package ${bussiPackage}.${entityPackage}.service.impl;

import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import ${bussiPackage}.${entityPackage}.entity.${entityName};
import ${bussiPackage}.${entityPackage}.mapper.${entityName}Mapper;
import ${bussiPackage}.${entityPackage}.service.I${entityName}Service;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
<#assign pidFieldName = "">
<#assign hasChildrenField = "">
<#list originalColumns as po>
<#if po.fieldDbName == tableVo.extendParams.pidField>
<#assign pidFieldName = po.fieldName>
</#if>
<#if po.fieldDbName == tableVo.extendParams.hasChildren>
<#assign hasChildrenField = po.fieldName>
</#if>
</#list>

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Service
public class ${entityName}ServiceImpl extends ServiceImpl<${entityName}Mapper, ${entityName}> implements I${entityName}Service {

	@Override
	public void add${entityName}(${entityName} ${entityName?uncap_first}) {
		if(oConvertUtils.isEmpty(${entityName?uncap_first}.get${pidFieldName?cap_first}())){
			${entityName?uncap_first}.set${pidFieldName?cap_first}(I${entityName}Service.ROOT_PID_VALUE);
		}else{
			//如果当前节点父ID不为空 则设置父节点的hasChildren 为1
			${entityName} parent = baseMapper.selectById(${entityName?uncap_first}.get${pidFieldName?cap_first}());
			if(parent!=null && !"1".equals(parent.get${hasChildrenField?cap_first}())){
				parent.set${hasChildrenField?cap_first}("1");
				baseMapper.updateById(parent);
			}
		}
		baseMapper.insert(${entityName?uncap_first});
	}
	
	@Override
	public void update${entityName}(${entityName} ${entityName?uncap_first}) {
		${entityName} entity = this.getById(${entityName?uncap_first}.getId());
		if(entity==null) {
			throw new JeecgBootException("未找到对应实体");
		}
		String old_pid = entity.get${pidFieldName?cap_first}();
		String new_pid = ${entityName?uncap_first}.get${pidFieldName?cap_first}();
		if(!old_pid.equals(new_pid)) {
			updateOldParentNode(old_pid);
			if(oConvertUtils.isEmpty(new_pid)){
				${entityName?uncap_first}.set${pidFieldName?cap_first}(I${entityName}Service.ROOT_PID_VALUE);
			}
			if(!I${entityName}Service.ROOT_PID_VALUE.equals(${entityName?uncap_first}.get${pidFieldName?cap_first}())) {
				baseMapper.updateTreeNodeStatus(${entityName?uncap_first}.get${pidFieldName?cap_first}(), I${entityName}Service.HASCHILD);
			}
		}
		baseMapper.updateById(${entityName?uncap_first});
	}
	
	@Override
	public void delete${entityName}(String id) throws JeecgBootException {
		${entityName} ${entityName?uncap_first} = this.getById(id);
		if(${entityName?uncap_first}==null) {
			throw new JeecgBootException("未找到对应实体");
		}
		updateOldParentNode(${entityName?uncap_first}.get${pidFieldName?cap_first}());
		baseMapper.deleteById(id);
	}
	
	
	
	/**
	 * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
	 * @param pid
	 */
	private void updateOldParentNode(String pid) {
		if(!I${entityName}Service.ROOT_PID_VALUE.equals(pid)) {
			Integer count = baseMapper.selectCount(new QueryWrapper<${entityName}>().eq("${tableVo.extendParams.pidField}", pid));
			if(count==null || count<=1) {
				baseMapper.updateTreeNodeStatus(pid, I${entityName}Service.NOCHILD);
			}
		}
	}

}
