package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.teaching.entity.TeachingClassroom;
import org.jeecg.modules.teaching.mapper.TeachingClassroomMapper;
import org.jeecg.modules.teaching.service.ITeachingClassroomService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @Description: 教室
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class TeachingClassroomServiceImpl extends ServiceImpl<TeachingClassroomMapper, TeachingClassroom> implements ITeachingClassroomService {
    /**
     * 获取所有可用教室
     * @return 可用教室列表
     */
    @Override
    public List<TeachingClassroom> getAvailableClassrooms() {
        return baseMapper.getAvailableClassrooms();
    }
    
    /**
     * 更新教室状态
     * @param id 教室ID
     * @param status 状态（0-维护中，1-可用）
     * @return 是否成功
     */
    @Override
    public boolean updateClassroomStatus(String id, Integer status) {
        TeachingClassroom classroom = getById(id);
        if (classroom != null) {
            classroom.setStatus(status);
            return updateById(classroom);
        }
        return false;
    }
} 