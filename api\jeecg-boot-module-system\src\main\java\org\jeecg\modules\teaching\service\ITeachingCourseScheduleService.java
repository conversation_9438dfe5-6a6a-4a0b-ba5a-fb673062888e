package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.vo.CourseScheduleExcelVO;
import org.jeecg.modules.teaching.vo.CourseScheduleVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 课程排期
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface ITeachingCourseScheduleService extends IService<TeachingCourseSchedule> {
    /**
     * 获取课程排期列表
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 课程排期列表
     */
    IPage<CourseScheduleVO> getScheduleList(Page<CourseScheduleVO> page, QueryWrapper<TeachingCourseSchedule> queryWrapper);
    
    /**
     * 检查时间冲突
     * @param schedule 排期信息
     * @return 检查结果
     */
    Result<Object> checkConflict(TeachingCourseSchedule schedule);
    
    /**
     * 批量导入课程排期
     * @param excelVOList Excel数据列表
     * @return 导入结果
     */
    Result<Object> importScheduleList(List<CourseScheduleExcelVO> excelVOList);
    
    /**
     * 生成Excel模板
     * @return Excel模板路径
     */
    String generateTemplate();

    /**
     * 判断用户是否是课程排期的教师
     * @param scheduleId 课程排期ID
     * @param teacherId 教师ID
     * @return 如果是课程排期的教师返回true，否则返回false
     */
    boolean isTeacherOfSchedule(String scheduleId, String teacherId);

    /**
     * 判断学生是否与课程排期相关
     * @param scheduleId 课程排期ID
     * @param studentId 学生ID
     * @return 如果学生与课程排期相关返回true，否则返回false
     */
    boolean isStudentRelatedToSchedule(String scheduleId, String studentId);

    /**
     * 获取已删除的单次课程记录
     * @return 已删除的单次课程记录，格式为 {parentId: [date1, date2, ...]}
     */
    Map<String, List<String>> getDeletedInstances();
    
    /**
     * 删除单次课程记录
     * @param parentId 父课程ID
     * @param instanceDate 单次课程日期，格式为 yyyy-MM-dd
     * @return 是否删除成功
     */
    boolean deleteSingleInstance(String parentId, String instanceDate);

    /**
     * 查询未来24小时内将开始且尚未创建提醒通知的课程
     * 
     * @param startTime 开始时间范围
     * @param endTime 结束时间范围
     * @return 符合条件的课程列表
     */
    List<TeachingCourseSchedule> findUpcomingCoursesWithoutReminder(Date startTime, Date endTime);

    /**
     * 标记课程已创建提醒通知
     * 
     * @param courseId 课程ID
     * @return 是否更新成功
     */
    boolean markCourseReminderCreated(String courseId);
} 