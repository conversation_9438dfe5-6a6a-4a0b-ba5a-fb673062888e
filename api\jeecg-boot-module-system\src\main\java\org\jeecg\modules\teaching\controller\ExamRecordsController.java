package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.entity.ExamRecord;
import org.jeecg.modules.teaching.service.IExamPaperService;
import org.jeecg.modules.teaching.service.IExamRecordService;
import org.jeecg.modules.teaching.vo.ExamRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 考试记录
 * @Author: jeecg-boot
 * @Date:   2023-06-18
 * @Version: V1.0
 */
@Api(tags="考试记录")
@RestController
@RequestMapping("/teaching/examSystem/examRecords")
@Slf4j
public class ExamRecordsController extends JeecgController<ExamRecord, IExamRecordService> {

    @Autowired
    private IExamRecordService examRecordService;

    @Autowired
    private IExamPaperService examPaperService;

    /**
     * 分页列表查询
     * @param examRecord 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @param req 请求
     * @return 分页结果
     */
    @AutoLog(value = "考试记录-分页列表查询")
    @ApiOperation(value="考试记录-分页列表查询", notes="根据条件查询考试记录")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(
            ExamRecord examRecord,
            @ApiParam(value = "页码", required = false) @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @ApiParam(value = "每页数量", required = false) @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            @ApiParam(value = "科目", required = false) @RequestParam(name="subject", required = false) String subject,
            @ApiParam(value = "级别", required = false) @RequestParam(name="level", required = false) String level,
            @ApiParam(value = "状态", required = false) @RequestParam(name="status", required = false) Integer status,
            @ApiParam(value = "排序字段", required = false) @RequestParam(name="sortField", required = false) String sortField,
            @ApiParam(value = "排序方向", required = false) @RequestParam(name="sortOrder", required = false) String sortOrder,
            HttpServletRequest req) {
        
        // 获取当前登录用户ID（统一方式）
        String userId = getCurrentUser().getId();
        log.info("当前登录用户ID: {}", userId);

        if (userId == null || userId.isEmpty()) {
            log.error("无法获取用户ID，可能是用户未登录");
            return Result.error("用户未登录");
        }

        examRecord.setUserId(userId);

        // 创建查询条件
        QueryWrapper<ExamRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);

        // 添加状态筛选
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        // 如果有科目或级别筛选条件，需要关联查询试卷表
        if ((subject != null && !subject.trim().isEmpty()) || (level != null && !level.trim().isEmpty())) {
            // 先查询符合条件的试卷ID
            QueryWrapper<ExamPaper> paperQueryWrapper = new QueryWrapper<>();
            if (subject != null && !subject.trim().isEmpty()) {
                paperQueryWrapper.eq("subject", subject);
            }
            if (level != null && !level.trim().isEmpty()) {
                // 标准化级别格式
                String standardizedLevel = standardizeLevel(level);
                paperQueryWrapper.eq("level", standardizedLevel);
            }

            List<ExamPaper> papers = examPaperService.list(paperQueryWrapper);
            if (papers.isEmpty()) {
                // 如果没有符合条件的试卷，返回空结果
                IPage<ExamRecordVO> emptyResult = new Page<>(pageNo, pageSize, 0);
                emptyResult.setRecords(new ArrayList<>());
                return Result.ok(emptyResult);
            }

            // 提取试卷ID列表
            List<String> paperIds = papers.stream().map(ExamPaper::getId).collect(Collectors.toList());
            queryWrapper.in("paper_id", paperIds);
        }

        // 处理排序
        if (sortField != null && sortOrder != null) {
            log.info("考试记录排序 - 原始字段: {}, 方向: {}", sortField, sortOrder);

            if ("duration".equals(sortField)) {
                // 用时排序：使用数据库计算表达式排序
                log.info("执行用时排序 - 使用数据库计算表达式");
                String durationExpression = "TIMESTAMPDIFF(SECOND, start_time, end_time)";
                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc(durationExpression);
                } else {
                    queryWrapper.orderByDesc(durationExpression);
                }
            } else {
                // 普通字段排序
                String dbFieldName = convertToDbFieldName(sortField);
                log.info("考试记录数据库排序 - 转换后字段: {}", dbFieldName);

                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc(dbFieldName);
                } else if ("desc".equals(sortOrder)) {
                    queryWrapper.orderByDesc(dbFieldName);
                }
            }
        } else {
            // 默认按开始时间倒序排列
            log.info("考试记录使用默认排序 - 按开始时间倒序");
            queryWrapper.orderByDesc("start_time");
        }

        log.info("查询条件: userId={}, subject={}, level={}, status={}, pageNo={}, pageSize={}",
                userId, subject, level, status, pageNo, pageSize);

        // 执行分页查询
        Page<ExamRecord> page = new Page<>(pageNo, pageSize);
        IPage<ExamRecord> pageList = examRecordService.page(page, queryWrapper);

        log.info("查询结果: 总记录数={}, 当前页记录数={}", pageList.getTotal(), pageList.getRecords().size());

        // 转换为VO对象，补充试卷信息
        List<ExamRecordVO> recordVOList = new ArrayList<>();
        for (ExamRecord record : pageList.getRecords()) {
            ExamRecordVO vo = convertToVO(record);
            recordVOList.add(vo);
        }

        // 创建新的分页结果
        IPage<ExamRecordVO> result = new Page<>(pageNo, pageSize, pageList.getTotal());
        result.setRecords(recordVOList);

        return Result.ok(result);
    }

    /**
     * 通过id查询考试记录详情
     */
    @AutoLog(value = "考试记录-通过id查询详情")
    @ApiOperation(value="考试记录-通过id查询详情", notes="获取考试记录详情，包含试卷信息和考试结果")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        try {
            ExamRecord examRecord = examRecordService.getById(id);
            if(examRecord == null) {
                return Result.error("未找到对应的考试记录");
            }

            // 获取试卷信息
            ExamPaper paper = examPaperService.getById(examRecord.getPaperId());
            if(paper == null) {
                return Result.error("未找到对应的试卷信息");
            }

            // 构建返回的VO对象
            ExamRecordVO vo = new ExamRecordVO();
            vo.setId(examRecord.getId());
            vo.setUserId(examRecord.getUserId());
            vo.setPaperId(examRecord.getPaperId());
            vo.setStartTime(examRecord.getStartTime());
            vo.setEndTime(examRecord.getEndTime());
            vo.setScore(examRecord.getScore());
            vo.setScoreDetails(examRecord.getScoreDetails()); // 包含详细分数统计
            vo.setStatus(examRecord.getStatus());

            // 设置试卷信息
            vo.setPaperTitle(paper.getTitle());
            vo.setSubject(paper.getSubject());
            vo.setLevel(paper.getLevel());
            vo.setDifficulty(paper.getDifficulty());
            vo.setYear(paper.getYear());
            vo.setExamDuration(paper.getExamDuration());

            log.info("获取考试记录详情成功 - ID: {}, 试卷: {}, 分数: {}",
                    examRecord.getId(), paper.getTitle(), examRecord.getScore());

            return Result.ok(vo);
        } catch (Exception e) {
            log.error("获取考试记录详情失败", e);
            return Result.error("获取考试记录详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取考试记录的完整试卷预览信息
     */
    @AutoLog(value = "考试记录-获取试卷预览")
    @ApiOperation(value="考试记录-获取试卷预览", notes="获取考试记录的完整试卷信息，包含题目内容和用户答案，用于试卷预览")
    @GetMapping(value = "/getPaperPreview")
    public Result<?> getPaperPreview(@RequestParam(name="id",required=true) String id) {
        log.info("开始获取考试记录试卷预览 - 记录ID: {}", id);

        try {
            // 调用服务层方法获取完整的试卷预览信息
            return examRecordService.getExamRecordPaperPreview(id);
        } catch (Exception e) {
            log.error("获取考试记录试卷预览失败 - 记录ID: {}", id, e);
            return Result.error("获取试卷预览失败: " + e.getMessage());
        }
    }

    /**
     * 将各种格式的级别标准化为"X级"格式
     * @param level 原始级别文本，如"一级"、"二级"等
     * @return 标准化的级别文本
     */
    private String standardizeLevel(String level) {
        if (level == null || level.isEmpty()) {
            return level;
        }

        // 如果已经是标准格式，直接返回
        if (level.endsWith("级")) {
            return level;
        }

        // 如果是纯数字，转换为中文级别
        try {
            int num = Integer.parseInt(level);
            return numberToChinese(num) + "级";
        } catch (NumberFormatException e) {
            // 如果是单个中文数字，添加"级"
            switch (level) {
                case "一": return "一级";
                case "二": return "二级";
                case "三": return "三级";
                case "四": return "四级";
                case "五": return "五级";
                case "六": return "六级";
                case "七": return "七级";
                case "八": return "八级";
                case "九": return "九级";
                default: return level;
            }
        }
    }

    /**
     * 将数字转换为中文数字
     * @param num 数字
     * @return 中文数字
     */
    private String numberToChinese(int num) {
        switch (num) {
            case 1: return "一";
            case 2: return "二";
            case 3: return "三";
            case 4: return "四";
            case 5: return "五";
            case 6: return "六";
            case 7: return "七";
            case 8: return "八";
            case 9: return "九";
            default: return String.valueOf(num);
        }
    }



    /**
     * 将ExamRecord转换为ExamRecordVO
     */
    private ExamRecordVO convertToVO(ExamRecord record) {
        ExamRecordVO vo = new ExamRecordVO();
        // 复制基本属性
        vo.setId(record.getId());
        vo.setUserId(record.getUserId());
        vo.setPaperId(record.getPaperId());
        vo.setStartTime(record.getStartTime());
        vo.setEndTime(record.getEndTime());
        vo.setScore(record.getScore());
        vo.setStatus(record.getStatus());

        // 查询试卷信息
        ExamPaper paper = examPaperService.getById(record.getPaperId());
        if (paper != null) {
            vo.setPaperTitle(paper.getTitle());
            vo.setSubject(paper.getSubject());
            vo.setLevel(paper.getLevel());
            vo.setDifficulty(paper.getDifficulty());
            vo.setYear(paper.getYear());
            vo.setExamDuration(paper.getExamDuration());
        }

        // 添加调试日志，查看用时计算情况
        long actualSeconds = record.getEndTime() != null && record.getStartTime() != null ?
            (record.getEndTime().getTime() - record.getStartTime().getTime()) / 1000 : 0;
        log.info("考试记录用时计算调试 - ID: {}, 状态: {} (1=已提交), 开始时间: {}, 结束时间: {}, 实际用时: {}秒, 显示用时: {}秒",
                record.getId(), record.getStatus(), record.getStartTime(), record.getEndTime(), actualSeconds, vo.getDuration());

        return vo;
    }

    /**
     * 将前端驼峰命名转换为数据库下划线命名
     */
    private String convertToDbFieldName(String camelCaseField) {
        if (camelCaseField == null) {
            return null;
        }

        // 处理特定字段映射
        switch (camelCaseField) {
            case "startTime":
                return "start_time";
            case "endTime":
                return "end_time";
            case "userId":
                return "user_id";
            case "paperId":
                return "paper_id";
            default:
                // 通用驼峰转下划线
                return camelCaseField.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
        }
    }
}