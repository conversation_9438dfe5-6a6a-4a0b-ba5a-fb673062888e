package org.jeecg.common.util.fastjson;

import com.alibaba.fastjson.serializer.SerializeConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartFile;

/**
 * FastJSON配置类，用于注册自定义序列化器
 */
@Configuration
public class FastjsonConfig {
    
    @Bean
    public SerializeConfig fastjsonSerializeConfig() {
        SerializeConfig config = SerializeConfig.getGlobalInstance();
        // 注册MultipartFile序列化器
        config.put(MultipartFile.class, new MultipartFileSerializer());
        // 注：由于内部类访问限制，我们只能处理MultipartFile接口
        // 具体实现类如StandardMultipartHttpServletRequest.StandardMultipartFile将通过接口自动处理
        return config;
    }
} 