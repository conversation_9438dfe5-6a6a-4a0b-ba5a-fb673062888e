package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingGift;

import java.util.List;

/**
 * 礼物配置服务接口
 */
public interface ITeachingGiftService extends IService<TeachingGift> {
    
    /**
     * 获取前台兑换中心可用礼物列表
     * @return 礼物列表
     */
    Result<List<TeachingGift>> getShoppingGifts();
    
    /**
     * 根据类型和行号获取礼物
     * @param giftType 礼物类型：0-普通，1-热门，2-珍稀
     * @param giftRow 礼物行号：1-5
     * @return 礼物列表
     */
    Result<List<TeachingGift>> getGiftsByTypeAndRow(Integer giftType, Integer giftRow);
    
    /**
     * 批量启用礼物
     * @param ids 礼物ID列表，逗号分隔
     * @return 操作结果
     */
    Result<?> batchEnableGift(String ids);
    
    /**
     * 批量禁用礼物
     * @param ids 礼物ID列表，逗号分隔
     * @return 操作结果
     */
    Result<?> batchDisableGift(String ids);
} 