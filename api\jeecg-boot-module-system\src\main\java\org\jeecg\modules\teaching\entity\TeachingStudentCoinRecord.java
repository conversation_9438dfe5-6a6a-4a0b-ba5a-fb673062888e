package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 学生金币记录实体类
 */
@Data
@TableName("teaching_student_coin_record")
public class TeachingStudentCoinRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    /** 用户ID */
    private String userId;
    
    /** 操作类型（1:获取, 2:消费） */
    private Integer operationType;
    
    /** 金币数量 */
    private Integer coinCount;
    
    /** 操作描述 */
    private String description;
    
    /** 操作来源（1:精选作品, 2:客观题, 3:编程题, 4:每日任务, 5:礼物兑换）*/
    private Integer source;
    
    /** 关联ID（作品ID、作业ID、礼物ID等） */
    private String relatedId;
    
    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 