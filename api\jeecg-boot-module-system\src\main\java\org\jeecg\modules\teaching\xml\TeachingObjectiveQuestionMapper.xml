<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.teaching.mapper.TeachingObjectiveQuestionMapper">

    <select id="queryListByUnitId" resultType="org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion">
        SELECT 
            q.*, 
            c.course_name as courseName, 
            u.unit_name as unitName
        FROM 
            teaching_objective_question q
        LEFT JOIN 
            teaching_course c ON q.course_id = c.id
        LEFT JOIN 
            teaching_course_unit u ON q.unit_id = u.id
        WHERE 
            (q.unit_id = #{unitId} OR (q.course_id = #{courseId} AND q.unit_id IS NULL))
        ORDER BY 
            q.create_time ASC
    </select>
    
    <select id="selectPageWithCourseName" resultType="org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion">
        SELECT 
            q.*, 
            c.course_name as courseName
        FROM 
            teaching_objective_question q
        LEFT JOIN 
            teaching_course c ON q.course_id = c.id
        <if test="ew != null">
            <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
                ${ew.customSqlSegment}
            </if>
        </if>
    </select>

</mapper> 