package org.jeecg.modules.system.config;

import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.resource.ResourceStore;
import cloud.tianai.captcha.resource.common.model.dto.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 验证码资源配置类
 */
@Slf4j
@Configuration
public class CaptchaResourceConfiguration {

    @Autowired
    private ResourceStore resourceStore;

    @PostConstruct
    public void init() throws Exception {
        // 背景图片存放在 resource/background 目录下
        String backgroundDirectory = "background";
        List<String> backgroundList = new ArrayList<>();

        String pattern = "classpath*:" + backgroundDirectory + "/**";
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        org.springframework.core.io.Resource[] resources = resourcePatternResolver.getResources(pattern);
        for (org.springframework.core.io.Resource resource : resources) {
            String path = resource.getURL().getPath();
            if (!path.endsWith("/")) {
                String background = path.substring(path.lastIndexOf(backgroundDirectory));
                log.info("Found background image: {}", background);
                backgroundList.add(background);
            }
        }

        backgroundList.forEach((String background) -> {
            // 添加自定义背景图片
            Resource resource = new Resource("classpath", background);
            resourceStore.addResource(CaptchaTypeConstant.CONCAT, resource);
            resourceStore.addResource(CaptchaTypeConstant.SLIDER, resource);
            resourceStore.addResource(CaptchaTypeConstant.ROTATE, resource);
            resourceStore.addResource(CaptchaTypeConstant.WORD_IMAGE_CLICK, resource);

            log.info("Added background resource: {}", background);
        });

        // 打印最终的背景列表
        backgroundList.forEach(background -> log.info("Background image: {}", background));
    }
} 