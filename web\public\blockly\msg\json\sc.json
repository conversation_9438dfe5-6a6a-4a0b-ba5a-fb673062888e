{"@metadata": {"authors": ["Taxandru", "<PERSON><PERSON><PERSON>", "Via maxima"]}, "VARIABLES_DEFAULT_NAME": "item", "TODAY": "Oe", "DUPLICATE_BLOCK": "Dùplica", "ADD_COMMENT": "Agiunghe unu cumentu", "REMOVE_COMMENT": "Fùlia unu cumentu", "EXTERNAL_INPUTS": "Intradas esternas", "INLINE_INPUTS": "Intradas in lìnia", "DELETE_BLOCK": "<PERSON><PERSON><PERSON>", "DELETE_X_BLOCKS": "Fulia %1 Blocus", "DELETE_ALL_BLOCKS": "Scancellu su %1 de is brocus?", "CLEAN_UP": "Lìmpia is brocus", "COLLAPSE_BLOCK": "Serra e stringi Brocu", "COLLAPSE_ALL": "Serra e stringi Brocus", "EXPAND_BLOCK": "<PERSON><PERSON>", "EXPAND_ALL": "<PERSON><PERSON>", "DISABLE_BLOCK": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ENABLE_BLOCK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HELP": "<PERSON><PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Muda valori:", "RENAME_VARIABLE": "Muda nòmini a variabili...", "RENAME_VARIABLE_TITLE": "A is variabilis '%1' muda nòmini a:", "NEW_VARIABLE": "Variabili noa...", "NEW_VARIABLE_TITLE": "Nòmini de sa variabili noa:", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Scebera unu colori de sa tauledda.", "COLOUR_RANDOM_TITLE": "Unu colori a brítiu", "COLOUR_RANDOM_TOOLTIP": "Scebera unu colori a brítiu.", "COLOUR_RGB_TITLE": "colora cun", "COLOUR_RGB_RED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COLOUR_RGB_GREEN": "birdi", "COLOUR_RGB_BLUE": "blue", "COLOUR_RGB_TOOLTIP": "Cuncorda unu colori cun su tanti de arrubiu, birdi, e blue. <PERSON><PERSON> is valoris depint essi intra 0 e 100.", "COLOUR_BLEND_TITLE": "mestura", "COLOUR_BLEND_COLOUR1": "colori 1", "COLOUR_BLEND_COLOUR2": "colori 2", "COLOUR_BLEND_RATIO": "raportu", "COLOUR_BLEND_TOOLTIP": "Amestura duus coloris cun unu raportu (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "repiti %1 bortas", "CONTROLS_REPEAT_INPUT_DO": "fai", "CONTROLS_REPEAT_TOOLTIP": "Fait pariga de cumandus prus bortas.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "repiti interis", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "repiti fintzas", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Interis su valori est berus, tandu fai pariga de cumandus.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Interis su valori est frassu, tandu fai pariga de cumandus.", "CONTROLS_FOR_TOOLTIP": "Fait pigai a sa variàbili \"%1\" i valoris de su primu numeru a s'urtimu, a su passu impostau e fait su brocu.", "CONTROLS_FOR_TITLE": "po %1 de %2 fintzas %3 a passus de %4", "CONTROLS_FOREACH_TITLE": "po dònnia item %1 in lista %2", "CONTROLS_FOREACH_TOOLTIP": "Po dònnia item in sa lista, ponit sa variàbili '%1' pari a s'item, e tandu fait pariga de cumandus.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "sàrtiat a foras de sa lòriga", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "sighit cun su repicu afatànti", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Bessit de sa lòriga.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Sartiat su chi abarrat de sa loriga, e sighit cun su repicu afatànti.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Amonestu: <PERSON><PERSON><PERSON> brocu ddu podis ponni sceti aintru de una lòriga.", "CONTROLS_IF_TOOLTIP_1": "Si su valori est berus, tandu fait pariga de cumandus.", "CONTROLS_IF_TOOLTIP_2": "Si su valori est berus, tandu fai su primu brocu de is cumandus. Sinuncas, fai su segundu brocu de is cumandus.", "CONTROLS_IF_TOOLTIP_3": "Si su primu valori est beridadi, tandu fai su primu brocu de is cumandus. <PERSON><PERSON><PERSON>, si su segundu valori est beridadi, fai su segundu brocu de is cumandus.", "CONTROLS_IF_TOOLTIP_4": "Si su primu valori est berus, tandu fai su primu brocu de is cumandus. <PERSON><PERSON><PERSON>, si su segundu valori est berus, fai su segundu brocu de is cumandus. Si mancu unu valori est berus, tandu fai s'urtimu brocu de is cumandus.", "CONTROLS_IF_MSG_IF": "si", "CONTROLS_IF_MSG_ELSEIF": "sinuncas si", "CONTROLS_IF_MSG_ELSE": "sinuncas", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, fù<PERSON>, o assenta is partis po torrai a sètiu custu brocu si.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Aciungi una cunditzioni a su brocu si.", "CONTROLS_IF_ELSE_TOOLTIP": "Aciungi una urtima cunditzioni piga-totu a su brocu si.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON><PERSON> berus si is inputs funt unu uguali a s'àteru.", "LOGIC_COMPARE_TOOLTIP_NEQ": "<PERSON><PERSON> berus si is inputs non funt unu uguali a s'àteru.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON>rat berus si su primu input est prus piticu de s'àteru.", "LOGIC_COMPARE_TOOLTIP_LTE": "<PERSON>rat berus si su primu input est prus piticu o uguali a s'àteru.", "LOGIC_COMPARE_TOOLTIP_GT": "<PERSON>rat berus si su primu input est prus mannu de s'àteru.", "LOGIC_COMPARE_TOOLTIP_GTE": "<PERSON>rat berus si su primu input est prus mannu o uguali a s'àteru.", "LOGIC_OPERATION_TOOLTIP_AND": "<PERSON><PERSON> berus si ambos is inputs funt berus.", "LOGIC_OPERATION_AND": "and", "LOGIC_OPERATION_TOOLTIP_OR": "<PERSON>rat berus si assumancu unu de is inputs est berus.", "LOGIC_OPERATION_OR": "or", "LOGIC_NEGATE_TITLE": "non %1", "LOGIC_NEGATE_TOOLTIP": "Torrat berus si s'input est frassu. Torrat frassu si s'input est berus.", "LOGIC_BOOLEAN_TRUE": "berus", "LOGIC_BOOLEAN_FALSE": "frassu", "LOGIC_BOOLEAN_TOOLTIP": "<PERSON><PERSON> berus o frassu.", "LOGIC_NULL": "null", "LOGIC_NULL_TOOLTIP": "<PERSON><PERSON> null.", "LOGIC_TERNARY_CONDITION": "cump<PERSON><PERSON><PERSON>", "LOGIC_TERNARY_IF_TRUE": "si berus", "LOGIC_TERNARY_IF_FALSE": "si frassu", "LOGIC_TERNARY_TOOLTIP": "‎Cumproa sa cunditzioni in 'cumproa'. Si sa cunditzioni est berus, torrat su valori 'si berus'; sinuncas torrat su valori 'si frassu'.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "Unu numeru", "MATH_ARITHMETIC_HELPURL": "https://en.wikipedia.org/wiki/Arithmetic", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON>rat sa summa de is duus nùmerus.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Torrat sa diferèntzia de is duus nùmerus.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Torrat su produtu de is duus nùmerus.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Torrat su cuotzienti de is duus nùmerus.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Torrat su primu numeru artziau a sa potenza de su segundu nùmeru.", "MATH_SINGLE_HELPURL": "https://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "arraxina cuadra", "MATH_SINGLE_TOOLTIP_ROOT": "<PERSON><PERSON> s'arraxina cuadra de unu numeru.", "MATH_SINGLE_OP_ABSOLUTE": "assolutu", "MATH_SINGLE_TOOLTIP_ABS": "Torrat su valori assolútu de unu numeru.", "MATH_SINGLE_TOOLTIP_NEG": "Torrat su valori negau de unu numeru.", "MATH_SINGLE_TOOLTIP_LN": "Torrat su logaritmu naturali de unu numeru.", "MATH_SINGLE_TOOLTIP_LOG10": "Torrat su logaritmu a basi 10 de unu numeru.", "MATH_SINGLE_TOOLTIP_EXP": "Torrat (e) a sa potèntzia de unu numeru.", "MATH_SINGLE_TOOLTIP_POW10": "Tor<PERSON> (10) a sa potèntzia de unu numeru.", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "Torrat su sinu de unu gradu (no radianti).", "MATH_TRIG_TOOLTIP_COS": "Torrat su cosinu de unu gradu (no radianti).", "MATH_TRIG_TOOLTIP_TAN": "Torrat sa tangenti de unu gradu (no radianti).", "MATH_TRIG_TOOLTIP_ASIN": "Torrat su arcsinu de unu numeru.", "MATH_TRIG_TOOLTIP_ACOS": "Torrat su arccosinu de unu numeru.", "MATH_TRIG_TOOLTIP_ATAN": "Torrat su arctangenti de unu numeru.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant", "MATH_CONSTANT_TOOLTIP": "<PERSON>rat una de is costantis comunas: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), o ∞ (infiniu).", "MATH_IS_EVEN": "est paris", "MATH_IS_ODD": "est dísparu", "MATH_IS_PRIME": "est primu", "MATH_IS_WHOLE": "est intreu", "MATH_IS_POSITIVE": "est positivu", "MATH_IS_NEGATIVE": "est negativu", "MATH_IS_DIVISIBLE_BY": "fait a ddu dividi po", "MATH_IS_TOOLTIP": "<PERSON>umprova si unu numeru est paris, d<PERSON><PERSON><PERSON>, primu, intreu, positivu, negativu o si fait a ddu dividi po unu numeru giau. <PERSON><PERSON> berus o frassu.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "muda %1 de %2", "MATH_CHANGE_TOOLTIP": "Aciungi unu numeru a sa variabili '%1'.", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "Arretunda unu numeru faci a susu o faci a bàsciu.", "MATH_ROUND_OPERATOR_ROUND": "<PERSON><PERSON><PERSON><PERSON>", "MATH_ROUND_OPERATOR_ROUNDUP": "Arretunda faci a susu", "MATH_ROUND_OPERATOR_ROUNDDOWN": "arretunda faci a bàsciu.", "MATH_ONLIST_OPERATOR_SUM": "suma sa lista", "MATH_ONLIST_TOOLTIP_SUM": "Torrat sa suma de totu is numerus de sa lista.", "MATH_ONLIST_OPERATOR_MIN": "minimu de sa lista", "MATH_ONLIST_TOOLTIP_MIN": "Torrat su numeru prus piticu de sa lista.", "MATH_ONLIST_OPERATOR_MAX": "massimu de sa lista", "MATH_ONLIST_TOOLTIP_MAX": "Torrat su numeru prus mannu de sa lista", "MATH_ONLIST_OPERATOR_AVERAGE": "mèdia de sa <PERSON>a", "MATH_ONLIST_TOOLTIP_AVERAGE": "Torrat sa mèdia (aritimètica) de is valoris de sa lista.", "MATH_ONLIST_OPERATOR_MEDIAN": "<PERSON>u de sa lista", "MATH_ONLIST_TOOLTIP_MEDIAN": "Torrat su numeru medianu de sa lista.", "MATH_ONLIST_OPERATOR_MODE": "modas de sa lista", "MATH_ONLIST_TOOLTIP_MODE": "Torrat una lista de is itams prus frecuentis de sa lista.", "MATH_ONLIST_OPERATOR_STD_DEV": "deviadura standard de sa lista", "MATH_ONLIST_TOOLTIP_STD_DEV": "Torrat sa deviadura standard de sa lista.", "MATH_ONLIST_OPERATOR_RANDOM": "unu item a brìtiu de sa lista", "MATH_ONLIST_TOOLTIP_RANDOM": "Torrat unu item a brìtiu de sa lista.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "arrestu de %1 ÷ %2", "MATH_MODULO_TOOLTIP": "<PERSON><PERSON> s'arrestu de sa divisioni de duus numerus.", "MATH_CONSTRAIN_TITLE": "custringi %1 de %2 a %3", "MATH_CONSTRAIN_TOOLTIP": "Custringi unu numeru aintru de is liminaxus giaus (cumprendius).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "numeru intreu a brítiu de %1 a %2", "MATH_RANDOM_INT_TOOLTIP": "Torrat unu numeru intreu a brìtiu intra duus nùmerus giaus (cumpresus).", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "una fratzioni a brìtiu", "MATH_RANDOM_FLOAT_TOOLTIP": "Torrat una fratzioni a brìtiu intra 0.0 (cumpresu) e 1.0 (bogau).", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "<PERSON> lìtera, par<PERSON><PERSON>, o linia de testu.", "TEXT_JOIN_TITLE_CREATEWITH": "scri testu cun", "TEXT_JOIN_TOOLTIP": "Fait unu testu ponendi a pari parigas de items.", "TEXT_CREATE_JOIN_TITLE_JOIN": "auni a pari", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, f<PERSON><PERSON>, o assenta is partis po torrai a sètiu custu brocu de testu.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Acciungi unu item a su testu.", "TEXT_APPEND_TITLE": "a %1 acciungi su testu %2", "TEXT_APPEND_TOOLTIP": "Aciungit testu a sa variàbili '%1'.", "TEXT_LENGTH_TITLE": "longària de %1", "TEXT_LENGTH_TOOLTIP": "Torrat su numeru de lìteras (cun is spàtzius) in su testu giau.", "TEXT_ISEMPTY_TITLE": "%1 est buidu", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON> berus si su testu giau est buidu.", "TEXT_INDEXOF_TOOLTIP": "Torrat s'indixi de sa primu/urtima ocasioni de su primu testu in su segundu testu. Torrat %1 si su testu no ddu agatat.", "TEXT_INDEXOF_TITLE": "in su testu %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "circa prima ocasioni de su testu", "TEXT_INDEXOF_OPERATOR_LAST": "circa urtima ocasioni de su testu", "TEXT_CHARAT_FROM_START": "piga sa lìtera #", "TEXT_CHARAT_FROM_END": "piga sa lìtera # de sa fini", "TEXT_CHARAT_FIRST": "piga sa prima lìtera", "TEXT_CHARAT_LAST": "piga s'urtima lìtera", "TEXT_CHARAT_RANDOM": "piga una lìtera a brìtiu", "TEXT_CHARAT_TOOLTIP": "Torrat sa lìtera de su postu giau.", "TEXT_GET_SUBSTRING_TOOLTIP": "<PERSON>rat su testu inditau.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "in su testu", "TEXT_GET_SUBSTRING_START_FROM_START": "piga suta-stringa de sa lìtera #", "TEXT_GET_SUBSTRING_START_FROM_END": "piga suta-stringa de sa lìtera # fintzas a fini", "TEXT_GET_SUBSTRING_START_FIRST": "piga suta-stringa de sa primu lìtera", "TEXT_GET_SUBSTRING_END_FROM_START": "a sa lìtera #", "TEXT_GET_SUBSTRING_END_FROM_END": "a sa lìtera # de sa fini", "TEXT_GET_SUBSTRING_END_LAST": "a s'urtima lìtera", "TEXT_CHANGECASE_TOOLTIP": "Torrat una copia de su testu inditau mudendi mausch<PERSON>u/min<PERSON><PERSON>.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "a mauschínu", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "a minúdu", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "cun Primu lìtera a Mauschínu", "TEXT_TRIM_TOOLTIP": "Torrat una copia de su testu bogaus is sp<PERSON><PERSON>ius de unu o de ambus is càbudus.", "TEXT_TRIM_OPERATOR_BOTH": "bogat sp<PERSON><PERSON>ius de ambus càbudus de", "TEXT_TRIM_OPERATOR_LEFT": "bogat sp<PERSON><PERSON><PERSON> de su càbudu de manca de", "TEXT_TRIM_OPERATOR_RIGHT": "bogat sp<PERSON><PERSON><PERSON> de su càbudu de dereta de", "TEXT_PRINT_TITLE": "scri %1", "TEXT_PRINT_TOOLTIP": "<PERSON><PERSON> su testu, numeru o àteru valori.", "TEXT_PROMPT_TYPE_TEXT": "pregonta po su testu", "TEXT_PROMPT_TYPE_NUMBER": "pregonta po unu numeru", "TEXT_PROMPT_TOOLTIP_NUMBER": "Pregonta unu nùmeru a s'impitadore.", "TEXT_PROMPT_TOOLTIP_TEXT": "Pregonta testu a s'impitadore.", "LISTS_CREATE_EMPTY_TITLE": "fait una lista buida", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON> una lista, de longària 0, chena records de datus.", "LISTS_CREATE_WITH_TOOLTIP": "Fait una lista cun calisiollat numeru de items.", "LISTS_CREATE_WITH_INPUT_WITH": "fait una lista cun", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, f<PERSON><PERSON>, o assenta is partis po torrai a sètiu custu brocu lista.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Acciungi unu item a sa lista.", "LISTS_REPEAT_TOOLTIP": "Fait una lista cun unu numeru giau repitiu su tanti de is bortas inditadas.", "LISTS_REPEAT_TITLE": "fait una lista cun item %1 repitiu %2 bortas", "LISTS_LENGTH_TITLE": "longària de %1", "LISTS_LENGTH_TOOLTIP": "Torrat sa longària de una lista.", "LISTS_ISEMPTY_TITLE": "%1 est buidu", "LISTS_ISEMPTY_TOOLTIP": "<PERSON><PERSON> berus si sa lista est buida.", "LISTS_INLIST": "in lista", "LISTS_INDEX_OF_FIRST": "circa prima ocasioni de s'item", "LISTS_INDEX_OF_LAST": "circa urtima ocasioni de s'item", "LISTS_INDEX_OF_TOOLTIP": "Torrat s'indixi de sa primu/urtima ocasioni de s'item in sa lista. Torrat %1 si s'item non s'agatat.", "LISTS_GET_INDEX_GET": "piga", "LISTS_GET_INDEX_GET_REMOVE": "piga e fùlia", "LISTS_GET_INDEX_REMOVE": "<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_FROM_END": "# de sa fini", "LISTS_GET_INDEX_FIRST": "primu", "LISTS_GET_INDEX_LAST": "urtimu", "LISTS_GET_INDEX_RANDOM": "a brìtiu (random)", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 est po su primu elementu.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 est po s'urtimu elementu.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "<PERSON>rat s'elementu de su postu inditau de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Torrat su primu elementu de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "<PERSON><PERSON> s'urtimu elementu de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Torrat un'elementu a brìtiu de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Fùliat e torrat s'elementu de su postu inditau de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Fùliat e torrat su primu elementu de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Fùliat e torrat s'urtimu elementu de una lista.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Fùliat e torrat un'elementu a brìtiu de una lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "<PERSON><PERSON><PERSON><PERSON> s'elementu de su postu inditau de una lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Fùliat su primu elementu de una lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "<PERSON><PERSON><PERSON><PERSON> s'urtimu elementu de una lista.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Fùliat unu elementu a brìtiu de una lista.", "LISTS_SET_INDEX_SET": "imposta", "LISTS_SET_INDEX_INSERT": "inserta a", "LISTS_SET_INDEX_INPUT_TO": "a", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Impostat s'elementu in su postu inditau de una lista.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Impostat su primu elementu in una lista.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Impostat s'urtimu elementu in una lista.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Impostat unu elementu random in una lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Insertat s'elementu in su postu inditau in una lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Insertat s'elementu a su cumintzu de sa lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Aciungit s'elementu a sa fini de sa lista.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Aciungit s'elementu a brítiu in sa lista.", "LISTS_GET_SUBLIST_START_FROM_START": "bogandi suta-lista de #", "LISTS_GET_SUBLIST_START_FROM_END": "bogandi suta-lista de # de sa fini.", "LISTS_GET_SUBLIST_START_FIRST": "bogandi suta-lista de su primu", "LISTS_GET_SUBLIST_END_FROM_START": "fintzas a #", "LISTS_GET_SUBLIST_END_FROM_END": "a # de sa fini", "LISTS_GET_SUBLIST_END_LAST": "a s'urtimu", "LISTS_GET_SUBLIST_TOOLTIP": "Fait una copia de sa parti inditada de sa lista.", "LISTS_SPLIT_LIST_FROM_TEXT": "fai una lista de unu testu", "LISTS_SPLIT_TEXT_FROM_LIST": "fai unu testu de una lista", "LISTS_SPLIT_WITH_DELIMITER": "cun  separadori", "LISTS_SPLIT_TOOLTIP_SPLIT": "Dividi su testu in un'elencu de testus, firmendi po dònnia separadori.", "LISTS_SPLIT_TOOLTIP_JOIN": "Auni una lista de testus in d-unu sceti, ponendi separadoris.", "VARIABLES_GET_TOOLTIP": "Torrat su valori de custa variabili.", "VARIABLES_GET_CREATE_SET": "Fait 'imposta %1'", "VARIABLES_SET": "imposta %1 a %2", "VARIABLES_SET_TOOLTIP": "Imposta custa variabili uguali a s'input.", "VARIABLES_SET_CREATE_GET": "Fait 'piga %1'", "PROCEDURES_DEFNORETURN_TITLE": "po", "PROCEDURES_DEFNORETURN_PROCEDURE": "fait calincuna cosa", "PROCEDURES_BEFORE_PARAMS": "con:", "PROCEDURES_CALL_BEFORE_PARAMS": "cun", "PROCEDURES_DEFNORETURN_TOOLTIP": "Fait una funtzioni chena output.", "PROCEDURES_DEFRETURN_RETURN": "torrat", "PROCEDURES_DEFRETURN_TOOLTIP": "Fait una funtzioni cun output.", "PROCEDURES_ALLOW_STATEMENTS": "permiti decraratzionis", "PROCEDURES_DEF_DUPLICATE_WARNING": "Amonestu: <PERSON>usta funtzioni tenit parametrus duplicaus.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_CALLNORETURN_TOOLTIP": "Arròllia sa funtzione '%1' cuncordada dae s'impitadore.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_CALLRETURN_TOOLTIP": "Arròllia sa funtzione '%1' cuncordada dae s'impitadore e imprea s'output suu.", "PROCEDURES_MUTATORCONTAINER_TITLE": "inputs", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, fùlia, o assenta is inputs a custa funtzioni.", "PROCEDURES_MUTATORARG_TITLE": "nomini input:", "PROCEDURES_MUTATORARG_TOOLTIP": "Aciungi un input a sa funtzioni.", "PROCEDURES_HIGHLIGHT_DEF": "Marca sa definitzioni de funtzioni.", "PROCEDURES_CREATE_DO": "Ingenerau'%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Si unu valori est berus, tandu torrat unu segundu valori.", "PROCEDURES_IFRETURN_WARNING": "Amonestu: <PERSON><PERSON><PERSON> brocu ddu podis ponni sceti aintru de una funtzioni.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}