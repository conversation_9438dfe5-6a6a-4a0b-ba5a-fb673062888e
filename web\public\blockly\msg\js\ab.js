// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Иацҵатәуп ахцәажәара";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Can't delete the variable '%1' because it's part of the definition of the function '%2'";  // untranslated
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Ишәыԥсах аҵакы";
Blockly.Msg["CLEAN_UP"] = "Иқәгатәуп аблокқәа";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "Иеикәрҳәтәуп Аблокқәа";
Blockly.Msg["COLLAPSE_BLOCK"] = "Иеикәрҳәтәуп Аблокқәа";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "аԥштәы 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "аԥштәы 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "аԥштәы 1 ахәҭа";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "еилаҵатәуп";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Blends two colours together with a given ratio (0.0 - 1.0).";  // untranslated
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://ab.wikipedia.org/wiki/Аԥштәы";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Иалышәх аԥштәы";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "иарбанзаалакь аԥштәы";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Иалнахуеит аԥштәы машәыршақә";
Blockly.Msg["COLOUR_RGB_BLUE"] = "жәҩангәԥштәы";
Blockly.Msg["COLOUR_RGB_GREEN"] = "аиаҵәа";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "аҟаԥшь";
Blockly.Msg["COLOUR_RGB_TITLE"] = "аԥштәы аҟынтәи";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Create a colour with the specified amount of red, green, and blue. All values must be between 0 and 100.";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "ацикл иҭыҵтәуп";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "ииастәуп ацикл анаҩстәи ашьаҿахьы";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Иааннакылоит абри ацикл.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Skip the rest of this loop, and continue with the next iteration.";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Агәҽанҵара:Ари аблок ахархәара амоуп ацикл аҩныҵҟа мацара.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "for each item %1 in list %2";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "For each item in a list, set the variable '%1' to the item, and then do some statements.";  // untranslated
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "ацикл %1 ала %2 инаркны %3 рҟынӡа ашьаҿа %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Have the variable '%1' take on the values from the start number to the end number, counting by the specified interval, and do the specified blocks.";  // untranslated
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Иацнаҵоит аҭагыазаашьа аблок \"акәзар\" ахь";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Add a final, catch-all condition to the if block.";  // untranslated
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Add, remove, or reorder sections to reconfigure this if block.";  // untranslated
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "акәымзар";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "акәымзар";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "акәзар";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Аҭагылазаашьа иашазар, инанагӡоит акомандақәа.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Аҭагылазаашьа иашазар, инанагӡоит актәи аблок акомандақәа. Акәымзар инанагӡоит аҩбатәи аблок акомандақәа.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "If the first value is true, then do the first block of statements. Otherwise, if the second value is true, do the second block of statements.";  // untranslated
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "If the first value is true, then do the first block of statements. Otherwise, if the second value is true, do the second block of statements. If none of the values are true, do the last block of statements.";  // untranslated
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://ab.wikipedia.org/wiki/Ацикл";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "инагӡатәуп";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "инагӡалатәуп %1 - нтә";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Инанагӡоит акомандақәа кырынтә";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "инагӡалатәуп акәымзар";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "инагӡалатәуп акәзар";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Аҭагылазаашьа мцнаҵы, инанагӡалоит акомандақәа.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Аҭагылазаашьа иашанаҵ, инанагӡалоит акомандақәа.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Ианыхтәуп аблокқәа (%1) зегьы?";
Blockly.Msg["DELETE_BLOCK"] = "Ианыхтәуп аблок";
Blockly.Msg["DELETE_VARIABLE"] = "Ианыхтәуп аҽеиҭак '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Delete %1 uses of the '%2' variable?";  // untranslated
Blockly.Msg["DELETE_X_BLOCKS"] = "Ианыхтәуп %1 блокк";
Blockly.Msg["DIALOG_CANCEL"] = "Аҟәыхра";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Иаҿыхтәуп Аблок";
Blockly.Msg["DUPLICATE_BLOCK"] = "Акопиа ахыхтәуп";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicate Comment";  // untranslated
Blockly.Msg["ENABLE_BLOCK"] = "Иаҿыхтәуп Аблокқәа";
Blockly.Msg["EXPAND_ALL"] = "Иаарԥштәуп Аблокқәа";
Blockly.Msg["EXPAND_BLOCK"] = "Иаарԥштәуп Аблокқәа";
Blockly.Msg["EXTERNAL_INPUTS"] = "External Inputs";  // untranslated
Blockly.Msg["HELP"] = "Ацхыраара";
Blockly.Msg["INLINE_INPUTS"] = "Inline Inputs";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "иаԥцатәуп иҭацәу ахьӡынҵа";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Returns a list, of length 0, containing no data records";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "ахьӡынҵа";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Add, remove, or reorder sections to reconfigure this list block.";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "иаԥҵатәуп ахьӡынҵа аҟынтәи";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Иацнаҵоит аелемент ахьӡынҵахьы";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Create a list with any number of items.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "актәи";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "№ анҵәамнҭа аҟынтәи";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "игатәуп";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "иалхны ианыхтәуп";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "аҵыхәтәантәи";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "иарбанзаалакь";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "ианыхтәуп";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Иҳанаҭоит ахьӡынҵа актәи аелемент.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Returns the item at the specified position in a list.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Иҳанаҭоит ахьӡынҵа аҵыхәтәантәи аелемент.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Иҳанаҭоит ахьӡынҵа иарбанзаалакь елементк.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Removes and returns the first item in a list.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Removes and returns the item at the specified position in a list.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Removes and returns the last item in a list.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Removes and returns a random item in a list.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Ианнахәуеит ахьӡынҵа актәи аелемент.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Removes the item at the specified position in a list.";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Ианнахәуеит ахьӡынҵа аҵыхәтәантәи аелемент.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Ианнахәуеит ахьӡынҵа иарбанзаалакь елементк.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "to # from end";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "№ ала";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "to last";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "get sub-list from first";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "get sub-list from # from end";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "get sub-list from #";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Creates a copy of the specified portion of a list.";  // untranslated
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 - аҵыхәтәантәи аелемент.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 - актәи аелемент.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "find first occurrence of item";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "find last occurrence of item";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Returns the index of the first/last occurrence of the item in the list. Returns %1 if item is not found.";  // untranslated
Blockly.Msg["LISTS_INLIST"] = "ахьӡынҵа аҟны";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 ҭацәуп";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Иҳанаҭоит аиаша, ахьӡынҵа ҭацәызар.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "аура %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Иҳанаҭоит ахьӡынҵа аура.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "create list with item %1 repeated %2 times";  // untranslated
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Creates a list consisting of the given value repeated the specified number of times.";  // untranslated
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Reverse a copy of a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "=";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "ибжьаргылатәуп";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "иаҭатәуп";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Inserts the item at the start of a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Inserts the item at the specified position in a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Иацнаҵоит аелемент ахьӡынҵа анҵәамҭахь.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Inserts the item randomly in a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Sets the first item in a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Sets the item at the specified position in a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Sets the last item in a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Sets a random item in a list.";  // untranslated
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";  // untranslated
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "еиҵоу-еиҳауала";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "еиҳау-еиҵоуала";
Blockly.Msg["LISTS_SORT_TITLE"] = "еилыԥшаатәуп %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Еилыԥшаатәуп ахьӡынҵа акопиа.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alphabetic, ignore case";  // untranslated
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "ахыԥхьаӡаратәи";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "алфавитла";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "make list from text";  // untranslated
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "еизгатәуп атеқст ахьӡынҵа аҟынтәи";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Join a list of texts into one text, separated by a delimiter.";  // untranslated
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Split text into a list of texts, breaking at each delimiter.";  // untranslated
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "with delimiter";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "амц";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Иҳанаҭоит аҵакы аиаша ма амц";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "аиаша";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://ru.wikipedia.org/wiki/Аиҟарамра";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Иҳанаҭоит иҵоуроу аҵакы, иҭагалақәоу еиҟаразар.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Иҳанаҭоит аҵакы аиаша, актәи иҭагалоу аҩбатәи аасҭа еиҳазар.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Иҳанаҭоит аҵакы аиаша, актәи иҭагалоу аҩбатәи аасҭа еиҳазар ма иаҟаразар.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Иҳанаҭоит иҵоуроу аҵакы, актәи иҭагалоу аҩбатәи аасҭа еиҵазар.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Иҳанаҭоит аҵакы аиаша, актәи иҭагалоу аҩбатәи аасҭа еиҵазар ма иаҟаразар.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Иҳанаҭоит иҵоуроу аҵакы, иҭагалақәоу еиҟарамзар.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "%1 акәӡам";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Иҳанаҭоит аҵакы аиаша, иҭагалоу мцызар. Иҳанаҭоит аҵакы амц, иҭагалоу иашазар.";
Blockly.Msg["LOGIC_NULL"] = "акагьы";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Иҳанаҭоит акагьы";
Blockly.Msg["LOGIC_OPERATION_AND"] = "и";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "ма";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Иҳанаҭоит аҵакы аиаша, иҭагалоу аҩбагьы иашазар.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Иҳанаҭоит аҵакы аиаша, иҭагалоу руак иашазар.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "иалхтәуп ала";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "амц акәзар";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "аиаша акәзар";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Check the condition in 'test'. If the condition is true, returns the 'if true' value; otherwise returns the 'if false' value.";  // untranslated
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://ab.wikipedia.org/wiki/Арифметика";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Иҳанаҭоит ҩ-хыԥхьаӡарак реицҵалыҵ.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Иҳанаҭоит ҩ-хыԥхьаӡарак ршалыҵ.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Иҳанаҭоит ҩ-хыԥхьаӡарак реигырхалыҵ.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Иҳанаҭоит ҩ-хыԥхьаӡарак рышьҭыхлыҵ.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Return the first number raised to the power of the second number.";  // untranslated
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";  // untranslated
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";  // untranslated
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Return the arctangent of point (X, Y) in degrees from -180 to 180.";  // untranslated
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";  // untranslated
Blockly.Msg["MATH_CHANGE_TITLE"] = "иеизырҳатәуп %1 %2 рыла";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Иацнаҵоит ахыԥхьаӡара аҽеиҭак '%1' ахь.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://ab.wikipedia.org/wiki/Аматематикатә_константа";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Иҳанаҭооит аконстантақәа руак: π (3.141...), e (2.718...), φ (1.618...), sqrt(2) (1.414...), sqrt(½) (0.707...) ма ∞ (аҵыхәаԥҵәарадара).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "constrain %1 low %2 high %3";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Constrain a number to be between the specified limits (inclusive).";  // untranslated
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "ишоит ала";
Blockly.Msg["MATH_IS_EVEN"] = "еиҩшо";
Blockly.Msg["MATH_IS_NEGATIVE"] = "иҵоурам";
Blockly.Msg["MATH_IS_ODD"] = "еиҩымшо";
Blockly.Msg["MATH_IS_POSITIVE"] = "иҵоуроу";
Blockly.Msg["MATH_IS_PRIME"] = "имариоу";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Check if a number is an even, odd, prime, whole, positive, negative, or if it is divisible by certain number. Returns true or false.";  // untranslated
Blockly.Msg["MATH_IS_WHOLE"] = "аибга";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://ru.wikipedia.org/wiki/Ашара_цәынхала";
Blockly.Msg["MATH_MODULO_TITLE"] = "ацәынха %1 : %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Иҳанаҭоит ацәынха ҩ-хыԥхьаӡарак ршараан.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://ab.wikipedia.org/wiki/Ахыԥхьаӡара";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Ахыԥхьаӡара.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "ахьӡынҵа арифметикатә бжьара";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "ахьӡынҵа аҟны иреиҳау";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "ахьӡынҵа амедиана";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "ахьӡынҵа аҟны иреиҵо";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "ахьӡынҵа амода";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "ахьӡынҵа иарбанзаалакь аелемент";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standard deviation of list";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "ахьӡынҵа аицҵалыҵ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Иҳанаҭоит ахьӡынҵа аҟны ахыԥхьаӡарақәа зегьы рарифметикатә бжьара.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Иҳанаҭоит ахьӡынҵа аҟны иреиҳау ахыԥхьаӡара.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Иҳанаҭоит ахьӡынҵа аҟны ахыԥхьаӡарақәа зегьы рмедиана.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Иҳанаҭоит ахьӡынҵа аҟны иреицо ахыԥхьаӡара.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Return a list of the most common item(s) in the list.";  // untranslated
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Иҳанаҭоит ахьӡынҵа аҟны иарбанзаалакь елементк.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Return the standard deviation of the list.";  // untranslated
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Иҳанаҭоит ахьӡынҵа иаҵанакуа ахыԥхьаӡарақәа зегьы реицҵалыҵ.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "иарбанзаалакь ахыԥхьашара 0 инаркны (иалаҵаны)  1 аҟынӡа";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Иҳанаҭоит иарбанзаалакь ахыԥхьаӡара 0.0 инаркны (иалаҵаны) 1.0 аҟынӡа.";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";  // untranslated
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "иарбанзаалакь еибгоу ахыԥхьаӡара %1 инаркны %2 нӡа";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Return a random integer between the two specified limits, inclusive.";  // untranslated
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://ab.wikipedia.org/wiki/Ахыркәшара";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "ихыркәшатәуп";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "ихыркәшатәуп еиҵоу ахь";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "ихыркәшатәуп еиҳау ахь";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Round a number up or down.";  // untranslated
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/Square_root";  // untranslated
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "амодуль";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "square root";  // untranslated
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Иҳанаҭоит ахыԥхьаӡара амодуль.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Return e to the power of a number.";  // untranslated
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Иҳанаҭоит ахыԥхьаӡара иԥсабаратәу алогарифм.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Иҳанаҭоит ахыԥхьаӡара ажәабатә логарифм.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Иҳанаҭоит иаҿагыло ахыԥхьаӡара.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Return 10 to the power of a number.";  // untranslated
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Return the square root of a number.";  // untranslated
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://ab.wikipedia.org/wiki/Атригонометриатә_функциа";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Иҳанаҭоит арккосинус градусла.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Иҳанаҭоит арксинус градусла.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Иҳанаҭоит арктангенс градусла.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Иҳанаҭоит акосинус градусла.";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Иҳанаҭоит асинус градусла.";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Иҳанаҭоит атангенс градусла.";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Create colour variable...";  // untranslated
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Create number variable...";  // untranslated
Blockly.Msg["NEW_STRING_VARIABLE"] = "Create string variable...";  // untranslated
Blockly.Msg["NEW_VARIABLE"] = "Иаԥҵатәуп аҽеиҭак";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Аҽеиҭак ахьӡ ҿыц:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "New variable type:";  // untranslated
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "аоператорқәа азин рыҭара";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "аҟынтәи:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://ru.wikipedia.org/wiki/Ацхыраагӡатә программа";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Run the user-defined function '%1'.";  // untranslated
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://ru.wikipedia.org/wiki/Ацхыраагӡатә программа";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Run the user-defined function '%1' and use its output.";  // untranslated
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "аҟынтәи:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Иаԥҵатәуп ааԥхьара '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Абри афункциа ахҳәа азыҟашәҵа...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "иҟаҵатәуп џьара акы";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "азы";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Иаԥнаҵоит апроцедура, аҵакы ҳазымҭо.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "ирхынҳәтәуп";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Иаԥнаҵоит апроцедура, аҵакы ҳазҭо.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Warning: This function has duplicate parameters.";  // untranslated
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Highlight function definition";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Актәи аҵакы иашазар, иҳанаҭоит аҩбатәи аҵакы.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Агәаҽанҵара: Ари аблок ахархәара ауеит афункциа аԥҵара аҟны.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "апараметр ахьӡ:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Add an input to the function.";  // untranslated
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "апараметрқәа";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Add, remove, or reorder inputs to this function.";  // untranslated
Blockly.Msg["REDO"] = "Аиҭаҟаҵара";
Blockly.Msg["REMOVE_COMMENT"] = "Ианыхтәуп ахцәажәара";
Blockly.Msg["RENAME_VARIABLE"] = "Аҽеиҭак ахьӡ ԥсахтәуп";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Аҽеиҭакқәа'%1' зегь рыхьӡ ԥсахтәуп аҟны:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "%1 ахьы иацҵатәуп атеқст %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Иацҵатәуп атеқст аҽеиҭак «%1» ахь.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "to lower case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "to Title Case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "to UPPER CASE";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Return a copy of the text in a different case.";  // untranslated
Blockly.Msg["TEXT_CHARAT_FIRST"] = "игатәуп актәи анбан";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "игатәуп анбан № анҵәамҭа аҟынтәи";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "игатәуп анбан №";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "игатәуп аҵыхәтәантәи анбан";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "игатәуп иарбанзаалакь нбанк";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "атеқст %1 %2 аҟны";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Иҳанаҭоит анбан иарбоу апозициа аҟны";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "count %1 in %2";  // untranslated
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Count how many times some text occurs within some other text.";  // untranslated
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Иацҵатәуп аелемент атеқст ахь.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "иеиԥшьтәуп";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Add, remove, or reorder sections to reconfigure this text block.";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "to letter # from end";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "анбан № ала";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "аҵыхәтәантәи анбан ала";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "атеқст аҟны";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "get substring from first letter";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "get substring from letter # from end";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "get substring from letter #";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Иҳанаҭоит атеқст аҟны иарбоу ахәҭа.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "find first occurrence of text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "find last occurrence of text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "атеқст %1 %2 %3 аҟны";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Returns the index of the first/last occurrence of the first text in the second text. Returns %1 if text is not found.";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 ҭацәуп";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Иҳанаҭоит аҵакы аиаша, иҟоу атеқст ҭацәызар.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "иаԥҵатәуп атеқст аҟынтәи";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Create a piece of text by joining together any number of items.";  // untranslated
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "аура %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Иҳанаҭоит асимволқәа рхыԥхьаӡара (абжьажьқәа алаҵаны) иҟоу атеқст аҟны.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "икьыԥхьтәуп %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Иакьыԥхьуеит атеқст, ахыԥхьаӡара ма даҽа обиеқтк.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Prompt for user for a number.";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Prompt for user for some text.";  // untranslated
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "prompt for number with message";  // untranslated
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "prompt for text with message";  // untranslated
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "иԥсахтәуп %1 %2 ала %3 аҟны";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Replace all occurances of some text within some other text.";  // untranslated
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Reverses the order of the characters in the text.";  // untranslated
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://ab.wikipedia.org/wiki/Ацәаҳәатә_хкы";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Анбан, ажәа ма ацәаҳәа атеқст аҟны.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "trim spaces from both sides of";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "trim spaces from left side of";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "trim spaces from right side of";  // untranslated
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Return a copy of the text with spaces removed from one or both ends.";  // untranslated
Blockly.Msg["TODAY"] = "Иахьа";
Blockly.Msg["UNDO"] = "Иаҟәыхтәуп";
Blockly.Msg["UNNAMED_KEY"] = "unnamed";  // untranslated
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "аелемент";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Иаԥҵатәуп аблок \"иаҭатәуп %1\" азы";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Иҳанаҭоит аҽеиҭак аҵакы.";
Blockly.Msg["VARIABLES_SET"] = "иаҭатәуп %1 = %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Create 'get %1'";  // untranslated
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Sets this variable to be equal to the input.";  // untranslated
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Аҽеиҭак ахьӡ '%1' змоу ыҟоуп.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "A variable named '%1' already exists for another type: '%2'.";  // untranslated
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Say something...";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";