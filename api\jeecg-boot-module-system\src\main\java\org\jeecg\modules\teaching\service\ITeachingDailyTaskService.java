package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingDailyTask;

import java.util.List;
import java.util.Map;

/**
 * 每日任务服务接口
 */
public interface ITeachingDailyTaskService extends IService<TeachingDailyTask> {

    /**
     * 获取用户每日任务状态
     *
     * @param userId 用户ID
     * @return 任务状态列表
     */
    Result<List<Map<String, Object>>> getUserDailyTasks(String userId);

    /**
     * 完成每日签到
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Integer> completeSignIn(String userId);

    /**
     * 处理评论作品任务
     *
     * @param userId 用户ID
     * @param workId 作品ID
     * @return 获得的金币数量
     */
    Result<Integer> handleCommentTask(String userId, String workId);

    /**
     * 处理点赞作品任务
     *
     * @param userId 用户ID
     * @param workId 作品ID
     * @return 获得的金币数量
     */
    Result<Integer> handleLikeTask(String userId, String workId);
} 