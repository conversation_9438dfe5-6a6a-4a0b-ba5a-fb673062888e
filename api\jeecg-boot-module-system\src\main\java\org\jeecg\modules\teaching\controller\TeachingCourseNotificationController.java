package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;


import org.jeecg.modules.system.service.ISysConfigService;
import org.jeecg.modules.teaching.constant.NotificationConstant;
import org.jeecg.modules.teaching.entity.TeachingCourseNotification;
import org.jeecg.modules.teaching.entity.TeachingCourseSchedule;
import org.jeecg.modules.teaching.service.ITeachingCourseNotificationService;
import org.jeecg.modules.teaching.service.ITeachingCourseScheduleService;
import org.jeecg.modules.teaching.service.ITeachingNotificationReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 课程通知
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Api(tags = "课程通知")
@RestController
@RequestMapping("/teaching/notification")
@Slf4j
public class TeachingCourseNotificationController extends JeecgController<TeachingCourseNotification, ITeachingCourseNotificationService> {

    @Autowired
    private ITeachingCourseNotificationService teachingCourseNotificationService;

    @Autowired
    private ITeachingCourseScheduleService teachingCourseScheduleService;



    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ITeachingNotificationReadService teachingNotificationReadService;

    private static final String CONFIG_KEY_NOTIFY_BEFORE_MINUTES = "courseNotifyBeforeMinutes";

    /**
     * 获取系统默认通知提前时间(分钟)
     *
     * @return 默认通知提前时间，如果配置不存在或无效则返回15分钟
     */
    private Integer getDefaultNotifyBeforeMinutes() {
        try {
            String configValue = sysConfigService.getConfigItem(CONFIG_KEY_NOTIFY_BEFORE_MINUTES);
            if (configValue != null && !configValue.isEmpty()) {
                return Integer.parseInt(configValue);
            }
        } catch (Exception e) {
            log.error("获取默认通知时间配置异常", e);
        }
        return 15;
    }

    /**
     * 分页列表查询
     */
    @AutoLog(value = "课程通知-分页列表查询")
    @ApiOperation(value = "课程通知-分页列表查询", notes = "课程通知-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingCourseNotification teachingCourseNotification,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<TeachingCourseNotification> queryWrapper = QueryGenerator.initQueryWrapper(teachingCourseNotification, req.getParameterMap());
        Page<TeachingCourseNotification> page = new Page<>(pageNo, pageSize);
        IPage<TeachingCourseNotification> pageList = teachingCourseNotificationService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "课程通知-添加")
    @ApiOperation(value = "课程通知-添加", notes = "课程通知-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TeachingCourseNotification teachingCourseNotification) {
        teachingCourseNotificationService.save(teachingCourseNotification);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "课程通知-编辑")
    @ApiOperation(value = "课程通知-编辑", notes = "课程通知-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody TeachingCourseNotification teachingCourseNotification) {
        teachingCourseNotificationService.updateById(teachingCourseNotification);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "课程通知-通过id删除")
    @ApiOperation(value = "课程通知-通过id删除", notes = "课程通知-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        teachingCourseNotificationService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "课程通知-批量删除")
    @ApiOperation(value = "课程通知-批量删除", notes = "课程通知-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.teachingCourseNotificationService.removeByIds(java.util.Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "课程通知-通过id查询")
    @ApiOperation(value = "课程通知-通过id查询", notes = "课程通知-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        TeachingCourseNotification teachingCourseNotification = teachingCourseNotificationService.getById(id);
        if (teachingCourseNotification == null) {
            return Result.error("未找到对应数据");
        }
        
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            // 查看通知详情时自动创建未读记录（如果不存在记录）
            // 注意：这里只创建记录，不立即标记为已读，遵循懒处理机制
            try {
                teachingNotificationReadService.markReadStatus(id, sysUser.getId(), false);
            } catch (Exception e) {
                log.error("创建通知读取状态记录失败", e);
            }
        }
        
        return Result.ok(teachingCourseNotification);
    }

    /**
     * 获取当前用户的课程通知列表
     */
    @AutoLog(value = "获取当前用户的课程通知列表")
    @ApiOperation(value = "获取当前用户的课程通知列表", notes = "获取当前用户的课程通知列表")
    @GetMapping(value = "/myNotifications")
    public Result<?> myNotifications(
        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
        @RequestParam(name = "notificationType", required = false) String notificationType) {
        
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        // 查询当前用户的课程通知
        Page<TeachingCourseNotification> page = new Page<>(pageNo, pageSize);
        IPage<TeachingCourseNotification> pageList = teachingCourseNotificationService.getUserNotifications(page, sysUser.getId(), notificationType);
        
        return Result.ok(pageList);
    }

    /**
     * 创建课程通知
     */
    @AutoLog(value = "创建课程通知")
    @ApiOperation(value = "创建课程通知", notes = "创建课程通知")
    @PostMapping(value = "/createNotification")
    public Result<?> createNotification(@RequestParam(name = "scheduleId", required = true) String scheduleId,
                                       @RequestParam(name = "notificationType", required = true) String notificationType,
                                       @RequestParam(name = "notifyTeachers", defaultValue = "true") Boolean notifyTeachers,
                                       @RequestParam(name = "notifyStudents", defaultValue = "true") Boolean notifyStudents) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }

        // 查询课程排期
        TeachingCourseSchedule schedule = teachingCourseScheduleService.getById(scheduleId);
        if (schedule == null) {
            return Result.error("未找到对应课程排期信息");
        }
        
        // 确保参数是有效的布尔值
        boolean notifyTeachersFlag = notifyTeachers != null && notifyTeachers;
        boolean notifyStudentsFlag = notifyStudents != null && notifyStudents;
        
        log.info("创建通知参数: scheduleId={}, notificationType={}, notifyTeachers={}, notifyStudents={}", 
                scheduleId, notificationType, notifyTeachersFlag, notifyStudentsFlag);

        boolean success = false;

        switch (notificationType) {
            case NotificationConstant.NOTIFICATION_TYPE_NEW:
                success = teachingCourseNotificationService.createNewCourseNotification(schedule, notifyTeachersFlag, notifyStudentsFlag);
                break;
            case NotificationConstant.NOTIFICATION_TYPE_CANCEL:
                success = teachingCourseNotificationService.createCancelCourseNotification(schedule, notifyTeachersFlag, notifyStudentsFlag);
                break;
            case NotificationConstant.NOTIFICATION_TYPE_REMIND:
                // 从系统配置中获取默认提醒时间
                long remindMinutes = getDefaultNotifyBeforeMinutes();
                Date remindTime = new Date(schedule.getStartTime().getTime() - remindMinutes * 60 * 1000);
                success = teachingCourseNotificationService.createCourseReminderNotification(schedule, remindTime, notifyTeachersFlag, notifyStudentsFlag);
                break;
            default:
                return Result.error("不支持的通知类型");
        }

        if (success) {
            return Result.ok("通知创建成功!");
        } else {
            return Result.error("通知创建失败!");
        }
    }

    /**
     * 创建课程提醒通知
     */
    @AutoLog(value = "创建课程提醒通知")
    @ApiOperation(value = "创建课程提醒通知", notes = "创建课程提醒通知")
    @PostMapping(value = "/createReminderNotification")
    public Result<?> createReminderNotification(@RequestParam(name = "scheduleId", required = true) String scheduleId,
                                             @RequestParam(name = "remindMinutes", required = false) Integer remindMinutes,
                                             @RequestParam(name = "notifyTeachers", defaultValue = "true") Boolean notifyTeachers,
                                             @RequestParam(name = "notifyStudents", defaultValue = "true") Boolean notifyStudents) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }

        // 查询课程排期
        TeachingCourseSchedule schedule = teachingCourseScheduleService.getById(scheduleId);
        if (schedule == null) {
            return Result.error("未找到对应课程排期信息");
        }
        
        // 确保参数是有效的布尔值
        boolean notifyTeachersFlag = notifyTeachers != null && notifyTeachers;
        boolean notifyStudentsFlag = notifyStudents != null && notifyStudents;
        
        log.info("创建提醒通知参数: scheduleId={}, remindMinutes={}, notifyTeachers={}, notifyStudents={}", 
                scheduleId, remindMinutes, notifyTeachersFlag, notifyStudentsFlag);

        // 如果没有指定提醒时间，则使用系统默认配置
        if (remindMinutes == null || remindMinutes <= 0) {
            remindMinutes = getDefaultNotifyBeforeMinutes();
        }

        // 计算提醒时间
        Date remindTime = new Date(schedule.getStartTime().getTime() - remindMinutes * 60 * 1000);
        Date now = new Date();
        
        // 如果提醒时间已过，则设置为当前时间之后5分钟
        if (remindTime.before(now)) {
            // 计算调整后的时间（当前时间后5分钟，并确保分钟为偶数，秒数为00）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.MINUTE, 5); // 先加5分钟
            
            // 调整分钟为偶数
            int minute = calendar.get(Calendar.MINUTE);
            if (minute % 2 != 0) {
                calendar.add(Calendar.MINUTE, 1); // 如果是奇数，加1变成偶数
            }
            
            // 调整秒数为00
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            
            remindTime = calendar.getTime();
        }

        boolean success = teachingCourseNotificationService.createCourseReminderNotification(schedule, remindTime, notifyTeachersFlag, notifyStudentsFlag);

        if (success) {
            return Result.ok("提醒通知创建成功!");
        } else {
            return Result.error("提醒通知创建失败!");
        }
    }
    
    /**
     * 手动处理通知发送
     */
    @AutoLog(value = "手动处理通知发送")
    @ApiOperation(value = "手动处理通知发送", notes = "手动处理通知发送")
    @GetMapping(value = "/processNotifications")
    public Result<?> processNotifications() {
        int count = teachingCourseNotificationService.processNotifications();
        return Result.ok("处理了 " + count + " 条通知");
    }

    /**
     * 手动处理课程提醒通知
     */
    @AutoLog(value = "手动处理课程提醒通知")
    @ApiOperation(value = "手动处理课程提醒通知", notes = "手动处理课程提醒通知")
    @GetMapping(value = "/processReminderNotifications")
    public Result<?> processReminderNotifications() {
        int count = teachingCourseNotificationService.processReminderNotifications();
        return Result.ok("处理了 " + count + " 条提醒通知");
    }
    
    // 以下为新增接口
    
    /**
     * 获取当前用户的最近课程通知
     */
    @AutoLog(value = "获取当前用户的最近课程通知")
    @ApiOperation(value = "获取当前用户的最近课程通知", notes = "获取当前用户的最近课程通知")
    @GetMapping(value = "/recentNotifications")
    public Result<?> recentNotifications(@RequestParam(name = "notificationType", required = false) String notificationType) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        // 查询当前用户的最近课程通知（最多5条）
        Page<TeachingCourseNotification> page = new Page<>(1, 5);
        IPage<TeachingCourseNotification> pageList = teachingCourseNotificationService.getUserRecentNotifications(page, sysUser.getId(), notificationType);
        
        return Result.ok(pageList);
    }

    /**
     * 标记通知为已读
     */
    @AutoLog(value = "标记通知为已读")
    @ApiOperation(value = "标记通知为已读", notes = "标记通知为已读")
    @GetMapping(value = "/markAsRead")
    public Result<?> markAsRead(@RequestParam(name = "id", required = true) String id) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        boolean success = teachingCourseNotificationService.markAsRead(id, sysUser.getId());
        
        if (success) {
            return Result.ok("标记已读成功");
        } else {
            return Result.error("标记已读失败");
        }
    }

    /**
     * 检查通知的已读状态
     */
    @AutoLog(value = "检查通知的已读状态")
    @ApiOperation(value = "检查通知的已读状态", notes = "检查通知的已读状态")
    @GetMapping(value = "/checkReadStatus")
    public Result<?> checkReadStatus(@RequestParam(name = "ids", required = true) String ids) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        // 分割通知ID
        String[] idArray = ids.split(",");
        List<String> idList = Arrays.asList(idArray);
        
        // 查询已读状态
        List<String> readIds = teachingNotificationReadService.getReadNotificationIds(sysUser.getId(), idList);
        
        return Result.ok(readIds);
    }

    /**
     * 获取当前用户未读通知数量
     */
    @AutoLog(value = "获取当前用户未读通知数量")
    @ApiOperation(value = "获取当前用户未读通知数量", notes = "获取当前用户未读通知数量")
    @GetMapping(value = "/getUnreadCount")
    public Result<?> getUnreadCount() {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        int count = teachingNotificationReadService.getUnreadCount(sysUser.getId());
        return Result.ok(count);
    }

    /**
     * 发送单条通知
     */
    @AutoLog(value = "发送单条通知")
    @ApiOperation(value = "发送单条通知", notes = "发送单条通知")
    @GetMapping(value = "/sendNotification")
    public Result<?> sendNotification(@RequestParam(name = "id", required = true) String id) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        // 获取通知对象
        TeachingCourseNotification notification = teachingCourseNotificationService.getById(id);
        if (notification == null) {
            return Result.error("未找到该通知");
        }
        
        // 如果通知已发送，提示用户
        if (NotificationConstant.NOTIFICATION_SENT.equals(notification.getIsSent())) {
            return Result.error("该通知已发送，无需重复发送");
        }
        
        // 发送通知
        boolean success = teachingCourseNotificationService.sendNotification(notification);
        if (success) {
            return Result.ok("通知发送成功");
        } else {
            return Result.error("通知发送失败");
        }
    }

    /**
     * 标记当前用户所有通知为已读
     */
    @AutoLog(value = "标记当前用户所有通知为已读")
    @ApiOperation(value = "标记当前用户所有通知为已读", notes = "标记当前用户所有通知为已读")
    @GetMapping(value = "/markAllAsRead")
    public Result<?> markAllAsRead() {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            return Result.error("请先登录");
        }
        
        try {
            // 1. 获取所有与当前用户相关的通知
            List<TeachingCourseNotification> notifications = teachingCourseNotificationService.getUserAllNotifications(sysUser.getId());
            
            if (notifications == null || notifications.isEmpty()) {
                return Result.ok("没有需要标记的通知");
            }
            
            // 2. 标记所有通知为已读
            int count = 0;
            for (TeachingCourseNotification notification : notifications) {
                boolean success = teachingCourseNotificationService.markAsRead(notification.getId(), sysUser.getId());
                if (success) {
                    count++;
                }
            }
            
            return Result.ok("成功标记 " + count + " 条通知为已读");
        } catch (Exception e) {
            log.error("标记所有通知为已读失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }
} 