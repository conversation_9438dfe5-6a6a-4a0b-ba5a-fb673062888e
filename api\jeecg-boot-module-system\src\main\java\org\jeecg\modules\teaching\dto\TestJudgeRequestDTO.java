package org.jeecg.modules.teaching.dto;

import lombok.Data;

/**
 * 测试判题请求DTO
 */
@Data
public class TestJudgeRequestDTO {
    
    /**
     * 题目ID（可选）
     */
    private String pid;
    
    /**
     * 时间限制
     */
    private Integer timeLimit;

    /**
     * 内存限制
     */
    private Integer memoryLimit;

    /**
     * 堆栈限制
     */
    private Integer stackLimit;
    
    /**
     * 编程语言
     */
    private String language;
    
    /**
     * 代码
     */
    private String code;
    
    /**
     * 用户输入
     */
    private String testCaseInput;
    
    /**
     * 预期输出
     */
    private String expectedOutput;
} 