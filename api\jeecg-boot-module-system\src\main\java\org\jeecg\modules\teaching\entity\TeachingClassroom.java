package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 教室
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Data
@TableName("teaching_classroom")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="teaching_classroom对象", description="教室")
public class TeachingClassroom implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    

    /**教室名称*/
    @Excel(name = "教室名称", width = 15)
    @ApiModelProperty(value = "教室名称")
    // 此处有映射，但只有Excel注解和ApiModelProperty注解
    // Excel注解用于Excel导入导出功能，指定了列名和宽度
    // ApiModelProperty注解用于Swagger API文档生成
    // 没有添加数据库列映射注解如@TableField，因为默认情况下，
    // MyBatis-Plus会自动将驼峰命名的属性映射到下划线命名的数据库字段
    // 例如：classroomName 将自动映射到 classroom_name 字段
    private String classroomName;
    // End of Selection
    
    /**教室容量*/
    @Excel(name = "教室容量", width = 15)
    @ApiModelProperty(value = "教室容量")
    private Integer capacity;
    
    /**教室类型*/
    @Excel(name = "教室类型", width = 15)
    @ApiModelProperty(value = "教室类型")
    private Integer roomType;
    
    /**电脑/平板配置*/
    @Excel(name = "电脑/平板配置", width = 15)
    @ApiModelProperty(value = "电脑/平板配置")
    private String equipment;
    
    /**教具配置*/
    @Excel(name = "教具配置", width = 15)
    @ApiModelProperty(value = "教具配置")
    private String teachingTools;
    
    /**状态*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;
    
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 