package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.teaching.entity.TeachingNotificationRead;

import java.util.List;

/**
 * @Description: 课程通知已读状态
 * @Author: jeecg-boot
 * @Date:   2025-06-26
 * @Version: V1.0
 */
public interface ITeachingNotificationReadService extends IService<TeachingNotificationRead> {
    /**
     * 获取用户未读通知数量
     * @param userId 用户ID
     * @return 未读数量
     */
    int getUnreadCount(String userId);
    
    /**
     * 获取用户已读的通知ID列表
     * @param userId 用户ID
     * @param notificationIds 需要检查的通知ID列表
     * @return 已读的通知ID列表
     */
    List<String> getReadNotificationIds(String userId, List<String> notificationIds);
    
    /**
     * 标记通知为已读或创建未读记录
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @param isRead 是否标记为已读
     * @return 操作结果
     */
    TeachingNotificationRead markReadStatus(String notificationId, String userId, boolean isRead);
}