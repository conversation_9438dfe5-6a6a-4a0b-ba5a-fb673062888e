{"@metadata": {"authors": ["Chrumps", "Cirasean", "<PERSON><PERSON><PERSON><PERSON>", "Expert3222", "<PERSON><PERSON>", "Fringoo", "InternerowyGołąb", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Krzyz23", "Liuxinyu970226", "Mateon1", "Mazab IZW", "Pbz", "Pio387", "Rail", "<PERSON><PERSON>je<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "WaldiSt", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "아라"]}, "VARIABLES_DEFAULT_NAME": "element", "UNNAMED_KEY": "bez nazwy", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON>", "ADD_COMMENT": "<PERSON><PERSON><PERSON>", "REMOVE_COMMENT": "Us<PERSON>ń komentarz", "DUPLICATE_COMMENT": "Zduplikowany komentarz", "EXTERNAL_INPUTS": "Zewnętrzne Wejścia", "INLINE_INPUTS": "Wbudowan<PERSON>", "DELETE_BLOCK": "<PERSON><PERSON><PERSON>", "DELETE_X_BLOCKS": "Usuń %1 Bloki(ów)", "DELETE_ALL_BLOCKS": "Usunąć wszystkie klocki z %1?", "CLEAN_UP": "Uporządkuj Bloki", "COLLAPSE_BLOCK": "<PERSON><PERSON><PERSON>", "COLLAPSE_ALL": "Zwiń Bloki", "EXPAND_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "EXPAND_ALL": "Rozwiń Bloki", "DISABLE_BLOCK": "Wyłącz Klocek", "ENABLE_BLOCK": "Włącz Blok", "HELP": "Pomoc", "UNDO": "Cof<PERSON>j", "REDO": "Ponów", "CHANGE_VALUE_TITLE": "<PERSON><PERSON><PERSON> wartość:", "RENAME_VARIABLE": "Zmień nazwę zmiennej...", "RENAME_VARIABLE_TITLE": "Zmień nazwy wszystkich '%1' zmiennych na:", "NEW_VARIABLE": "Utwórz zmienną...", "NEW_STRING_VARIABLE": "Utwórz zmienną typu string", "NEW_NUMBER_VARIABLE": "Utwórz zmienną typu number", "NEW_COLOUR_VARIABLE": "Utwórz zmienną colour", "NEW_VARIABLE_TYPE_TITLE": "Nowy typ zmiennej:", "NEW_VARIABLE_TITLE": "Nowa nazwa zmiennej:", "VARIABLE_ALREADY_EXISTS": "Zmienna o nazwie '%1' już istnieje.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Zmienna o nazwie '%1' już istnieje i jest typu '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Usunąć %1 wystąpień zmiennej '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "<PERSON><PERSON> można us<PERSON> zmiennej '%1', poni<PERSON><PERSON>ż jest częś<PERSON>ą definicji funkcji '%2'", "DELETE_VARIABLE": "Usuń zmienną '%1'", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Wybierz kolor z palety.", "COLOUR_RANDOM_TITLE": "losowy kolor", "COLOUR_RANDOM_TOOLTIP": "Wybierz kolor w sposób losowy.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "kolor z", "COLOUR_RGB_RED": "czerwony", "COLOUR_RGB_GREEN": "zielony", "COLOUR_RGB_BLUE": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_RGB_TOOLTIP": "Utwórz kolor składający sie z podanej ilości czerwieni, zieleni i błękitu. Zakres wartości: 0 do 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "wymieszaj", "COLOUR_BLEND_COLOUR1": "kolor 1", "COLOUR_BLEND_COLOUR2": "kolor 2", "COLOUR_BLEND_RATIO": "<PERSON><PERSON><PERSON><PERSON>", "COLOUR_BLEND_TOOLTIP": "Miesza dwa kolory w danej proporcji (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "powtórz %1 razy", "CONTROLS_REPEAT_INPUT_DO": "wykonaj", "CONTROLS_REPEAT_TOOLTIP": "Wykonaj niektóre instrukcje kilka razy.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "powtarzaj dopóki", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "powtarzaj aż do", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Dopóki wyrażenie jest prawdziwe, wykonaj zada<PERSON>.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Dopóki wyrażenie jest nieprawdziwe, wyk<PERSON>j zada<PERSON>.", "CONTROLS_FOR_TOOLTIP": "Zmiennej '%1' przypisuje wartości z podanego zakresu z podanym interwałem i wykonuje zadane bloki.", "CONTROLS_FOR_TITLE": "licz z %1 od %2 do %3 co %4 (wartość kroku)", "CONTROLS_FOREACH_TITLE": "dla każdego elementu %1 listy %2", "CONTROLS_FOREACH_TOOLTIP": "Przypisz zmiennej '%1' kolejno wartość każdego elementu listy, a następnie wykonaj kilka instrukcji.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "przer<PERSON>j p<PERSON>tl<PERSON>", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "przejdź do kolejnej iteracji pętli", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Przerwij działanie pętli.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Pomiń resztę pętli i kontynuuj w kolejnej iteracji.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Uwaga: Ten klocek może być użyty tylko wewnątrz pętli.", "CONTROLS_IF_TOOLTIP_1": "<PERSON><PERSON><PERSON> warunek jest speł<PERSON>, wyk<PERSON>j zadane <PERSON>.", "CONTROLS_IF_TOOLTIP_2": "<PERSON><PERSON><PERSON> warunek jest speł<PERSON>, wykonaj pier<PERSON>zy blok instrukcji.  W przeciwnym razie, wykonaj drugi blok instrukcji.", "CONTROLS_IF_TOOLTIP_3": "<PERSON><PERSON><PERSON> pierwszy warunek jest spełniony, wykonaj pierwszy blok instrukcji.  W przeci<PERSON>ym razie, jeśli drugi warunek jest spełniony, wykonaj drugi blok instrukcji.", "CONTROLS_IF_TOOLTIP_4": "<PERSON><PERSON><PERSON> pierwszy warunek jest spełniony, wykonaj pierwszy blok czynności.  W przeciwnym razie jeśli drugi warunek jest spełniony, wykonaj drugi blok czynności.  Je<PERSON><PERSON> żaden z warunków nie zostanie spełniony, wykonaj ostatni blok czynności.", "CONTROLS_IF_MSG_IF": "je<PERSON><PERSON>", "CONTROLS_IF_MSG_ELSEIF": "w przeciwnym razie, je<PERSON>li", "CONTROLS_IF_MSG_ELSE": "w przeciwnym razie", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON>, usuń lub zmień kolejno<PERSON>, <PERSON><PERSON><PERSON> zmodyfikować ten klocek „jeśli”.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Dodaj warunek do klocka „jeśli”.", "CONTROLS_IF_ELSE_TOOLTIP": "Dodaj ostatni zawsze prawdziwy warunek do klocka „jeśli”.", "IOS_OK": "OK", "IOS_CANCEL": "<PERSON><PERSON><PERSON>", "IOS_ERROR": "Błąd", "IOS_PROCEDURES_INPUTS": "WEJŚCIA", "IOS_PROCEDURES_ADD_INPUT": "+ <PERSON><PERSON><PERSON>", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Zezwalaj na czynności.", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Ta funkcja ma zduplikowane wejścia.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON><PERSON>", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "Zmień nazwę", "IOS_VARIABLES_DELETE_BUTTON": "Usuń", "IOS_VARIABLES_VARIABLE_NAME": "Nazwa zmiennej", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Nie możesz utworzyć funkcji bez nazwy.", "LOGIC_COMPARE_HELPURL": "https://pl.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "LOGIC_COMPARE_TOOLTIP_EQ": "Zwrac<PERSON> \"prawda\", jeś<PERSON> wejścia są identyczne.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Zwrac<PERSON> \"prawda\", je<PERSON><PERSON> wejścia nie są identyczne.", "LOGIC_COMPARE_TOOLTIP_LT": "<PERSON><PERSON><PERSON><PERSON> \"prawda\" je<PERSON><PERSON> pierwsze wejście jest mniejsze od drugiego.", "LOGIC_COMPARE_TOOLTIP_LTE": "Zw<PERSON><PERSON> \"prawda\", je<PERSON><PERSON> pierwsze wejście jest mniejsze lub równe drugiemu.", "LOGIC_COMPARE_TOOLTIP_GT": "Z<PERSON><PERSON><PERSON> \"prawda\" je<PERSON><PERSON> pierwsze wejście jest większe od drugiego.", "LOGIC_COMPARE_TOOLTIP_GTE": "Zw<PERSON><PERSON> \"prawda\", je<PERSON><PERSON> pierwsze wejście jest większe lub równe drugiemu.", "LOGIC_OPERATION_TOOLTIP_AND": "Zwrac<PERSON> \"prawda\" jeśli na obydwóch wejściach jest \"prawda\".", "LOGIC_OPERATION_AND": "i", "LOGIC_OPERATION_TOOLTIP_OR": "Zwraca \"prawda\" jeśli co najmniej na jednyk wejściu jest \"prawda\".", "LOGIC_OPERATION_OR": "lub", "LOGIC_NEGATE_TITLE": "nie %1", "LOGIC_NEGATE_TOOLTIP": "Zwraca \"prawda\", jeśli wejściu jest \"fałsz\". Zwraca \"fałsz\", jeśli na wejściu jest \"prawda\".", "LOGIC_BOOLEAN_TRUE": "prawda", "LOGIC_BOOLEAN_FALSE": "fałsz", "LOGIC_BOOLEAN_TOOLTIP": "Zwraca 'prawda' lub 'fał<PERSON>'.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "nic", "LOGIC_NULL_TOOLTIP": "Zwraca nic.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "je<PERSON><PERSON> prawda", "LOGIC_TERNARY_IF_FALSE": "je<PERSON><PERSON> fałsz", "LOGIC_TERNARY_TOOLTIP": "Spraw<PERSON><PERSON> warunek w „test”. <PERSON><PERSON><PERSON> warunek jest prawdziwy, to <PERSON><PERSON><PERSON><PERSON><PERSON> „jeśli prawda”; jeśli nie jest prawdziwy to <PERSON><PERSON><PERSON><PERSON><PERSON> „jeśli fałsz”.", "MATH_NUMBER_HELPURL": "https://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "Liczba.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "/", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tg", "MATH_TRIG_ASIN": "arcsin", "MATH_TRIG_ACOS": "arccos", "MATH_TRIG_ATAN": "arctg", "MATH_ARITHMETIC_HELPURL": "https://pl.wikipedia.org/wiki/<PERSON>rytmetyka", "MATH_ARITHMETIC_TOOLTIP_ADD": "Z<PERSON><PERSON><PERSON><PERSON> sumę dwóch liczb.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "<PERSON><PERSON><PERSON><PERSON>ć różnicę dwóch liczb.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Z<PERSON><PERSON><PERSON>ć iloczyn dwóch liczb.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Z<PERSON><PERSON><PERSON>ć iloraz dw<PERSON>ch liczb.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Zwróć pierwszą liczbę podniesioną do potęgi o wykładniku drugiej liczby.", "MATH_SINGLE_HELPURL": "https://pl.wikipedia.org/wiki/Pierwiastek_kwadratowy", "MATH_SINGLE_OP_ROOT": "pierwiastek kwadratowy", "MATH_SINGLE_TOOLTIP_ROOT": "Z<PERSON><PERSON><PERSON>ć pierwiastek kwadratowy danej liczby.", "MATH_SINGLE_OP_ABSOLUTE": "wartość bezwzględna", "MATH_SINGLE_TOOLTIP_ABS": "<PERSON><PERSON><PERSON><PERSON><PERSON> war<PERSON> bezwzględną danej liczby.", "MATH_SINGLE_TOOLTIP_NEG": "<PERSON><PERSON><PERSON><PERSON>ć negację danej l<PERSON>.", "MATH_SINGLE_TOOLTIP_LN": "Zwróć logarytm naturalny danej liczby.", "MATH_SINGLE_TOOLTIP_LOG10": "Zwraca logarytm dziesiętny danej liczby.", "MATH_SINGLE_TOOLTIP_EXP": "Zwróć e do potęgi danej liczby.", "MATH_SINGLE_TOOLTIP_POW10": "Zwróć 10 do potęgi danej liczby.", "MATH_TRIG_HELPURL": "https://pl.wikipedia.org/wiki/Funkcje_trygonometryczne", "MATH_TRIG_TOOLTIP_SIN": "<PERSON><PERSON><PERSON><PERSON><PERSON> warto<PERSON>ć sinusa o stopniu (nie w radianach).", "MATH_TRIG_TOOLTIP_COS": "<PERSON><PERSON><PERSON><PERSON><PERSON> warto<PERSON> cosinusa o stopniu (nie w radianach).", "MATH_TRIG_TOOLTIP_TAN": "Zwróć tangens o stopniu (nie w radianach).", "MATH_TRIG_TOOLTIP_ASIN": "<PERSON><PERSON><PERSON><PERSON><PERSON> arcus sinus danej l<PERSON>.", "MATH_TRIG_TOOLTIP_ACOS": "<PERSON><PERSON><PERSON><PERSON><PERSON> arcus cosinus danej l<PERSON>.", "MATH_TRIG_TOOLTIP_ATAN": "<PERSON><PERSON><PERSON><PERSON><PERSON> arcus tangens danej l<PERSON>.", "MATH_CONSTANT_HELPURL": "https://pl.wikipedia.org/wiki/Stała_(matemat<PERSON>a)", "MATH_CONSTANT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON><PERSON> jedną wspólną stałą: π (3.141), e (2.718...), φ (1.618...), sqrt(2) (1.414...), sqrt(½) (0.707...) lub ∞ (<PERSON><PERSON><PERSON><PERSON><PERSON>).", "MATH_IS_EVEN": "jest parzysta", "MATH_IS_ODD": "jest ni<PERSON><PERSON><PERSON><PERSON>", "MATH_IS_PRIME": "jest licz<PERSON><PERSON> pier<PERSON>", "MATH_IS_WHOLE": "jest licz<PERSON>ą całkowitą", "MATH_IS_POSITIVE": "jest doda<PERSON>nia", "MATH_IS_NEGATIVE": "jest u<PERSON><PERSON>na", "MATH_IS_DIVISIBLE_BY": "jest podzielna przez", "MATH_IS_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy liczba jest parzy<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ca<PERSON><PERSON><PERSON><PERSON>, do<PERSON><PERSON><PERSON>, u<PERSON><PERSON>na, lub czy jest podzielna przez podaną liczbę. Zw<PERSON><PERSON> war<PERSON> \"prawda\" lub \"fałsz\".", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "zmień %1 o %2", "MATH_CHANGE_TOOLTIP": "Dodaj liczbę do zmiennej '%1'.", "MATH_ROUND_HELPURL": "https://pl.wikipedia.org/wiki/Zaokrąglanie", "MATH_ROUND_TOOLTIP": "Zaokrąglij w górę lub w dół.", "MATH_ROUND_OPERATOR_ROUND": "zaokrąglij", "MATH_ROUND_OPERATOR_ROUNDUP": "zaokrąglij w górę", "MATH_ROUND_OPERATOR_ROUNDDOWN": "zaokrąglij w dół", "MATH_ONLIST_HELPURL": "", "MATH_ONLIST_OPERATOR_SUM": "suma elementów listy", "MATH_ONLIST_TOOLTIP_SUM": "Zwróć sumę wszystkich liczb z listy.", "MATH_ONLIST_OPERATOR_MIN": "<PERSON>na war<PERSON> z listy", "MATH_ONLIST_TOOLTIP_MIN": "Zwróć najmniejszą liczbę w liście.", "MATH_ONLIST_OPERATOR_MAX": "maks<PERSON><PERSON><PERSON> wartość z listy", "MATH_ONLIST_TOOLTIP_MAX": "Zwróć największą liczbę w liście.", "MATH_ONLIST_OPERATOR_AVERAGE": "średnia elementów listy", "MATH_ONLIST_TOOLTIP_AVERAGE": "Zwr<PERSON><PERSON> śred<PERSON> (średnią arytmetyczną) wartości liczbowych z listy.", "MATH_ONLIST_OPERATOR_MEDIAN": "mediana listy", "MATH_ONLIST_TOOLTIP_MEDIAN": "<PERSON><PERSON><PERSON><PERSON><PERSON> medianę listy.", "MATH_ONLIST_OPERATOR_MODE": "dominanty listy", "MATH_ONLIST_TOOLTIP_MODE": "Zwróć listę najczęściej występujących elementów w liście.", "MATH_ONLIST_OPERATOR_STD_DEV": "odchylenie standardowe listy", "MATH_ONLIST_TOOLTIP_STD_DEV": "Z<PERSON><PERSON><PERSON>ć odchylenie standardowe listy.", "MATH_ONLIST_OPERATOR_RANDOM": "losowy element z listy", "MATH_ONLIST_TOOLTIP_RANDOM": "Zwróć losowy element z listy.", "MATH_MODULO_HELPURL": "https://pl.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "MATH_MODULO_TITLE": "reszta z dzielenia %1 przez %2", "MATH_MODULO_TOOLTIP": "Z<PERSON><PERSON><PERSON>ć resztę z dzielenia dwóch liczb przez siebie.", "MATH_CONSTRAIN_TITLE": "ogranicz %1 z dołu %2 z góry %3", "MATH_CONSTRAIN_TOOLTIP": "Ogra<PERSON><PERSON> liczbę, aby była w określonych granicach (włącznie).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "losowa liczba całkowita od %1 do %2", "MATH_RANDOM_INT_TOOLTIP": "Z<PERSON><PERSON><PERSON>ć losową liczbę całkowitą w ramach dwóch wyznaczonych granic, włącznie.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "losowy ułamek", "MATH_RANDOM_FLOAT_TOOLTIP": "Zwróć losowy ułamek między 0.0 (włącznie), a 1.0 (wyłącznie).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TOOLTIP": "Zwraca arcus tangens punktu (X, Y) w <PERSON><PERSON><PERSON> od -180 do 180.", "TEXT_TEXT_HELPURL": "https://pl.wikipedia.org/wiki/Teksto<PERSON>_typ_danych", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON>, wyraz lub linia tekstu.", "TEXT_JOIN_TITLE_CREATEWITH": "utwórz tekst z", "TEXT_JOIN_TOOLTIP": "Tworzy fragment tekstu, łącząc ze sobą dowolną liczbę tekstów.", "TEXT_CREATE_JOIN_TITLE_JOIN": "połącz", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON>, usuń lub z<PERSON>ń kolejn<PERSON> sek<PERSON>, aby z<PERSON><PERSON><PERSON><PERSON>wać klocek tekstowy.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Dodaj element do tekstu.", "TEXT_APPEND_TITLE": "dodaj tekst %2 do %1", "TEXT_APPEND_TOOLTIP": "Dołącz tekst do zmiennej '%1'.", "TEXT_LENGTH_TITLE": "długość %1", "TEXT_LENGTH_TOOLTIP": "Zwraca liczbę liter (łącznie ze spacjami) w podanym tekście.", "TEXT_ISEMPTY_TITLE": "%1 jest pusty", "TEXT_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> (true), je<PERSON><PERSON> podany tekst jest pusty.", "TEXT_INDEXOF_TOOLTIP": "Zwraca indeks pierwszego/ostatniego wystąpienia pierwszego tekstu w drugim tekście. Zwraca wartość %1, jeśli tekst nie został znaleziony.", "TEXT_INDEXOF_TITLE": "w tekście %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "z<PERSON><PERSON><PERSON>ź pierwsze wystąpienie tekstu", "TEXT_INDEXOF_OPERATOR_LAST": "znajdź ostatnie wystąpienie tekstu", "TEXT_CHARAT_TITLE": "w tekście %1 %2", "TEXT_CHARAT_FROM_START": "pobierz literę #", "TEXT_CHARAT_FROM_END": "pobierz literę # od końca", "TEXT_CHARAT_FIRST": "pobierz pierwszą <PERSON>", "TEXT_CHARAT_LAST": "pobierz ostatnią literę", "TEXT_CHARAT_RANDOM": "pobierz losową literę", "TEXT_CHARAT_TAIL": "", "TEXT_CHARAT_TOOLTIP": "Zwraca literę z określonej pozycji.", "TEXT_GET_SUBSTRING_TOOLTIP": "Zwraca określoną cz<PERSON> tekstu.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "w tekście", "TEXT_GET_SUBSTRING_START_FROM_START": "pobierz podciąg od # litery", "TEXT_GET_SUBSTRING_START_FROM_END": "pobierz podciąg od # litery od końca", "TEXT_GET_SUBSTRING_START_FIRST": "pobierz podciąg od pierwszej litery", "TEXT_GET_SUBSTRING_END_FROM_START": "do # litery", "TEXT_GET_SUBSTRING_END_FROM_END": "do # litery od końca", "TEXT_GET_SUBSTRING_END_LAST": "do ostatniej litery", "TEXT_GET_SUBSTRING_TAIL": "", "TEXT_CHANGECASE_TOOLTIP": "Zwraca kopię tekstu z odwruconą wielkością liter.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "na WIELKIE LITERY", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "na małe litery", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "na Pierwsza Duża", "TEXT_TRIM_TOOLTIP": "Zwraca kopię tekstu z usuniętymi spacjami z jednego lub z obu końców tekstu.", "TEXT_TRIM_OPERATOR_BOTH": "usuń spacje po obu stronach", "TEXT_TRIM_OPERATOR_LEFT": "usuń spacje z lewej strony", "TEXT_TRIM_OPERATOR_RIGHT": "usuń spacje z prawej strony", "TEXT_PRINT_TITLE": "wydrukuj %1", "TEXT_PRINT_TOOLTIP": "Wyświetl określony tekst, licz<PERSON><PERSON> lub inną warto<PERSON>.", "TEXT_PROMPT_TYPE_TEXT": "poproś o tekst z tą wiadomością", "TEXT_PROMPT_TYPE_NUMBER": "poproś o liczbę z tą wiadomością", "TEXT_PROMPT_TOOLTIP_NUMBER": "Zapytaj użytkownika  o liczbę.", "TEXT_PROMPT_TOOLTIP_TEXT": "Zapytaj użytkownika o jakiś tekst.", "TEXT_COUNT_MESSAGE0": "policz %1 w %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Liczy ile razy dany tekst występuje w innym tekście.", "TEXT_REPLACE_MESSAGE0": "zamień %1 na %2 w %3", "TEXT_REPLACE_TOOLTIP": "Zastąp wszystkie wystąpienia danego tekstu innym.", "TEXT_REVERSE_MESSAGE0": "odwróć %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Odwraca kolejność znaków w tekście.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "utwórz pustą listę", "LISTS_CREATE_EMPTY_TOOLTIP": "Zwraca listę o długości 0, nie zawierającą danych", "LISTS_CREATE_WITH_TOOLTIP": "Utwórz listę z dowolną ilością elementów.", "LISTS_CREATE_WITH_INPUT_WITH": "utwórz listę z", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "lista", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, usuń lub zmień kolejność sekcji aby przekonfigurować blok tej listy.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Dodaj element do listy.", "LISTS_REPEAT_TOOLTIP": "Tworzy listę zawierającą podaną wartość powtórzoną żądaną ilość razy.", "LISTS_REPEAT_TITLE": "utwórz listę powtarzając %1 %2 razy.", "LISTS_LENGTH_TITLE": "długość %1", "LISTS_LENGTH_TOOLTIP": "Zwraca długość listy.", "LISTS_ISEMPTY_TITLE": "%1 jest pusta", "LISTS_ISEMPTY_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON> \"prawda\" jeśli lista jest pusta.", "LISTS_INLIST": "na liście", "LISTS_INDEX_OF_FIRST": "zna<PERSON><PERSON>ź pierwsze wystąpienie elementu", "LISTS_INDEX_OF_LAST": "znajdź ostatanie wystąpienie elementu", "LISTS_INDEX_OF_TOOLTIP": "Zwraca indeks pierwszego/ostatniego wystąpienia elementu z listy. Zwraca %1, je<PERSON>li nie zostanie znaleziony.", "LISTS_GET_INDEX_GET": "p<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_GET_REMOVE": "<PERSON><PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_REMOVE": "usuń", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# od końca", "LISTS_GET_INDEX_FIRST": "<PERSON><PERSON><PERSON>", "LISTS_GET_INDEX_LAST": "ostatni", "LISTS_GET_INDEX_RANDOM": "losowy", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 to pierwszy element.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 to ostatni element.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Zwraca element z konkretnej pozycji na liście.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Zwraca pierwszy element z listy.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Zwraca ostatni element z listy.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Zwraca losowy element z listy.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Usuwa i zwraca element z określonej pozycji na liście.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Usuwa i zwraca pierwszy element z listy.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Usuwa i zwraca ostatni element z listy.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Usuwa i zwraca losowy element z listy.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Usuwa element z określonej pozycji na liście.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Usuwa pierwszy element z listy.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Usuwa ostatni element z listy.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Usuwa losowy element z listy.", "LISTS_SET_INDEX_SET": "ustaw", "LISTS_SET_INDEX_INSERT": "wstaw w", "LISTS_SET_INDEX_INPUT_TO": "jako", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Ustawia element w określonym miejscu na liście.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Ustawia pierwszy element na liście.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Ustawia ostatni element na liście.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Ustawia losowy element na liście.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Wstawia element na żądanej pozycji listy.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Wstawia element na początku listy.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Dodaj element na koniec listy.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Wstawia element w losowym miejscu na liście.", "LISTS_GET_SUBLIST_START_FROM_START": "utwórz listę podrzędną z #", "LISTS_GET_SUBLIST_START_FROM_END": "utwórz listę podrzędną z # od końca", "LISTS_GET_SUBLIST_START_FIRST": "utwórz listę podrzędną od pierwszego", "LISTS_GET_SUBLIST_END_FROM_START": "do #", "LISTS_GET_SUBLIST_END_FROM_END": "do # od końca", "LISTS_GET_SUBLIST_END_LAST": "do ostatniego", "LISTS_GET_SUBLIST_TAIL": "", "LISTS_GET_SUBLIST_TOOLTIP": "Tworzy kopię żądanej części listy.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "sortuj %1 %2 %3", "LISTS_SORT_TOOLTIP": "Sort<PERSON>j kopię listy.", "LISTS_SORT_ORDER_ASCENDING": "rosnąco", "LISTS_SORT_ORDER_DESCENDING": "<PERSON><PERSON>ą<PERSON>", "LISTS_SORT_TYPE_NUMERIC": "numerycznie", "LISTS_SORT_TYPE_TEXT": "alfabetycznie", "LISTS_SORT_TYPE_IGNORECASE": "alfabetycznie, ignoruj wielkość liter", "LISTS_SPLIT_LIST_FROM_TEXT": "utwórz listę z tekstu", "LISTS_SPLIT_TEXT_FROM_LIST": "utwórz tekst z listy", "LISTS_SPLIT_WITH_DELIMITER": "z separatorem", "LISTS_SPLIT_TOOLTIP_SPLIT": "Rozdziela tekst zgodnie z separatorem tworząc listę z powstałych elementów.", "LISTS_SPLIT_TOOLTIP_JOIN": "Łączy listę tekstów w jeden tekst, rozdzielany separatorem.", "LISTS_REVERSE_MESSAGE0": "odwróć %1", "LISTS_REVERSE_TOOLTIP": "Odwraca kolejność danych w kopii listy.", "ORDINAL_NUMBER_SUFFIX": "", "VARIABLES_GET_TOOLTIP": "Zwraca war<PERSON> tej zmiennej.", "VARIABLES_GET_CREATE_SET": "Utwórz klocek 'ustaw %1'", "VARIABLES_SET": "przypisz %1 wartość %2", "VARIABLES_SET_TOOLTIP": "Wartości zmiennej i  wejście będą identyczne.", "VARIABLES_SET_CREATE_GET": "Utwórz klocek 'pobierz %1'", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "do", "PROCEDURES_DEFNORETURN_PROCEDURE": "<PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_BEFORE_PARAMS": "z:", "PROCEDURES_CALL_BEFORE_PARAMS": "z:", "PROCEDURES_DEFNORETURN_DO": "", "PROCEDURES_DEFNORETURN_TOOLTIP": "Tworzy funkcję nie posiadającą wyjścia.", "PROCEDURES_DEFNORETURN_COMMENT": "Opisz tę funkcję...", "PROCEDURES_DEFRETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFRETURN_RETURN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_DEFRETURN_TOOLTIP": "Tworzy funkcję posiadającą wyjście.", "PROCEDURES_ALLOW_STATEMENTS": "zezwól na czynności", "PROCEDURES_DEF_DUPLICATE_WARNING": "Uwaga: <PERSON> funkcja ma powtórzone parametry.", "PROCEDURES_CALLNORETURN_HELPURL": "https://pl.wikipedia.org/wiki/Podprogram", "PROCEDURES_CALLNORETURN_TOOLTIP": "Uruchom zdefiniowaną przez użytkownika funkcję '%1'.", "PROCEDURES_CALLRETURN_HELPURL": "https://pl.wikipedia.org/wiki/Podprogram", "PROCEDURES_CALLRETURN_TOOLTIP": "Uruchom zdefiniowaną przez użytkownika funkcję '%1' i użyj jej wyjścia.", "PROCEDURES_MUTATORCONTAINER_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, usuń lub zmień kolejność wejść tej funk<PERSON>.", "PROCEDURES_MUTATORARG_TITLE": "nazwa wejścia:", "PROCEDURES_MUTATORARG_TOOLTIP": "<PERSON><PERSON>j wejście do funkcji.", "PROCEDURES_HIGHLIGHT_DEF": "Podświetl definic<PERSON><PERSON>", "PROCEDURES_CREATE_DO": "Utwórz '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "<PERSON><PERSON><PERSON> warunek jest spełniony zwróć drugą wartość.", "PROCEDURES_IFRETURN_WARNING": "Uwaga: Ten klocek może być używany tylko w definicji funkcji.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Powiedz coś...", "COLLAPSED_WARNINGS_WARNING": "Zwinięte bloki zawierają ostrzeżenia.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "<PERSON><PERSON><PERSON>"}