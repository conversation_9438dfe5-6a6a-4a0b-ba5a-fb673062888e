package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.teaching.entity.TeachingClassroom;
import java.util.List;

/**
 * @Description: 教室
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface ITeachingClassroomService extends IService<TeachingClassroom> {
    /**
     * 获取所有可用教室
     * @return 可用教室列表
     */
    List<TeachingClassroom> getAvailableClassrooms();
    
    /**
     * 更新教室状态
     * @param id 教室ID
     * @param status 状态（0-维护中，1-可用）
     * @return 是否成功
     */
    boolean updateClassroomStatus(String id, Integer status);
} 