package org.jeecg.modules.teaching.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.teaching.entity.TeachingClassroom;
import java.util.List;

/**
 * @Description: 教室
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface TeachingClassroomMapper extends BaseMapper<TeachingClassroom> {
    /**
     * 获取所有可用教室
     * @return 可用教室列表
     */
    List<TeachingClassroom> getAvailableClassrooms();
} 