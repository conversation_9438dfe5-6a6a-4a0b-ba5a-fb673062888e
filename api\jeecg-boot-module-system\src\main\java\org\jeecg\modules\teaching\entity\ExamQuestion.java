package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 考试题目
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@ApiModel(value="exam_question对象", description="考试题目")
@Data
@TableName("exam_question")
public class ExamQuestion implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "题目描述/标题")
    private String title;
    
    @TableField("question_type")
    @ApiModelProperty(value = "题目类型 (1:单选题, 2:判断题, 3:编程题)")
    private Integer questionType;

    @ApiModelProperty(value = "所属科目 (Scratch, Python, C++)")
    private String subject;

    @ApiModelProperty(value = "题目级别 (Scratch:1-4, Python/C++:1-8)")
    private String level;

    @ApiModelProperty(value = "难度 (1:简单, 2:中等, 3:困难)")
    private Integer difficulty;

    @ApiModelProperty(value = "题目内容 (JSON格式)")
    private String content;

    @ApiModelProperty(value = "作者/出题人")
    private String author;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 