-- ----------------------------
-- 创建课程通知已读状态表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `teaching_notification_read` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `notification_id` varchar(36) NOT NULL COMMENT '通知ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `read_flag` varchar(10) DEFAULT '0' COMMENT '阅读状态（0未读，1已读）',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_notification_user` (`notification_id`,`user_id`),
  KEY `idx_user_read` (`user_id`,`read_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程通知已读状态表'; 