{"@metadata": {"authors": ["Acamicamacaraca", "BadDog", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Perevod16", "Rancher", "Zoranzoki21", "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "아라"]}, "VARIABLES_DEFAULT_NAME": "ставка", "UNNAMED_KEY": "неименовано", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "Ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ADD_COMMENT": "Дод<PERSON><PERSON> коментар", "REMOVE_COMMENT": "Уклони коментар", "DUPLICATE_COMMENT": "Дупли<PERSON><PERSON><PERSON> коментар", "EXTERNAL_INPUTS": "Спољашњи улази", "INLINE_INPUTS": "Редни улази", "DELETE_BLOCK": "Избриши блок", "DELETE_X_BLOCKS": "Избриши %1 блокова", "DELETE_ALL_BLOCKS": "Избрисати свих %1 блокова?", "CLEAN_UP": "Очисти блокове", "COLLAPSE_BLOCK": "Скупи блок", "COLLAPSE_ALL": "Скупи блокове", "EXPAND_BLOCK": "Прошири блок", "EXPAND_ALL": "Прошири блокове", "DISABLE_BLOCK": "Онемогући блок", "ENABLE_BLOCK": "Омогући блок", "HELP": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UNDO": "Опозови", "REDO": "Понови", "CHANGE_VALUE_TITLE": "Промена вредности:", "RENAME_VARIABLE": "Преименуј променљиву…", "RENAME_VARIABLE_TITLE": "Преименуј све ’%1’ променљиве у:", "NEW_VARIABLE": "Направи променљиву…", "NEW_STRING_VARIABLE": "Направи променљиву ниске...", "NEW_NUMBER_VARIABLE": "Направи променљиву броја...", "NEW_COLOUR_VARIABLE": "Направи променљиву боје...", "NEW_VARIABLE_TYPE_TITLE": "Нова врста променљиве:", "NEW_VARIABLE_TITLE": "Име нове променљиве:", "VARIABLE_ALREADY_EXISTS": "Променљива под именом ’%1’ већ постоји.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Променљива под именом ’%1’ већ постоји за други тип: ’%2’.", "DELETE_VARIABLE_CONFIRMATION": "Избрисати %1 употребу променљиве „%2”?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Није могуће избрисати променљиву „%1” јер је део дефиниције функције „%2”", "DELETE_VARIABLE": "Избриши променљиву ’%1’", "COLOUR_PICKER_HELPURL": "https://sr.wikipedia.org/wiki/Боја", "COLOUR_PICKER_TOOLTIP": "Одаберите боју са палете.", "COLOUR_RANDOM_TITLE": "случајна боја", "COLOUR_RANDOM_TOOLTIP": "Одаберите боју насумично.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "боја са", "COLOUR_RGB_RED": "црвена", "COLOUR_RGB_GREEN": "зелена", "COLOUR_RGB_BLUE": "плава", "COLOUR_RGB_TOOLTIP": "Направите боју са одређеном количином црвене, зелене и плаве. Све вредности морају бити између 0 и 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "помешај", "COLOUR_BLEND_COLOUR1": "боја 1", "COLOUR_BLEND_COLOUR2": "боја 2", "COLOUR_BLEND_RATIO": "однос", "COLOUR_BLEND_TOOLTIP": "Меша две боје заједно са датим односом (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://sr.wikipedia.org/wiki/For_петља", "CONTROLS_REPEAT_TITLE": "понови %1 пута", "CONTROLS_REPEAT_INPUT_DO": "изврши", "CONTROLS_REPEAT_TOOLTIP": "Изврши неке наредбе неколико пута.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "понављати док", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "понављати до", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Док је вредност тачна, извршава неке наредбе.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Док је вредност нетачна, извршава неке наредбе.", "CONTROLS_FOR_TOOLTIP": "Имај промењиву \"%1\" узми вредности од почетног броја до задњег броја, бројећи по одређеном интервалу, и изврши одређене блокове.", "CONTROLS_FOR_TITLE": "преброј са %1 од %2 до %3 од %4", "CONTROLS_FOREACH_TITLE": "за сваку ставку %1 на списку %2", "CONTROLS_FOREACH_TOOLTIP": "За сваку ставку унутар листе, подеси промењиву '%1' по ставци, и онда начини неке изјаве-наредбе.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "изађи из петље", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "настави са следећом итерацијом петље", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Напусти садржај петље.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Прескочи остатак ове петље, и настави са следећом итерацијом(понављанјем).", "CONTROLS_FLOW_STATEMENTS_WARNING": "Упозорење: Овај блок може да се употреби само унутар петље.", "CONTROLS_IF_TOOLTIP_1": "ако је вредност тачна, онда изврши неке наредбе-изјаве.", "CONTROLS_IF_TOOLTIP_2": "ако је вредност тачна, онда изврши први блок наредби, У супротном, изврши други блок наредби.", "CONTROLS_IF_TOOLTIP_3": "Ако је прва вредност тачна, онда изврши први блок наредби, у супротном, ако је друга вредност тачна , изврши други блок наредби.", "CONTROLS_IF_TOOLTIP_4": "Ако је прва вредност тачна, онда изврши први блок наредби, у супротном, ако је друга вредност тачна , изврши други блок наредби. Ако ни једна од вредности није тачна, изврши последнји блок наредби.", "CONTROLS_IF_MSG_IF": "ако", "CONTROLS_IF_MSG_ELSEIF": "иначе-ако", "CONTROLS_IF_MSG_ELSE": "иначе", "CONTROLS_IF_IF_TOOLTIP": "До<PERSON><PERSON><PERSON>, уклони, или преуреди делове како бих реконфигурисали овај иф блок.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Додајте услов блоку „ако“.", "CONTROLS_IF_ELSE_TOOLTIP": "Додај коначни, catch-all  (ухвати све) услове иф блока.", "IOS_OK": "У реду", "IOS_CANCEL": "Откажи", "IOS_ERROR": "Грешка", "IOS_PROCEDURES_INPUTS": "УНОСИ", "IOS_PROCEDURES_ADD_INPUT": "+ Додај унос", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Дозволи изјаве", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Ова функција има дуплиране уносе.", "IOS_VARIABLES_ADD_VARIABLE": "+ Додај променљиву", "IOS_VARIABLES_ADD_BUTTON": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_RENAME_BUTTON": "Преим<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "Избриши", "IOS_VARIABLES_VARIABLE_NAME": "Име променљиве", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Не можете да користите празно име променљиве.", "LOGIC_COMPARE_HELPURL": "https://sr.wikipedia.org/wiki/Неједнакост", "LOGIC_COMPARE_TOOLTIP_EQ": "Враћа вредност „тачно“ ако су оба улаза једнака.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Враћа вредност „тачно“ ако су оба уноса неједнака.", "LOGIC_COMPARE_TOOLTIP_LT": "Враћа вредност „тачно“ ако је први унос мањи од другог.", "LOGIC_COMPARE_TOOLTIP_LTE": "Враћа вредност „тачно“ ако је први унос мањи или једнак другом.", "LOGIC_COMPARE_TOOLTIP_GT": "Враћа вредност „тачно“ ако је први унос већи од другог.", "LOGIC_COMPARE_TOOLTIP_GTE": "Враћа вредност „тачно“ ако је први унос већи или једнак другом.", "LOGIC_OPERATION_TOOLTIP_AND": "Враћа вредност „тачно“ ако су оба уноса тачна.", "LOGIC_OPERATION_AND": "и", "LOGIC_OPERATION_TOOLTIP_OR": "Враћа вредност „тачно“ ако је бар један од уноса тачан.", "LOGIC_OPERATION_OR": "или", "LOGIC_NEGATE_TITLE": "није %1", "LOGIC_NEGATE_TOOLTIP": "Враћа вредност „тачно“ ако је унос нетачан. Враћа вредност „нетачно“ ако је унос тачан.", "LOGIC_BOOLEAN_TRUE": "тачно", "LOGIC_BOOLEAN_FALSE": "нетачно", "LOGIC_BOOLEAN_TOOLTIP": "Враћа или „тачно“ или „нетачно“.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "без вредности", "LOGIC_NULL_TOOLTIP": "Враћа „без вредности“.", "LOGIC_TERNARY_HELPURL": "https://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "проба", "LOGIC_TERNARY_IF_TRUE": "ако је тачно", "LOGIC_TERNARY_IF_FALSE": "ако је нетачно", "LOGIC_TERNARY_TOOLTIP": "Проверите услов у „проба”. Ако је услов тачан, тада враћа „ако је тачно” вредност; у другом случају враћа „ако је нетачно” вредност.", "MATH_NUMBER_HELPURL": "https://sr.wikipedia.org/wiki/Број", "MATH_NUMBER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "син", "MATH_TRIG_COS": "цос", "MATH_TRIG_TAN": "тан", "MATH_TRIG_ASIN": "арц син", "MATH_TRIG_ACOS": "арц цос", "MATH_TRIG_ATAN": "арц тан", "MATH_ARITHMETIC_HELPURL": "https://sr.wikipedia.org/wiki/Аритметика", "MATH_ARITHMETIC_TOOLTIP_ADD": "Враћа збир два броја.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Враћа разлику два броја.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Враћа производ два броја.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Враћа количник два броја.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Враћа први број степенован другим.", "MATH_SINGLE_HELPURL": "https://sr.wikipedia.org/wiki/Квадратни_корен", "MATH_SINGLE_OP_ROOT": "квадратни корен", "MATH_SINGLE_TOOLTIP_ROOT": "Враћа квадратни корен броја.", "MATH_SINGLE_OP_ABSOLUTE": "апсолутно", "MATH_SINGLE_TOOLTIP_ABS": "Враћа апсолутну вредност броја.", "MATH_SINGLE_TOOLTIP_NEG": "Враћа негацију броја.", "MATH_SINGLE_TOOLTIP_LN": "Враћа природни логаритам броја.", "MATH_SINGLE_TOOLTIP_LOG10": "Враћа логаритам броја са основом 10.", "MATH_SINGLE_TOOLTIP_EXP": "Враћа е-број на степен броја.", "MATH_SINGLE_TOOLTIP_POW10": "Враћа 10-ти степен броја.", "MATH_TRIG_HELPURL": "https://sr.wikipedia.org/wiki/Тригонометријске_функције", "MATH_TRIG_TOOLTIP_SIN": "Враћа синус степена (не радијан).", "MATH_TRIG_TOOLTIP_COS": "Враћа косинус степена (не радијан).", "MATH_TRIG_TOOLTIP_TAN": "Враћа тангенс степена (не радијан).", "MATH_TRIG_TOOLTIP_ASIN": "Враћа аркус синус броја.", "MATH_TRIG_TOOLTIP_ACOS": "Враћа аркус косинус броја.", "MATH_TRIG_TOOLTIP_ATAN": "Враћа аркус тангенс броја.", "MATH_CONSTANT_HELPURL": "https://sr.wikipedia.org/wiki/Математичка_константа", "MATH_CONSTANT_TOOLTIP": "Враћа једну од заједничких константи: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), или ∞ (бесконачно).", "MATH_IS_EVEN": "је паран", "MATH_IS_ODD": "је непаран", "MATH_IS_PRIME": "је прост", "MATH_IS_WHOLE": "је цео", "MATH_IS_POSITIVE": "је позитиван", "MATH_IS_NEGATIVE": "је негативан", "MATH_IS_DIVISIBLE_BY": "је дељив са", "MATH_IS_TOOLTIP": "Проверава да ли је број паран, непаран, прост, цео, позитиван, негативан, или дељив са одређеним бројем. Враћа „тачно” или „нетачно”.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "промени %1 за %2", "MATH_CHANGE_TOOLTIP": "Додаје број променљивој „%1”.", "MATH_ROUND_HELPURL": "https://sr.wikipedia.org/wiki/Заокруживање", "MATH_ROUND_TOOLTIP": "Заокружује број на већу или мању вредност.", "MATH_ROUND_OPERATOR_ROUND": "заокружи", "MATH_ROUND_OPERATOR_ROUNDUP": "заокружи навише", "MATH_ROUND_OPERATOR_ROUNDDOWN": "заокружи наниже", "MATH_ONLIST_HELPURL": "", "MATH_ONLIST_OPERATOR_SUM": "збир списка", "MATH_ONLIST_TOOLTIP_SUM": "Враћа збир свих бројева са списка.", "MATH_ONLIST_OPERATOR_MIN": "мин. списка", "MATH_ONLIST_TOOLTIP_MIN": "Враћа најмањи број са списка.", "MATH_ONLIST_OPERATOR_MAX": "макс. списка", "MATH_ONLIST_TOOLTIP_MAX": "Враћа највећи број са списка.", "MATH_ONLIST_OPERATOR_AVERAGE": "просек списка", "MATH_ONLIST_TOOLTIP_AVERAGE": "Враћа просек (аритметичку средину) бројева са списка.", "MATH_ONLIST_OPERATOR_MEDIAN": "медијана списка", "MATH_ONLIST_TOOLTIP_MEDIAN": "Враћа медијану са списка.", "MATH_ONLIST_OPERATOR_MODE": "модус списка", "MATH_ONLIST_TOOLTIP_MODE": "Враћа списак најчешћих ставки на списку.", "MATH_ONLIST_OPERATOR_STD_DEV": "стандардна девијација списка", "MATH_ONLIST_TOOLTIP_STD_DEV": "Враћа стандардну девијацију списка.", "MATH_ONLIST_OPERATOR_RANDOM": "случајна ставка списка", "MATH_ONLIST_TOOLTIP_RANDOM": "Враћа случајни елемент са списка.", "MATH_MODULO_HELPURL": "https://sr.wikipedia.org/wiki/Конгруенција", "MATH_MODULO_TITLE": "подсетник од %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Враћа подсетник од дељења два броја.", "MATH_CONSTRAIN_TITLE": "ограничи %1 ниско %2 високо %3", "MATH_CONSTRAIN_TOOLTIP": "Ограничава број на доње и горње границе (укључиво).", "MATH_RANDOM_INT_HELPURL": "https://sr.wikipedia.org/wiki/Генератор_случајних_бројева", "MATH_RANDOM_INT_TITLE": "сличајно одабрани цијели број од %1 до %2", "MATH_RANDOM_INT_TOOLTIP": "Враћа случајно одабрани цели број између две одређене границе, уклјучиво.", "MATH_RANDOM_FLOAT_HELPURL": "https://sr.wikipedia.org/wiki/Генератор_случајних_бројева", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "случајни разломак", "MATH_RANDOM_FLOAT_TOOLTIP": "Враћа случајни разломак између 0.0 (укључиво) и 1.0 (искључиво).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "атан2 од X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Врати арктангенту тачке (X, Y) у степенима од -180 до 180.", "TEXT_TEXT_HELPURL": "https://sr.wikipedia.org/wiki/Ниска", "TEXT_TEXT_TOOLTIP": "Слово, реч или ред текста.", "TEXT_JOIN_TITLE_CREATEWITH": "напиши текст са", "TEXT_JOIN_TOOLTIP": "Направити дио текста спајајући различите ставке.", "TEXT_CREATE_JOIN_TITLE_JOIN": "спој", "TEXT_CREATE_JOIN_TOOLTIP": "До<PERSON><PERSON><PERSON>, уклони, или другачије поредај одјелке како би изнова поставили овај текст блок.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Додајте ставку у текст.", "TEXT_APPEND_TITLE": "на %1 додај текст %2", "TEXT_APPEND_TOOLTIP": "Додаје текст променљивој „%1”.", "TEXT_LENGTH_TITLE": "дужина текста %1", "TEXT_LENGTH_TOOLTIP": "Враћа број слова (уклјучујући размаке) у датом тексту.", "TEXT_ISEMPTY_TITLE": "%1 је празан", "TEXT_ISEMPTY_TOOLTIP": "Враћа „тачно” ако је достављени текст празан.", "TEXT_INDEXOF_TOOLTIP": "Враћа индекс првог/задњег појављивања првог текста у другом тексту. Враћа %1 ако текст није пронађен.", "TEXT_INDEXOF_TITLE": "у тексту %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "пронађи прво појављивање текста", "TEXT_INDEXOF_OPERATOR_LAST": "пронађи последње појављивање текста", "TEXT_CHARAT_TITLE": "у тексту %1 %2", "TEXT_CHARAT_FROM_START": "преузми слово #", "TEXT_CHARAT_FROM_END": "преузми слово # са краја", "TEXT_CHARAT_FIRST": "преузми прво слово", "TEXT_CHARAT_LAST": "преузми последње слово", "TEXT_CHARAT_RANDOM": "преузми случајно слово", "TEXT_CHARAT_TAIL": "", "TEXT_CHARAT_TOOLTIP": "Враћа слово на одређени положај.", "TEXT_GET_SUBSTRING_TOOLTIP": "Враћа одређени део текста.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "у тексту", "TEXT_GET_SUBSTRING_START_FROM_START": "преузми подниску из слова #", "TEXT_GET_SUBSTRING_START_FROM_END": "преузми подниску из слова # са краја", "TEXT_GET_SUBSTRING_START_FIRST": "преузми подниску из првог слова", "TEXT_GET_SUBSTRING_END_FROM_START": "слову #", "TEXT_GET_SUBSTRING_END_FROM_END": "слову # са краја", "TEXT_GET_SUBSTRING_END_LAST": "последњем слову", "TEXT_GET_SUBSTRING_TAIL": "", "TEXT_CHANGECASE_TOOLTIP": "Враћа примерак текста са другачијом величином слова.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "великим словима", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "малим словима", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "свака реч великим словом", "TEXT_TRIM_TOOLTIP": "Враћа копију текста са уклонјеним простором са једног од два краја.", "TEXT_TRIM_OPERATOR_BOTH": "трим празнине са обе стране", "TEXT_TRIM_OPERATOR_LEFT": "скратити простор са леве стране", "TEXT_TRIM_OPERATOR_RIGHT": "скратити простор са десне стране", "TEXT_PRINT_TITLE": "прикажи %1", "TEXT_PRINT_TOOLTIP": "Прикажите одређени текст, број или другу вредност на екрану.", "TEXT_PROMPT_TYPE_TEXT": "питај за текст са поруком", "TEXT_PROMPT_TYPE_NUMBER": "питај за број са поруком", "TEXT_PROMPT_TOOLTIP_NUMBER": "Питајте корисника за број.", "TEXT_PROMPT_TOOLTIP_TEXT": "Питајте корисника за унос текста.", "TEXT_COUNT_MESSAGE0": "број %1 у %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Броји колико пута се неки текст појављује унутар неког другог текста.", "TEXT_REPLACE_MESSAGE0": "замена %1 са %2 у %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "Замена свих појава неког текста унутар неког другог текста.", "TEXT_REVERSE_MESSAGE0": "обрнуто %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Обрће редослед карактера у тексту.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "направи празан списак", "LISTS_CREATE_EMPTY_TOOLTIP": "Враћа списак, дужине 0, без података", "LISTS_CREATE_WITH_TOOLTIP": "Направите списак са било којим бројем ставки.", "LISTS_CREATE_WITH_INPUT_WITH": "направи списак са", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "списак", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "Додајте, избришите, или преуредите делове како би се реорганизовали овај блок листе.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Додајте ставку на списак.", "LISTS_REPEAT_TOOLTIP": "Прави листу која се састоји од задане вредности коју понавлјамо одређени број шута.", "LISTS_REPEAT_TITLE": "Направити списак са ставком %1 која се понавља %2 пута", "LISTS_LENGTH_TITLE": "дужина списка %1", "LISTS_LENGTH_TOOLTIP": "Враћа дужину списка.", "LISTS_ISEMPTY_TITLE": "%1 је празан", "LISTS_ISEMPTY_TOOLTIP": "Враћа вредност „тачно” ако је списак празан.", "LISTS_INLIST": "на списку", "LISTS_INDEX_OF_FIRST": "пронађи прво појављивање ставке", "LISTS_INDEX_OF_LAST": "пронађи последње појављивање ставке", "LISTS_INDEX_OF_TOOLTIP": "Враћа индекс прве/последње појаве ставке на списку. Враћа %1 ако ставка није пронађена.", "LISTS_GET_INDEX_GET": "преузми", "LISTS_GET_INDEX_GET_REMOVE": "преузми и уклони", "LISTS_GET_INDEX_REMOVE": "уклони", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# са краја", "LISTS_GET_INDEX_FIRST": "прва", "LISTS_GET_INDEX_LAST": "последња", "LISTS_GET_INDEX_RANDOM": "случајна", "LISTS_GET_INDEX_TAIL": "", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 је прва ставка.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 је последња ставка.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Враћа ставку на одређену позицију на списку.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Враћа прву ставку на списку.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Враћа последњу ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Враћа случајну ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Уклања и враћа ставку са одређеног положаја са списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Уклања и враћа прву ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Уклања и враћа последњу ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Уклања и враћа случајну ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Уклања ставку са одређеног положаја са списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Уклања прву ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Уклања последњу ставку са списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Уклања случајну ставку са списка.", "LISTS_SET_INDEX_SET": "постави", "LISTS_SET_INDEX_INSERT": "убаци на", "LISTS_SET_INDEX_INPUT_TO": "као", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Поставља ставку на одређени положај на списку.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Поставља прву ставку на списку.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Поставља последњу ставку на списку.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Поставља случајну ставку на списку.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Убацује ставку на одређени положај на списку.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Убацује ставку на почетак списка.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Додајте ставку на крај списка.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Убацује ставку на случајно место на списку.", "LISTS_GET_SUBLIST_START_FROM_START": "преузми подсписак од #", "LISTS_GET_SUBLIST_START_FROM_END": "преузми подсписак из # са краја", "LISTS_GET_SUBLIST_START_FIRST": "преузми подсписак од прве", "LISTS_GET_SUBLIST_END_FROM_START": "до #", "LISTS_GET_SUBLIST_END_FROM_END": "до # од краја", "LISTS_GET_SUBLIST_END_LAST": "до последње", "LISTS_GET_SUBLIST_TAIL": "", "LISTS_GET_SUBLIST_TOOLTIP": "Прави копију одређеног дела списка.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "сортирај %1 %2 %3", "LISTS_SORT_TOOLTIP": "Сортирајте копију списка.", "LISTS_SORT_ORDER_ASCENDING": "растуће", "LISTS_SORT_ORDER_DESCENDING": "опадајуће", "LISTS_SORT_TYPE_NUMERIC": "као бројеве", "LISTS_SORT_TYPE_TEXT": "азбучно", "LISTS_SORT_TYPE_IGNORECASE": "азбучно, игнориши мала и велика слова", "LISTS_SPLIT_LIST_FROM_TEXT": "направите листу са текста", "LISTS_SPLIT_TEXT_FROM_LIST": "направи текст из списка", "LISTS_SPLIT_WITH_DELIMITER": "са граничником", "LISTS_SPLIT_TOOLTIP_SPLIT": "Раздваја текст у списак текстова, преламањем на сваком граничнику.", "LISTS_SPLIT_TOOLTIP_JOIN": "Спаја списак текстова у један текст, раздвојених граничником.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "обрнуто %1", "LISTS_REVERSE_TOOLTIP": "Обрни копију списка.", "ORDINAL_NUMBER_SUFFIX": "", "VARIABLES_GET_TOOLTIP": "Враћа вредност ове променљиве.", "VARIABLES_GET_CREATE_SET": "Направи блок за доделу вредности %1", "VARIABLES_SET": "постави %1 у %2", "VARIABLES_SET_TOOLTIP": "Поставља променљиву тако да буде једнака улазу.", "VARIABLES_SET_CREATE_GET": "Направи блок за преузимање вредности из „%1”", "PROCEDURES_DEFNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "до", "PROCEDURES_DEFNORETURN_PROCEDURE": "урадите нешто", "PROCEDURES_BEFORE_PARAMS": "са:", "PROCEDURES_CALL_BEFORE_PARAMS": "са:", "PROCEDURES_DEFNORETURN_DO": "", "PROCEDURES_DEFNORETURN_TOOLTIP": "Прави функцију без излаза.", "PROCEDURES_DEFNORETURN_COMMENT": "Опишите ову функцију…", "PROCEDURES_DEFRETURN_HELPURL": "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFRETURN_RETURN": "врати", "PROCEDURES_DEFRETURN_TOOLTIP": "Прави функцију са излазом.", "PROCEDURES_ALLOW_STATEMENTS": "дозволи изјаве", "PROCEDURES_DEF_DUPLICATE_WARNING": "Упозорење: Ова функција има дуплиране параметре.", "PROCEDURES_CALLNORETURN_HELPURL": "https://sr.wikipedia.org/wiki/Потпрограм", "PROCEDURES_CALLNORETURN_TOOLTIP": "Покреће кориснички дефинисану функцију „%1”.", "PROCEDURES_CALLRETURN_HELPURL": "https://sr.wikipedia.org/wiki/Потпрограм", "PROCEDURES_CALLRETURN_TOOLTIP": "Покреће кориснички дефинисану функцију „%1” и користи њен излаз.", "PROCEDURES_MUTATORCONTAINER_TITLE": "улази", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "Додајте, уклоните или преуредите уносе за ову функцију.", "PROCEDURES_MUTATORARG_TITLE": "име параметра:", "PROCEDURES_MUTATORARG_TOOLTIP": "Додајте улазни параметар финкцији.", "PROCEDURES_HIGHLIGHT_DEF": "Истакни дефиницију функције", "PROCEDURES_CREATE_DO": "Направи „%1”", "PROCEDURES_IFRETURN_TOOLTIP": "Ако је прва вредност тачна, враћа другу вредност.", "PROCEDURES_IFRETURN_WARNING": "Упозорење: Овај блок се може користити једино унутар дефиниције функције.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Кажите нешто…", "WORKSPACE_ARIA_LABEL": "Блоклијев радни простор", "COLLAPSED_WARNINGS_WARNING": "Срушени блокови садрже упозорења.", "DIALOG_OK": "У реду", "DIALOG_CANCEL": "Откажи"}