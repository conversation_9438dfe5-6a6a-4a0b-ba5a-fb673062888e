package org.jeecg.modules.teaching.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 题目内容处理服务
 * 处理模板化内容、图片信息提取、数据结构兼容性等
 */
@Service
@Slf4j
public class QuestionContentService {

    /**
     * 数据版本常量
     */
    public static final String VERSION_1_0 = "1.0";
    public static final String VERSION_2_0 = "2.0";
    public static final String VERSION_2_1 = "2.1";
    public static final String CURRENT_VERSION = VERSION_2_1;

    /**
     * 题目类型常量
     */
    public static final int TYPE_SINGLE_CHOICE = 1;
    public static final int TYPE_JUDGE = 2;
    public static final int TYPE_PROGRAMMING = 3;

    /**
     * 处理题目保存前的内容
     * @param examQuestion 题目对象
     */
    public void processQuestionBeforeSave(ExamQuestion examQuestion) {
        if (examQuestion.getContent() == null || examQuestion.getContent().trim().isEmpty()) {
            return;
        }

        try {
            JSONObject content = JSON.parseObject(examQuestion.getContent());
            
            // 确保版本信息
            if (!content.containsKey("version")) {
                content.put("version", CURRENT_VERSION);
            }

            // 提取并处理图片信息
            List<String> imageUrls = extractImageUrls(content);
            if (!imageUrls.isEmpty()) {
                content.put("images", buildImageInfoList(imageUrls));
                content.put("hasRichContent", true);
            }

            // 提取并处理数学公式信息
            List<String> formulas = extractMathFormulas(content);
            if (!formulas.isEmpty()) {
                content.put("formulas", buildFormulaInfoList(formulas));
                content.put("hasRichContent", true);
            }

            // 更新content字段
            examQuestion.setContent(content.toJSONString());

            log.info("题目内容处理完成 - ID: {}, 图片数量: {}, 公式数量: {}", 
                    examQuestion.getId(), imageUrls.size(), formulas.size());

        } catch (Exception e) {
            log.error("处理题目内容失败 - ID: {}", examQuestion.getId(), e);
        }
    }

    /**
     * 处理题目读取后的内容
     * @param examQuestion 题目对象
     */
    public void processQuestionAfterLoad(ExamQuestion examQuestion) {
        if (examQuestion.getContent() == null || examQuestion.getContent().trim().isEmpty()) {
            return;
        }

        try {
            JSONObject content = JSON.parseObject(examQuestion.getContent());
            String version = content.getString("version");

            // 根据版本进行兼容性处理
            if (VERSION_1_0.equals(version)) {
                upgradeFromV1ToV2(content, examQuestion);
            }

            // 确保必要的字段存在
            ensureRequiredFields(content, examQuestion.getQuestionType());

            // 更新content字段
            examQuestion.setContent(content.toJSONString());

        } catch (Exception e) {
            log.error("处理题目内容失败 - ID: {}", examQuestion.getId(), e);
        }
    }

    /**
     * 从V1.0升级到V2.1
     * @param content 内容对象
     * @param examQuestion 题目对象
     */
    private void upgradeFromV1ToV2(JSONObject content, ExamQuestion examQuestion) {
        log.info("升级题目数据格式 - ID: {}, 从V1.0到V2.1", examQuestion.getId());

        // 设置新版本
        content.put("version", CURRENT_VERSION);

        // 根据题目类型进行升级
        if (examQuestion.getQuestionType() == TYPE_SINGLE_CHOICE || examQuestion.getQuestionType() == TYPE_JUDGE) {
            // 客观题：构建模板内容
            String templateContent = buildTemplateFromLegacyData(content, examQuestion);
            content.put("template_content", templateContent);
            content.put("template_content_rich", templateContent);
            content.put("useTemplate", true);
            content.put("templateFields", new String[]{"template_content"});
            content.put("richFields", new String[]{"analysis"});
        } else if (examQuestion.getQuestionType() == TYPE_PROGRAMMING) {
            // 编程题：设置分字段标识
            content.put("useTemplate", false);
            content.put("templateFields", new String[]{});
            content.put("richFields", new String[]{"description", "input_format", "output_format", "analysis"});
        }

        content.put("hasRichContent", false);
        content.put("images", new ArrayList<>());
        content.put("formulas", new ArrayList<>());
    }

    /**
     * 从旧数据构建模板内容
     * @param content 内容对象
     * @param examQuestion 题目对象
     * @return 模板内容
     */
    private String buildTemplateFromLegacyData(JSONObject content, ExamQuestion examQuestion) {
        if (examQuestion.getQuestionType() == TYPE_SINGLE_CHOICE) {
            // 单选题
            StringBuilder template = new StringBuilder();
            template.append("题目：").append(examQuestion.getTitle() != null ? examQuestion.getTitle() : "请输入题目标题").append("\n\n");
            
            if (content.containsKey("options")) {
                List<String> options = content.getJSONArray("options").toJavaList(String.class);
                if (options.size() >= 4) {
                    template.append("A. ").append(options.get(0)).append("\n");
                    template.append("B. ").append(options.get(1)).append("\n");
                    template.append("C. ").append(options.get(2)).append("\n");
                    template.append("D. ").append(options.get(3));
                } else {
                    template.append("A. 选项A内容\nB. 选项B内容\nC. 选项C内容\nD. 选项D内容");
                }
            } else {
                template.append("A. 选项A内容\nB. 选项B内容\nC. 选项C内容\nD. 选项D内容");
            }
            
            return template.toString();
        } else if (examQuestion.getQuestionType() == TYPE_JUDGE) {
            // 判断题
            return "题目：" + (examQuestion.getTitle() != null ? examQuestion.getTitle() : "请输入题目标题");
        }
        
        return "";
    }

    /**
     * 确保必要字段存在
     * @param content 内容对象
     * @param questionType 题目类型
     */
    private void ensureRequiredFields(JSONObject content, Integer questionType) {
        // 确保版本字段
        if (!content.containsKey("version")) {
            content.put("version", CURRENT_VERSION);
        }

        // 确保图片和公式字段
        if (!content.containsKey("images")) {
            content.put("images", new ArrayList<>());
        }
        if (!content.containsKey("formulas")) {
            content.put("formulas", new ArrayList<>());
        }

        // 确保富文本标识
        if (!content.containsKey("hasRichContent")) {
            content.put("hasRichContent", false);
        }

        // 根据题目类型确保特定字段
        if (questionType == TYPE_SINGLE_CHOICE || questionType == TYPE_JUDGE) {
            if (!content.containsKey("useTemplate")) {
                content.put("useTemplate", true);
            }
            if (!content.containsKey("templateFields")) {
                content.put("templateFields", new String[]{"template_content"});
            }
            if (!content.containsKey("richFields")) {
                content.put("richFields", new String[]{"analysis"});
            }
        } else if (questionType == TYPE_PROGRAMMING) {
            if (!content.containsKey("useTemplate")) {
                content.put("useTemplate", false);
            }
            if (!content.containsKey("templateFields")) {
                content.put("templateFields", new String[]{});
            }
            if (!content.containsKey("richFields")) {
                content.put("richFields", new String[]{"description", "input_format", "output_format", "analysis"});
            }
        }
    }

    /**
     * 提取图片URL
     * @param content 内容对象
     * @return 图片URL列表
     */
    private List<String> extractImageUrls(JSONObject content) {
        List<String> imageUrls = new ArrayList<>();
        String contentStr = content.toJSONString();
        
        // 匹配HTML中的img标签
        Pattern imgPattern = Pattern.compile("<img[^>]+src=[\"']([^\"']+)[\"'][^>]*>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = imgPattern.matcher(contentStr);
        
        while (matcher.find()) {
            String url = matcher.group(1);
            if (!imageUrls.contains(url)) {
                imageUrls.add(url);
            }
        }
        
        return imageUrls;
    }

    /**
     * 提取数学公式
     * @param content 内容对象
     * @return 公式列表
     */
    private List<String> extractMathFormulas(JSONObject content) {
        List<String> formulas = new ArrayList<>();
        String contentStr = content.toJSONString();
        
        // 匹配数学公式标记
        Pattern mathPattern = Pattern.compile("data-math=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = mathPattern.matcher(contentStr);
        
        while (matcher.find()) {
            String formula = matcher.group(1);
            if (!formulas.contains(formula)) {
                formulas.add(formula);
            }
        }
        
        return formulas;
    }

    /**
     * 构建图片信息列表
     * @param imageUrls 图片URL列表
     * @return 图片信息列表
     */
    private List<JSONObject> buildImageInfoList(List<String> imageUrls) {
        List<JSONObject> imageInfoList = new ArrayList<>();
        
        for (int i = 0; i < imageUrls.size(); i++) {
            JSONObject imageInfo = new JSONObject();
            imageInfo.put("id", "img_" + System.currentTimeMillis() + "_" + i);
            imageInfo.put("url", imageUrls.get(i));
            imageInfo.put("alt", "题目图片");
            imageInfo.put("usage", "content");
            imageInfo.put("uploadTime", new java.util.Date());
            imageInfo.put("size", 0);
            imageInfoList.add(imageInfo);
        }
        
        return imageInfoList;
    }

    /**
     * 构建公式信息列表
     * @param formulas 公式列表
     * @return 公式信息列表
     */
    private List<JSONObject> buildFormulaInfoList(List<String> formulas) {
        List<JSONObject> formulaInfoList = new ArrayList<>();
        
        for (int i = 0; i < formulas.size(); i++) {
            JSONObject formulaInfo = new JSONObject();
            formulaInfo.put("id", "formula_" + System.currentTimeMillis() + "_" + i);
            formulaInfo.put("latex", formulas.get(i));
            formulaInfo.put("usage", "content");
            formulaInfo.put("createTime", new java.util.Date());
            formulaInfoList.add(formulaInfo);
        }
        
        return formulaInfoList;
    }
}
