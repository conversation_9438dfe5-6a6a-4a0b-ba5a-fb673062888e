{"@metadata": {"authors": ["HushBeg", "Uzdil", "چۈشكۈن"]}, "VARIABLES_DEFAULT_NAME": "تۈر", "TODAY": "بۈگۈن", "DUPLICATE_BLOCK": "كۆچۈرۈش", "ADD_COMMENT": "ئىزاھات قوشۇش", "REMOVE_COMMENT": "ئىزاھاتنى ئۆچۈرۈش", "EXTERNAL_INPUTS": "سىرتقى كىرگۈزۈش", "INLINE_INPUTS": "تاق قۇرلۇق كىرگۈزۈش", "DELETE_BLOCK": "بۆلەك ئۆچۈرۈش", "DELETE_X_BLOCKS": "بۆلەك %1 نى ئۆچۈرۈش", "DELETE_ALL_BLOCKS": "ھەممىنى ئۆچۈرۈش %1 پارچىمۇ؟", "CLEAN_UP": "بۆلەكنى رەتلەش", "COLLAPSE_BLOCK": "قاتلىنىش بۆلىكى", "COLLAPSE_ALL": "قاتلىنىش بۆلىكى", "EXPAND_BLOCK": "ئېچىلىش بۆلىكى", "EXPAND_ALL": "ئېچىلىش بۆلىكى", "DISABLE_BLOCK": "چەكلەنگەن بۆلەك", "ENABLE_BLOCK": "قوزغىتىلغان بۆلەك", "HELP": "ياردەم", "UNDO": "يېنىۋال", "REDO": "قايتىلاش", "CHANGE_VALUE_TITLE": "قىممەت ئۆزگەرتىش:", "RENAME_VARIABLE": "ئۆزگەرگۈچى مىقدارغا قايتا نام قويۇش", "RENAME_VARIABLE_TITLE": "بارلىق بۆلەك “%1\" ئۆزگەرگۈچى مىقدار قايتا ناملىنىپ :", "NEW_VARIABLE": "ئۆزگەرگۈچى مىقدار ... قۇرۇش", "NEW_VARIABLE_TITLE": "يېڭى ئۆزگەرگۈچى مىقدار نامى:", "VARIABLE_ALREADY_EXISTS": "ئىسم مەۋجۇت “%1” ئۆزگەرگۈچى", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "ئىسىملىك“%1” ئۆزگەرگۈچى مىقدار مەۋجۇت بولۇپ تۇرىدۇ ، لېكىن يەنە بىر ئۆزگەرگۈچى مىقدار تىپى بولۇش سۈپىتى بىلەن “%2” مەۋجۇت .", "DELETE_VARIABLE_CONFIRMATION": "ئۆچۈرۈش “%2” ئۆزگەرگۈچى مىقدار%1 ئىشلىتىلىش ئورنى بارمۇ؟", "DELETE_VARIABLE": "“%1” ئۆزگەرگۈچى مىقدارنى ئۆچۈرۈش", "COLOUR_PICKER_HELPURL": "https://zh.wikipedia.org/wiki/رەڭگى", "COLOUR_PICKER_TOOLTIP": " تاختىدىن رەڭنى تاللاڭ", "COLOUR_RANDOM_TITLE": "خالىغان رەڭ", "COLOUR_RANDOM_TOOLTIP": "ئىختىيارىي بىر رەڭنى تاللاڭ", "COLOUR_RGB_TITLE": "رەڭگى", "COLOUR_RGB_RED": "قىزىل", "COLOUR_RGB_GREEN": "يېشىل", "COLOUR_RGB_BLUE": "كۆك", "COLOUR_BLEND_TITLE": "ئارىلاش", "COLOUR_BLEND_COLOUR1": "رەڭ 1", "COLOUR_BLEND_COLOUR2": "رەڭ 2", "COLOUR_BLEND_RATIO": "نىسبەت", "CONTROLS_REPEAT_HELPURL": "https://zh.wikipedia.org/wiki/Forئايلىنىش", "CONTROLS_REPEAT_TITLE": "تەكرار %1قېتىم", "CONTROLS_REPEAT_INPUT_DO": "ئىجرا", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "تەكرار بولۇش", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "تەكرارلىقى", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "ئۈزۈلۈپ ئايلىنىش", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": " كىيىنكى قېتىم داۋاملىق ئايلىنىشن", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "ئۇنىڭ دەۋرىي ئۈزۈلۈش ئۆز ئىچىگە ئالىدۇ .", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "بۇ ئايلىنىشنىڭ قالغان قىسمى ئاتلاپ ئۆتۈپ كېتىدۇ ، ھەمدە داۋاملىق كېلەر قېتىملىق ئىتېراتسىيە .", "CONTROLS_FLOW_STATEMENTS_WARNING": "ئاگاھلاندۇرۇش : بۇ پەقەت بىر ئايلىنىش دەۋرى ئىچىدە ئىشلىتىشكە بولىدۇ .", "CONTROLS_IF_TOOLTIP_1": "ئەگەر قىممىتى ھەقىقەتەن ، بەزى جۈملە .", "CONTROLS_IF_TOOLTIP_2": "ئەگەر قىممىتى ھەقىقەتەن ، ئۇنداقتا نىڭ بىر جۈملە . ئۇنداق بولمايدىكەن، ئىككىنچى جۈملىسى ئىجرا قىلىندى .", "CONTROLS_IF_TOOLTIP_3": "ئەگەر تۇنجى قىممىتى ھەقىقەتەن ، ئۇنداقتا نىڭ بىر جۈملە . ئۇنداق بولمايدىكەن، ئەگەر ئىككىنچى قىممىتى ، ئۇنداقتا ئىككىنچى پارچىنىڭ جۈملە .", "CONTROLS_IF_TOOLTIP_4": "ئەگەر تۇنجى قىممىتى ھەقىقەتەن ، ئۇنداقتا نىڭ بىر جۈملە . ئۇنداق بولمايدىكەن، ئەگەر ئىككىنچى قىممىتى ، بولسا ئىجرا قىلىش جۈملىسى ئىشككى پارچە . ئەگەر قىممىتى يوق ، ئۇنداقتا ئەڭ ئاخىرقى بىر جۈملىسى .", "CONTROLS_IF_MSG_IF": "ئەگەر", "CONTROLS_IF_MSG_ELSEIF": "ئۇنداق بولمىسا ئەگەر", "CONTROLS_IF_MSG_ELSE": "ئۇنداق بولمىسا", "CONTROLS_IF_IF_TOOLTIP": "كۆپۈيۈپ كىتىدۇ، ئۆچۈرۈش ياكى قايتا تىزىلغان بايرام « if （ سۆزىنىڭ پارچە قايتىدىن تەقسىملەش .", "CONTROLS_IF_ELSEIF_TOOLTIP": "بۇ بىلمەيمىز جۈملە بۆلىكى قوشۇلۇپ بىر if شەرتى .", "CONTROLS_IF_ELSE_TOOLTIP": "ئەڭ ئاخىرقى قوشۇش ، ھەممە ئەھۋالنى ئۆز ئىچىگە ئالىدۇ بايرىمىدا بىلمەيمىز ifپارچىلىرى .", "IOS_OK": "ماقۇل", "IOS_CANCEL": "ۋاز كەچ", "IOS_ERROR": "خاتالىق", "IOS_PROCEDURES_INPUTS": "كىرگۈزۈش", "IOS_PROCEDURES_ADD_INPUT": "+ كىرگۈزۈپ قوشۇش", "IOS_PROCEDURES_ALLOW_STATEMENTS": "كېلىشىمگە قوشۇلۇش", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "بۇنىڭدا مەزمۇننى قايتا كىرگۈزۈش ئىقتىدارى بار .", "IOS_VARIABLES_ADD_VARIABLE": "+ ئۆزگەرگۈچى مىقدار قوشۇش", "IOS_VARIABLES_ADD_BUTTON": "قوشۇش", "IOS_VARIABLES_RENAME_BUTTON": "ئىسىم ئۆزگەرتىش", "IOS_VARIABLES_DELETE_BUTTON": "ئۆچۈرۈش", "IOS_VARIABLES_VARIABLE_NAME": "ئۆزگەرگۈچى مىقدارنىڭ نامى", "IOS_VARIABLES_EMPTY_NAME_ERROR": "سىز ئۆزگەرگۈچى مىقدار نامى ئىشلىتىشكە بولمايدۇ .", "LOGIC_COMPARE_HELPURL": "https://zh.wikipedia.org/wiki/ تەڭ ئەمەس", "LOGIC_COMPARE_TOOLTIP_EQ": "ئەگەر ئىككى دانە كىرگۈزۈش نەتىجىسى تەڭ بولسا ، راستىنلا كەينىگە قايتسا.", "LOGIC_COMPARE_TOOLTIP_NEQ": "ئەگەر ئىككى دانە كىرگۈزۈش نەتىجىسى تەڭ بولمايدۇ ، بەك كەلدى .", "LOGIC_COMPARE_TOOLTIP_LT": "ئەگەر تۇنجى كىرگۈزۈش نەتىجىدە ئىشككىنچى كىچىك بولسا راستىنلا كەينىگە قايتسا .", "LOGIC_COMPARE_TOOLTIP_LTE": "ئەگەر تۇنجى كىرگۈزۈش نەتىجىسى ئىككىنچى كىرگۈزۈش نەتىجىسى تىن تۆۋەن ياكى شۇنىڭغا تەڭ بولسا راستىنلا كەينىگە قايتسا .", "LOGIC_COMPARE_TOOLTIP_GT": "ئەگەر تۇنجى كىرگۈزۈش نەتىجىسى ئىشككىنچى چوڭ بولسا راستىنلا كەينىگە قايتسا .", "LOGIC_COMPARE_TOOLTIP_GTE": "ئەگەر تۇنجى كىرگۈزۈش نەتىجىدە ئىشككىنچى كىچىك بولسا راستىنلا كەينىگە قايتسا .", "LOGIC_OPERATION_AND": "ۋە", "LOGIC_OPERATION_OR": "ياكى", "LOGIC_NEGATE_TITLE": "ئەمەس%1", "LOGIC_BOOLEAN_TRUE": "ھەقىقىي", "LOGIC_BOOLEAN_FALSE": "يالغان", "LOGIC_BOOLEAN_TOOLTIP": "راست ياكى يالغان قايتىش", "LOGIC_NULL": "قۇرۇق", "LOGIC_NULL_TOOLTIP": " نۆلگە قايتىش", "LOGIC_TERNARY_CONDITION": "سىناق", "LOGIC_TERNARY_IF_TRUE": "ئەگەر راست بولسا", "LOGIC_TERNARY_IF_FALSE": "ئەگەر يالغان بولسا", "MATH_NUMBER_HELPURL": "https://zh.wikipedia.org/wiki/سان", "MATH_NUMBER_TOOLTIP": "بىر سان.", "MATH_ARITHMETIC_HELPURL": "https://zh.wikipedia.org/wiki/ئارىفمېتىكىلىق", "MATH_SINGLE_HELPURL": "https://zh.wikipedia.org/wiki/كۋادرات يىلتىز", "MATH_SINGLE_OP_ROOT": " كۋادرات يىلتىز", "MATH_SINGLE_OP_ABSOLUTE": "مۇتلەق", "MATH_TRIG_HELPURL": "https://zh.wikipedia.org/wiki/ترىگونومېتىرىيىلىك فۇنكسىيە", "MATH_CONSTANT_HELPURL": "https://zh.wikipedia.org/wiki/ماتېماتىكا تۇراقلىق سانى", "MATH_IS_EVEN": "جۈپ سان", "MATH_IS_ODD": " تاق سان", "MATH_IS_PRIME": "تۈپ سان", "MATH_IS_WHOLE": "پۈتۈن سان", "MATH_IS_POSITIVE": "مۇسبەت", "MATH_IS_NEGATIVE": " مەنپى", "MATH_IS_DIVISIBLE_BY": "پۈتۈن بۆلۈنۈش", "MATH_CHANGE_HELPURL": "https://zh.wikipedia.org/wiقوشۇش", "MATH_CHANGE_TITLE": " ئۆزگەرتىش %1 دىن %2", "MATH_ROUND_HELPURL": "https://zh.wikipedia.org/wiki/سانلىق قىممەت تۈزىتىش", "MATH_ROUND_OPERATOR_ROUND": "تۆۋەنگە تارتىڭ", "MATH_ROUND_OPERATOR_ROUNDUP": " تۆۋەنگە تارتىڭ", "MATH_ROUND_OPERATOR_ROUNDDOWN": "تۆۋەنگە تارتىڭ", "MATH_ONLIST_OPERATOR_MIN": "جەدۋەل ئىچىدىكى ئەڭ كىچىك قىممەت", "MATH_ONLIST_TOOLTIP_MIN": " جەدۋەلدىكى ئەڭ كىچىك سانغا قايتىش", "MATH_ONLIST_OPERATOR_MAX": " جەدۋەلدىكى ئەڭ چوڭ قىممەت", "MATH_ONLIST_OPERATOR_AVERAGE": "جەدۋەل ئىچىدىكى ئوتتۇرىچە سان", "MATH_ONLIST_OPERATOR_MEDIAN": "جەدۋەلدىكى ئوتتۇرا سان", "MATH_ONLIST_TOOLTIP_MEDIAN": " جەدۋەلدىكى ئوتتۇرا سانغا قايتىش", "MATH_ONLIST_OPERATOR_MODE": " جەدۋەل ھالىتى", "MATH_MODULO_HELPURL": "https://zh.wikipedia.org/wiki/مودېل ھېسابى", "TEXT_CREATE_JOIN_TITLE_JOIN": "   قوشۇش", "LISTS_GET_INDEX_GET": "قولغا كەلتۈرۈش", "LISTS_GET_INDEX_REMOVE": "چىقىرىۋىتىش", "LISTS_GET_INDEX_FIRST": "تۇنجى", "LISTS_GET_INDEX_LAST": "ئاخىرقى", "LISTS_GET_INDEX_RANDOM": "خالىغانچە", "LISTS_SET_INDEX_SET": "تەڭشەك", "LISTS_SET_INDEX_INSERT": "قىستۇرۇڭ", "LISTS_SORT_ORDER_ASCENDING": "يۇقىرىغا", "LISTS_SORT_ORDER_DESCENDING": "تۆۋەنگە", "LISTS_SORT_TYPE_NUMERIC": "سان بويىچە تىزىل", "LISTS_SORT_TYPE_TEXT": " ھەرپ بويىچە تىزىل", "LISTS_SORT_TYPE_IGNORECASE": "ھەرب بويىچە تىزىل، چوڭ كىچىك يېزىلىش ھېساپ قىلىنمايدۇ", "DIALOG_OK": "ماقۇل", "DIALOG_CANCEL": "ۋاز كەچ"}