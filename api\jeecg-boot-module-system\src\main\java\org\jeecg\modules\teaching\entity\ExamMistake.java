package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 错题记录
 * @Author: jeecg-boot
 * @Date:   2023-06-17
 * @Version: V1.0
 */
@ApiModel(value="exam_mistake对象", description="错题记录")
@Data
@TableName("exam_mistake")
public class ExamMistake implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "题目ID")
    private String questionId;
    
    @ApiModelProperty(value = "上次错误答案")
    private String lastAnswer;
    
    @ApiModelProperty(value = "错误次数")
    private Integer mistakeCount;
    
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近一次答错时间")
    private Date lastMistakeTime;
} 