-- 学生金币表
CREATE TABLE `teaching_student_coin` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `coin_count` int(11) DEFAULT 0 COMMENT '金币数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生金币表';

-- 学生金币记录表
CREATE TABLE `teaching_student_coin_record` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型（1:获取, 2:消费）',
  `coin_count` int(11) DEFAULT 0 COMMENT '金币数量',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `source` tinyint(1) DEFAULT NULL COMMENT '操作来源（1:精选作品, 2:客观题, 3:编程题, 4:每日任务, 5:礼物兑换）',
  `related_id` varchar(36) DEFAULT NULL COMMENT '关联ID（作品ID、作业ID、礼物ID等）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生金币记录表';

-- 每日任务记录表
CREATE TABLE `teaching_daily_task` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `task_type` tinyint(1) NOT NULL COMMENT '任务类型（1:每日签到, 2:评论作品, 3:点赞作品）',
  `status` tinyint(1) DEFAULT 0 COMMENT '完成状态（0:未完成, 1:已完成）',
  `task_date` date DEFAULT NULL COMMENT '任务日期',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `related_id` varchar(36) DEFAULT NULL COMMENT '关联ID（如评论ID、点赞作品ID等）',
  PRIMARY KEY (`id`),
  KEY `idx_user_task_date` (`user_id`, `task_date`) COMMENT '用户任务日期索引',
  KEY `idx_task_type_date` (`task_type`, `task_date`) COMMENT '任务类型日期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每日任务记录表';

-- 礼品兑换记录表
CREATE TABLE `teaching_gift_exchange` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `gift_id` varchar(36) NOT NULL COMMENT '礼品ID',
  `gift_name` varchar(100) DEFAULT NULL COMMENT '礼品名称',
  `coin_count` int(11) DEFAULT 0 COMMENT '消费金币',
  `exchange_time` datetime DEFAULT NULL COMMENT '兑换时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态（0:待领取, 1:已领取）',
  `receive_time` datetime DEFAULT NULL COMMENT '领取时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  KEY `idx_exchange_time` (`exchange_time`) COMMENT '兑换时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='礼品兑换记录表'; 