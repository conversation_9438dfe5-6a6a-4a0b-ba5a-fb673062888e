package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 课程通知
 * @Author: jeecg-boot
 * @Date:   2025-06-25
 * @Version: V1.0
 */
@Data
@TableName("teaching_course_notification")
public class TeachingCourseNotification implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    /**
     * 关联课程排期ID
     */
    private String scheduleId;
    
    /**
     * 通知类型：NEW-新增课程, UPDATE-变更课程, CANCEL-取消课程, REMIND-课程提醒
     */
    private String notificationType;
    
    /**
     * 通知标题
     */
    private String title;
    
    /**
     * 通知内容
     */
    private String content;
    
    /**
     * 发送人
     */
    private String sender;
    
    /**
     * 接收人ID，逗号分隔
     */
    private String receivers;
    
    /**
     * 关联系统通知ID
     */
    private String announcementId;
    
    /**
     * 发送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;
    
    /**
     * 是否已发送：0-未发送，1-已发送
     */
    private Integer isSent;
    
    /**
     * 提醒时间（用于课程提醒）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date remindTime;
    
    /**
     * 是否通知教师：0-否，1-是
     */
    private Integer notifyTeachers;
    
    /**
     * 是否通知学生：0-否，1-是
     */
    private Integer notifyStudents;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 