{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Liridon", "아라"]}, "VARIABLES_DEFAULT_NAME": "send", "UNNAMED_KEY": "pa <PERSON><PERSON><PERSON>", "TODAY": "Sot", "DUPLICATE_BLOCK": "<PERSON><PERSON><PERSON>", "ADD_COMMENT": "Vendos nje <PERSON>", "REMOVE_COMMENT": "<PERSON><PERSON><PERSON> k<PERSON>", "DUPLICATE_COMMENT": "Koment Dublikatë", "EXTERNAL_INPUTS": "Hyrjet e jashtme", "INLINE_INPUTS": "Hyrjet e brendshme", "DELETE_BLOCK": "<PERSON><PERSON><PERSON> b<PERSON>", "DELETE_X_BLOCKS": "Fshij %1 blloqe", "DELETE_ALL_BLOCKS": "Fshijë të gjitha %1 të blloqeve?", "CLEAN_UP": "<PERSON><PERSON> b<PERSON>", "COLLAPSE_BLOCK": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>n", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "EXPAND_BLOCK": "<PERSON><PERSON><PERSON><PERSON>", "EXPAND_ALL": "<PERSON><PERSON><PERSON>", "DISABLE_BLOCK": "Çak<PERSON><PERSON><PERSON> bllokun", "ENABLE_BLOCK": "Aktivizo bllokun", "HELP": "Ndihmë", "UNDO": "Zhbëj", "REDO": "Ribëj", "CHANGE_VALUE_TITLE": "<PERSON><PERSON><PERSON><PERSON>:", "RENAME_VARIABLE": "Ndrysho emrin variables...", "RENAME_VARIABLE_TITLE": "Ndrysho emrin e te gjitha '%1' variablave ne :", "NEW_VARIABLE": "<PERSON><PERSON><PERSON>...", "NEW_STRING_VARIABLE": "<PERSON><PERSON><PERSON> varg", "NEW_NUMBER_VARIABLE": "<PERSON><PERSON><PERSON> n<PERSON>", "NEW_COLOUR_VARIABLE": "<PERSON><PERSON><PERSON> varia<PERSON> ng<PERSON>...", "NEW_VARIABLE_TYPE_TITLE": "Tip i ri i variablës:", "NEW_VARIABLE_TITLE": "Emri i identifikatorit të ri:", "VARIABLE_ALREADY_EXISTS": "Një variabël e quajtur '%1' tashmë ekziston.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "<PERSON>jë variabël me emrin '%1' veç ek<PERSON> për një tip tjetër: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Fshi përdorimin %1 të variablës '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Nuk mund të fshihet variabla '%1' sepse është pjesë e definicionit të funksionit '%2'", "DELETE_VARIABLE": "Fshi variablën '%1'", "COLOUR_PICKER_HELPURL": "http://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Zgjidh nje ngjyre nga nje game ngjyrash.", "COLOUR_RANDOM_TITLE": "ngjyre e rastesishme", "COLOUR_RANDOM_TOOLTIP": "Zgjidhni një ngjyrë në mënyrë të rastësishme.", "COLOUR_RGB_HELPURL": "http://www.december.com/html/spec/colorper.html", "COLOUR_RGB_TITLE": "ngjyre me", "COLOUR_RGB_RED": "e kuqe", "COLOUR_RGB_GREEN": "jeshile", "COLOUR_RGB_BLUE": "blu", "COLOUR_RGB_TOOLTIP": "<PERSON><PERSON>jo një ngjyrë me shumën e specifikuar te te kuqes, te g<PERSON><PERSON><PERSON><PERSON>res, dhe bluse. Te gjitha vlerat duhet te jene mes 0 dhe 100.", "COLOUR_BLEND_HELPURL": "http://meyerweb.com/eric/tools/color-blend/", "COLOUR_BLEND_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COLOUR_BLEND_COLOUR1": "Ngjyra 1", "COLOUR_BLEND_COLOUR2": "Ngjyra 2", "COLOUR_BLEND_RATIO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COLOUR_BLEND_TOOLTIP": "<PERSON>zien dy ngjyra së bashku me një raport të dhënë (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "http://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "përsërit %1 herë", "CONTROLS_REPEAT_INPUT_DO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONTROLS_REPEAT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON><PERSON> disa fjali disa herë.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "<PERSON><PERSON><PERSON><PERSON><PERSON> derisa", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Përderisa një vlerë është e saktë, atëherë ekzekuto disa fjali.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Përderisa një vlerë është e pasaktë, atëherë ekzekuto disa fjali.", "CONTROLS_FOR_TOOLTIP": "Bëje identifikuesin \"%1\" që ta ketë vlerat prej numrit të fillimit deri tek numri i fundit, duke num<PERSON><PERSON><PERSON> nga intervali i specifikuar, dhe ti bëj blloqet e specifikuara.", "CONTROLS_FOR_TITLE": "numero me %1 nga %2 ne %3 me nga %4", "CONTROLS_FOREACH_TITLE": "per cdo produkt %1 ne liste %2", "CONTROLS_FOREACH_TOOLTIP": "Per cdo produkt ne nje \"liste\" \"vendos\" ndryshoren '%1' produktit, dhe pastaj bej disa deklarata.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "dil nga nje faze perseritese", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "vazhdo me <PERSON>in tjeter te nje faze perseritese", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "<PERSON><PERSON>u nga unaza.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "<PERSON><PERSON><PERSON><PERSON><PERSON> pje<PERSON>n e mbetur të unazës, dhe vazhdo me ripërsëritjen tjetër.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Paralajmërim: <PERSON><PERSON> bllok mund të përdoret vetëm brenda unazës.", "CONTROLS_IF_TOOLTIP_1": "Nëse një vlerë është e saktë, atëherë ekzekuto disa fjali.", "CONTROLS_IF_TOOLTIP_2": "Nëse një vlerë është e saktë, atëherë ekzekuto bllokun e parë të fjalive. Përndryshe, ekzekuto bllokun e dytë të fjalive.", "CONTROLS_IF_TOOLTIP_3": "Nëse vlera e parë është e saktë, atëherë ekzekuto bllokun e parë të fjalive. Përndryshe, nëse vlera e dytë është e saktë, ekzekuto bllokun e dytë të fjalive.", "CONTROLS_IF_TOOLTIP_4": "Nëse vlera e parë është e saktë, atëherë ekzekuto bllokun e parë të fjalive. Përndryshe, nëse vlera e dytë është e saktë, ekzekuto bllokun e dytë të fjalive. Nëse asnjëra nga vlerat nuk është e saktë, ekzekuto bllokun e fundit të fjalive.", "CONTROLS_IF_MSG_IF": "në<PERSON>", "CONTROLS_IF_MSG_ELSEIF": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CONTROLS_IF_MSG_ELSE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CONTROLS_IF_IF_TOOLTIP": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ose rir<PERSON>llo sektoret për ta rikonfiguruar këte bllok nëse.", "CONTROLS_IF_ELSEIF_TOOLTIP": "\"Vendos\" \"kushtein\"tek \"pjesa\" \"if\"", "CONTROLS_IF_ELSE_TOOLTIP": "Shto një përf<PERSON>ues, që i mbërrin të gjitha kushtet në bllokun nëse.", "IOS_OK": "<PERSON><PERSON>ll", "IOS_CANCEL": "<PERSON><PERSON>", "IOS_ERROR": "<PERSON><PERSON><PERSON>", "IOS_PROCEDURES_INPUTS": "INPUTET", "IOS_PROCEDURES_ADD_INPUT": "+ Shto Inputet", "IOS_PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON>", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "Ky funksion ka inpute të dy<PERSON>.", "IOS_VARIABLES_ADD_VARIABLE": "+ <PERSON><PERSON><PERSON>", "IOS_VARIABLES_ADD_BUTTON": "Shto", "IOS_VARIABLES_RENAME_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "Grise", "IOS_VARIABLES_VARIABLE_NAME": "Emri i variablës", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Nuk mund të përdorni variabël pa emër.", "LOGIC_COMPARE_HELPURL": "http://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "Ktheje të saktë nëse të dy hyrjet janë të barabarta me njëra-tjetrën.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Ktheje të saktë nëse të dy hyrjet nuk janë të barabarta me njëra-tjetrën.", "LOGIC_COMPARE_TOOLTIP_LT": "Ktheje të saktë nëse hyrja e parë është më e vogël se hyrja e dytë.", "LOGIC_COMPARE_TOOLTIP_LTE": "Ktheje të saktë nëse hyrja e parë është më e vogël ose e barabartë me hyrjen e dytë.", "LOGIC_COMPARE_TOOLTIP_GT": "Ktheje të saktë nëse hyrja e parë është më e madhe se hyrja e dytë.", "LOGIC_COMPARE_TOOLTIP_GTE": "Ktheje të saktë nëse hyrja e parë është më e madhe ose e barabartë me hyrjen e dytë.", "LOGIC_OPERATION_TOOLTIP_AND": "Kthehet të saktë nëse të dy hyrjet janë të sakta.", "LOGIC_OPERATION_AND": "dhe", "LOGIC_OPERATION_TOOLTIP_OR": "Kthehet e saktë nëse së paku njëra nga hyrjet është e saktë.", "LOGIC_OPERATION_OR": "ose", "LOGIC_NEGATE_TITLE": "jo %1", "LOGIC_NEGATE_TOOLTIP": "Kthehet e saktë nëse hyrja është e pasaktë. Kthehet e pasaktë nëse hyrja është e saktë.", "LOGIC_BOOLEAN_TRUE": "e saktë", "LOGIC_BOOLEAN_FALSE": "e pasaktë", "LOGIC_BOOLEAN_TOOLTIP": "Kthehet ose të saktë ose të pasaktë.", "LOGIC_NULL_HELPURL": "http://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "pavlerë", "LOGIC_NULL_TOOLTIP": "Kthehet e pavlerë.", "LOGIC_TERNARY_HELPURL": "http://en.wikipedia.org/wiki/%3F:", "LOGIC_TERNARY_CONDITION": "test", "LOGIC_TERNARY_IF_TRUE": "nëse e saktë", "LOGIC_TERNARY_IF_FALSE": "nëse e pasaktë", "LOGIC_TERNARY_TOOLTIP": "Kontrollo kushtin në 'test'. Nëse kushti është i saktë, kthen vlerën 'nëse e saktë'; përndryshe kthen vlerën 'nëse e pasaktë'.", "MATH_NUMBER_HELPURL": "http://en.wikipedia.org/wiki/Number", "MATH_NUMBER_TOOLTIP": "<PERSON><PERSON><PERSON> numë<PERSON>.", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "x", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "asinus", "MATH_TRIG_ACOS": "acosinus", "MATH_TRIG_ATAN": "atangjentë", "MATH_ARITHMETIC_HELPURL": "http://sq.wikipedia.org/wiki/Aritmetika", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON><PERSON>n shumën e dy numrave.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "<PERSON><PERSON>n ndryshimin e dy numrave.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "<PERSON><PERSON>n produktin e dy numrave.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "<PERSON><PERSON><PERSON> her<PERSON> e dy numrave.", "MATH_ARITHMETIC_TOOLTIP_POWER": "<PERSON><PERSON><PERSON> numrin e parë të ngritur në fuqinë e numrit të dytë.", "MATH_SINGLE_HELPURL": "http://en.wikipedia.org/wiki/Square_root", "MATH_SINGLE_OP_ROOT": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "MATH_SINGLE_TOOLTIP_ROOT": "Kthen rrënjën katrore të një numri.", "MATH_SINGLE_OP_ABSOLUTE": "absolut", "MATH_SINGLE_TOOLTIP_ABS": "Kthen vlerën absolute të një numri.", "MATH_SINGLE_TOOLTIP_NEG": "Kthe negacionin e një numri.", "MATH_SINGLE_TOOLTIP_LN": "Kthen logaritmën natyrale të një numri.", "MATH_SINGLE_TOOLTIP_LOG10": "Kthen 10 logaritmet bazë të një numri.", "MATH_SINGLE_TOOLTIP_EXP": "Kthen e në fuqinë e një numri.", "MATH_SINGLE_TOOLTIP_POW10": "Kthen 10 në fuqinë e një numri.", "MATH_TRIG_HELPURL": "http://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "<PERSON><PERSON> kos<PERSON>in e nje kendi (jo ne radiant).", "MATH_TRIG_TOOLTIP_COS": "Kthe kosinusin e nje grade (jo ne radiant).", "MATH_TRIG_TOOLTIP_TAN": "Kthe tangentin e nje kendi (jo radiant).", "MATH_TRIG_TOOLTIP_ASIN": "Rik<PERSON> sin-1 e nje numeri.", "MATH_TRIG_TOOLTIP_ACOS": "Rikthe cos-1 e nje numeri.", "MATH_TRIG_TOOLTIP_ATAN": "Kthe tg-1 e nje numeri.", "MATH_CONSTANT_HELPURL": "http://en.wikipedia.org/wiki/Mathematical_constant", "MATH_CONSTANT_TOOLTIP": "<PERSON><PERSON>n një nga konstantet e përbashk<PERSON>ta: : π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (infiniti).", "MATH_IS_EVEN": "është çift", "MATH_IS_ODD": "është tek", "MATH_IS_PRIME": "është prim", "MATH_IS_WHOLE": "është i plotë", "MATH_IS_POSITIVE": "është pozitiv", "MATH_IS_NEGATIVE": "është negativ", "MATH_IS_DIVISIBLE_BY": "është i pjestueshme me", "MATH_IS_TOOLTIP": "Kontrollo nëse një numër është çift, tek, prim, i plotë, pozitiv, negativ, ose nëse është i pjestueshëm me një numër të caktuar. Kthehet e saktë ose e pasaktë.", "MATH_CHANGE_HELPURL": "http://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "ndrysho %1 nga %2", "MATH_CHANGE_TOOLTIP": "Shto një numër në ndryshoren '%1'.", "MATH_ROUND_HELPURL": "http://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "Rrumbullakësimi i numrit të lartë ose të ulët.", "MATH_ROUND_OPERATOR_ROUND": "rrumbullakësimi", "MATH_ROUND_OPERATOR_ROUNDUP": "rrumbullakësimi i lartë", "MATH_ROUND_OPERATOR_ROUNDDOWN": "rrumbullakësimi i ulët", "MATH_ONLIST_OPERATOR_SUM": "mbled<PERSON><PERSON> e <PERSON>ës", "MATH_ONLIST_TOOLTIP_SUM": "<PERSON>the shumën e të gjithë numrave të listës.", "MATH_ONLIST_OPERATOR_MIN": "numri më i ulët i listës", "MATH_ONLIST_TOOLTIP_MIN": "<PERSON>the numrin me të vogël të listës.", "MATH_ONLIST_OPERATOR_MAX": "numri më i madh i listës", "MATH_ONLIST_TOOLTIP_MAX": "Kthe numrin më të madh të listës.", "MATH_ONLIST_OPERATOR_AVERAGE": "mesatarja e listës", "MATH_ONLIST_TOOLTIP_AVERAGE": "Kthen mesatarën (kuptimi aritmetik) i vlerave numerike të listës.", "MATH_ONLIST_OPERATOR_MEDIAN": "mediana e <PERSON>ë<PERSON>", "MATH_ONLIST_TOOLTIP_MEDIAN": "<PERSON><PERSON> numrin median të listës.", "MATH_ONLIST_OPERATOR_MODE": "modat e listës", "MATH_ONLIST_TOOLTIP_MODE": "<PERSON>the listën e sendit(eve) më të zakonshme të listës.", "MATH_ONLIST_OPERATOR_STD_DEV": "devijimi standard i listës", "MATH_ONLIST_TOOLTIP_STD_DEV": "Kthe devijimin standard të listës.", "MATH_ONLIST_OPERATOR_RANDOM": "send i rastësishëm i listës", "MATH_ONLIST_TOOLTIP_RANDOM": "Kthe një element të rastësishëm nga lista.", "MATH_MODULO_HELPURL": "http://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "mbetësi i %1 ÷ %2", "MATH_MODULO_TOOLTIP": "<PERSON><PERSON><PERSON> m<PERSON> nga pjestimi i dy numrave.", "MATH_CONSTRAIN_TITLE": "detyro %1 e ulët %2 e lartë %3", "MATH_CONSTRAIN_TOOLTIP": "Vëni një numër që të jetë në mes të kufive të specifikuara(përfshirëse).", "MATH_RANDOM_INT_HELPURL": "http://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "numër i plotë i rastësishëm nga %1 deri në %2", "MATH_RANDOM_INT_TOOLTIP": "Kthe një numër të plotë të rastësishëm të dy kufijve të specifikuar, të përfshirë.", "MATH_RANDOM_FLOAT_HELPURL": "http://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "fraksioni i rastësishëm", "MATH_RANDOM_FLOAT_TOOLTIP": "Kthe fraksionin e rastësishëm në mes të 0.0 (përfshirëse) dhe 1.0 (jopërfshirëse).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 of X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Ktheni arkangjentin e pikë<PERSON> (X, Y) në gradë nga -180 në 180.", "TEXT_TEXT_HELPURL": "http://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "<PERSON><PERSON>, f<PERSON><PERSON>, ose r<PERSON>t teksti.", "TEXT_JOIN_TITLE_CREATEWITH": "krijo tekst me", "TEXT_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON> nje pjese te tekstit duke bash<PERSON><PERSON> se bashku disa sende", "TEXT_CREATE_JOIN_TITLE_JOIN": "bashkang<PERSON>", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ose r<PERSON>llo sektoret për ta rikonfiguruar këtë bllok teksti.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Shto nje gje ne tekst", "TEXT_APPEND_TITLE": "ne %1 shto tekst %2", "TEXT_APPEND_TOOLTIP": "shto tekst tek varibla '%1'.", "TEXT_LENGTH_TITLE": "gjatesi %1", "TEXT_LENGTH_TOOLTIP": "Pergjig<PERSON> me nje numer <PERSON> (duke perfshire hapesire) ne tekstin e dhene.", "TEXT_ISEMPTY_TITLE": "%1 eshte bosh", "TEXT_ISEMPTY_TOOLTIP": "Kthehet e vertete neqoftese teksti i dhene eshte bosh.", "TEXT_INDEXOF_TOOLTIP": "Pergjigjet me indeksin e pare/fundit te rastisjes se tekstit te pare ne tekstin e dyte. Pergjigjet me %1 ne qofte se teksti nuk u gjet.", "TEXT_INDEXOF_TITLE": "ne tekst %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "g<PERSON>j rast<PERSON>jen e pare te tekstit", "TEXT_INDEXOF_OPERATOR_LAST": "g<PERSON>j rastisjen e fundit te tekstit", "TEXT_CHARAT_TITLE": "në tekst %1 %2", "TEXT_CHARAT_FROM_START": "merr shkronjen #", "TEXT_CHARAT_FROM_END": "merr shkronjen # nga fundi", "TEXT_CHARAT_FIRST": "merr shkronjen e pare", "TEXT_CHARAT_LAST": "merr shkronjen e fundit", "TEXT_CHARAT_RANDOM": "merr nje shkronje te rastesishme", "TEXT_CHARAT_TOOLTIP": "Kthe nje shkronje nga nje pozicion i caktuar.", "TEXT_GET_SUBSTRING_TOOLTIP": "Pergjigjet me nje pjese te caktuar teksti.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ne tekst", "TEXT_GET_SUBSTRING_START_FROM_START": "Merr nenvargun nga shkronja #", "TEXT_GET_SUBSTRING_START_FROM_END": "merr nenvargun nga shkronja # nga fundi", "TEXT_GET_SUBSTRING_START_FIRST": "merr vlerat qe vazhdojne me shkronjen e pare", "TEXT_GET_SUBSTRING_END_FROM_START": "ne shkronjen #", "TEXT_GET_SUBSTRING_END_FROM_END": "ne shkronjen # nga fundi", "TEXT_GET_SUBSTRING_END_LAST": "tek shkronja e fundit", "TEXT_CHANGECASE_TOOLTIP": "Kthe nje kopje te tekstit ne nje rast te ndryshem.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "me shkronja te medha shtypi", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "me shkronja te vogla shtypi", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "Fillimi me shkronje te madhe shtypi", "TEXT_TRIM_TOOLTIP": "Pergjigju me nje kopje te tekstit me hapesira te fshira nga njera ane ose te dyja anet.", "TEXT_TRIM_OPERATOR_BOTH": "prit hapesirat nga te dyja anet", "TEXT_TRIM_OPERATOR_LEFT": "prit hapesirat nga ana e majte", "TEXT_TRIM_OPERATOR_RIGHT": "prit hapesirat nga ana e djathte", "TEXT_PRINT_TITLE": "printo %1", "TEXT_PRINT_TOOLTIP": "<PERSON>o tekstin e caktuar, numer ose vlere tjeter.", "TEXT_PROMPT_TYPE_TEXT": "kerko tekst me njoftim", "TEXT_PROMPT_TYPE_NUMBER": "kerko nje numer me njoftim", "TEXT_PROMPT_TOOLTIP_NUMBER": "Kerk<PERSON>ji perdoruesit nje numer.", "TEXT_PROMPT_TOOLTIP_TEXT": "Kerkoji perdoruesit ca tekst.", "TEXT_COUNT_MESSAGE0": "numro %1 në %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Numrin sa herë paraqitet një tekst brenda një teksti tjetër.", "TEXT_REPLACE_MESSAGE0": "zëvendëso %1 me %2 në %3", "TEXT_REPLACE_TOOLTIP": "Zëvendëso të gjitha paraqitjet e një teksti brenda një teksti tjetër.", "TEXT_REVERSE_MESSAGE0": "kthe %1", "TEXT_REVERSE_TOOLTIP": "Kthen renditjen e karaktereve në tekst.", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "krijo një listë të z<PERSON>zët", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON><PERSON><PERSON> një <PERSON>, te gjatësisë 0, duke mos p<PERSON> asnj<PERSON> regjistrim të të dhënave", "LISTS_CREATE_WITH_TOOLTIP": "<PERSON><PERSON><PERSON> një listë me ndonjë numbër të sendeve.", "LISTS_CREATE_WITH_INPUT_WITH": "krijo listë me", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "listë", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ose r<PERSON>llo sektoret për ta rikonfiguruar këtë bllok të listës.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Shto një send në listë.", "LISTS_REPEAT_TOOLTIP": "Krijon në listë qe përmban vlerën e dhënë të përsëritur aq herë sa numri i specifikuar.", "LISTS_REPEAT_TITLE": "krijo listën me sendin %1 të përsëritur %2 herë", "LISTS_LENGTH_TITLE": "gjatësia e %1", "LISTS_LENGTH_TOOLTIP": "Kthen g<PERSON>tësinë e listës.", "LISTS_ISEMPTY_TITLE": "%1 është e zbraztë", "LISTS_ISEMPTY_TOOLTIP": "Kthehet i saktë nëse lista është e zbraztë.", "LISTS_INLIST": "në listë", "LISTS_INDEX_OF_FIRST": "gjen ndo<PERSON>jen e parë të sendit", "LISTS_INDEX_OF_LAST": "gjen ndodhjen e fundit të sendit", "LISTS_INDEX_OF_TOOLTIP": "Kthen indek<PERSON> e ndodh<PERSON> së parë/fudit të sendit në listë. Kthen %1 nëse teksti nuk është gjetur.", "LISTS_GET_INDEX_GET": "merr", "LISTS_GET_INDEX_GET_REMOVE": "merr dhe fshij", "LISTS_GET_INDEX_REMOVE": "largo", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# nga fundi", "LISTS_GET_INDEX_FIRST": "i parë", "LISTS_GET_INDEX_LAST": "i fundit", "LISTS_GET_INDEX_RANDOM": "i rastësishëm", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 është sendi i parë.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 është sendi i fundit.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Kthen një send në pozicionin e specifikuar në listë.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Rikthen tek artikulli i par në list.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Kthen artikullin e fundit në list.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "<PERSON><PERSON><PERSON> një send të rastësishëm në listë.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Fshin dhe kthen sendin në pozicionin e specifikuar në listë.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "<PERSON>shin dhe kthen sendin e parë në listë.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Fshin dhe kthen sendin e fundit në listë.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "<PERSON>shin dhe kthen një send të rastësishëm në listë.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Fshin sendin në pozicionin e specifikuar në listë.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Fshin sendin e parë në listë.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Fshin sendin e fundit në listë.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "<PERSON><PERSON><PERSON> një send të rastësishëm në listë.", "LISTS_SET_INDEX_SET": "vendos", "LISTS_SET_INDEX_INSERT": "fut në", "LISTS_SET_INDEX_INPUT_TO": "siku<PERSON>", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Vendos sendin në pozicionin e specifikuar në listë.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Vendos sendin e parë në listë.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Vendos sendin e fundit në listë.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Vendos një send të rastësishëm në listë.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Fut sendin në pozicionin e specifikuar të listës.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Fut sendin në fillim të listës.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Bashkangjit sendin në fund të listës.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Fut sendin rastësisht në listë.", "LISTS_GET_SUBLIST_START_FROM_START": "merr nën-listën nga #", "LISTS_GET_SUBLIST_START_FROM_END": "merr nën listën nga # nga fundi", "LISTS_GET_SUBLIST_START_FIRST": "merr nën-listën nga i pari", "LISTS_GET_SUBLIST_END_FROM_START": "tek #", "LISTS_GET_SUBLIST_END_FROM_END": "tek # nga fundi", "LISTS_GET_SUBLIST_END_LAST": "tek i fundit", "LISTS_GET_SUBLIST_TOOLTIP": "Krijon në kopje të pjesës së specifikuar të listës.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "rendit %1 %2 %3", "LISTS_SORT_TOOLTIP": "Rendit një kopje të listës.", "LISTS_SORT_ORDER_ASCENDING": "ngjitje", "LISTS_SORT_ORDER_DESCENDING": "zbritje", "LISTS_SORT_TYPE_NUMERIC": "numerike", "LISTS_SORT_TYPE_TEXT": "alfabetike", "LISTS_SORT_TYPE_IGNORECASE": "alfabetike, injoro madhësinë e shkronjave", "LISTS_SPLIT_LIST_FROM_TEXT": "b<PERSON><PERSON> <PERSON> nga teksti", "LISTS_SPLIT_TEXT_FROM_LIST": "b<PERSON><PERSON> tekst nga lista", "LISTS_SPLIT_WITH_DELIMITER": "me ndar<PERSON>s", "LISTS_SPLIT_TOOLTIP_SPLIT": "<PERSON><PERSON><PERSON> tekstin në një listë te<PERSON>, duke ndar<PERSON> në secilin ndarë<PERSON>.", "LISTS_SPLIT_TOOLTIP_JOIN": "<PERSON>jit një listë tekstesh në një, të ndara me ndarës.", "LISTS_REVERSE_MESSAGE0": "kthe %1", "LISTS_REVERSE_TOOLTIP": "Ndërro renditjen e një kopjeje të listës.", "VARIABLES_GET_TOOLTIP": "Pergjigjet me nje vlere te kesaj variable.", "VARIABLES_GET_CREATE_SET": "Krijo 'vendos %1", "VARIABLES_SET": "vendos %1 ne %2", "VARIABLES_SET_TOOLTIP": "Vendos kete variable te jete e barabarte me te dhenat ne hyrje.", "VARIABLES_SET_CREATE_GET": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> %1", "PROCEDURES_DEFNORETURN_HELPURL": "http://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFNORETURN_TITLE": "te", "PROCEDURES_DEFNORETURN_PROCEDURE": "b<PERSON><PERSON>", "PROCEDURES_BEFORE_PARAMS": "me:", "PROCEDURES_CALL_BEFORE_PARAMS": "me:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Krijon një funksion pa dalje.", "PROCEDURES_DEFNORETURN_COMMENT": "Përshkruaj këtë funksion...", "PROCEDURES_DEFRETURN_HELPURL": "http://en.wikipedia.org/wiki/Procedure_%28computer_science%29", "PROCEDURES_DEFRETURN_RETURN": "rikthe", "PROCEDURES_DEFRETURN_TOOLTIP": "Krijon një funksion me një dalje.", "PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON>", "PROCEDURES_DEF_DUPLICATE_WARNING": "Paralajmërim: K<PERSON> funksion ka parametra të dyfishuar.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Lësho funksionin e definuar nga përdoruesi '%1'.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Lëho funksionin e definuar nga përdoruesi '%1' dhe përdor daljen e tij.", "PROCEDURES_MUTATORCONTAINER_TITLE": "Informacioni i futur", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, hiq, ose rirendit inputet e këtij funksioni.", "PROCEDURES_MUTATORARG_TITLE": "<PERSON><PERSON> emrin:", "PROCEDURES_MUTATORARG_TOOLTIP": "Shto një input në këtë funksion.", "PROCEDURES_HIGHLIGHT_DEF": "Thekso definicionin e funksionit", "PROCEDURES_CREATE_DO": "<PERSON><PERSON>jo '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Nëse një vlerë është e saktë, atëherë kthe një vlerë të dytë.", "PROCEDURES_IFRETURN_WARNING": "Paralajmërim: <PERSON><PERSON> bllok mund të përdoret vetëm brenda definicionit të funksionit.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "<PERSON><PERSON><PERSON> dicka...", "WORKSPACE_ARIA_LABEL": "Hapësira e punës e <PERSON>ly", "COLLAPSED_WARNINGS_WARNING": "Blloqet e shembura përmbajnë paralajmërime.", "DIALOG_OK": "<PERSON><PERSON>ll", "DIALOG_CANCEL": "<PERSON><PERSON>"}