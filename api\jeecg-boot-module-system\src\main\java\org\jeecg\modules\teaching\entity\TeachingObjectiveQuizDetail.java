package org.jeecg.modules.teaching.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 客观题答题详情
 */
@Data
@TableName("teaching_objective_quiz_detail")
public class TeachingObjectiveQuizDetail implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    @Excel(name = "答题记录ID", width = 15)
    private String recordId;
    
    @Excel(name = "题目ID", width = 15)
    private String questionId;
    
    @Excel(name = "题目内容", width = 30)
    private String questionContent;
    
    @Excel(name = "题目类型", width = 15)
    private Integer questionType;
    
    @Excel(name = "用户答案", width = 15)
    private String userAnswer;
    
    @Excel(name = "正确答案", width = 15)
    private String correctAnswer;
    
    @Excel(name = "是否正确", width = 15)
    private Integer isCorrect;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 