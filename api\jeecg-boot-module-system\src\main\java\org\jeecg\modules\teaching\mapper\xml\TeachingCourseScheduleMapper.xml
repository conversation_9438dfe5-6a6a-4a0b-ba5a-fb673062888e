<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.teaching.mapper.TeachingCourseScheduleMapper">

    <select id="getScheduleList" resultType="org.jeecg.modules.teaching.vo.CourseScheduleVO">
        SELECT 
            s.*,
            d.depart_name as deptName,
            c.course_name as courseName,
            u.realname as teacherName,
            r.classroom_name as roomName
        FROM teaching_course_schedule s
        LEFT JOIN sys_depart d ON s.class_id = d.id
        LEFT JOIN teaching_course c ON s.course_id = c.id
        LEFT JOIN sys_user u ON s.teacher_id = u.id
        LEFT JOIN teaching_classroom r ON s.classroom_id = r.id
        ${ew.customSqlSegment}
    </select>
    
    <select id="checkConflict" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM teaching_course_schedule
        WHERE 
            (
                <if test="schedule.id != null and schedule.id != ''">
                    id != #{schedule.id} AND
                </if>
                (
                    (class_id = #{schedule.classId} OR teacher_id = #{schedule.teacherId} OR classroom_id = #{schedule.classroomId})
                    AND
                    (
                        (start_time &lt;= #{schedule.startTime} AND end_time > #{schedule.startTime})
                        OR
                        (start_time &lt; #{schedule.endTime} AND end_time >= #{schedule.endTime})
                        OR
                        (start_time >= #{schedule.startTime} AND end_time &lt;= #{schedule.endTime})
                    )
                    <if test="schedule.repeatType != null and schedule.repeatType == 2 and schedule.weekdays != null">
                        AND (
                            repeat_type != 2 
                            OR 
                            (repeat_type = 2 AND 
                             (
                                <foreach item="day" collection="schedule.weekdays.split(',')" separator=" OR ">
                                    week_days LIKE CONCAT('%', #{day}, '%')
                                </foreach>
                             )
                            )
                        )
                    </if>
                )
            )
    </select>
    
    <select id="checkClassConflict" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM teaching_course_schedule
        WHERE 
            (
                <if test="schedule.id != null and schedule.id != ''">
                    id != #{schedule.id} AND
                </if>
                (
                    class_id = #{schedule.classId}
                    AND
                    (
                        (start_time &lt;= #{schedule.startTime} AND end_time > #{schedule.startTime})
                        OR
                        (start_time &lt; #{schedule.endTime} AND end_time >= #{schedule.endTime})
                        OR
                        (start_time >= #{schedule.startTime} AND end_time &lt;= #{schedule.endTime})
                    )
                    <if test="schedule.repeatType != null and schedule.repeatType == 2 and schedule.weekdays != null">
                        AND (
                            repeat_type != 2 
                            OR 
                            (repeat_type = 2 AND 
                             (
                                <foreach item="day" collection="schedule.weekdays.split(',')" separator=" OR ">
                                    week_days LIKE CONCAT('%', #{day}, '%')
                                </foreach>
                             )
                            )
                        )
                    </if>
                )
            )
    </select>
    
    <select id="checkTeacherConflict" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM teaching_course_schedule
        WHERE 
            (
                <if test="schedule.id != null and schedule.id != ''">
                    id != #{schedule.id} AND
                </if>
                (
                    teacher_id = #{schedule.teacherId}
                    AND
                    (
                        (start_time &lt;= #{schedule.startTime} AND end_time > #{schedule.startTime})
                        OR
                        (start_time &lt; #{schedule.endTime} AND end_time >= #{schedule.endTime})
                        OR
                        (start_time >= #{schedule.startTime} AND end_time &lt;= #{schedule.endTime})
                    )
                    <if test="schedule.repeatType != null and schedule.repeatType == 2 and schedule.weekdays != null">
                        AND (
                            repeat_type != 2 
                            OR 
                            (repeat_type = 2 AND 
                             (
                                <foreach item="day" collection="schedule.weekdays.split(',')" separator=" OR ">
                                    week_days LIKE CONCAT('%', #{day}, '%')
                                </foreach>
                             )
                            )
                        )
                    </if>
                )
            )
    </select>
    
    <select id="checkRoomConflict" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM teaching_course_schedule
        WHERE 
            (
                <if test="schedule.id != null and schedule.id != ''">
                    id != #{schedule.id} AND
                </if>
                (
                    classroom_id = #{schedule.classroomId}
                    AND
                    (
                        (start_time &lt;= #{schedule.startTime} AND end_time > #{schedule.startTime})
                        OR
                        (start_time &lt; #{schedule.endTime} AND end_time >= #{schedule.endTime})
                        OR
                        (start_time >= #{schedule.startTime} AND end_time &lt;= #{schedule.endTime})
                    )
                    <if test="schedule.repeatType != null and schedule.repeatType == 2 and schedule.weekdays != null">
                        AND (
                            repeat_type != 2 
                            OR 
                            (repeat_type = 2 AND 
                             (
                                <foreach item="day" collection="schedule.weekdays.split(',')" separator=" OR ">
                                    week_days LIKE CONCAT('%', #{day}, '%')
                                </foreach>
                             )
                            )
                        )
                    </if>
                )
            )
    </select>
</mapper> 