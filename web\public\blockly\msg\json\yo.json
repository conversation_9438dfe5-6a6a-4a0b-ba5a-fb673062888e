{"@metadata": {"authors": ["Mapmeld"]}, "VARIABLES_DEFAULT_NAME": "nkan", "TODAY": "<PERSON><PERSON>", "DUPLICATE_BLOCK": "Ṣe ẹẹda", "ADD_COMMENT": "Ṣe afikun ọrọ iwoye", "REMOVE_COMMENT": "Yọ afikun ọr<PERSON> i<PERSON>ye", "DUPLICATE_COMMENT": "Ṣe ẹẹda afikun ọrọ iwoye", "EXTERNAL_INPUTS": "<PERSON><PERSON> a<PERSON>n ok<PERSON>e", "INLINE_INPUTS": "Afiku tẹle n tẹle", "DELETE_BLOCK": "<PERSON>a <PERSON>ul<PERSON> rẹ", "DELETE_X_BLOCKS": "Paa %1 awọn Bulọọku rẹ", "DELETE_ALL_BLOCKS": "Paa gbogbo %1 bulọọku rẹ?", "CLEAN_UP": "Nu Bulọọku kuro", "COLLAPSE_BLOCK": "Bi Bulọọku <PERSON>", "COLLAPSE_ALL": "<PERSON>i awọn Bulọọ<PERSON>", "EXPAND_BLOCK": "Fẹ Bulọọku", "EXPAND_ALL": "Fẹ awọn Bulọọ<PERSON>", "DISABLE_BLOCK": "Sọ Bulọọku di alaiṣiṣẹ", "ENABLE_BLOCK": "<PERSON><PERSON>ulọ<PERSON> ṣiṣẹ", "HELP": "Iranwọ", "UNDO": "<PERSON><PERSON> <PERSON>e", "REDO": "<PERSON><PERSON><PERSON>", "CHANGE_VALUE_TITLE": "Ṣe ayipada iye:", "RENAME_VARIABLE": "Tun or<PERSON>ọ oniruuru kọ...", "RENAME_VARIABLE_TITLE": "Tun orukọ gbogbo '%1' v oniruru kọ si:", "NEW_VARIABLE": "Ṣe idasile onir<PERSON>...", "NEW_STRING_VARIABLE": "Ṣe idasile asop<PERSON> oniruru...", "NEW_NUMBER_VARIABLE": "Ṣe idasile n<PERSON>́mb<PERSON> oniruru...", "NEW_COLOUR_VARIABLE": "Ṣe idasile awọ oniruuru...", "NEW_VARIABLE_TYPE_TITLE": "<PERSON><PERSON> onir<PERSON> tuntun:", "NEW_VARIABLE_TITLE": "Orukọ oniruuru tuntun:", "VARIABLE_ALREADY_EXISTS": "Onir<PERSON>uru ti a darukọ '%1' ti wa tẹlẹtẹlẹ.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Oniruuru ti a darukọ '%1' ti wa tẹlẹtẹlẹ fun iru miran: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Paa %1 lilo '%2' oniruuru rẹ?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "E ko lee paa Oniruuru rẹ ' %1' nitori wipe o je ara itumọ isise eto yi '%2'", "DELETE_VARIABLE": "Paa awon '%1' <PERSON><PERSON><PERSON><PERSON> rẹ", "COLOUR_PICKER_HELPURL": "https://en.wikipedia.org/wiki/Color", "COLOUR_PICKER_TOOLTIP": "Yan awọ kan lati inu patako awọ.", "COLOUR_RANDOM_TITLE": "aw<PERSON>", "COLOUR_RANDOM_TOOLTIP": "Yan awọ kan ni ọna <PERSON>.", "COLOUR_RGB_TITLE": "awọ pelu:", "COLOUR_RGB_RED": "awọ pupu", "COLOUR_RGB_GREEN": "alawọ ewe", "COLOUR_RGB_BLUE": "alaw<PERSON> omi aro", "COLOUR_RGB_TOOLTIP": "Ṣe idasile awọ kan pelu iye awọ pupu, al<PERSON><PERSON> ewe, ati alawọ omi aro. Gbogbo iye re gbọdọ je laarin 0 and 100.", "COLOUR_BLEND_TITLE": "apapọ", "COLOUR_BLEND_COLOUR1": "awọ 1", "COLOUR_BLEND_COLOUR2": "awọ 2", "COLOUR_BLEND_RATIO": "ipin", "COLOUR_BLEND_TOOLTIP": "Da awo meji papo pelu ipin (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://en.wikipedia.org/wiki/For_loop", "CONTROLS_REPEAT_TITLE": "Iye igba %1 ti tun ṣe", "CONTROLS_REPEAT_INPUT_DO": "ṣe", "CONTROLS_REPEAT_TOOLTIP": "Ṣe awon alaye ni igba pupo.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "tun ṣe nigbati", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "tun ṣe titi ti", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Nigbati iye kan ba je otito, tun awon koodu kan ṣe.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "<PERSON><PERSON><PERSON><PERSON> awon iye kan ba iro, tun awon koodu kan ṣe.", "CONTROLS_FOR_TOOLTIP": "Ni awon oriṣiriṣi '%1' ṣe imulo lori iye kọọkan lati ori nọmba tio beere titi de eyin to pari nọmba, kaa ni pase aarin kan pato. Tun koodu yi se nigba kọọkan:", "CONTROLS_FOR_TITLE": "iyipada %1 lati %2 si %3 fifi kun %4", "CONTROLS_FOREACH_TITLE": "fun nigba kọọkan %1 ni akojọ %2", "CONTROLS_FOREACH_TOOLTIP": "Fun nkan kọọkan ninu a<PERSON> kan, ṣe eto oriṢiriṢi '%1' si nkan naa, ki o si tun koodu naa ṣe.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "ya kuro ninu lupu", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "beere pelu aṣiṣe lupu", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Ya kuro ninu akojọ lupu.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Ṣe afoda awon lupu to ku yii, kii o si tesiwaju pelu awon aṣiṣe lupu.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Ikilo: <PERSON><PERSON><PERSON><PERSON><PERSON> yii se lo ninu aṣiṣe lupu yii nikan.", "CONTROLS_IF_TOOLTIP_1": "Bi iye yii ba je otito, lẹyinna ṣe awọn alaye.", "CONTROLS_IF_TOOLTIP_2": "Bi iye yii ba je o<PERSON>to, lẹyinna ṣe alaye bulọọku akọkọ. Bibẹẹkọ, ṣe alaye akọkọ bulọọku keji.", "CONTROLS_IF_TOOLTIP_3": "Bi iye akọkọ yii ba je otito, lẹyinna ṣe alaye bulọọku akọkọ. Bibẹẹkọ, Bi iye keji yii ba je otito, ṣe alaye akọkọ bulọọku keji.", "CONTROLS_IF_TOOLTIP_4": "Bi iye akọkọ yii ba je otito, lẹyinna ṣe alaye bulọọku akọkọ. Bi iye keji yii ba je otito, ṣe alaye akọkọ bulọọku keji. Bi eyikeyi iye naa ko ba je otito, ṣe alaye akọkọ bulọọku ti o gbeyin.", "CONTROLS_IF_MSG_IF": "bi", "CONTROLS_IF_MSG_ELSEIF": "bibẹẹkọ bi", "CONTROLS_IF_MSG_ELSE": "bibẹẹkọ", "CONTROLS_IF_IF_TOOLTIP": "Ṣe a<PERSON>kun, se a<PERSON><PERSON><PERSON><PERSON>, tabi se a tun beere abala yii lati se a tun gbejade bulọọku yii.", "CONTROLS_IF_ELSEIF_TOOLTIP": "Ṣe afikun si ipo yii bi bulọọku.", "CONTROLS_IF_ELSE_TOOLTIP": "Ṣe afikun i<PERSON>, mu-gbogbo ipo si bulọọku.", "IOS_OK": "O DARA", "IOS_CANCEL": "F<PERSON><PERSON>", "IOS_ERROR": "<PERSON>ṣ<PERSON><PERSON><PERSON>", "IOS_PROCEDURES_INPUTS": "SII", "IOS_PROCEDURES_ADD_INPUT": "+ Fi afikun sii", "IOS_PROCEDURES_ALLOW_STATEMENTS": "<PERSON><PERSON> koodu laaye", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "<PERSON><PERSON> a<PERSON>n ok<PERSON>e", "IOS_VARIABLES_ADD_VARIABLE": "+ Ṣe afikun or<PERSON>risi", "IOS_VARIABLES_ADD_BUTTON": "Ìròpọ̀", "IOS_VARIABLES_RENAME_BUTTON": "À<PERSON>únṣ<PERSON><PERSON>́<PERSON>", "IOS_VARIABLES_DELETE_BUTTON": "Paarẹ", "IOS_VARIABLES_VARIABLE_NAME": "<PERSON><PERSON><PERSON>", "IOS_VARIABLES_EMPTY_NAME_ERROR": "O ko le lo oruko orisirisi ti o sofo.", "LOGIC_COMPARE_HELPURL": "https://en.wikipedia.org/wiki/Inequality_(mathematics)", "LOGIC_COMPARE_TOOLTIP_EQ": "<PERSON> otito pada b <PERSON><PERSON><PERSON><PERSON> mejeji ba dogba bakanna.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Da otito pada bi afikun mejeji ko ba dogba bakanna.", "LOGIC_COMPARE_TOOLTIP_LT": "Da otito pada bi afikun akooko ba kere ju afiku keji lo.", "LOGIC_COMPARE_TOOLTIP_LTE": "Da otito pada bi afikun akooko ba kere ju tabi dogba pelu afiku keji lo.", "LOGIC_COMPARE_TOOLTIP_GT": "Da otito pada bi afikun akooko ba tobi ju afiku keji lo.", "LOGIC_COMPARE_TOOLTIP_GTE": "Da otito pada bi afikun akooko ba tobi ju tabi dogba pelu afiku keji lo.", "LOGIC_OPERATION_TOOLTIP_AND": "Da otito pada bi afikun mejeji ba je otito.", "LOGIC_OPERATION_AND": "ati", "LOGIC_OPERATION_TOOLTIP_OR": "Da otitọ pada bi o kere ju afikun kan ba je otito.", "LOGIC_OPERATION_OR": "tabi", "LOGIC_NEGATE_TITLE": "kii ṣe %1", "LOGIC_NEGATE_TOOLTIP": "Da otitọ pada bi afikun ba je irọ. Da iro pada bi afikun ba je otito.", "LOGIC_BOOLEAN_TRUE": "otitọ", "LOGIC_BOOLEAN_FALSE": "ir<PERSON>", "LOGIC_BOOLEAN_TOOLTIP": "Da pada bi o je otito tabi iro.", "LOGIC_NULL": "ofo", "LOGIC_NULL_TOOLTIP": "Da ofo pada.", "LOGIC_TERNARY_CONDITION": "idan<PERSON>", "LOGIC_TERNARY_IF_TRUE": "bi otitọ", "LOGIC_TERNARY_IF_FALSE": "bi irọ", "LOGIC_TERNARY_TOOLTIP": "Ṣe ayewo ipo naa ni 'idanwo'. Bi ipo nab a je otito, Da pada 'bi otito' iye; bibẹẹkọ da pada  'bi iro' iye.", "MATH_NUMBER_HELPURL": "https://yo.wikipedia.org/wiki/Nọ́mbà", "MATH_NUMBER_TOOLTIP": "Nọ́mbà kan.", "MATH_ARITHMETIC_HELPURL": "https://yo.wikipedia.org/wiki/Ìṣírò", "MATH_ARITHMETIC_TOOLTIP_ADD": "<PERSON> apapo awọn nọmba meji pada.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Da iyatọ awọn nọmba meji naa pada.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "<PERSON> abajade awọn nọmba meji naa pada.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Da adarọ iye ti awọn nọmba meji pada.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Da nọmba akọkọ ti a gbe si agbara ti nọmba keji pada.", "MATH_SINGLE_HELPURL": "https://yo.wikipedia.org/wiki/Gb%C3%B2ngb%C3%B2_al%C3%A1gb%C3%A1ram%C3%A9j%C3%AC", "MATH_SINGLE_OP_ROOT": "Ipilẹ onihamẹrin", "MATH_SINGLE_TOOLTIP_ROOT": "Da Ipilẹ onihamẹrin nọmba kan pada.", "MATH_SINGLE_OP_ABSOLUTE": "<PERSON><PERSON><PERSON>", "MATH_SINGLE_TOOLTIP_ABS": "Da iye patapata ti nọmba kan pada.", "MATH_SINGLE_TOOLTIP_NEG": "Da ilodisi ti nọmba kan pada", "MATH_SINGLE_TOOLTIP_LN": "Da lọgaridimu adayeba ti nọmba kan pada.", "MATH_SINGLE_TOOLTIP_LOG10": "Da ipilẹ 10 lọ<PERSON>id<PERSON>u nọmba kan pada.", "MATH_SINGLE_TOOLTIP_EXP": "Da e pada si agbara ti nọmba kan.", "MATH_SINGLE_TOOLTIP_POW10": "Da 10 pada si agbara n<PERSON>mba kan.", "MATH_TRIG_HELPURL": "https://en.wikipedia.org/wiki/Trigonometric_functions", "MATH_TRIG_TOOLTIP_SIN": "Da sine ti digiri pada (kii <PERSON>e Radian).", "MATH_TRIG_TOOLTIP_COS": "Da cosine ti digiri pada (kii ṣe Radian).", "MATH_TRIG_TOOLTIP_TAN": "Da tangent ti digiri pada (kii <PERSON>e Radian).", "MATH_TRIG_TOOLTIP_ASIN": "Da arcsine ti digiri pada.", "MATH_TRIG_TOOLTIP_ACOS": "Da arccosine ti digiri pada.", "MATH_TRIG_TOOLTIP_ATAN": "Da arctangent ti digiri pada.", "MATH_CONSTANT_HELPURL": "https://en.wikipedia.org/wiki/Mathematical_constant", "MATH_CONSTANT_TOOLTIP": "<PERSON> ọkan ninu awọn aiyipada ti o wọpọ pada: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (ailopin).", "MATH_IS_EVEN": "je se e pin", "MATH_IS_ODD": "je ai se e pin", "MATH_IS_PRIME": "je nọ́<PERSON><PERSON> àkọ́kọ́", "MATH_IS_WHOLE": "je odidi", "MATH_IS_POSITIVE": "je di dara", "MATH_IS_NEGATIVE": "je ai dara", "MATH_IS_DIVISIBLE_BY": "je sisee pin pẹlu", "MATH_IS_TOOLTIP": "Ṣe ayẹwo boya nọmba jẹ eyi to se pin, ai se pin, a<PERSON><PERSON>, odi<PERSON>, ti o dara, ti ko dara, tabi ti o ba se e pin pelu nọmba kan. Pada otitọ tabi irọ.", "MATH_CHANGE_HELPURL": "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter", "MATH_CHANGE_TITLE": "iyipada %1 nipasẹ %2", "MATH_CHANGE_TOOLTIP": "Se afiku si nọmba orisirisi '%1'.", "MATH_ROUND_HELPURL": "https://en.wikipedia.org/wiki/Rounding", "MATH_ROUND_TOOLTIP": "Pa oju nọmba de soke tabi si isalẹ.", "MATH_ROUND_OPERATOR_ROUND": "pa ju de", "MATH_ROUND_OPERATOR_ROUNDUP": "pa ju de soke", "MATH_ROUND_OPERATOR_ROUNDDOWN": "pa ju de si isalẹ", "MATH_ONLIST_OPERATOR_SUM": "apao a<PERSON>", "MATH_ONLIST_TOOLTIP_SUM": "Da apapo gbogbo awọn nọmba inu akojọ pada.", "MATH_ONLIST_OPERATOR_MIN": "akoj<PERSON> ti o kere ju", "MATH_ONLIST_TOOLTIP_MIN": "Da nọmba ti o kere julọ ninu akojọ pada.", "MATH_ONLIST_OPERATOR_MAX": "akojọ ti o pọ ju", "MATH_ONLIST_TOOLTIP_MAX": "Da nọmba ti o tobi julọ ninu akojọ pada.", "MATH_ONLIST_OPERATOR_AVERAGE": "<PERSON><PERSON><PERSON>", "MATH_ONLIST_TOOLTIP_AVERAGE": "Da idameji pada (<PERSON><PERSON> isiro) ti awọn nọmba iye inu akojọ.", "MATH_ONLIST_OPERATOR_MEDIAN": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "MATH_ONLIST_TOOLTIP_MEDIAN": "<PERSON> agbedemeji nọmba inu akojọ pada.", "MATH_ONLIST_OPERATOR_MODE": "awọn ipo ti akoj<PERSON>", "MATH_ONLIST_TOOLTIP_MODE": "Da akojọ ti eyi ti o wọpọ julọ ninu akojọ.", "MATH_ONLIST_OPERATOR_STD_DEV": "iṣiro deede ti akoj<PERSON>", "MATH_ONLIST_TOOLTIP_STD_DEV": "Da iṣiro deede ti akojọ pada.", "MATH_ONLIST_OPERATOR_RANDOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nkan ti akoj<PERSON>", "MATH_ONLIST_TOOLTIP_RANDOM": "Da àrìnàkò ida ipilẹ nkan lati inu akojọ.", "MATH_MODULO_HELPURL": "https://en.wikipedia.org/wiki/Modulo_operation", "MATH_MODULO_TITLE": "iyokù %1 ÷ %2", "MATH_MODULO_TOOLTIP": "Da iyokù lati pinpin awọn nọmba meji pada.", "MATH_CONSTRAIN_TITLE": "atokọ %1 kukuru %2 giga %3", "MATH_CONSTRAIN_TOOLTIP": "Ṣe atokọ nọmba laarin awọn nọmba kukuru ati giga. (ini afikun).", "MATH_RANDOM_INT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_INT_TITLE": "oniruru abala lati %1 si %2", "MATH_RANDOM_INT_TOOLTIP": "Da àrìn<PERSON><PERSON><PERSON> abala laarin awon opin pato meji pada, ini afikun.", "MATH_RANDOM_FLOAT_HELPURL": "https://en.wikipedia.org/wiki/Random_number_generation", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "on<PERSON>uru ipin", "MATH_RANDOM_FLOAT_TOOLTIP": "Da àrìnàkò ida pada laarin 0.0 (ini afikun) ati 1.0 (iyasọtọ).", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Da ojuami arctangent pada (X, Y) ni awon digiri lati -180 si 180.", "TEXT_TEXT_HELPURL": "https://en.wikipedia.org/wiki/String_(computer_science)", "TEXT_TEXT_TOOLTIP": "Lẹta, ọr<PERSON>, tabi ila ọrọ.", "TEXT_JOIN_TITLE_CREATEWITH": "ṣẹ ẹda ọrọ pẹlu", "TEXT_JOIN_TOOLTIP": "Ṣẹda ọrọ kan nipa ṣiṣepọ gbogbo awọn ohun kan.", "TEXT_CREATE_JOIN_TITLE_JOIN": "darapọ", "TEXT_CREATE_JOIN_TOOLTIP": "<PERSON><PERSON><PERSON>, y<PERSON> kuro, tabi ṣe atunṣe awọn apakan lati se atunkọ ọrọ bulooku yii.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Ṣe afikun nkan si ọrọ naa.", "TEXT_APPEND_TITLE": "si %1 fikun ọr<PERSON> %2", "TEXT_APPEND_TOOLTIP": "Ṣe afikun awon ọrọ oniruru '%1'.", "TEXT_LENGTH_TITLE": "Gigun ti %1", "TEXT_LENGTH_TOOLTIP": "Da nọmba awọn lẹta pada (pẹlu awọn alafo) ninu ọrọ ti a pese.", "TEXT_ISEMPTY_TITLE": "%1 je isofo", "TEXT_ISEMPTY_TOOLTIP": "Da otitọ pada ti ọrọ ti a pese ba ṣofo.", "TEXT_INDEXOF_TOOLTIP": "Da atọka ti akọkọ / iṣẹlẹ to kẹhin ti akojọ. Da %1 akojọ pada ti o ko ba ri.", "TEXT_INDEXOF_TITLE": "ninu %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "wa isele ak<PERSON> ti o wa ninu ọr<PERSON>", "TEXT_INDEXOF_OPERATOR_LAST": "wa isele igbeyin ti o wa ninu <PERSON>r<PERSON>", "TEXT_CHARAT_TITLE": "ninu %1 %2", "TEXT_CHARAT_FROM_START": "gba lẹta #", "TEXT_CHARAT_FROM_END": "gba lẹta # lati opin", "TEXT_CHARAT_FIRST": "gba lẹta akọkọ", "TEXT_CHARAT_LAST": "gba lẹta ti o kẹhin", "TEXT_CHARAT_RANDOM": "gba lẹta àrìnàkò", "TEXT_CHARAT_TOOLTIP": "Da lẹta naa pada si ipo ti a ti sọ tẹlẹ.", "TEXT_GET_SUBSTRING_TOOLTIP": "Da ipin kan pato ti ọrọ naa pada.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ninu", "TEXT_GET_SUBSTRING_START_FROM_START": "gba substring lati lẹta #", "TEXT_GET_SUBSTRING_START_FROM_END": "gba substring lati lẹta # lati opin", "TEXT_GET_SUBSTRING_START_FIRST": "gba substring lati lẹta akọkọ", "TEXT_GET_SUBSTRING_END_FROM_START": "si lẹta #", "TEXT_GET_SUBSTRING_END_FROM_END": "si lẹta # lati opin", "TEXT_GET_SUBSTRING_END_LAST": "si lẹta kẹhin", "TEXT_CHANGECASE_TOOLTIP": "aa <-> AA", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "si ALFABETI NLA", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "si alfabeti kekere", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "<PERSON> <PERSON><PERSON>", "TEXT_TRIM_TOOLTIP": "Da ẹda ti ọrọ naa pada pẹlu awọn alafo miiran ti o kuro lati ọkan tabi awọn opin mejeeji.", "TEXT_TRIM_OPERATOR_BOTH": "ge awọn alafo lati awọn igun mejeji ti", "TEXT_TRIM_OPERATOR_LEFT": "ge awọn alafo lati apa osi ti", "TEXT_TRIM_OPERATOR_RIGHT": "ge awọn alafo lati apa otun ti", "TEXT_PRINT_TITLE": "tẹ ọrọ %1", "TEXT_PRINT_TOOLTIP": "Tẹ <PERSON>r<PERSON> kan pato, n<PERSON><PERSON> tabi iye awon miiran.", "TEXT_PROMPT_TYPE_TEXT": "kiakia fun ọrọ pẹlu ifiranṣẹ", "TEXT_PROMPT_TYPE_NUMBER": "kiakia fun nọmba pẹlu ifiranṣẹ", "TEXT_PROMPT_TOOLTIP_NUMBER": "<PERSON><PERSON><PERSON> fun olumulo fun nọmba.", "TEXT_PROMPT_TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> fun olumulo fun awon ifiranṣẹ.", "TEXT_COUNT_MESSAGE0": "ka %1 ni %2", "TEXT_COUNT_TOOLTIP": "Ka iye igba diẹ ninu awọn ọrọ kan waye laarin awọn ọrọ miiran.", "TEXT_REPLACE_MESSAGE0": "Ṣe iropọ %1 pelu %2 in %3", "TEXT_REPLACE_TOOLTIP": "Ṣe iropo gbogbo awọn iṣẹlẹ ti o sele ninu awọn ọrọ laarin awọn ọrọ miiran.", "TEXT_REVERSE_MESSAGE0": "Ṣe iyipada %1", "TEXT_REVERSE_TOOLTIP": "Ṣe iyipada aṣẹ awọn ohun kikọ inu ọrọ naa.", "LISTS_CREATE_EMPTY_TITLE": "Ṣẹda akojọ aṣayan tio <PERSON>ofo", "LISTS_CREATE_EMPTY_TOOLTIP": "<PERSON> akoj<PERSON> pada, ti gigun 0, ko ni awon akosile alaye", "LISTS_CREATE_WITH_TOOLTIP": "Ṣẹda akojọ pẹlu nọmba eyikeyi ti awọn akojo.", "LISTS_CREATE_WITH_INPUT_WITH": "ṣẹda akojọ", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "a<PERSON>jọ", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, y<PERSON>, tabi yọ, tunṣe awọn apakan lati akojọ bulooku yii.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Fi nkan kun akojọ.", "LISTS_REPEAT_TOOLTIP": "Ṣẹda akojọ kan ti o wa fun iye tun nọmba kan pato ti akoko ti a ti yan.", "LISTS_REPEAT_TITLE": "ṣẹda akojọ pẹlu nkan %1 tun ṣe %2 igba", "LISTS_LENGTH_TITLE": "gigun ti %1", "LISTS_LENGTH_TOOLTIP": "Da gigun ti akojo pada.", "LISTS_ISEMPTY_TITLE": "%1 je ofo", "LISTS_ISEMPTY_TOOLTIP": "Da otitọ pada nigbati akojọ ba ṣofo.", "LISTS_INLIST": "ni akojọ", "LISTS_INDEX_OF_FIRST": "wa awon nkan akọkọ ti o sele", "LISTS_INDEX_OF_LAST": "wa iṣẹlẹ ti o kẹhin ti akojọ", "LISTS_INDEX_OF_TOOLTIP": "Da atọka ti akọkọ / iṣẹlẹ to kẹhin ti akojọ. Da %1 akojọ pada ti o ko ba ri.", "LISTS_GET_INDEX_GET": "gba", "LISTS_GET_INDEX_GET_REMOVE": "gba ati yọ", "LISTS_GET_INDEX_REMOVE": "y<PERSON>", "LISTS_GET_INDEX_FROM_END": "# lati opin", "LISTS_GET_INDEX_FIRST": "àkọ́kọ́", "LISTS_GET_INDEX_LAST": "tógbẹ̀yìn", "LISTS_GET_INDEX_RANDOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 jẹ ohun a<PERSON>ọk<PERSON>.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 jẹ ohun ti o kẹhin.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Returns the item at the specified position in a list.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Da akojọ akọkọ pada.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Da akojọ ti o kẹhin pada.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "<PERSON> ohun àrìn<PERSON><PERSON> kan pada ninu akojọ", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Yọ ki o si da akojọ naa pada kuro ni ipo kan pato ti o wa.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Yọ ki o si da akojọ kuro ni akọkọ pada.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Yọ ki o si da akojọ ti o kẹhin pada", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Yọ ki o si da akojọ ti o kẹhin àrìnàkò pada", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Yọ ki o si da akojọ naa pada kuro ni ipo kan pato ti o wa.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Da akojọ akọkọ pada.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Da akojọ ti o kẹhin pada.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "<PERSON> ohun àrìn<PERSON><PERSON> kan pada ninu akojọ", "LISTS_SET_INDEX_SET": "<PERSON><PERSON>", "LISTS_SET_INDEX_INSERT": "fi sii ni", "LISTS_SET_INDEX_INPUT_TO": "bii", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Ṣeto ohun akọkọ sii ipo kan pato ti a ti yan ni akojọ.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Fi ohun kan sii ni ibẹrẹ akojọ.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Fi ohun kan kun si opin akoj<PERSON>.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Fi ohun kan kun si à<PERSON>ìn<PERSON> a<PERSON>.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Fi ohun kan sii ipo kan pato ti a ti yan ni akojọ.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Fi ohun kan sii ni ibẹrẹ akojọ.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Fi ohun kan kun si opin akoj<PERSON>.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Fi ohun kan kun si à<PERSON>ìn<PERSON> a<PERSON>.", "LISTS_GET_SUBLIST_START_FROM_START": "gba ipin -akoj<PERSON> lati #", "LISTS_GET_SUBLIST_START_FROM_END": "gba ipin -akojọ lati # lati opin", "LISTS_GET_SUBLIST_START_FIRST": "gba ipin -akojọ lati akọkọ", "LISTS_GET_SUBLIST_END_FROM_START": "sii #", "LISTS_GET_SUBLIST_END_FROM_END": "sii # lati opin", "LISTS_GET_SUBLIST_END_LAST": "sii opin", "LISTS_GET_SUBLIST_TOOLTIP": "Ṣẹda ẹda ti apa kan ti o wa ninu akoj<PERSON>.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "to %1 %2 %3", "LISTS_SORT_TOOLTIP": "To ẹda akojọ lẹsẹẹsẹ.", "LISTS_SORT_ORDER_ASCENDING": "si oke", "LISTS_SORT_ORDER_DESCENDING": "si isalẹ", "LISTS_SORT_TYPE_NUMERIC": "a<PERSON><PERSON><PERSON>", "LISTS_SORT_TYPE_TEXT": "a<PERSON>", "LISTS_SORT_TYPE_IGNORECASE": "a<PERSON>, fo<PERSON><PERSON>", "LISTS_SPLIT_LIST_FROM_TEXT": "ṣe akojọ lati inu ọrọ", "LISTS_SPLIT_TEXT_FROM_LIST": "ṣe ọr<PERSON> lati akoj<PERSON>", "LISTS_SPLIT_WITH_DELIMITER": "pẹlu dẹlimita", "LISTS_SPLIT_TOOLTIP_SPLIT": "Pin ọrọ sinu akojọ awọn ọrọ kan, fọ ni dẹlimita kọọkan.", "LISTS_SPLIT_TOOLTIP_JOIN": "Darapọ mọ akojọ awọn ọrọ sinu ọrọ kan, ti a pin nipase delimita.", "LISTS_REVERSE_MESSAGE0": "Ṣe iyipada %1", "LISTS_REVERSE_TOOLTIP": "Ṣe iyipada ẹda ti akojọ kan.", "VARIABLES_GET_TOOLTIP": "Da iye orisirisi yii pada.", "VARIABLES_GET_CREATE_SET": "Ṣe idasile 'ṣeto %1'", "VARIABLES_SET": "ṣeto %1 sii %2", "VARIABLES_SET_TOOLTIP": "Ṣeto oniruru yii lati je bakanna sii igbasilẹ.", "VARIABLES_SET_CREATE_GET": "Ṣe idasile 'gba %1'", "PROCEDURES_DEFNORETURN_TITLE": "sii", "PROCEDURES_DEFNORETURN_PROCEDURE": "Ṣe awon alaye ni igba pupo.", "PROCEDURES_BEFORE_PARAMS": "pẹlu:", "PROCEDURES_CALL_BEFORE_PARAMS": "pẹlu:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Ṣẹda iṣẹ kan lai si iṣagbe<PERSON>de.", "PROCEDURES_DEFNORETURN_COMMENT": "Ṣe apejuwe iṣẹ yii...", "PROCEDURES_DEFRETURN_RETURN": "tun tẹ", "PROCEDURES_DEFRETURN_TOOLTIP": "Ṣẹda iṣẹ pẹlu iṣagbejade kan.", "PROCEDURES_ALLOW_STATEMENTS": "gba alaye laaye", "PROCEDURES_DEF_DUPLICATE_WARNING": "Ikilo: Isẹ yii ni awọn ẹda odiwọn.", "PROCEDURES_CALLNORETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLNORETURN_TOOLTIP": "Ṣe ṣalaye-iṣẹ ti olumulo '%1'.", "PROCEDURES_CALLRETURN_HELPURL": "https://en.wikipedia.org/wiki/Subroutine", "PROCEDURES_CALLRETURN_TOOLTIP": "Ṣe ṣalaye-iṣẹ ti olumulo '%1' kii o sii lo iṣagbejade rẹ.", "PROCEDURES_MUTATORCONTAINER_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "<PERSON><PERSON><PERSON>, y<PERSON>, tabi tun beere awọn igbewọle si iṣẹ yii.", "PROCEDURES_MUTATORARG_TITLE": "igbew<PERSON><PERSON>:", "PROCEDURES_MUTATORARG_TOOLTIP": "Ṣe afikun kan sii igbewọle si iṣẹ yii.", "PROCEDURES_HIGHLIGHT_DEF": "Ṣafihan iṣẹ isọtunmọ", "PROCEDURES_CREATE_DO": "Ṣe idasile '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Ti iye ba jẹ otitọ, lẹhinna da iye keji pada.", "PROCEDURES_IFRETURN_WARNING": "Ikilo: <PERSON><PERSON><PERSON><PERSON><PERSON> yii le ṣee lo nikan laarin itumọ iṣẹ kan", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Sọ nkankan...", "DIALOG_OK": "O DARA", "DIALOG_CANCEL": "F<PERSON><PERSON>"}