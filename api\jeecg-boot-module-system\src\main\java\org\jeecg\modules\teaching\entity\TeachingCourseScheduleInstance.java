package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 单次课程实例
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
@Data
@TableName("teaching_course_schedule_instance")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="teaching_course_schedule_instance对象", description="单次课程实例")
public class TeachingCourseScheduleInstance implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    /** 父课程ID */
    @Excel(name = "父课程ID", width = 15)
    @ApiModelProperty(value = "父课程ID")
    private String parentId;
    
    /** 关联课程ID */
    @Excel(name = "关联课程ID", width = 15)
    @ApiModelProperty(value = "关联课程ID")
    private String courseId;
    
    /** 关联教室ID */
    @Excel(name = "关联教室ID", width = 15)
    @ApiModelProperty(value = "关联教室ID")
    private String classroomId;
    
    /** 关联教师ID */
    @Excel(name = "关联教师ID", width = 15)
    @ApiModelProperty(value = "关联教师ID")
    private String teacherId;
    
    /** 关联班级ID */
    @Excel(name = "关联班级ID", width = 15)
    @ApiModelProperty(value = "关联班级ID")
    private String classId;
    
    /** 排课标题 */
    @Excel(name = "排课标题", width = 15)
    @ApiModelProperty(value = "排课标题")
    private String scheduleTitle;
    
    /** 学生名单 */
    @Excel(name = "学生名单", width = 15)
    @ApiModelProperty(value = "学生名单")
    private String studentNames;
    
    /** 实例日期 */
    @Excel(name = "实例日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "实例日期")
    private Date instanceDate;
    
    /** 开始时间 */
    @Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    /** 结束时间 */
    @Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    /** 显示颜色 */
    @Excel(name = "显示颜色", width = 15)
    @ApiModelProperty(value = "显示颜色")
    private String color;
    
    /** 排课描述 */
    @Excel(name = "排课描述", width = 15)
    @ApiModelProperty(value = "排课描述")
    private String description;
    
    /** 状态：0-取消，1-正常 */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态：0-取消，1-正常")
    private Integer status;
    
    /** 提前多少分钟提醒 */
    @Excel(name = "提前多少分钟提醒", width = 15)
    @ApiModelProperty(value = "提前多少分钟提醒")
    private Integer notifyBeforeMinutes;
    
    /** 是否通知教师：0-不通知，1-通知 */
    @Excel(name = "是否通知教师", width = 15)
    @ApiModelProperty(value = "是否通知教师：0-不通知，1-通知")
    private Integer notifyTeachers;
    
    /** 是否通知学生：0-不通知，1-通知 */
    @Excel(name = "是否通知学生", width = 15)
    @ApiModelProperty(value = "是否通知学生：0-不通知，1-通知")
    private Integer notifyStudents;
    
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /** 是否已创建提醒通知：0-未创建，1-已创建 */
    @Excel(name = "是否已创建提醒通知", width = 15)
    @ApiModelProperty(value = "是否已创建提醒通知：0-未创建，1-已创建")
    private Integer reminderCreated;
} 