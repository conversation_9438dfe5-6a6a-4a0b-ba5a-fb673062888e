{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Facenapalm", "<PERSON><PERSON>", "MS", "Mailman", "Mouse21", "<PERSON><PERSON><PERSON>", "Okras", "<PERSON>", "RedFox", "Redredsonia", "<PERSON><PERSON><PERSON>", "SimondR", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Vlad5250"]}, "VARIABLES_DEFAULT_NAME": "элемент", "UNNAMED_KEY": "без названия", "TODAY": "Сегодня", "DUPLICATE_BLOCK": "Дублировать", "ADD_COMMENT": "Добавить комментарий", "REMOVE_COMMENT": "Удалить комментарий", "DUPLICATE_COMMENT": "Дублировать комментарий", "EXTERNAL_INPUTS": "Вставки снаружи", "INLINE_INPUTS": "Вставки внутри", "DELETE_BLOCK": "Удалить блок", "DELETE_X_BLOCKS": "Удалить %1 блоков", "DELETE_ALL_BLOCKS": "Удалить все блоки (%1)?", "CLEAN_UP": "Очистить блоки", "COLLAPSE_BLOCK": "Свернуть блок", "COLLAPSE_ALL": "Свернуть блоки", "EXPAND_BLOCK": "Развернуть блок", "EXPAND_ALL": "Развернуть блоки", "DISABLE_BLOCK": "Отключить блок", "ENABLE_BLOCK": "Включить блок", "HELP": "Справка", "UNDO": "Отменить", "REDO": "Повторить", "CHANGE_VALUE_TITLE": "Измените значение:", "RENAME_VARIABLE": "Переименовать переменную…", "RENAME_VARIABLE_TITLE": "Переименовать все переменные '%1' в:", "NEW_VARIABLE": "Создать переменную…", "NEW_STRING_VARIABLE": "Создать строковую переменную...", "NEW_NUMBER_VARIABLE": "Создать числовую переменную...", "NEW_COLOUR_VARIABLE": "Создать переменную цвета...", "NEW_VARIABLE_TYPE_TITLE": "Новый тип переменной:", "NEW_VARIABLE_TITLE": "Имя новой переменной:", "VARIABLE_ALREADY_EXISTS": "Переменная с именем '%1' уже существует.", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "Название переменной '%1' уже используется другой типа: '%2'.", "DELETE_VARIABLE_CONFIRMATION": "Удалить %1 использований переменной '%2'?", "CANNOT_DELETE_VARIABLE_PROCEDURE": "Невозможно удалить переменную '%1', поскольку она является частью определения функции '%2'", "DELETE_VARIABLE": "Удалить переменную '%1'", "COLOUR_PICKER_HELPURL": "https://ru.wikipedia.org/wiki/Цвет", "COLOUR_PICKER_TOOLTIP": "Выберите цвет из палитры.", "COLOUR_RANDOM_TITLE": "случайный цвет", "COLOUR_RANDOM_TOOLTIP": "Выбирает цвет случайным образом.", "COLOUR_RGB_TITLE": "цвет из", "COLOUR_RGB_RED": "красного", "COLOUR_RGB_GREEN": "зелёного", "COLOUR_RGB_BLUE": "синего", "COLOUR_RGB_TOOLTIP": "Создаёт цвет с указанной пропорцией красного, зелёного и синего. Все значения должны быть между 0 и 100.", "COLOUR_BLEND_TITLE": "смешать", "COLOUR_BLEND_COLOUR1": "цвет 1", "COLOUR_BLEND_COLOUR2": "цвет 2", "COLOUR_BLEND_RATIO": "доля цвета 1", "COLOUR_BLEND_TOOLTIP": "Смешивает два цвета в заданном соотношении (0.0 - 1.0).", "CONTROLS_REPEAT_HELPURL": "https://ru.wikipedia.org/wiki/Цикл_(программирование)", "CONTROLS_REPEAT_TITLE": "повторить %1 раз", "CONTROLS_REPEAT_INPUT_DO": "выполнить", "CONTROLS_REPEAT_TOOLTIP": "Выполняет некоторые команды несколько раз.", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "повторять, пока", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "повторять, пока не", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "Пока значение истинно, выполняет команды.", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "Пока значение ложно, выполняет команды", "CONTROLS_FOR_TOOLTIP": "Присваивает переменной '%1' значения от начального до конечного с заданным шагом и выполняет указанные команды.", "CONTROLS_FOR_TITLE": "цикл по %1 от %2 до %3 с шагом %4", "CONTROLS_FOREACH_TITLE": "для каждого элемента %1 в списке %2", "CONTROLS_FOREACH_TOOLTIP": "Для каждого элемента в списке, присваивает переменной '%1' значение элемента  и выполняет указанные  команды.", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "выйти из цикла", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "перейти к следующему шагу цикла", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "Прерывает этот цикл.", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "Пропускает остаток цикла и переходит к следующему шагу.", "CONTROLS_FLOW_STATEMENTS_WARNING": "Предупреждение: этот блок может использоваться только внутри цикла.", "CONTROLS_IF_TOOLTIP_1": "Если условие истинно, выполняет команды.", "CONTROLS_IF_TOOLTIP_2": "Если условие истинно, выполняет первый блок команд. Иначе выполняется второй блок команд.", "CONTROLS_IF_TOOLTIP_3": "Если первое условие истинно, то выполняет первый блок команд. Иначе, если второе условие истинно, выполняет второй блок команд.", "CONTROLS_IF_TOOLTIP_4": "Если первое условие истинно, то выполняет первый блок команд.  В противном случае, если второе условие истинно, выполняет второй блок команд.  Если ни одно из условий не истинно, выполняет последний блок команд.", "CONTROLS_IF_MSG_IF": "если", "CONTROLS_IF_MSG_ELSEIF": "иначе если", "CONTROLS_IF_MSG_ELSE": "иначе", "CONTROLS_IF_IF_TOOLTIP": "Добавьте, удалите, переставьте фрагменты для переделки блока \"если\".", "CONTROLS_IF_ELSEIF_TOOLTIP": "Добавляет условие к блоку \"если\"", "CONTROLS_IF_ELSE_TOOLTIP": "Добавить заключительный подблок для случая, когда все условия ложны.", "IOS_OK": "OK", "IOS_CANCEL": "Отмена", "IOS_ERROR": "Ошибка", "IOS_PROCEDURES_INPUTS": "ВХОД", "IOS_PROCEDURES_ADD_INPUT": "+ Добавить входную переменную", "IOS_PROCEDURES_ALLOW_STATEMENTS": "Разрешить операторы", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "У этой функции есть дублирующиеся входные переменные.", "IOS_VARIABLES_ADD_VARIABLE": "+ Добавить переменную", "IOS_VARIABLES_ADD_BUTTON": "Добавить", "IOS_VARIABLES_RENAME_BUTTON": "Переименовать", "IOS_VARIABLES_DELETE_BUTTON": "Удалить", "IOS_VARIABLES_VARIABLE_NAME": "Имя переменной", "IOS_VARIABLES_EMPTY_NAME_ERROR": "Вы не можете использовать пустое имя переменной.", "LOGIC_COMPARE_HELPURL": "https://ru.wikipedia.org/wiki/Неравенство", "LOGIC_COMPARE_TOOLTIP_EQ": "Возвращает положительное значение, если вводы равны.", "LOGIC_COMPARE_TOOLTIP_NEQ": "Возвращает положительное значение, если вводы не равны.", "LOGIC_COMPARE_TOOLTIP_LT": "Возвращает положительное значение, если первый ввод меньше второго.", "LOGIC_COMPARE_TOOLTIP_LTE": "Возвращает значение истина, если первая вставка меньше или равна  второй.", "LOGIC_COMPARE_TOOLTIP_GT": "Возвращает значение истина, если первая вставка больше второй.", "LOGIC_COMPARE_TOOLTIP_GTE": "Возвращает значение истина, если первая вставка больше или равна  второй.", "LOGIC_OPERATION_TOOLTIP_AND": "Возвращает значение истина, если обе вставки истинны.", "LOGIC_OPERATION_AND": "и", "LOGIC_OPERATION_TOOLTIP_OR": "Возвращает значение истина, если хотя бы одна из вставок истинна.", "LOGIC_OPERATION_OR": "или", "LOGIC_NEGATE_TITLE": "не %1", "LOGIC_NEGATE_TOOLTIP": "Возвращает значение истина, если вставка ложна. Возвращает значение ложь, если вставка истинна.", "LOGIC_BOOLEAN_TRUE": "истина", "LOGIC_BOOLEAN_FALSE": "ложь", "LOGIC_BOOLEAN_TOOLTIP": "Возвращает значение истина или ложь.", "LOGIC_NULL_HELPURL": "https://en.wikipedia.org/wiki/Nullable_type", "LOGIC_NULL": "ничто", "LOGIC_NULL_TOOLTIP": "Возвращает ничто.", "LOGIC_TERNARY_HELPURL": "https://ru.wikipedia.org/wiki/Тернарная_условная_операция", "LOGIC_TERNARY_CONDITION": "выбрать по", "LOGIC_TERNARY_IF_TRUE": "если истина", "LOGIC_TERNARY_IF_FALSE": "если ложь", "LOGIC_TERNARY_TOOLTIP": "Проверяет условие выбора. Если условие истинно, возвращает первое значение, в противном случае возвращает второе значение.", "MATH_NUMBER_HELPURL": "https://ru.wikipedia.org/wiki/Число", "MATH_NUMBER_TOOLTIP": "Число.", "MATH_ARITHMETIC_HELPURL": "https://ru.wikipedia.org/wiki/Арифметика", "MATH_ARITHMETIC_TOOLTIP_ADD": "Возвращает сумму двух чисел.", "MATH_ARITHMETIC_TOOLTIP_MINUS": "Возвращает разность двух чисел.", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "Возвращает произведение двух чисел.", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "Возвращает частное от деления первого числа на второе.", "MATH_ARITHMETIC_TOOLTIP_POWER": "Возвращает первое число, возведённое в степень второго числа.", "MATH_SINGLE_HELPURL": "https://ru.wikipedia.org/wiki/Квадратный_корень", "MATH_SINGLE_OP_ROOT": "квадратный корень", "MATH_SINGLE_TOOLTIP_ROOT": "Возвращает квадратный корень числа.", "MATH_SINGLE_OP_ABSOLUTE": "модуль", "MATH_SINGLE_TOOLTIP_ABS": "Возвращает модуль числа", "MATH_SINGLE_TOOLTIP_NEG": "Возвращает противоположное число.", "MATH_SINGLE_TOOLTIP_LN": "Возвращает натуральный логарифм числа.", "MATH_SINGLE_TOOLTIP_LOG10": "Возвращает десятичный логарифм числа.", "MATH_SINGLE_TOOLTIP_EXP": "Возвращает е в указанной степени.", "MATH_SINGLE_TOOLTIP_POW10": "Возвращает 10 в указанной степени.", "MATH_TRIG_HELPURL": "https://ru.wikipedia.org/wiki/Тригонометрические_функции", "MATH_TRIG_TOOLTIP_SIN": "Возвращает синус угла в градусах.", "MATH_TRIG_TOOLTIP_COS": "Возвращает косинус угла в градусах.", "MATH_TRIG_TOOLTIP_TAN": "Возвращает тангенс угла в градусах.", "MATH_TRIG_TOOLTIP_ASIN": "Возвращает арксинус (в градусах).", "MATH_TRIG_TOOLTIP_ACOS": "Возвращает арккосинус (в градусах).", "MATH_TRIG_TOOLTIP_ATAN": "Возвращает арктангенс (в градусах)", "MATH_CONSTANT_HELPURL": "https://ru.wikipedia.org/wiki/Математическая_константа", "MATH_CONSTANT_TOOLTIP": "Возвращает одну из распространённых  констант: π (3.141...), e (2.718...), φ (1.618...), sqrt(2) (1.414...), sqrt(½) (0.707...) или ∞ (бесконечность).", "MATH_IS_EVEN": "чётное", "MATH_IS_ODD": "нечётное", "MATH_IS_PRIME": "простое", "MATH_IS_WHOLE": "целое", "MATH_IS_POSITIVE": "положительное", "MATH_IS_NEGATIVE": "отрицательное", "MATH_IS_DIVISIBLE_BY": "делится на", "MATH_IS_TOOLTIP": "Проверяет, является ли число чётным, нечётным, простым, целым, положительным, отрицательным или оно кратно определённому числу.  Возвращает значение истина или ложь.", "MATH_CHANGE_HELPURL": "https://ru.wikipedia.org/wiki/%D0%98%D0%B4%D0%B8%D0%BE%D0%BC%D0%B0_%28%D0%BF%D1%80%D0%BE%D0%B3%D1%80%D0%B0%D0%BC%D0%BC%D0%B8%D1%80%D0%BE%D0%B2%D0%B0%D0%BD%D0%B8%D0%B5%29#.D0.98.D0.BD.D0.BA.D1.80.D0.B5.D0.BC.D0.B5.D0.BD.D1.82", "MATH_CHANGE_TITLE": "увеличить %1 на %2", "MATH_CHANGE_TOOLTIP": "Добавляет число к переменной '%1'.", "MATH_ROUND_HELPURL": "https://ru.wikipedia.org/wiki/Округление", "MATH_ROUND_TOOLTIP": "Округляет число до большего или меньшего.", "MATH_ROUND_OPERATOR_ROUND": "округлить", "MATH_ROUND_OPERATOR_ROUNDUP": "округлить к большему", "MATH_ROUND_OPERATOR_ROUNDDOWN": "округлить к меньшему", "MATH_ONLIST_OPERATOR_SUM": "сумма списка", "MATH_ONLIST_TOOLTIP_SUM": "Возвращает сумму всех чисел в списке.", "MATH_ONLIST_OPERATOR_MIN": "наименьшее в списке", "MATH_ONLIST_TOOLTIP_MIN": "Возвращает наименьшее число списка.", "MATH_ONLIST_OPERATOR_MAX": "наибольшее в списке", "MATH_ONLIST_TOOLTIP_MAX": "Возвращает наибольшее число списка.", "MATH_ONLIST_OPERATOR_AVERAGE": "среднее арифметическое списка", "MATH_ONLIST_TOOLTIP_AVERAGE": "Возвращает среднее арифметическое списка.", "MATH_ONLIST_OPERATOR_MEDIAN": "медиана списка", "MATH_ONLIST_TOOLTIP_MEDIAN": "Возвращает медиану списка.", "MATH_ONLIST_OPERATOR_MODE": "моды списка", "MATH_ONLIST_TOOLTIP_MODE": "Возвращает список наиболее часто встречающихся элементов списка.", "MATH_ONLIST_OPERATOR_STD_DEV": "стандартное отклонение списка", "MATH_ONLIST_TOOLTIP_STD_DEV": "Возвращает стандартное отклонение списка.", "MATH_ONLIST_OPERATOR_RANDOM": "случайный элемент списка", "MATH_ONLIST_TOOLTIP_RANDOM": "Возвращает случайный элемент списка.", "MATH_MODULO_HELPURL": "https://ru.wikipedia.org/wiki/Деление_с_остатком", "MATH_MODULO_TITLE": "остаток от %1 : %2", "MATH_MODULO_TOOLTIP": "Возвращает остаток от деления двух чисел.", "MATH_CONSTRAIN_TITLE": "ограничить %1 снизу %2 сверху %3", "MATH_CONSTRAIN_TOOLTIP": "Ограничивает число нижней и верхней границами (включительно).", "MATH_RANDOM_INT_HELPURL": "https://ru.wikipedia.org/wiki/Генератор_псевдослучайных_чисел", "MATH_RANDOM_INT_TITLE": "случайное целое число от %1 для %2", "MATH_RANDOM_INT_TOOLTIP": "Возвращает случайное число между двумя заданными пределами (включая и их).", "MATH_RANDOM_FLOAT_HELPURL": "https://ru.wikipedia.org/wiki/Генератор_псевдослучайных_чисел", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "случайное число от 0 (включительно) до 1", "MATH_RANDOM_FLOAT_TOOLTIP": "Возвращает случайное число от 0.0 (включительно) до 1.0.", "MATH_ATAN2_HELPURL": "https://en.wikipedia.org/wiki/Atan2", "MATH_ATAN2_TITLE": "atan2 от X:%1 Y:%2", "MATH_ATAN2_TOOLTIP": "Возвращает арктангенс точки (X, Y) в градусах от -180 до 180.", "TEXT_TEXT_HELPURL": "https://ru.wikipedia.org/wiki/Строковый_тип", "TEXT_TEXT_TOOLTIP": "Буква, слово или строка текста.", "TEXT_JOIN_TITLE_CREATEWITH": "создать текст из", "TEXT_JOIN_TOOLTIP": "Создаёт фрагмент текста, объединяя любое число элементов", "TEXT_CREATE_JOIN_TITLE_JOIN": "соединить", "TEXT_CREATE_JOIN_TOOLTIP": "Добавьте, удалите, переставьте фрагменты для переделки текстового блока.", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "Добавить элемент к тексту.", "TEXT_APPEND_TITLE": "к %1 добавить текст %2", "TEXT_APPEND_TOOLTIP": "Добавить текст к переменной «%1».", "TEXT_LENGTH_TITLE": "длина %1", "TEXT_LENGTH_TOOLTIP": "Возвращает число символов (включая пробелы) в заданном тексте.", "TEXT_ISEMPTY_TITLE": "%1 пуст", "TEXT_ISEMPTY_TOOLTIP": "Возвращает значение истина, если предоставленный текст пуст.", "TEXT_INDEXOF_TOOLTIP": "Возвращает номер позиции первого/последнего вхождения первого текста во  втором.  Возвращает %1, если текст не найден.", "TEXT_INDEXOF_TITLE": "в тексте %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "найти первое вхождение текста", "TEXT_INDEXOF_OPERATOR_LAST": "найти последнее вхождение текста", "TEXT_CHARAT_TITLE": "в тексте %1 %2", "TEXT_CHARAT_FROM_START": "взять букву №", "TEXT_CHARAT_FROM_END": "взять букву № с конца", "TEXT_CHARAT_FIRST": "взять первую букву", "TEXT_CHARAT_LAST": "взять последнюю букву", "TEXT_CHARAT_RANDOM": "взять случайную букву", "TEXT_CHARAT_TOOLTIP": "Возвращает букву в указанной позиции.", "TEXT_GET_SUBSTRING_TOOLTIP": "Возвращает указанную часть текста.", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "в тексте", "TEXT_GET_SUBSTRING_START_FROM_START": "взять подстроку с буквы №", "TEXT_GET_SUBSTRING_START_FROM_END": "взять подстроку с буквы № с конца", "TEXT_GET_SUBSTRING_START_FIRST": "взять подстроку с первой буквы", "TEXT_GET_SUBSTRING_END_FROM_START": "по букву №", "TEXT_GET_SUBSTRING_END_FROM_END": "по букву № с конца", "TEXT_GET_SUBSTRING_END_LAST": "по последнюю букву", "TEXT_CHANGECASE_TOOLTIP": "Возвращает копию текста с ЗАГЛАВНЫМИ или строчными буквами.", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "в ЗАГЛАВНЫЕ БУКВЫ", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "в строчные буквы", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "в Заглавные Начальные Буквы", "TEXT_TRIM_TOOLTIP": "Возвращает копию текста с пробелами, удалёнными с одного или обоих концов.", "TEXT_TRIM_OPERATOR_BOTH": "обрезать пробелы с двух сторон", "TEXT_TRIM_OPERATOR_LEFT": "обрезать пробелы слева", "TEXT_TRIM_OPERATOR_RIGHT": "обрезать пробелы справа", "TEXT_PRINT_TITLE": "напечатать %1", "TEXT_PRINT_TOOLTIP": "Печатает текст, число или другой объект.", "TEXT_PROMPT_TYPE_TEXT": "запросить текст с подсказкой", "TEXT_PROMPT_TYPE_NUMBER": "запросить число с подсказкой", "TEXT_PROMPT_TOOLTIP_NUMBER": "Запросить у пользователя число.", "TEXT_PROMPT_TOOLTIP_TEXT": "Запросить у пользователя текст.", "TEXT_COUNT_MESSAGE0": "подсчитать количество %1 в %2", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "Подсчитать, сколько раз отрывок текста появляется в другом тексте.", "TEXT_REPLACE_MESSAGE0": "заменить %1 на %2 в %3", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "Заменить все вхождения некоторого текста другим текстом.", "TEXT_REVERSE_MESSAGE0": "изменить порядок на обратный %1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "Меняет порядок символов в тексте на обратный.", "LISTS_CREATE_EMPTY_TITLE": "создать пустой список", "LISTS_CREATE_EMPTY_TOOLTIP": "Возвращает список длины 0, не содержащий данных", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "Создаёт список с любым числом элементов.", "LISTS_CREATE_WITH_INPUT_WITH": "создать список из", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "список", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "Добавьте, удалите, переставьте элементы для переделки блока списка.", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "Добавляет элемент к списку.", "LISTS_REPEAT_TOOLTIP": "Создаёт список, состоящий из заданного числа копий элемента.", "LISTS_REPEAT_TITLE": "создать список из элемента %1, повторяющегося %2 раз", "LISTS_LENGTH_TITLE": "длина %1", "LISTS_LENGTH_TOOLTIP": "Возвращает длину списка.", "LISTS_ISEMPTY_TITLE": "%1 пуст", "LISTS_ISEMPTY_TOOLTIP": "Возвращает значение истина, если список пуст.", "LISTS_INLIST": "в списке", "LISTS_INDEX_OF_FIRST": "найти первое вхождение элемента", "LISTS_INDEX_OF_LAST": "найти последнее вхождение элемента", "LISTS_INDEX_OF_TOOLTIP": "Возвращает номер позиции первого/последнего вхождения элемента в списке. Возвращает %1, если элемент не найден.", "LISTS_GET_INDEX_GET": "взять", "LISTS_GET_INDEX_GET_REMOVE": "взять и удалить", "LISTS_GET_INDEX_REMOVE": "удалить", "LISTS_GET_INDEX_FROM_END": "№ с конца", "LISTS_GET_INDEX_FIRST": "первый", "LISTS_GET_INDEX_LAST": "последний", "LISTS_GET_INDEX_RANDOM": "произвольный", "LISTS_INDEX_FROM_START_TOOLTIP": "%1 - первый элемент.", "LISTS_INDEX_FROM_END_TOOLTIP": "%1 - последний элемент.", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "Возвращает элемент в указанной позиции списка.", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "Возвращает первый элемент списка.", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "Возвращает последний элемент списка.", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "Возвращает случайный элемент списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "Удаляет и возвращает элемент в указанной позиции списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "Удаляет и возвращает первый элемент списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "Удаляет и возвращает последний элемент списка.", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "Удаляет и возвращает случайный элемент списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "Удаляет элемент в указанной позиции списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "Удаляет первый элемент списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "Удаляет последний элемент списка.", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "Удаляет случайный элемент списка.", "LISTS_SET_INDEX_SET": "присвоить", "LISTS_SET_INDEX_INSERT": "вставить в", "LISTS_SET_INDEX_INPUT_TO": "=", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "Присваивает значение элементу в указанной позиции списка.", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "Присваивает значение первому элементу списка.", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "Присваивает значение последнему элементу списка.", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "Присваивает значение случайному элементу списка.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "Вставляет элемент в указанной позиции списка.", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "Вставляет элемент в начало списка.", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "Добавляет элемент в конец списка.", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "Вставляет элемент в случайное место в списке.", "LISTS_GET_SUBLIST_START_FROM_START": "взять подсписок с №", "LISTS_GET_SUBLIST_START_FROM_END": "взять подсписок с № с конца", "LISTS_GET_SUBLIST_START_FIRST": "взять подсписок с первого", "LISTS_GET_SUBLIST_END_FROM_START": "по №", "LISTS_GET_SUBLIST_END_FROM_END": "по № с конца", "LISTS_GET_SUBLIST_END_LAST": "по последний", "LISTS_GET_SUBLIST_TOOLTIP": "Создаёт копию указанной части списка.", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "сортировать %1 %2 %3", "LISTS_SORT_TOOLTIP": "Сортировать копию списка.", "LISTS_SORT_ORDER_ASCENDING": "по возрастанию", "LISTS_SORT_ORDER_DESCENDING": "по убыванию", "LISTS_SORT_TYPE_NUMERIC": "числовая", "LISTS_SORT_TYPE_TEXT": "по алфавиту", "LISTS_SORT_TYPE_IGNORECASE": "по алфавиту, без учёта регистра", "LISTS_SPLIT_HELPURL": "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists", "LISTS_SPLIT_LIST_FROM_TEXT": "сделать список из текста", "LISTS_SPLIT_TEXT_FROM_LIST": "собрать текст из списка", "LISTS_SPLIT_WITH_DELIMITER": "с разделителем", "LISTS_SPLIT_TOOLTIP_SPLIT": "Разбивает текст в список текстов, по разделителям.", "LISTS_SPLIT_TOOLTIP_JOIN": "Соединяет список текстов в один текст с разделителями.", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "изменить порядок на обратный %1", "LISTS_REVERSE_TOOLTIP": "Изменить порядок списка на обратный.", "VARIABLES_GET_TOOLTIP": "Возвращает значение этой переменной.", "VARIABLES_GET_CREATE_SET": "Создать блок \"присвоить\" для %1", "VARIABLES_SET": "присвоить %1 = %2", "VARIABLES_SET_TOOLTIP": "Присваивает переменной значение вставки.", "VARIABLES_SET_CREATE_GET": "Создать вставку %1", "PROCEDURES_DEFNORETURN_TITLE": "чтобы", "PROCEDURES_DEFNORETURN_PROCEDURE": "выполнить что-то", "PROCEDURES_BEFORE_PARAMS": "с:", "PROCEDURES_CALL_BEFORE_PARAMS": "с:", "PROCEDURES_DEFNORETURN_TOOLTIP": "Создаёт процедуру, не возвращающую значение.", "PROCEDURES_DEFNORETURN_COMMENT": "Опишите эту функцию…", "PROCEDURES_DEFRETURN_RETURN": "вернуть", "PROCEDURES_DEFRETURN_TOOLTIP": "Создаёт процедуру, возвращающую значение.", "PROCEDURES_ALLOW_STATEMENTS": "разрешить операторы", "PROCEDURES_DEF_DUPLICATE_WARNING": "Предупреждение: эта функция имеет повторяющиеся параметры.", "PROCEDURES_CALLNORETURN_HELPURL": "https://ru.wikipedia.org/wiki/Подпрограмма", "PROCEDURES_CALLNORETURN_TOOLTIP": "Исполняет определённую пользователем процедуру '%1'.", "PROCEDURES_CALLRETURN_HELPURL": "https://ru.wikipedia.org/wiki/Подпрограмма", "PROCEDURES_CALLRETURN_TOOLTIP": "Исполняет определённую пользователем процедуру '%1' и возвращает вычисленное  значение.", "PROCEDURES_MUTATORCONTAINER_TITLE": "параметры", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "Добавить, удалить или изменить порядок входных параметров для этой функции.", "PROCEDURES_MUTATORARG_TITLE": "имя параметра:", "PROCEDURES_MUTATORARG_TOOLTIP": "Добавить входной параметр в функцию.", "PROCEDURES_HIGHLIGHT_DEF": "Выделить определение процедуры", "PROCEDURES_CREATE_DO": "Создать вызов '%1'", "PROCEDURES_IFRETURN_TOOLTIP": "Если первое значение истинно, возвращает второе значение.", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "Предупреждение: Этот блок может использоваться только внутри определения функции.", "WORKSPACE_COMMENT_DEFAULT_TEXT": "Напишите здесь что-нибудь...", "WORKSPACE_ARIA_LABEL": "Рабочая область Blockly", "COLLAPSED_WARNINGS_WARNING": "Свёрнутые блоки содержат предупреждения.", "DIALOG_OK": "OK", "DIALOG_CANCEL": "Отмена"}