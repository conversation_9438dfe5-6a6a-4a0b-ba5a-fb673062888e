package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingGift;
import org.jeecg.modules.teaching.mapper.TeachingGiftMapper;
import org.jeecg.modules.teaching.service.ITeachingGiftService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 礼物配置服务实现类
 */
@Service
public class TeachingGiftServiceImpl extends ServiceImpl<TeachingGiftMapper, TeachingGift> implements ITeachingGiftService {
    
    @Override
    public Result<List<TeachingGift>> getShoppingGifts() {
        QueryWrapper<TeachingGift> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);  // 只查询启用状态的礼物
        queryWrapper.orderByAsc("gift_row");  // 先按行号排序
        queryWrapper.orderByAsc("gift_order");  // 再按排序号排序
        List<TeachingGift> list = this.list(queryWrapper);
        Result<List<TeachingGift>> result = new Result<List<TeachingGift>>();
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }
    
    @Override
    public Result<List<TeachingGift>> getGiftsByTypeAndRow(Integer giftType, Integer giftRow) {
        QueryWrapper<TeachingGift> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);  // 只查询启用状态的礼物
        
        if (giftType != null) {
            queryWrapper.eq("gift_type", giftType);
        }
        
        if (giftRow != null) {
            queryWrapper.eq("gift_row", giftRow);
        }
        
        queryWrapper.orderByAsc("gift_order");  // 按排序号排序
        List<TeachingGift> list = this.list(queryWrapper);
        Result<List<TeachingGift>> result = new Result<List<TeachingGift>>();
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> batchEnableGift(String ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("参数不能为空");
        }
        List<String> idList = Arrays.asList(ids.split(","));
        
        // 构建更新条件
        UpdateWrapper<TeachingGift> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", idList);
        updateWrapper.set("status", 1); // 设置为启用状态
        
        boolean success = this.update(updateWrapper);
        if (success) {
            return Result.ok("批量启用成功！");
        } else {
            return Result.error("批量启用失败！");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> batchDisableGift(String ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("参数不能为空");
        }
        List<String> idList = Arrays.asList(ids.split(","));
        
        // 构建更新条件
        UpdateWrapper<TeachingGift> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", idList);
        updateWrapper.set("status", 0); // 设置为禁用状态
        
        boolean success = this.update(updateWrapper);
        if (success) {
            return Result.ok("批量禁用成功！");
        } else {
            return Result.error("批量禁用失败！");
        }
    }
} 