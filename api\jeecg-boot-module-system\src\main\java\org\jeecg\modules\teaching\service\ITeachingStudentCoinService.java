package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingStudentCoin;

/**
 * 学生金币服务接口
 */
public interface ITeachingStudentCoinService extends IService<TeachingStudentCoin> {

    /**
     * 获取用户金币数
     *
     * @param userId 用户ID
     * @return 金币数
     */
    Result<Integer> getUserCoinCount(String userId);

    /**
     * 增加用户金币
     *
     * @param userId 用户ID
     * @param coinCount 增加金币数
     * @param source 来源（1:精选作品, 2:客观题, 3:编程题, 4:每日任务）
     * @param description 描述
     * @param relatedId 关联ID
     * @return 操作结果
     */
    Result<Integer> addUserCoin(String userId, Integer coinCount, Integer source, String description, String relatedId);

    /**
     * 消费用户金币
     *
     * @param userId 用户ID
     * @param coinCount 消费金币数
     * @param description 描述
     * @param giftId 礼物ID
     * @param giftName 礼物名称
     * @return 操作结果
     */
    Result<Integer> consumeUserCoin(String userId, Integer coinCount, String description, String giftId, String giftName);
} 