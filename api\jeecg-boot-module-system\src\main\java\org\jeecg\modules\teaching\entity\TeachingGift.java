package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 礼物配置实体类
 */
@Data
@TableName("teaching_gift")
public class TeachingGift implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    private String id;
    
    /**
     * 礼物名称
     */
    private String giftName;
    
    /**
     * 礼物图片路径
     */
    private String giftImg;
    
    /**
     * 所需金币数量
     */
    private Integer coinCount;
    
    /**
     * 礼物类型：0-普通，1-热门，2-珍稀
     */
    private Integer giftType;
    
    /**
     * 排序号
     */
    private Integer giftOrder;
    
    /**
     * 所在行号(1-5)
     */
    private Integer giftRow;
    
    /**
     * 礼物描述
     */
    private String giftDesc;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 