package org.jeecg.modules.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuizDetail;

import java.util.List;

/**
 * 客观题答题详情 Service
 */
public interface ITeachingObjectiveQuizDetailService extends IService<TeachingObjectiveQuizDetail> {
    
    /**
     * 根据记录ID查询答题详情，按题目创建时间排序
     * @param recordId 答题记录ID
     * @return 答题详情列表
     */
    List<TeachingObjectiveQuizDetail> listByRecordIdOrderByQuestionCreateTime(String recordId);
} 