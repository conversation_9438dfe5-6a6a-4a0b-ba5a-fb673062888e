package org.jeecg.modules.teaching.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.teaching.entity.ExamQuestion;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.mapper.ExamQuestionMapper;
import org.jeecg.modules.teaching.mapper.ExamPaperMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 重复检测工具类
 * 提供数据库层面的重复检测功能
 */
@Slf4j
@Component
public class DuplicateCheckUtil {
    
    @Autowired
    private ExamQuestionMapper examQuestionMapper;
    
    @Autowired
    private ExamPaperMapper examPaperMapper;
    
    /**
     * 检查题目是否存在精确重复
     * @param question 待检查的题目
     * @return 重复检测结果
     */
    public Map<String, Object> checkQuestionExactDuplicate(ExamQuestion question) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 添加调试日志
            log.info("精确重复检测 - 题目信息: title=[{}], subject=[{}], level=[{}], type=[{}], id=[{}], content长度=[{}]",
                question.getTitle(), question.getSubject(), question.getLevel(),
                question.getQuestionType(), question.getId(),
                question.getContent() != null ? question.getContent().length() : "null");

            // 构建查询条件：相同标题、科目、级别、类型和内容
            QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("title", question.getTitle())
                       .eq("question_type", question.getQuestionType());

            // 处理可能为null的字段
            if (question.getSubject() != null) {
                queryWrapper.eq("subject", question.getSubject());
            } else {
                queryWrapper.isNull("subject");
            }

            if (question.getLevel() != null) {
                queryWrapper.eq("level", question.getLevel());
            } else {
                queryWrapper.isNull("level");
            }

            if (question.getContent() != null) {
                queryWrapper.eq("content", question.getContent());
            } else {
                queryWrapper.isNull("content");
            }

            // 如果是更新操作，排除当前题目
            if (question.getId() != null) {
                queryWrapper.ne("id", question.getId());
                log.info("排除当前题目ID: {}", question.getId());
            }

            List<ExamQuestion> duplicates = examQuestionMapper.selectList(queryWrapper);
            log.info("精确重复检测结果: 找到 {} 个重复题目", duplicates.size());

            // 如果没有找到重复，但应该有重复，输出详细的对比信息
            if (duplicates.isEmpty()) {
                // 查询所有相同标题和类型的题目，看看哪个字段不匹配
                QueryWrapper<ExamQuestion> debugQuery = new QueryWrapper<>();
                debugQuery.eq("title", question.getTitle())
                         .eq("question_type", question.getQuestionType());
                List<ExamQuestion> similarTitles = examQuestionMapper.selectList(debugQuery);

                if (!similarTitles.isEmpty()) {
                    log.warn("发现相同标题的题目，但字段不完全匹配:");
                    for (ExamQuestion existing : similarTitles) {
                        log.warn("数据库中的题目: subject=[{}], level=[{}], content长度=[{}]",
                            existing.getSubject(), existing.getLevel(),
                            existing.getContent() != null ? existing.getContent().length() : "null");
                        log.warn("导入的题目: subject=[{}], level=[{}], content长度=[{}]",
                            question.getSubject(), question.getLevel(),
                            question.getContent() != null ? question.getContent().length() : "null");

                        // 详细对比每个字段
                        boolean subjectMatch = Objects.equals(existing.getSubject(), question.getSubject());
                        boolean levelMatch = Objects.equals(existing.getLevel(), question.getLevel());
                        boolean contentMatch = Objects.equals(existing.getContent(), question.getContent());

                        log.warn("字段匹配情况: subject={}, level={}, content={}",
                            subjectMatch, levelMatch, contentMatch);
                    }
                }
            }
            
            result.put("hasDuplicate", !duplicates.isEmpty());
            result.put("duplicateCount", duplicates.size());
            if (!duplicates.isEmpty()) {
                result.put("duplicateQuestions", duplicates);
            }
            
        } catch (Exception e) {
            log.error("检查题目精确重复时发生错误", e);
            result.put("error", true);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查试卷是否存在精确重复
     * @param paper 待检查的试卷
     * @return 重复检测结果
     */
    public Map<String, Object> checkPaperExactDuplicate(ExamPaper paper) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建查询条件：相同标题、科目、级别和年份
            QueryWrapper<ExamPaper> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("title", paper.getTitle())
                       .eq("subject", paper.getSubject())
                       .eq("level", paper.getLevel());
            
            // 如果年份不为空，也加入查询条件
            if (paper.getYear() != null) {
                queryWrapper.eq("year", paper.getYear());
            }
            
            // 如果是更新操作，排除当前试卷
            if (paper.getId() != null) {
                queryWrapper.ne("id", paper.getId());
            }
            
            List<ExamPaper> duplicates = examPaperMapper.selectList(queryWrapper);
            
            result.put("hasDuplicate", !duplicates.isEmpty());
            result.put("duplicateCount", duplicates.size());
            if (!duplicates.isEmpty()) {
                result.put("duplicatePapers", duplicates);
            }
            
        } catch (Exception e) {
            log.error("检查试卷精确重复时发生错误", e);
            result.put("error", true);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查题目标题是否存在相似重复（模糊匹配）
     * @param title 题目标题
     * @param subject 科目
     * @param level 级别
     * @param questionType 题目类型
     * @param excludeId 排除的题目ID（用于更新时排除自身）
     * @return 相似题目列表
     */
    public List<ExamQuestion> checkQuestionSimilarTitle(String title, String subject, String level, 
                                                       Integer questionType, String excludeId) {
        try {
            QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
            
            // 使用LIKE进行模糊匹配
            if (StringUtils.isNotBlank(title)) {
                queryWrapper.like("title", title);
            }
            
            if (StringUtils.isNotBlank(subject)) {
                queryWrapper.eq("subject", subject);
            }
            
            if (StringUtils.isNotBlank(level)) {
                queryWrapper.eq("level", level);
            }
            
            if (questionType != null) {
                queryWrapper.eq("question_type", questionType);
            }
            
            if (StringUtils.isNotBlank(excludeId)) {
                queryWrapper.ne("id", excludeId);
            }
            
            return examQuestionMapper.selectList(queryWrapper);
            
        } catch (Exception e) {
            log.error("检查题目相似标题时发生错误", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 批量检查题目重复
     * @param questions 待检查的题目列表
     * @return 重复检测结果
     */
    public Map<String, Object> batchCheckQuestionDuplicates(List<ExamQuestion> questions) {
        Map<String, Object> result = new HashMap<>();
        int duplicateCount = 0;
        int totalCount = questions.size();
        
        try {
            for (ExamQuestion question : questions) {
                Map<String, Object> checkResult = checkQuestionExactDuplicate(question);
                if ((Boolean) checkResult.getOrDefault("hasDuplicate", false)) {
                    duplicateCount++;
                }
            }
            
            result.put("totalCount", totalCount);
            result.put("duplicateCount", duplicateCount);
            result.put("uniqueCount", totalCount - duplicateCount);
            result.put("duplicateRate", totalCount > 0 ? (double) duplicateCount / totalCount : 0.0);
            
        } catch (Exception e) {
            log.error("批量检查题目重复时发生错误", e);
            result.put("error", true);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 清理重复题目（保留最新的一条）
     * @param title 题目标题
     * @param subject 科目
     * @param level 级别
     * @param questionType 题目类型
     * @return 清理结果
     */
    public Map<String, Object> cleanDuplicateQuestions(String title, String subject, String level, Integer questionType) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("title", title)
                       .eq("subject", subject)
                       .eq("level", level)
                       .eq("question_type", questionType)
                       .orderByDesc("create_time");
            
            List<ExamQuestion> duplicates = examQuestionMapper.selectList(queryWrapper);
            
            if (duplicates.size() > 1) {
                // 保留第一条（最新的），删除其他的
                for (int i = 1; i < duplicates.size(); i++) {
                    examQuestionMapper.deleteById(duplicates.get(i).getId());
                }
                
                result.put("cleaned", true);
                result.put("removedCount", duplicates.size() - 1);
                result.put("keptQuestion", duplicates.get(0));
            } else {
                result.put("cleaned", false);
                result.put("message", "没有发现重复题目");
            }
            
        } catch (Exception e) {
            log.error("清理重复题目时发生错误", e);
            result.put("error", true);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }
}
