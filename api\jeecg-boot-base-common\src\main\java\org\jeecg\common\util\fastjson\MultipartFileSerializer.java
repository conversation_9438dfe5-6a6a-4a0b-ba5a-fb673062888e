package org.jeecg.common.util.fastjson;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * MultipartFile类型的FastJSON序列化器
 * 专门用于解决文件上传序列化问题
 */
public class MultipartFileSerializer implements ObjectSerializer {
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        SerializeWriter out = serializer.out;
        if (object == null) {
            out.writeNull();
            return;
        }
        
        // 只序列化MultipartFile对象的基本属性，避免序列化文件内容和输入流
        MultipartFile file = (MultipartFile) object;
        out.writeString("{\"fileName\":\"" + file.getOriginalFilename() 
            + "\",\"contentType\":\"" + file.getContentType()
            + "\",\"size\":" + file.getSize() + "}");
    }
} 