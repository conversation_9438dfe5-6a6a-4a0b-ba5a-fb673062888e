-- 创建客观题答题记录表
CREATE TABLE `teaching_objective_quiz_record` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `course_id` varchar(32) NOT NULL COMMENT '课程ID',
  `unit_id` varchar(32) NOT NULL COMMENT '单元ID',
  `total_count` int(11) DEFAULT '0' COMMENT '总题数',
  `correct_count` int(11) DEFAULT '0' COMMENT '正确题数',
  `score` decimal(5,2) DEFAULT '0.00' COMMENT '得分',
  `answers_json` text COMMENT '答题记录JSON',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_student_course` (`student_id`,`course_id`),
  KEY `idx_unit` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客观题答题记录表'; 