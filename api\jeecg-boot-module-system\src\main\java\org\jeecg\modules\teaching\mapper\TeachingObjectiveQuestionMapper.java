package org.jeecg.modules.teaching.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.teaching.entity.TeachingObjectiveQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;

/**
 * @Description: 客观题
 * @Author: jeecg-boot
 * @Date:   2023-05-10
 * @Version: V1.0
 */
@Mapper
public interface TeachingObjectiveQuestionMapper extends BaseMapper<TeachingObjectiveQuestion> {
    
    /**
     * 根据单元ID查询相关客观题
     * @param unitId 单元ID
     * @param courseId 课程ID
     * @return 客观题列表
     */
    List<TeachingObjectiveQuestion> queryListByUnitId(@Param("unitId") String unitId, @Param("courseId") String courseId);
    
    /**
     * 分页查询客观题并关联课程名称
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return 分页结果
     */
    IPage<TeachingObjectiveQuestion> selectPageWithCourseName(IPage<TeachingObjectiveQuestion> page, @Param("ew") Wrapper<TeachingObjectiveQuestion> wrapper);
    
} 