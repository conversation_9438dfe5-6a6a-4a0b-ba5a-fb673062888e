package org.jeecg.modules.teaching.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.teaching.entity.TeachingGift;
import org.jeecg.modules.teaching.service.ITeachingGiftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 礼物配置控制器
 */
@Api(tags = "礼物配置")
@RestController
@RequestMapping("/teaching/gift")
@Slf4j
public class TeachingGiftController extends JeecgController<TeachingGift, ITeachingGiftService> {

    @Autowired
    private ITeachingGiftService teachingGiftService;
    
    /**
     * 分页列表查询
     */
    @AutoLog(value = "礼物配置-分页列表查询")
    @ApiOperation(value = "礼物配置-分页列表查询", notes = "礼物配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TeachingGift teachingGift,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<TeachingGift> queryWrapper = QueryGenerator.initQueryWrapper(teachingGift, req.getParameterMap());
        Page<TeachingGift> page = new Page<TeachingGift>(pageNo, pageSize);
        IPage<TeachingGift> pageList = teachingGiftService.page(page, queryWrapper);
        return Result.ok(pageList);
    }
    
    /**
     * 添加
     */
    @AutoLog(value = "礼物配置-添加")
    @ApiOperation(value = "礼物配置-添加", notes = "礼物配置-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TeachingGift teachingGift) {
        teachingGiftService.save(teachingGift);
        return Result.ok("添加成功！");
    }
    
    /**
     * 编辑
     */
    @AutoLog(value = "礼物配置-编辑")
    @ApiOperation(value = "礼物配置-编辑", notes = "礼物配置-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody TeachingGift teachingGift) {
        teachingGiftService.updateById(teachingGift);
        return Result.ok("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @AutoLog(value = "礼物配置-通过id删除")
    @ApiOperation(value = "礼物配置-通过id删除", notes = "礼物配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        teachingGiftService.removeById(id);
        return Result.ok("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @AutoLog(value = "礼物配置-批量删除")
    @ApiOperation(value = "礼物配置-批量删除", notes = "礼物配置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.teachingGiftService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }
    
    /**
     * 批量启用礼物
     */
    @AutoLog(value = "礼物配置-批量启用")
    @ApiOperation(value = "礼物配置-批量启用", notes = "礼物配置-批量启用")
    @PutMapping(value = "/batchEnable")
    public Result<?> batchEnable(@RequestParam(name = "ids", required = true) String ids) {
        return teachingGiftService.batchEnableGift(ids);
    }
    
    /**
     * 批量禁用礼物
     */
    @AutoLog(value = "礼物配置-批量禁用")
    @ApiOperation(value = "礼物配置-批量禁用", notes = "礼物配置-批量禁用")
    @PutMapping(value = "/batchDisable")
    public Result<?> batchDisable(@RequestParam(name = "ids", required = true) String ids) {
        return teachingGiftService.batchDisableGift(ids);
    }
    
    /**
     * 获取前台兑换中心可用礼物列表
     */
    @AutoLog(value = "获取前台兑换中心可用礼物列表")
    @ApiOperation(value = "获取前台兑换中心可用礼物列表", notes = "获取前台兑换中心可用礼物列表")
    @GetMapping(value = "/getShoppingGifts")
    public Result<List<TeachingGift>> getShoppingGifts() {
        return teachingGiftService.getShoppingGifts();
    }
    
    /**
     * 根据类型和行号获取礼物
     */
    @AutoLog(value = "根据类型和行号获取礼物")
    @ApiOperation(value = "根据类型和行号获取礼物", notes = "根据类型和行号获取礼物")
    @GetMapping(value = "/getGiftsByTypeAndRow")
    public Result<List<TeachingGift>> getGiftsByTypeAndRow(
            @RequestParam(name = "giftType", required = false) Integer giftType,
            @RequestParam(name = "giftRow", required = false) Integer giftRow) {
        return teachingGiftService.getGiftsByTypeAndRow(giftType, giftRow);
    }
    
    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "礼物配置-通过id查询")
    @ApiOperation(value = "礼物配置-通过id查询", notes = "礼物配置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        TeachingGift teachingGift = teachingGiftService.getById(id);
        if (teachingGift == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(teachingGift);
    }
} 