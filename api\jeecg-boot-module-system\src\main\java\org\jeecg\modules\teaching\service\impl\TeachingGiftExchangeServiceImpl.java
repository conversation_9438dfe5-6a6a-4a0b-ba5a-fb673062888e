package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.teaching.entity.TeachingGiftExchange;
import org.jeecg.modules.teaching.mapper.TeachingGiftExchangeMapper;
import org.jeecg.modules.teaching.service.ITeachingGiftExchangeService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 礼品兑换记录服务实现类
 */
@Service
public class TeachingGiftExchangeServiceImpl extends ServiceImpl<TeachingGiftExchangeMapper, TeachingGiftExchange> implements ITeachingGiftExchangeService {

    @Override
    public Result<?> receiveGift(String id, String userId) {
        Result<Boolean> result = new Result<>();
        try {
            // 查询兑换记录
            TeachingGiftExchange giftExchange = this.getById(id);
            if (giftExchange == null) {
                result.setSuccess(false);
                result.setMessage("兑换记录不存在");
                return result;
            }
            
            // 验证是否是当前用户的兑换记录
            if (!giftExchange.getUserId().equals(userId)) {
                result.setSuccess(false);
                result.setMessage("无权操作此兑换记录");
                return result;
            }
            
            // 更新状态为已领取
            giftExchange.setStatus(1);
            giftExchange.setReceiveTime(new Date());
            this.updateById(giftExchange);
            
            result.setSuccess(true);
            result.setResult(true);
            result.setMessage("礼品领取成功");
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("礼品领取失败：" + e.getMessage());
        }
        return result;
    }

    @Override
    public Result<?> getUserExchangeRecords(String userId, Integer pageNo, Integer pageSize) {
        Result<IPage<TeachingGiftExchange>> result = new Result<>();
        try {
            // 创建分页对象
            Page<TeachingGiftExchange> page = new Page<>(pageNo, pageSize);
            
            // 构建查询条件
            QueryWrapper<TeachingGiftExchange> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.orderByDesc("exchange_time");
            
            // 执行分页查询
            IPage<TeachingGiftExchange> pageList = this.page(page, queryWrapper);
            
            result.setSuccess(true);
            result.setResult(pageList);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("获取兑换记录失败：" + e.getMessage());
        }
        return result;
    }
} 