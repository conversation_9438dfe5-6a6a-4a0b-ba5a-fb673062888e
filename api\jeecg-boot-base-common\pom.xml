<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>jeecg-boot-base-common</artifactId>

	<parent>
		<groupId>org.jeecgframework.boot</groupId>
		<artifactId>jeecg-boot-parent</artifactId>
		<version>2.7.0</version>
	</parent>
	
	<repositories>
		<repository>
			<id>aliyun</id>
			<name>aliyun Repository</name>
			<url>https://maven.aliyun.com/repository/public</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>jeecg</id>
			<name>jeecg Repository</name>
			<url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
	
</project>