package org.jeecg.modules.teaching.constant;

/**
 * 课程通知相关常量
 */
public class NotificationConstant {
    
    /**
     * 通知类型
     */
    public static final String NOTIFICATION_TYPE_NEW = "NEW";         // 新增课程
    public static final String NOTIFICATION_TYPE_UPDATE = "UPDATE";   // 变更课程
    public static final String NOTIFICATION_TYPE_CANCEL = "CANCEL";   // 取消课程
    public static final String NOTIFICATION_TYPE_REMIND = "REMIND";   // 课程提醒
    
    /**
     * 发送状态
     */
    public static final Integer NOTIFICATION_UNSENT = 0;              // 未发送
    public static final Integer NOTIFICATION_SENT = 1;                // 已发送
    
    /**
     * 通知对象设置
     */
    public static final Integer NOTIFY_NO = 0;                        // 不通知
    public static final Integer NOTIFY_YES = 1;                       // 通知
    
    /**
     * 消息类型
     */
    public static final String MSG_CATEGORY_COURSE = "3";             // 课程通知
    
    /**
     * 通知优先级
     */
    public static final String PRIORITY_HIGH = "H";                   // 高优先级（课程取消）
    public static final String PRIORITY_MEDIUM = "M";                 // 中优先级（课程变更、提醒）
    public static final String PRIORITY_LOW = "L";                    // 低优先级（新增课程）
} 