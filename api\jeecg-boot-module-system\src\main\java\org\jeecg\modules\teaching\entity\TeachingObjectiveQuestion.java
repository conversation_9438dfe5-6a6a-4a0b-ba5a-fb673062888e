package org.jeecg.modules.teaching.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 客观题
 * @Author: jeecg-boot
 * @Date:   2023-05-10
 * @Version: V1.0
 */
@ApiModel(value="teaching_objective_question对象", description="客观题")
@Data
@TableName("teaching_objective_question")
public class TeachingObjectiveQuestion implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID) // 阶段二：MyBatis-Plus 3.4.x兼容性修复
    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "题目类型：1-单选题，2-判断题")
    private Integer questionType;
    
    @ApiModelProperty(value = "题目内容")
    private String questionContent;
    
    @ApiModelProperty(value = "选项内容，JSON格式存储，单选题使用")
    private String options;
    
    @ApiModelProperty(value = "正确答案，单选为选项索引(A,B,C,D)，判断为(T,F)")
    private String correctAnswer;
    
    @ApiModelProperty(value = "答案解析")
    private String explanation;
    
    @ApiModelProperty(value = "关联课程ID")
    private String courseId;
    
    @ApiModelProperty(value = "关联单元ID")
    private String unitId;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "所属课程名称")
    private transient String courseName;
    
    @ApiModelProperty(value = "所属单元名称")
    private transient String unitName;
} 