<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="unicorn1" x="0px" y="0px" width="125.03532409667969" height="130.69454956054688" viewBox="9.500001907348633 6.499183654785156 125.03532409667969 130.69454956054688" enable-background="new 0 0 150 150" xml:space="preserve">
  <g>
    <g id="Back_Leg2">
      <g id="Hoof_3_">
        <path id="Fill_12_" fill="#808080" d="M27.334,124.985c-0.062,0.167-0.676,1.541-0.676,1.541&#13;&#10;&#9;&#9;&#9;c-0.295,0.636-0.568,1.279-0.652,1.982c1.831,2.15,8.109,3.533,10.486,1.953c-0.814-1.099-1.561-2.238-2.233-3.41l-0.528-1.396&#13;&#10;&#9;&#9;&#9;l0.544-0.547c-4.852-3.149-7.402-0.261-7.402-0.261l0,0C27.127,124.868,27.363,124.905,27.334,124.985z" stroke-width="1"/>
        <path id="Outline_12_" d="M38.659,130.075c-0.455-0.984-1.677-2.178-2.322-3.146c-0.145-0.218-0.276-0.435-0.413-0.65&#13;&#10;&#9;&#9;&#9;c-0.752-1.118-1.299-1.494-1.299-1.494l-0.325,0.329l-0.544,0.547l0.528,1.396c0.672,1.172,1.419,2.312,2.233,3.41&#13;&#10;&#9;&#9;&#9;c-2.377,1.579-8.655,0.198-10.486-1.953c0.084-0.704,0.357-1.346,0.652-1.983c0,0,0.613-1.373,0.676-1.541&#13;&#10;&#9;&#9;&#9;c0.029-0.078-0.207-0.118-0.46-0.137c-0.277-0.021-0.574-0.019-0.574-0.019l-0.813,1.304c-0.423,1.208-2.197,2.26-0.452,3.686&#13;&#10;&#9;&#9;&#9;C29.945,133.815,39.974,132.901,38.659,130.075z" stroke-width="1" fill="undefined"/>
      </g>
      <g id="Leg_3_">
        <path id="Fill_11_" fill="#FFFFFF" d="M38.693,100.966c-1.281,2.835-3.083,4.798-5.403,6.923&#13;&#10;&#9;&#9;&#9;c-1.087,0.996-1.341,3.106-1.619,4.449c-0.85,4.088-2.504,7.174-5.402,10.385c-0.117,0.841,1.623,1.54,1.794,1.646&#13;&#10;&#9;&#9;&#9;c2.031,0.844,6.489,1.923,5.887,0.719c-2.992-5.984,1.467-14.484,9.579-18.635c0,0,10.162-5.218,11.72-14.468&#13;&#10;&#9;&#9;&#9;c-0.068-0.928-0.207-1.952-0.437-3.11c-0.469-2.369-4.938-7.72-8.829-7.579c-0.463,0.017-1.001,0.218-1.562,0.519&#13;&#10;&#9;&#9;&#9;c-1.565,1.973-5.17,4.466-5.729,9.55C38.348,94.505,40.071,97.913,38.693,100.966z" stroke-width="1"/>
        <path id="Outline_11_" d="M56.562,91.375c-0.562-0.812-1.26,0.312-1.26,0.312c-0.015,0.101-0.036,0.198-0.053,0.298&#13;&#10;&#9;&#9;&#9;c-1.558,9.25-11.72,14.468-11.72,14.468c-8.112,4.15-12.57,12.65-9.579,18.635c0.602,1.204-3.856,0.125-5.887-0.719&#13;&#10;&#9;&#9;&#9;c-0.171-0.106-1.911-0.806-1.794-1.646c2.898-3.211,4.552-6.297,5.402-10.385c0.278-1.343,0.532-3.453,1.619-4.449&#13;&#10;&#9;&#9;&#9;c2.32-2.125,4.122-4.088,5.403-6.923c1.378-3.053-0.345-6.461,0-9.602c0.559-5.084,4.164-7.577,5.729-9.55&#13;&#10;&#9;&#9;&#9;c0.151-0.19,0.291-0.378,0.399-0.561l-0.351-0.567c-2.631,0.911-6.634,6.841-7.396,9.112c-2.16,6.424,5.478,9.551-5.688,16.686&#13;&#10;&#9;&#9;&#9;c-0.466,0.298-0.669,0.603-0.66,0.992c0.221,9.758-6.932,13.791-6.932,15.328c0,0.842,1.036,1.983,1.873,2.412&#13;&#10;&#9;&#9;&#9;c3.605,1.846,8.146,2.262,10.058,1.525c0.249-0.096,0.121-0.52,0.169-0.788c-0.527-0.834-0.967-1.735-1.161-2.738&#13;&#10;&#9;&#9;&#9;c-1.046-5.415,3.123-11.86,8.31-14.353c10.854-5.637,13.271-14.613,13.521-15.426S57.125,92.188,56.562,91.375z" stroke-width="1" fill="undefined"/>
      </g>
    </g>
    <g id="Front_Leg2">
      <g id="Hoof_1_">
        <path id="Fill_6_" fill="#808080" d="M121.202,65.353c0.019,0.177,0.085,1.681,0.085,1.681c0.021,0.701,0.064,1.398,0.305,2.065&#13;&#10;&#9;&#9;&#9;c2.6,1.105,8.834-0.469,10.254-2.946c-1.22-0.618-2.398-1.303-3.524-2.051l-1.097-1.011l0.242-0.733&#13;&#10;&#9;&#9;&#9;c-5.749-0.646-6.738,3.078-6.738,3.078l0,0C120.965,65.339,121.193,65.268,121.202,65.353z" stroke-width="1"/>
        <path id="Outline_6_" d="M133.609,64.838c-0.848-0.677-2.475-1.198-3.485-1.777c-0.227-0.129-0.44-0.265-0.659-0.396&#13;&#10;&#9;&#9;&#9;c-1.174-0.664-1.831-0.755-1.831-0.755l-0.145,0.439l-0.241,0.733l1.097,1.011c1.126,0.748,2.305,1.433,3.523,2.051&#13;&#10;&#9;&#9;&#9;c-1.42,2.477-7.653,4.051-10.254,2.946c-0.24-0.667-0.282-1.364-0.304-2.065c0,0-0.066-1.504-0.085-1.681&#13;&#10;&#9;&#9;&#9;c-0.01-0.085-0.237-0.013-0.474,0.083c-0.256,0.106-0.521,0.241-0.521,0.241l-0.144,1.53c0.161,1.269-0.955,3.004,1.244,3.499&#13;&#10;&#9;&#9;&#9;C127.488,72.083,136.049,66.777,133.609,64.838z" stroke-width="1" fill="undefined"/>
      </g>
      <g id="Leg_1_">
        <path id="Fill_5_" fill="#FFFFFF" d="M114.453,52.217C114.454,52.216,114.454,52.216,114.453,52.217&#13;&#10;&#9;&#9;&#9;c0.119,0.057,0.23,0.133,0.337,0.218c2.654,1.856,2.799,8.978,3.442,11.814c0.295,1.302,7.621-0.312,9.021-1.386&#13;&#10;&#9;&#9;&#9;c1.101-0.845-5.788-5.625-7.402-9.08c-0.781-1.68-1.437-3.446-1.664-5.269c-0.066-0.549,0.092-1.127-0.09-1.654&#13;&#10;&#9;&#9;&#9;c-0.31-0.912-2.355-0.551-3.104-0.495c-4.812,0.367-6.518,5.235-10.266,7.335c-0.822,0.461-9.518,4.999-9.957,5.103&#13;&#10;&#9;&#9;&#9;c-0.771,0.181-1.115-0.938-1.269-1.429c0-0.001,0.003-0.004,0.003-0.006c-0.524,0.397-0.975,0.86-1.298,1.418&#13;&#10;&#9;&#9;&#9;c-1.403,2.423-3.364,8.773-1.181,10.759c1.171,1.065,2.638,1.785,4.188,2.125c-0.078-0.268-0.142-0.523-0.202-0.693&#13;&#10;&#9;&#9;&#9;C104.156,66,102.947,53.131,114.453,52.217z" stroke-width="1"/>
        <path id="Outline_5_" d="M128.853,61.442c-3.652-2.897-6.319-6.065-7.829-10.833c-0.363-1.153-0.609-3.693-1.078-4.532&#13;&#10;&#9;&#9;&#9;c-2.098-3.742-7.651-2.094-9.949,0.494c-3.305,3.722-4.081,5.334-9.389,7.199c-0.897,0.316-4.957,2.457-5.812,2.795&#13;&#10;&#9;&#9;&#9;c-0.677,0.268-1.128,0.229-1.29,0.803c0,0.002-0.003,0.004-0.003,0.006c0.153,0.491,0.498,1.61,1.269,1.429&#13;&#10;&#9;&#9;&#9;c0.439-0.104,9.135-4.642,9.957-5.103c3.748-2.1,5.453-6.968,10.266-7.335c0.748-0.056,2.794-0.417,3.104,0.495&#13;&#10;&#9;&#9;&#9;c0.182,0.527,0.023,1.105,0.09,1.654c0.228,1.823,0.883,3.589,1.664,5.269c1.614,3.454,8.503,8.235,7.402,9.08&#13;&#10;&#9;&#9;&#9;c-1.4,1.075-8.727,2.688-9.021,1.386c-0.644-2.836-0.788-9.958-3.442-11.814c-0.11-0.077-0.217-0.161-0.336-0.219&#13;&#10;&#9;&#9;&#9;c0,0,0,0-0.001,0.001C102.947,53.131,104.156,66,95.014,70.978c0.061,0.17,0.124,0.425,0.202,0.693&#13;&#10;&#9;&#9;&#9;c0.187,0.636,0.468,1.346,1.067,1.183c0.701-0.191,2.311-1.983,3.907-3.994c1.879-2.368,3.74-5.04,4.079-5.776&#13;&#10;&#9;&#9;&#9;c0.667-1.452,1.183-3.029,2.279-4.52c0.877-1.191,2.124-2.326,4.123-3.298c0.562-0.273,1.555-0.551,2.406-0.938l0.001,0.004&#13;&#10;&#9;&#9;&#9;c0.031-0.019,0.059-0.028,0.089-0.045c0.096-0.045,0.188-0.091,0.279-0.14c1.026-0.417,1.577,0.238,1.855,0.836&#13;&#10;&#9;&#9;&#9;c0.75,2.416,1.257,5.035,1.311,7.493c0.019,0.889-0.254,2.689,0.404,3.38c0.649,0.68,2.209,0.353,3.062,0.289&#13;&#10;&#9;&#9;&#9;c-0.004,0.018-0.012,0.034-0.016,0.052C120.663,66.063,130.917,64.625,128.853,61.442z" stroke-width="1" fill="undefined"/>
      </g>
    </g>
    <g id="Mane">
      <linearGradient id="Fill_13_" gradientUnits="userSpaceOnUse" x1="-34.8481" y1="212.5713" x2="-14.2246" y2="212.5713" gradientTransform="matrix(1 0 0 -1 94 254)">
        <stop offset="0.1517" style="stop-color:#00E1FF"/>
        <stop offset="0.4775" style="stop-color:#FFFF00"/>
        <stop offset="0.6798" style="stop-color:#FF9A0E"/>
        <stop offset="1" style="stop-color:#FF0000"/>
      </linearGradient>
      <path id="Fill_10_" fill="url(#Fill_13_)" d="M65.851,42.68c7.294-2.068,3.018-9.284,10.768-8.28&#13;&#10;&#9;&#9;c1.398-7.181,2.812-8.911,2.278-9.872c-0.128-0.119-0.191-0.369-0.309-0.477c-0.851-0.8-2.524-1.133-3.69-0.989&#13;&#10;&#9;&#9;c-2.966,0.366-3.828,3.557-7.247,2.679c-3.053-0.785-3.999-4.259-0.486-5.968c-6.921,0-6.463,5.078-1.462,7.513&#13;&#10;&#9;&#9;c1.009,0.363,1.594,0.664,2.156,0.767c3.129,0.573,3.7,1.259,3.7,1.259c-2.096,1.031-3.685,2.438-4.149,3.291&#13;&#10;&#9;&#9;c-0.943,1.733,1.966,5.195,0.507,7.131c-0.793,1.053-3.58,1.767-4.348-1.248c-5.131,5.099,3.396,8.172,8.129,3.019&#13;&#10;&#9;&#9;c0.655,2.14-2.359,3.906-5.54,4.042c-0.549,0.473-1.106,0.935-0.981,1.688c0.228,1.38,1.604,2.91,0.795,4.291&#13;&#10;&#9;&#9;c-1.018,1.734-2.562,2.786-6.146,1.439c1.987,2.972,4.649,3.179,7.173,1.822c0.121,0.657-0.281,1.157-0.964,1.471&#13;&#10;&#9;&#9;c-0.12,0.095-0.245,0.185-0.375,0.268c-1.281,1.323-0.891,3.914-1.792,5.386c0,0.055,0.002,0.104,0.002,0.161&#13;&#10;&#9;&#9;c-0.034,0-0.076,0.008-0.113,0.011c-0.271,0.371-0.643,0.657-1.18,0.803c2.221,1.198,5.019-3.438,7.653-3.956&#13;&#10;&#9;&#9;c0,0,1.766-0.26,3.351-5.741c-0.416,0.222-0.888,0.421-1.229,0.691c-2.578,1.584-2.972,6.004-6.757,5.59&#13;&#10;&#9;&#9;c1.703-0.602,1.897-0.809,2.463-1.491c1.354-2.362,2.356-5.915,5.758-5.645c0.402-1.546,0.786-3.449,1.118-5.805&#13;&#10;&#9;&#9;c-3.551,0.385-4.333,6.642-9.501,6.668c4.605-0.614,4.75-7.798,9.727-8.384c0.053-0.445,0.104-0.904,0.154-1.377&#13;&#10;&#9;&#9;c0.333-3.195,0.719-5.824,1.113-8.003C70.2,35.213,74.105,42.499,65.851,42.68z" stroke-width="1"/>
      <path id="Outline_10_" d="M79.748,24.321c-0.007-0.032-0.015-0.056-0.021-0.086c-0.026-0.112-0.053-0.228-0.082-0.32&#13;&#10;&#9;&#9;c-0.052-0.977-2.33-2.364-3.664-2.541c-3.495-0.462-4.29,2.057-6.797,2.884c-1.622,0.533-2.945-1.015-2.386-2.39&#13;&#10;&#9;&#9;c0.392-0.967,1.819-1.107,2.7-1.484c-0.606-0.92-1.975-1.659-3.107-1.895c-1.786-0.373-3.672,0.412-4.771,1.689&#13;&#10;&#9;&#9;c-1.856,2.158-0.217,6.585,2.555,7.636l-0.014,0.006l0.255,0.078c0.071,0.022,0.141,0.049,0.214,0.065l4.834,1.476&#13;&#10;&#9;&#9;c0,0-1.202,0.237-3.302,2.962c-2.315,2.351,3.033,5.711-0.095,7.268c-1.587,0.79-1.318-2.111-0.76-3.312&#13;&#10;&#9;&#9;c-2.209,0.301-4.549,2.636-5.087,4.574c-0.414,1.489,1.669,3.833,3.267,4.314c0.189,0.057,0.383,0.1,0.578,0.141&#13;&#10;&#9;&#9;c-0.01,0.012-0.023,0.023-0.034,0.035c-1.829,2.127,1.15,3.76,0.637,5.147c-1.321,3.574-5.885,0.53-6.353-0.108&#13;&#10;&#9;&#9;c-0.824,4.599,3.254,6.33,6.107,6.166c-1.5,1.394-0.762,4.386-1.844,4.985c-0.712,0.395-1.947-0.165-2.475-0.618&#13;&#10;&#9;&#9;c-0.072,1.633,0.996,3.082,2.251,3.173c3.307,0.239,4.742-2.245,7.211-2.346c5.19-1.806,6.248-9.319,7.06-19.139&#13;&#10;&#9;&#9;s2.812-13.743,3.15-16.118C79.915,25.578,79.859,24.849,79.748,24.321z M75.159,44.815c-4.977,0.586-5.122,7.77-9.727,8.384&#13;&#10;&#9;&#9;c5.168-0.026,5.95-6.283,9.501-6.668c-0.332,2.355-0.716,4.258-1.118,5.805c-3.402-0.27-4.404,3.283-5.758,5.645&#13;&#10;&#9;&#9;c-0.566,0.682-0.76,0.889-2.463,1.491c3.785,0.414,4.179-4.006,6.757-5.59c0.341-0.271,0.813-0.47,1.229-0.691&#13;&#10;&#9;&#9;c-1.585,5.481-3.351,5.741-3.351,5.741c-2.634,0.518-5.432,5.154-7.653,3.956c0.537-0.146,0.908-0.432,1.18-0.803&#13;&#10;&#9;&#9;c0.04-0.055,0.076-0.113,0.111-0.171c0.901-1.472,0.512-4.063,1.792-5.386c0.06-0.062,0.114-0.127,0.181-0.183&#13;&#10;&#9;&#9;c0.068-0.026,0.13-0.056,0.193-0.085c0.683-0.314,1.085-0.814,0.964-1.471c-2.524,1.357-5.186,1.15-7.173-1.822&#13;&#10;&#9;&#9;c3.584,1.347,5.128,0.295,6.146-1.439c0.809-1.381-0.567-2.911-0.795-4.291c-0.125-0.753,0.432-1.216,0.981-1.688&#13;&#10;&#9;&#9;c3.18-0.136,6.195-1.901,5.54-4.042c-4.733,5.153-13.26,2.08-8.129-3.019c0.768,3.015,3.555,2.301,4.348,1.248&#13;&#10;&#9;&#9;c1.459-1.936-1.45-5.398-0.507-7.131c0.464-0.854,2.053-2.26,4.149-3.291c0,0-0.571-0.686-3.7-1.259&#13;&#10;&#9;&#9;c-0.562-0.103-1.147-0.404-2.156-0.767c-5.001-2.436-5.459-7.513,1.462-7.513c-3.513,1.709-2.567,5.183,0.486,5.968&#13;&#10;&#9;&#9;c3.419,0.878,4.281-2.313,7.247-2.679c1.166-0.144,2.839,0.189,3.69,0.989c0.117,0.108,0.181,0.358,0.309,0.477&#13;&#10;&#9;&#9;c0.533,0.961-0.88,2.691-2.278,9.872c-7.75-1.003-3.474,6.212-10.768,8.28c8.254-0.182,4.349-7.467,10.575-7.246&#13;&#10;&#9;&#9;c-0.395,2.179-0.78,4.808-1.113,8.003C75.263,43.911,75.212,44.37,75.159,44.815z" stroke-width="1" fill="undefined"/>
    </g>
    <g id="Body">
      <path id="Fill_4_" fill="#FFFFFF" d="M87.995,41.11C86.24,30.595,83.75,30.875,80.75,30.18s-3.54,0.795-3.54,0.795l-0.092,1.643&#13;&#10;&#9;&#9;c-0.074,0.784-0.162,1.582-0.179,1.716c-0.672,5.375-0.771,10.843-1.604,16.193C74.04,58.83,69.277,65.11,61.607,69.523&#13;&#10;&#9;&#9;c-5.159,2.968-14.488,4.215-16.712,9.763c-0.627,1.565-1.006,4.523-1.234,6.631c-0.122,1.125-1.403,14.282,20.15,7.2v-0.057&#13;&#10;&#9;&#9;c1.726-0.728,4.49-1.808,6.347-2.191c5.042-1.045,17.277-3.203,20.076-7.336c0.066-0.098,0.646-1.071,0.686-1.182&#13;&#10;&#9;&#9;c0,0,6.968-9.153,5.482-19.585C95.366,55.02,88.445,49.105,87.995,41.11z" stroke-width="1"/>
      <path id="Outline_4_" d="M97.568,59.484c-1.689-6.253-7.604-11.878-7.817-18.552c0,0-0.688-9.066-5.001-10.937&#13;&#10;&#9;&#9;s-9.722-2.998-9.548,3.253c-0.007,0.343-0.902,10.71-1.25,14.866c-0.368,4.385-2.835,9.426-5.186,13.084&#13;&#10;&#9;&#9;c-4.792,7.455-18.637,8.371-22.824,14.652l0-0.001c-2.892,3.851-2.406,6.891-2.701,9.292c-0.342,2.789-1.323,6.441,4.427,10.366&#13;&#10;&#9;&#9;s17.332-0.892,17.332-0.892c0.005-0.004,0.008-0.008,0.013-0.013c2.055-0.443,4.661-1.815,6.317-2.335&#13;&#10;&#9;&#9;c5.975-1.873,13.658-1.254,18.591-5.15c0.651-0.516,1.678-1.448,2.443-2.414l0.002,0.003c0.01-0.014,0.019-0.027,0.028-0.041&#13;&#10;&#9;&#9;c0.196-0.25,0.377-0.501,0.525-0.749c8.799-12.722,4.77-23.991,4.663-24.418C97.578,59.488,97.572,59.49,97.568,59.484z&#13;&#10;&#9;&#9; M90.92,82.352c-0.04,0.11-0.619,1.084-0.686,1.182c-2.799,4.133-15.034,6.291-20.076,7.336c-1.857,0.384-4.621,1.464-6.347,2.191&#13;&#10;&#9;&#9;v0.057c-21.553,7.082-20.271-6.075-20.15-7.2c0.228-2.107,0.606-5.065,1.234-6.631c2.224-5.548,11.553-6.795,16.712-9.763&#13;&#10;&#9;&#9;c7.67-4.413,12.433-10.693,13.729-18.996c0.833-5.35,0.932-10.818,1.604-16.193c0.017-0.134,0.104-0.932,0.179-1.716l0.092-1.643&#13;&#10;&#9;&#9;c0,0,0.54-1.49,3.54-0.795s5.49,0.415,7.245,10.93c0.45,7.995,7.371,13.91,8.407,21.656C97.888,73.199,90.92,82.352,90.92,82.352z" stroke-width="1" fill="undefined"/>
    </g>
    <g id="Back_Leg1">
      <g id="Hoof_2_">
        <path id="Fill_8_" fill="#808080" d="M51.346,129.256c-0.041,0.194-0.489,1.811-0.489,1.811c-0.22,0.75-0.414,1.504-0.39,2.294&#13;&#10;&#9;&#9;&#9;c2.375,2.067,9.524,2.549,11.883,0.413c-1.079-1.075-2.092-2.208-3.027-3.389l-0.813-1.45l0.509-0.693&#13;&#10;&#9;&#9;&#9;c-5.87-2.667-8.202,0.939-8.202,0.939l0,0C51.099,129.16,51.366,129.163,51.346,129.256z" stroke-width="1"/>
        <path id="Outline_8_" d="M64.673,132.988c-0.665-1.01-2.21-2.123-3.081-3.084c-0.195-0.216-0.376-0.433-0.562-0.648&#13;&#10;&#9;&#9;&#9;c-1.015-1.107-1.68-1.431-1.68-1.431l-0.305,0.416l-0.509,0.693l0.813,1.45c0.936,1.181,1.948,2.313,3.027,3.389&#13;&#10;&#9;&#9;&#9;c-2.358,2.136-9.508,1.654-11.883-0.413c-0.024-0.79,0.17-1.544,0.39-2.294c0,0,0.448-1.616,0.489-1.811&#13;&#10;&#9;&#9;&#9;c0.02-0.093-0.247-0.096-0.53-0.075c-0.309,0.023-0.636,0.075-0.636,0.075l-0.68,1.572c-0.267,1.4-2.048,2.854,0.112,4.137&#13;&#10;&#9;&#9;&#9;C55.689,138.557,66.591,135.885,64.673,132.988z" stroke-width="1" fill="undefined"/>
      </g>
      <g id="Leg_2_">
        <path id="Fill_9_" fill="#FFFFFF" d="M42.925,84.904c-1.594,5.904,2.292,11.468,2.611,16.763&#13;&#10;&#9;&#9;&#9;c0.213,3.552-0.761,9.138-2.522,13.228c-1.196,2.778,5.697,4.625,7.022,8.611c0.624,1.87-1.492,5.49,1.901,5.494l3.312,0.062&#13;&#10;&#9;&#9;&#9;c2.391-0.194,4.232-0.774,3.25-1.938c-4.016-4.758-9.59-9.577-8.25-14.812c1.111-4.337,7.4-7.516,10.051-12.254&#13;&#10;&#9;&#9;&#9;c1.686-3.016,1.9-5.884,3.061-8.981c0.145-0.029,0.395,0.049,0.535,0.163c-0.092-0.71-0.303-1.401-0.745-2.071&#13;&#10;&#9;&#9;&#9;c-1.53-2.316-3.356-4.754-5.821-6.153c-2.522-1.432-6.005-2.614-9.031-2.616c-2.162-0.001-3.639,0.665-4.628,1.709&#13;&#10;&#9;&#9;&#9;C44.177,82.358,43.099,84.258,42.925,84.904z" stroke-width="1"/>
        <path id="Outline_9_" d="M58.805,129.893c1.837-0.218,2.461-1.894,1.618-2.852c-2.954-3.354-7.132-6.762-8.315-10.748&#13;&#10;&#9;&#9;&#9;c-1.791-6.041,6.152-8.797,8.869-13.062c1.88-2.95,4.164-8.473,3.016-11.866c-0.015-0.044-0.049-0.086-0.096-0.124&#13;&#10;&#9;&#9;&#9;c-0.14-0.114-0.39-0.192-0.535-0.163c-1.161,3.098-1.375,5.966-3.061,8.981c-2.651,4.738-8.94,7.917-10.051,12.254&#13;&#10;&#9;&#9;&#9;c-1.34,5.235,4.234,10.055,8.25,14.812c0.982,1.163-0.859,1.743-3.25,1.938L51.938,129c-3.394-0.004-1.277-3.624-1.901-5.494&#13;&#10;&#9;&#9;&#9;c-1.325-3.986-8.218-5.833-7.022-8.611c1.761-4.09,2.735-9.676,2.522-13.228c-0.319-5.295-4.205-10.858-2.611-16.763&#13;&#10;&#9;&#9;&#9;c0.174-0.646,1.252-2.546,0.747-2.796c-0.091-0.045-0.226-0.044-0.432,0.034c-2.052,0.773-2.627,9.169-0.283,13.982&#13;&#10;&#9;&#9;&#9;c0.747,1.533,0.773,5.818,0.597,9.291c-0.179,3.584-3.372,8.839-3.015,11.455c0.342,2.503,6.175,3.752,7.263,5.835&#13;&#10;&#9;&#9;&#9;C50.962,128.755,43.661,131.688,58.805,129.893z" stroke-width="1" fill="undefined"/>
      </g>
    </g>
    <g id="Tail">
      <linearGradient id="Fill_14_" gradientUnits="userSpaceOnUse" x1="-81.3799" y1="170.0889" x2="-49.5835" y2="170.0889" gradientTransform="matrix(1 0 0 -1 94 254)">
        <stop offset="0.1517" style="stop-color:#00E1FF"/>
        <stop offset="0.4775" style="stop-color:#FFFF00"/>
        <stop offset="0.6798" style="stop-color:#FF9A0E"/>
        <stop offset="1" style="stop-color:#FF0000"/>
      </linearGradient>
      <path id="Fill_7_" fill="url(#Fill_14_)" d="M20.037,74.549c-3.373,6.562,0.439,13.24,0.518,19.891&#13;&#10;&#9;&#9;c0.043,3.69-3.067,5.828-6.933,5.258c2.07,1.398,5.468,2.552,8.004,1.953c2.631-0.625,4.47-2.447,4.967-4.879&#13;&#10;&#9;&#9;c0.901-4.408-4.812-15.312,0.064-17.545c4.542-2.078,6.631,6.695,13.763,4.178c0.234-0.082,0.639-0.255,1.092-0.436&#13;&#10;&#9;&#9;c-4.025-0.729-4.729-9.704-9.523-10.362c-4.258-0.586-8.431,2.452-10.295,5.683c-1.999,3.461-0.717,6.946,0.262,10.454&#13;&#10;&#9;&#9;c0.792,2.833,3.551,9.107,0.461,10.811c-0.933,0.512-2.754,0.86-3.829,0.711c4.756-1.358,4.211-5.315,3.829-7.769&#13;&#10;&#9;&#9;c-0.981-6.3-5.063-13.097,0.879-18.754c3.473-3.307,10.849-4.803,14.021-0.569c1.604,2.144,2.11,7.836,4.466,8.958&#13;&#10;&#9;&#9;c0.341,0.162,0.936,0.041,1.341,0.082l0-0.003c0,0,0.487-0.162,0.556-1.146C42.558,61.251,25.932,63.08,20.037,74.549z" stroke-width="1"/>
      <path id="Outline_7_" d="M42.542,69.007c-1.625-2.167-4.369-4.15-7.19-4.688c-5.77-1.1-13.16,2.038-16.039,6.725&#13;&#10;&#9;&#9;c-5.596,9.111-1.1,14.442-0.984,23.539c0.061,4.7-6.654,4.438-8.329,1.229c0.81,5.098,9.078,9.193,14.122,6.623&#13;&#10;&#9;&#9;c8.898-4.531,0.195-19.549,2.977-20.822c5.36-2.455,4.927,8.383,14.046,2.834c0.373-0.227,1.152-0.613,1.812-1.036l0,0.003&#13;&#10;&#9;&#9;c1.749-0.67,1.863-2.16,1.863-2.16C45.365,78.775,44.239,71.266,42.542,69.007z M43.125,82.211l0,0.003&#13;&#10;&#9;&#9;c-0.406-0.041-1,0.08-1.341-0.082c-2.356-1.122-2.862-6.814-4.466-8.958c-3.172-4.234-10.548-2.738-14.021,0.569&#13;&#10;&#9;&#9;c-5.942,5.657-1.86,12.454-0.879,18.754c0.382,2.453,0.927,6.41-3.829,7.769c1.075,0.149,2.896-0.199,3.829-0.711&#13;&#10;&#9;&#9;c3.09-1.703,0.331-7.978-0.461-10.811c-0.979-3.508-2.261-6.993-0.262-10.454c1.864-3.231,6.037-6.269,10.295-5.683&#13;&#10;&#9;&#9;c4.795,0.658,5.499,9.633,9.523,10.362c-0.454,0.181-0.858,0.354-1.092,0.436c-7.132,2.518-9.221-6.256-13.763-4.178&#13;&#10;&#9;&#9;c-4.876,2.233,0.837,13.137-0.064,17.545c-0.497,2.432-2.336,4.254-4.967,4.879c-2.536,0.599-5.934-0.555-8.004-1.953&#13;&#10;&#9;&#9;c3.866,0.57,6.976-1.567,6.933-5.258c-0.079-6.65-3.891-13.328-0.518-19.891c5.895-11.469,22.521-13.298,23.644,6.516&#13;&#10;&#9;&#9;C43.612,82.049,43.125,82.211,43.125,82.211z" stroke-width="1" fill="undefined"/>
    </g>
    <g id="Front_Leg1">
      <g id="Hoof">
        <path id="Fill_3_" fill="#808080" d="M116.944,97.506c-0.041,0.194-0.489,1.811-0.489,1.811c-0.22,0.75-0.414,1.504-0.39,2.294&#13;&#10;&#9;&#9;&#9;c2.375,2.067,9.524,2.549,11.883,0.413c-1.079-1.075-2.092-2.208-3.027-3.389l-0.813-1.45l0.509-0.693&#13;&#10;&#9;&#9;&#9;c-5.87-2.667-8.202,0.939-8.202,0.939l0,0C116.697,97.41,116.964,97.413,116.944,97.506z" stroke-width="1"/>
        <path id="Outline_3_" d="M130.271,101.238c-0.665-1.01-2.21-2.123-3.081-3.084c-0.195-0.216-0.376-0.433-0.562-0.648&#13;&#10;&#9;&#9;&#9;c-1.015-1.107-1.68-1.431-1.68-1.431l-0.305,0.416l-0.509,0.693l0.813,1.45c0.936,1.181,1.948,2.313,3.027,3.389&#13;&#10;&#9;&#9;&#9;c-2.358,2.136-9.508,1.654-11.883-0.413c-0.024-0.79,0.17-1.544,0.39-2.294c0,0,0.448-1.616,0.489-1.811&#13;&#10;&#9;&#9;&#9;c0.02-0.093-0.247-0.096-0.53-0.075c-0.309,0.023-0.636,0.075-0.636,0.075l-0.68,1.572c-0.267,1.4-2.048,2.854,0.112,4.137&#13;&#10;&#9;&#9;&#9;C121.287,106.807,132.189,104.135,130.271,101.238z" stroke-width="1" fill="undefined"/>
      </g>
      <g id="Leg">
        <path id="Fill_2_" fill="#FFFFFF" d="M89.064,79.293c9.555,4.066,17.011-2.341,25.074,0.986c1.805,3.158,3.451,8.302,2.791,11.869&#13;&#10;&#9;&#9;&#9;c-0.296,1.592-2.141,2.591-2.389,4.244c0.606,1.069,2.966,1.246,3.865,1.345c2.173,0.239,5.677,0.388,5.382-0.825&#13;&#10;&#9;&#9;&#9;c-1.136-4.672-4.491-9.102-4.158-14.241c3.912-4.741-2.128-8.371-4.95-7.911c-8.117,1.323-8.646-0.363-15.263-3.75&#13;&#10;&#9;&#9;&#9;c-1.947-0.998-4.802-0.793-5.592-1.197c-1.315,0.075-2.616,0.289-3.808,0.787c-2.854,1.192-4.324,3.945-2.856,6.842&#13;&#10;&#9;&#9;&#9;c0.322,0.636,0.825,1.502,1.448,2.335C88.702,79.564,88.852,79.386,89.064,79.293z" stroke-width="1"/>
        <path id="Outline_2_" d="M125.725,96.332c0,0,0.02-0.006,0.022-0.008c-1.729-2.338-3.028-4.776-3.956-7.639&#13;&#10;&#9;&#9;&#9;c-2.327-7.176,5.105-9.211-1.395-14.134c-5.922-4.482-7.627,1.938-17.888-3.437c-0.627-0.328-8.683-4.188-8.899-1.589&#13;&#10;&#9;&#9;&#9;c-0.011,0.124,0.073,0.214,0.216,0.287c0.79,0.404,3.645,0.199,5.592,1.197c6.616,3.387,7.146,5.073,15.263,3.75&#13;&#10;&#9;&#9;&#9;c2.822-0.46,8.862,3.17,4.95,7.911c-0.333,5.14,3.022,9.569,4.158,14.241c0.295,1.213-3.209,1.064-5.382,0.825&#13;&#10;&#9;&#9;&#9;c-0.899-0.099-3.259-0.275-3.865-1.345c0.248-1.653,2.093-2.652,2.389-4.244c0.66-3.567-0.986-8.711-2.791-11.869&#13;&#10;&#9;&#9;&#9;c-8.063-3.327-15.52,3.08-25.074-0.986c-0.213,0.093-0.362,0.271-0.455,0.483c-0.158,0.362-0.148,0.823,0.006,1.124&#13;&#10;&#9;&#9;&#9;c1.879,3.633,10.521,1.707,13.639,0.865c3.729-1.009,6.08-0.414,9.725,0.287c1.358,0.26,1.752,0.333,2.252,1.607&#13;&#10;&#9;&#9;&#9;c1.07,2.742,1.65,6.285,0.088,8.982c-0.688,1.186-2.236,2.023-2.34,3.461c0.049,0.08,0.119,0.145,0.173,0.221&#13;&#10;&#9;&#9;&#9;c0.402,0.43,2.097,2.056,5.206,2.664l0.019,0.049l0.012-0.041c0,0,3.828,0.333,7.429-0.623c0.393-0.123,0.782-0.241,1.193-0.396&#13;&#10;&#9;&#9;&#9;c0.001,0,0.001,0,0.001-0.001C126.014,97.972,126.39,97.27,125.725,96.332z" stroke-width="1" fill="undefined"/>
      </g>
    </g>
    <g id="Head">
      <g id="Head_1_">
        <path id="Fill_1_" fill="#FFFFFF" d="M85.6,39.938c1.352-0.597,2.75-0.642,4.008-1.483c2.312-1.549,2.836-3.712,2.564-6.222&#13;&#10;&#9;&#9;&#9;c0.799-0.763,1.932-1.111,3.061-1.195c2.516,3.957,8.24,4.214,8.959-0.989c-2.416-0.299-5.907,0.157-6.934-2.473l0.225-0.207&#13;&#10;&#9;&#9;&#9;c1.863-0.033,8.67-0.927,8.959-2.843c0.324-2.145-2.188-7.773-5.266-6.551c-2.242,0.889-3.496,3.753-6.304,4.573&#13;&#10;&#9;&#9;&#9;c-3.149,0.921-3.961,1.781-7.022,0.289c-1.771-0.864-4.89-0.364-6.523,0.905c-2.692,2.091-3.596,5.778-4.369,5.234&#13;&#10;&#9;&#9;&#9;c-2.874-2.02-5.567-1.799-6.932-1.607c-1.243,0.176-3.389,0.79-3.962,1.896c2.732,2.208,9.062,2.464,11.147,1.71&#13;&#10;&#9;&#9;&#9;c0.027-0.01,0.022,0.208,0.002,0.532c-0.094,1.111-0.462,5.66,2.996,7.952C82.116,40.723,85.6,39.938,85.6,39.938z" stroke-width="1"/>
        <path id="Outline_1_" d="M99.195,16.202c-1.988,1.496-3.383,3.569-5.582,4.944c-5.305,3.32-10.268-7.106-16.205,5.934&#13;&#10;&#9;&#9;&#9;c-3.67-1.858-12.349-2.386-13.864,2.266c3.074,2.26,7.301,3.396,11.254,2.971c0.394-0.064,0.773-0.177,1.146-0.309&#13;&#10;&#9;&#9;&#9;c0.805-0.285,0.473-0.175,1.268-0.501c0.021-0.324,0.025-0.542-0.002-0.532c-2.085,0.754-8.415,0.498-11.147-1.71&#13;&#10;&#9;&#9;&#9;c0.573-1.106,2.719-1.72,3.962-1.896c1.365-0.192,4.058-0.413,6.932,1.607c0.773,0.544,1.677-3.143,4.369-5.234&#13;&#10;&#9;&#9;&#9;c1.634-1.269,4.753-1.769,6.523-0.905c3.062,1.492,3.873,0.632,7.022-0.289c2.808-0.82,4.062-3.684,6.304-4.573&#13;&#10;&#9;&#9;&#9;c3.077-1.222,5.59,4.406,5.266,6.551c-0.289,1.916-7.096,2.81-8.959,2.843l-0.225,0.207c1.026,2.63,4.518,2.174,6.934,2.473&#13;&#10;&#9;&#9;&#9;c-0.719,5.203-6.443,4.946-8.959,0.989c-1.129,0.084-2.262,0.432-3.061,1.195c0.271,2.51-0.253,4.673-2.564,6.222&#13;&#10;&#9;&#9;&#9;c-1.258,0.841-2.656,0.886-4.008,1.483c0,0-0.184,0.729,1.025,1.06c3.858,1.057,7.358-3.196,7.102-6.505&#13;&#10;&#9;&#9;&#9;c0.605-0.402,1.154-0.653,1.693-0.752c0.723,0.602,1.432,1.033,2.152,1.336c3.4,1.425,8.645-0.401,8.643-4.616&#13;&#10;&#9;&#9;&#9;c-0.001-2.838-3.51-0.889-5.04-1.978c1.644-0.867,4.906-0.626,6.033-1.979C111.102,21.824,103.066,13.288,99.195,16.202z" stroke-width="1" fill="undefined"/>
      </g>
      <ellipse id="Back_Nostril" cx="102.838" cy="20.1" rx="0.877" ry="0.478" stroke-width="1"/>
      <ellipse id="Front_Nostril" cx="102.592" cy="23.014" rx="1.564" ry="0.879" stroke-width="1"/>
      <path id="Back_Eye" d="M87.695,24.15c0.598-0.441,0.644-1.188,0.255-1.792c-0.398-0.621,0.618-1.168,1.018-0.547&#13;&#10;&#9;&#9;c0.632,0.981,0.128,2.113-0.841,2.829C87.77,24.902,87.339,24.414,87.695,24.15z" stroke-width="1" fill="undefined"/>
      <path id="Front_Eye" d="M86.045,28.917c0.932-0.356,0.936-1.562,0.412-2.37c-0.5-0.771,0.762-1.456,1.262-0.686&#13;&#10;&#9;&#9;c0.958,1.479,0.577,3.612-1.126,4.261C85.715,30.457,85.166,29.251,86.045,28.917z" stroke-width="1" fill="undefined"/>
    </g>
    <g id="Horn">
      <path id="Fill" fill="#FBB03B" d="M79.445,23.593c1.604,0.38,2.931-0.621,3.242-1.615c-2.883-3.619-6.992-8.524-10.712-12.328&#13;&#10;&#9;&#9;C73.914,14.255,77.166,19.13,79.445,23.593z" stroke-width="1"/>
      <path id="Outline" d="M84.357,21.676C83.214,19.623,72.101,6.781,70.079,7.002c-0.737,0.759,5.944,14.759,8.45,17.916&#13;&#10;&#9;&#9;C82.012,25.988,85.182,23.152,84.357,21.676z M77.943,16.325c0.549,1.04,0.438,3.058-0.357,3.848&#13;&#10;&#9;&#9;c-0.338-0.595-0.682-1.194-1.027-1.795c0.041-0.031,0.081-0.062,0.114-0.115c0.501-0.783,0.773-1.717,0.745-2.641&#13;&#10;&#9;&#9;c0.157,0.182,0.312,0.362,0.468,0.544C77.906,16.22,77.912,16.267,77.943,16.325z M75.873,13.858&#13;&#10;&#9;&#9;c0.458,1.009,0.492,2.631,0.1,3.497c-0.422-0.736-0.841-1.473-1.251-2.212c0.287-0.685,0.423-1.436,0.358-2.173&#13;&#10;&#9;&#9;C75.345,13.264,75.609,13.561,75.873,13.858z M73.726,11.493c0.209,0.682,0.282,1.515,0.229,2.234&#13;&#10;&#9;&#9;c-0.731-1.371-1.414-2.736-1.979-4.077C72.551,10.239,73.137,10.86,73.726,11.493z M78.094,21.083&#13;&#10;&#9;&#9;c0.029-0.019,0.061-0.028,0.09-0.057c0.776-0.752,1.312-1.758,1.438-2.805c0.144,0.172,0.288,0.345,0.43,0.516&#13;&#10;&#9;&#9;c0.006,0.026,0.002,0.044,0.011,0.071c0.347,1.116-0.15,3.101-1.092,3.743c-0.021,0.015-0.032,0.034-0.051,0.05&#13;&#10;&#9;&#9;C78.652,22.099,78.378,21.593,78.094,21.083z M79.445,23.593c-0.019-0.036-0.039-0.072-0.057-0.108&#13;&#10;&#9;&#9;c0.007-0.004,0.014-0.004,0.021-0.009c1.048-0.714,1.841-1.809,2.063-3.008c0.423,0.521,0.831,1.028,1.215,1.51&#13;&#10;&#9;&#9;C82.376,22.972,81.05,23.973,79.445,23.593z" stroke-width="1" fill="undefined"/>
    </g>
  </g>
</svg>