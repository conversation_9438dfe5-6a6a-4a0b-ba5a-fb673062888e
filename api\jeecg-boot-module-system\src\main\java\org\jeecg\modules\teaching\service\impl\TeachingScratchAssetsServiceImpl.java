package org.jeecg.modules.teaching.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.teaching.entity.TeachingScratchAssets;
import org.jeecg.modules.teaching.mapper.TeachingScratchAssetsMapper;
import org.jeecg.modules.teaching.service.ITeachingScratchAssetsService;
import org.springframework.stereotype.Service;

/**
 * @Description: Scratch素材库
 * @Author: jeecg-boot
 * @Date:   2021-09-18
 * @Version: V1.0
 */
@Service
public class TeachingScratchAssetsServiceImpl extends ServiceImpl<TeachingScratchAssetsMapper, TeachingScratchAssets> implements ITeachingScratchAssetsService {

}
