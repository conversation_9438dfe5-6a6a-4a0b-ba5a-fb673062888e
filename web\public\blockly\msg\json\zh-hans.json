{"@metadata": {"authors": [")8", "A Chinese Wikipedian", "Ambet<PERSON>", "DGCK81LNN", "Deathkon", "Duzc2", "<PERSON><PERSON><PERSON>", "Htq110219891", "<PERSON><PERSON><PERSON>", "Liuxinyu970226", "Lu<PERSON><PERSON>cheng", "<PERSON><PERSON><PERSON>", "Qiyue2001", "Shatteredwind", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "WindWood", "<PERSON><PERSON><PERSON>", "Yfdyh000", "佛壁灯", "沈澄心", "아라"]}, "VARIABLES_DEFAULT_NAME": "项目", "UNNAMED_KEY": "未命名", "TODAY": "今天", "DUPLICATE_BLOCK": "复制", "ADD_COMMENT": "添加注释", "REMOVE_COMMENT": "删除注释", "DUPLICATE_COMMENT": "复制注释", "EXTERNAL_INPUTS": "外部输入", "INLINE_INPUTS": "单行输入", "DELETE_BLOCK": "删除块", "DELETE_X_BLOCKS": "删除 %1 个块", "DELETE_ALL_BLOCKS": "删除所有 %1 个块吗？", "CLEAN_UP": "整理块", "COLLAPSE_BLOCK": "折叠块", "COLLAPSE_ALL": "折叠块", "EXPAND_BLOCK": "展开块", "EXPAND_ALL": "展开块", "DISABLE_BLOCK": "禁用块", "ENABLE_BLOCK": "启用块", "HELP": "帮助", "UNDO": "撤销", "REDO": "重做", "CHANGE_VALUE_TITLE": "更改值：", "RENAME_VARIABLE": "重命名变量...", "RENAME_VARIABLE_TITLE": "将所有“%1”变量重命名为:", "NEW_VARIABLE": "创建变量...", "NEW_STRING_VARIABLE": "创建字符串变量...", "NEW_NUMBER_VARIABLE": "创建数字变量...", "NEW_COLOUR_VARIABLE": "创建颜色变量...", "NEW_VARIABLE_TYPE_TITLE": "新变量的类型：", "NEW_VARIABLE_TITLE": "新变量的名称：", "VARIABLE_ALREADY_EXISTS": "名字叫“%1”的变量已经存在了。", "VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE": "名字叫“%1”的变量已经有了另一个类型：“%2”。", "DELETE_VARIABLE_CONFIRMATION": "要删除对变量“%2”的%1个引用吗？", "CANNOT_DELETE_VARIABLE_PROCEDURE": "不能删除变量“%1”，因为它是函数“%2”定义的一部分", "DELETE_VARIABLE": "删除变量“%1”", "COLOUR_PICKER_HELPURL": "https://zh.wikipedia.org/wiki/颜色", "COLOUR_PICKER_TOOLTIP": "从调色板中选择一种颜色。", "COLOUR_RANDOM_TITLE": "随机颜色", "COLOUR_RANDOM_TOOLTIP": "随机选择一种颜色。", "COLOUR_RGB_TITLE": "颜色", "COLOUR_RGB_RED": "红色", "COLOUR_RGB_GREEN": "绿色", "COLOUR_RGB_BLUE": "蓝色", "COLOUR_RGB_TOOLTIP": "通过指定红色、绿色和蓝色的量创建一种颜色。所有的值必须在0和100之间。", "COLOUR_BLEND_TITLE": "混合", "COLOUR_BLEND_COLOUR1": "颜色1", "COLOUR_BLEND_COLOUR2": "颜色2", "COLOUR_BLEND_RATIO": "比例", "COLOUR_BLEND_TOOLTIP": "把两种颜色以一个给定的比例(0.0-1.0)进行混合。", "CONTROLS_REPEAT_HELPURL": "https://zh.wikipedia.org/wiki/For循环", "CONTROLS_REPEAT_TITLE": "重复 %1 次", "CONTROLS_REPEAT_INPUT_DO": "执行", "CONTROLS_REPEAT_TOOLTIP": "多次执行一些语句。", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "当条件满足时重复", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "重复直到条件满足", "CONTROLS_WHILEUNTIL_TOOLTIP_WHILE": "只要值为真，就一直循环执行一些语句。", "CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL": "只要值为假，就一直循环执行一些语句。", "CONTROLS_FOR_TOOLTIP": "用变量%1记录从开始数值到终止数值之间的数值，数值按指定间隔增加，并执行指定的块。", "CONTROLS_FOR_TITLE": "变量 %1 从 %2 数到 %3 每次增加 %4", "CONTROLS_FOREACH_TITLE": "遍历列表 %2 里的每一项 %1", "CONTROLS_FOREACH_TOOLTIP": "遍历列表中的每一项，将变量“%1”设为所选项，并执行一些语句。", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "跳出循环", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "继续下一轮循环", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK": "跳出包含它的循环。", "CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE": "跳过本轮循环的剩余部分，并继进行续下一轮循环。", "CONTROLS_FLOW_STATEMENTS_WARNING": "警告：这个块只能在循环内使用。", "CONTROLS_IF_TOOLTIP_1": "如果值为真，执行一些语句。", "CONTROLS_IF_TOOLTIP_2": "如果值为真，则执行第一块语句。否则，则执行第二块语句。", "CONTROLS_IF_TOOLTIP_3": "如果第一个值为真，则执行第一块的语句。否则，如果第二个值为真，则执行第二块的语句。", "CONTROLS_IF_TOOLTIP_4": "如果第一个值为真，则执行第一块对语句。否则，如果第二个值为真，则执行语句的第二块。如果没有值为真，则执行最后一块的语句。", "CONTROLS_IF_MSG_IF": "如果", "CONTROLS_IF_MSG_ELSEIF": "否则如果", "CONTROLS_IF_MSG_ELSE": "否则", "CONTROLS_IF_IF_TOOLTIP": "增加、删除或重新排列各节来重新配置这个if语句块。", "CONTROLS_IF_ELSEIF_TOOLTIP": "在这个if语句块中增加一个条件。", "CONTROLS_IF_ELSE_TOOLTIP": "在这个if语句块中添加一个最终的，包括所有其余情况的条件。", "IOS_OK": "确定", "IOS_CANCEL": "取消", "IOS_ERROR": "错误", "IOS_PROCEDURES_INPUTS": "输入", "IOS_PROCEDURES_ADD_INPUT": "+ 添加输入", "IOS_PROCEDURES_ALLOW_STATEMENTS": "允许的语句", "IOS_PROCEDURES_DUPLICATE_INPUTS_ERROR": "这个函数有多个输入。", "IOS_VARIABLES_ADD_VARIABLE": "+ 添加变量", "IOS_VARIABLES_ADD_BUTTON": "添加", "IOS_VARIABLES_RENAME_BUTTON": "重命名", "IOS_VARIABLES_DELETE_BUTTON": "删除", "IOS_VARIABLES_VARIABLE_NAME": "变量名", "IOS_VARIABLES_EMPTY_NAME_ERROR": "你不能使用空白的变量名。", "LOGIC_COMPARE_HELPURL": "https://zh.wikipedia.org/wiki/不等", "LOGIC_COMPARE_TOOLTIP_EQ": "如果两个输入结果相等，则返回真。", "LOGIC_COMPARE_TOOLTIP_NEQ": "如果两个输入结果不相等，则返回真。", "LOGIC_COMPARE_TOOLTIP_LT": "如果第一个输入结果比第二个小，则返回真。", "LOGIC_COMPARE_TOOLTIP_LTE": "如果第一个输入结果小于或等于第二个输入结果，则返回真。", "LOGIC_COMPARE_TOOLTIP_GT": "如果第一个输入结果比第二个大，则返回真。", "LOGIC_COMPARE_TOOLTIP_GTE": "如果第一个输入结果大于或等于第二个输入结果，则返回真。", "LOGIC_OPERATION_TOOLTIP_AND": "如果两个输入结果都为真，则返回真。", "LOGIC_OPERATION_AND": "并且", "LOGIC_OPERATION_TOOLTIP_OR": "如果至少有一个输入结果为真，则返回真。", "LOGIC_OPERATION_OR": "或", "LOGIC_NEGATE_HELPURL": "https://github.com/google/blockly/wiki/Logic#not", "LOGIC_NEGATE_TITLE": "%1不成立", "LOGIC_NEGATE_TOOLTIP": "如果输入结果为假，则返回真；如果输入结果为真，则返回假。", "LOGIC_BOOLEAN_TRUE": "真", "LOGIC_BOOLEAN_FALSE": "假", "LOGIC_BOOLEAN_TOOLTIP": "返回真或假。", "LOGIC_NULL": "空", "LOGIC_NULL_TOOLTIP": "返回空值。", "LOGIC_TERNARY_HELPURL": "https://zh.wikipedia.org/wiki/条件运算符", "LOGIC_TERNARY_CONDITION": "断言", "LOGIC_TERNARY_IF_TRUE": "如果为真", "LOGIC_TERNARY_IF_FALSE": "如果为假", "LOGIC_TERNARY_TOOLTIP": "检查“断言”里的条件语句。如果条件为真，则返回“如果为真”的值，否则，则返回“如果为假”的值。", "MATH_NUMBER_HELPURL": "https://zh.wikipedia.org/wiki/数", "MATH_NUMBER_TOOLTIP": "一个数值。", "MATH_ARITHMETIC_HELPURL": "https://zh.wikipedia.org/wiki/算术", "MATH_ARITHMETIC_TOOLTIP_ADD": "返回两个数值的和。", "MATH_ARITHMETIC_TOOLTIP_MINUS": "返回两个数值的差。", "MATH_ARITHMETIC_TOOLTIP_MULTIPLY": "返回两个数值的乘积。", "MATH_ARITHMETIC_TOOLTIP_DIVIDE": "返回两个数值的商。", "MATH_ARITHMETIC_TOOLTIP_POWER": "返回以第一个数值为底数，以第二个数值为幂的结果。", "MATH_SINGLE_HELPURL": "https://zh.wikipedia.org/wiki/平方根", "MATH_SINGLE_OP_ROOT": "平方根", "MATH_SINGLE_TOOLTIP_ROOT": "返回一个数的平方根。", "MATH_SINGLE_OP_ABSOLUTE": "绝对值", "MATH_SINGLE_TOOLTIP_ABS": "返回一个数值的绝对值。", "MATH_SINGLE_TOOLTIP_NEG": "返回一个数值的相反数。", "MATH_SINGLE_TOOLTIP_LN": "返回一个数值的自然对数。", "MATH_SINGLE_TOOLTIP_LOG10": "返回一个数值的以10为底的对数。", "MATH_SINGLE_TOOLTIP_EXP": "返回一个数值的e次幂。", "MATH_SINGLE_TOOLTIP_POW10": "返回一个数值的10次幂。", "MATH_TRIG_HELPURL": "https://zh.wikipedia.org/wiki/三角函数", "MATH_TRIG_TOOLTIP_SIN": "返回指定角度的正弦值（非弧度）。", "MATH_TRIG_TOOLTIP_COS": "返回指定角度的余弦值（非弧度）。", "MATH_TRIG_TOOLTIP_TAN": "返回指定角度的正切值（非弧度）。", "MATH_TRIG_TOOLTIP_ASIN": "返回一个数值的反正弦值。", "MATH_TRIG_TOOLTIP_ACOS": "返回一个数值的反余弦值。", "MATH_TRIG_TOOLTIP_ATAN": "返回一个数值的反正切值。", "MATH_CONSTANT_HELPURL": "https://zh.wikipedia.org/wiki/数学常数", "MATH_CONSTANT_TOOLTIP": "返回一个常见常量：π (3.141…)、e (2.718…)、φ (1.618…)、根号2 (1.414…)、根号二分之一 (0.707…)或∞ (无穷大)。", "MATH_IS_EVEN": "是偶数", "MATH_IS_ODD": "是奇数", "MATH_IS_PRIME": "是质数", "MATH_IS_WHOLE": "是整数", "MATH_IS_POSITIVE": "是正数", "MATH_IS_NEGATIVE": "是负数", "MATH_IS_DIVISIBLE_BY": "可被整除", "MATH_IS_TOOLTIP": "检查一个数值是否是偶数、奇数、质数、自然数、正数、负数或者是否能被某数整除。返回真或假。", "MATH_CHANGE_HELPURL": "https://zh.wikipedia.org/wiki/加法", "MATH_CHANGE_TITLE": "将 %1 增加 %2", "MATH_CHANGE_TOOLTIP": "为变量“%1”增加一个数值。", "MATH_ROUND_HELPURL": "https://zh.wikipedia.org/wiki/数值修约", "MATH_ROUND_TOOLTIP": "数字向上或向下舍入。", "MATH_ROUND_OPERATOR_ROUND": "四舍五入", "MATH_ROUND_OPERATOR_ROUNDUP": "向上舍入", "MATH_ROUND_OPERATOR_ROUNDDOWN": "向下舍入", "MATH_ONLIST_OPERATOR_SUM": "列表中数值的和", "MATH_ONLIST_TOOLTIP_SUM": "返回列表中的所有数值的和。", "MATH_ONLIST_OPERATOR_MIN": "列表最小值", "MATH_ONLIST_TOOLTIP_MIN": "返回列表中最小值。", "MATH_ONLIST_OPERATOR_MAX": "列表最大值", "MATH_ONLIST_TOOLTIP_MAX": "返回列表中最大值。", "MATH_ONLIST_OPERATOR_AVERAGE": "列表平均值", "MATH_ONLIST_TOOLTIP_AVERAGE": "返回列表中的数值的平均值。", "MATH_ONLIST_OPERATOR_MEDIAN": "列表中位数", "MATH_ONLIST_TOOLTIP_MEDIAN": "返回列表中数值的中位数。", "MATH_ONLIST_OPERATOR_MODE": "列表中的众数", "MATH_ONLIST_TOOLTIP_MODE": "返回列表中的出现次数最多的项的列表。", "MATH_ONLIST_OPERATOR_STD_DEV": "列表的标准差", "MATH_ONLIST_TOOLTIP_STD_DEV": "返回列表的标准差。", "MATH_ONLIST_OPERATOR_RANDOM": "列表中的随机一项", "MATH_ONLIST_TOOLTIP_RANDOM": "从列表中返回一个随机的元素。", "MATH_MODULO_HELPURL": "https://zh.wikipedia.org/wiki/模除", "MATH_MODULO_TITLE": "取 %1 ÷ %2 的余数", "MATH_MODULO_TOOLTIP": "返回这两个数字相除后的余数。", "MATH_CONSTRAIN_TITLE": "将 %1 限制在 最低 %2 到最高 %3 之间", "MATH_CONSTRAIN_TOOLTIP": "将一个数值限制在两个指定的数值范围（含边界）之间。", "MATH_RANDOM_INT_HELPURL": "https://zh.wikipedia.org/wiki/随机数生成器", "MATH_RANDOM_INT_TITLE": "从 %1 到 %2 范围内的随机整数", "MATH_RANDOM_INT_TOOLTIP": "返回一个限制在两个指定数值的范围（含边界）之间的随机整数。", "MATH_RANDOM_FLOAT_HELPURL": "https://zh.wikipedia.org/wiki/随机数生成器", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "随机小数", "MATH_RANDOM_FLOAT_TOOLTIP": "返回一个从0.0（含）到1.0（不含）之间的随机数。", "MATH_ATAN2_HELPURL": "https://zh.wikipedia.org/wiki/反正切2", "MATH_ATAN2_TITLE": "点(x:%1,y:%2)的方位角", "MATH_ATAN2_TOOLTIP": "返回点（X，Y）的反正切值，范围为-180到180度。", "TEXT_TEXT_HELPURL": "https://zh.wikipedia.org/wiki/字符串", "TEXT_TEXT_TOOLTIP": "一个字、词语或一行文本。", "TEXT_JOIN_TITLE_CREATEWITH": "创建文本，内容：", "TEXT_JOIN_TOOLTIP": "通过串起任意数量的项以建立一段文本。", "TEXT_CREATE_JOIN_TITLE_JOIN": "拼接", "TEXT_CREATE_JOIN_TOOLTIP": "添加、移除或重新排列各节来重新配置这个文本块。", "TEXT_CREATE_JOIN_ITEM_TOOLTIP": "将一个项添加到文本中。", "TEXT_APPEND_TITLE": "在%1之后加上文本%2", "TEXT_APPEND_TOOLTIP": "将一些文本追加到变量“%1”里。", "TEXT_LENGTH_TITLE": "%1的长度", "TEXT_LENGTH_TOOLTIP": "返回给定文本的字母数（包括空格）。", "TEXT_ISEMPTY_TITLE": "%1是空的", "TEXT_ISEMPTY_TOOLTIP": "如果给定的文本为空，则返回真。", "TEXT_INDEXOF_TOOLTIP": "返回第一个文本段在第二个文本段中的第一/最后一个匹配项的起始位置。如果未找到，则返回%1。", "TEXT_INDEXOF_TITLE": "在文本 %1 里 %2  %3", "TEXT_INDEXOF_OPERATOR_FIRST": "寻找第一次出现的文本", "TEXT_INDEXOF_OPERATOR_LAST": "寻找最后一次出现的文本", "TEXT_CHARAT_TITLE": "在文本%1 里 %2", "TEXT_CHARAT_FROM_START": "获取第#个字符", "TEXT_CHARAT_FROM_END": "获取倒数第#个字符", "TEXT_CHARAT_FIRST": "获取第一个字符", "TEXT_CHARAT_LAST": "获取最后一个字符", "TEXT_CHARAT_RANDOM": "获取随机一个字符", "TEXT_CHARAT_TAIL": "-", "TEXT_CHARAT_TOOLTIP": "返回位于指定位置的字符。", "TEXT_GET_SUBSTRING_TOOLTIP": "返回文本中指定的一部分。", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "从文本", "TEXT_GET_SUBSTRING_START_FROM_START": "获取子串，从第#个字符", "TEXT_GET_SUBSTRING_START_FROM_END": "获取子串，从倒数第#个字符", "TEXT_GET_SUBSTRING_START_FIRST": "获取子串，从第一个字符", "TEXT_GET_SUBSTRING_END_FROM_START": "到第#个字符", "TEXT_GET_SUBSTRING_END_FROM_END": "到倒数第#个字符", "TEXT_GET_SUBSTRING_END_LAST": "到最后一个字符", "TEXT_GET_SUBSTRING_TAIL": "-", "TEXT_CHANGECASE_TOOLTIP": "用不同的大小写模式复制并返回这段文字。", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "转为大写", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "转为小写", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "转为首字母大写", "TEXT_TRIM_TOOLTIP": "从某一端或同时从两端删除多余的空白，并返回这段文字的一个副本。", "TEXT_TRIM_OPERATOR_BOTH": "消除其两侧的空白", "TEXT_TRIM_OPERATOR_LEFT": "消除其左侧的空白", "TEXT_TRIM_OPERATOR_RIGHT": "消除其右侧的空白", "TEXT_PRINT_TITLE": "输出%1", "TEXT_PRINT_TOOLTIP": "输出指定的文字、数字或其他值。", "TEXT_PROMPT_TYPE_TEXT": "要求输入文本，并显示提示消息", "TEXT_PROMPT_TYPE_NUMBER": "要求输入数字，并显示提示消息", "TEXT_PROMPT_TOOLTIP_NUMBER": "要求用户输入数字。", "TEXT_PROMPT_TOOLTIP_TEXT": "要求用户输入一些文本。", "TEXT_COUNT_MESSAGE0": "计算%1在%2里出现的次数", "TEXT_COUNT_HELPURL": "https://github.com/google/blockly/wiki/Text#counting-substrings", "TEXT_COUNT_TOOLTIP": "计算在一段文本中，某个部分文本重复出现了多少次。", "TEXT_REPLACE_MESSAGE0": "把%3中的%1替换为%2", "TEXT_REPLACE_HELPURL": "https://github.com/google/blockly/wiki/Text#replacing-substrings", "TEXT_REPLACE_TOOLTIP": "在一段文本中，将出现过的某部分文本都替换掉。", "TEXT_REVERSE_MESSAGE0": "倒转文本%1", "TEXT_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Text#reversing-text", "TEXT_REVERSE_TOOLTIP": "将文本中各个字符的顺序倒转。", "LISTS_CREATE_EMPTY_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-empty-list", "LISTS_CREATE_EMPTY_TITLE": "创建空列表", "LISTS_CREATE_EMPTY_TOOLTIP": "返回一个列表，长度为 0，不包含任何数据记录", "LISTS_CREATE_WITH_HELPURL": "https://github.com/google/blockly/wiki/Lists#create-list-with", "LISTS_CREATE_WITH_TOOLTIP": "建立一个具有任意数量项目的列表。", "LISTS_CREATE_WITH_INPUT_WITH": "创建列表，内容：", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "列表", "LISTS_CREATE_WITH_CONTAINER_TOOLTIP": "增加、删除或重新排列各部分以此重新配置这个列表块。", "LISTS_CREATE_WITH_ITEM_TOOLTIP": "将一个项添加到列表中。", "LISTS_REPEAT_TOOLTIP": "建立包含指定重复次数的值的列表。", "LISTS_REPEAT_TITLE": "建立列表使用项 %1 重复 %2 次", "LISTS_LENGTH_TITLE": "%1的长度", "LISTS_LENGTH_TOOLTIP": "返回列表的长度。", "LISTS_ISEMPTY_TITLE": "%1是空的", "LISTS_ISEMPTY_TOOLTIP": "如果改列表为空，则返回真。", "LISTS_INLIST": "在列表中", "LISTS_INDEX_OF_FIRST": "寻找第一次出现的项", "LISTS_INDEX_OF_LAST": "寻找最后一次出现的项", "LISTS_INDEX_OF_TOOLTIP": "返回在列表中的第一/最后一个匹配项的索引值。如果找不到项目则返回%1。", "LISTS_GET_INDEX_GET": "取得", "LISTS_GET_INDEX_GET_REMOVE": "取得并移除", "LISTS_GET_INDEX_REMOVE": "移除", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "倒数第#项", "LISTS_GET_INDEX_FIRST": "第一项", "LISTS_GET_INDEX_LAST": "最后一项", "LISTS_GET_INDEX_RANDOM": "随机的一项", "LISTS_GET_INDEX_TAIL": "-", "LISTS_INDEX_FROM_START_TOOLTIP": "%1是第一项。", "LISTS_INDEX_FROM_END_TOOLTIP": "%1是最后一项。", "LISTS_GET_INDEX_TOOLTIP_GET_FROM": "返回在列表中的指定位置的项。", "LISTS_GET_INDEX_TOOLTIP_GET_FIRST": "返回列表中的第一项。", "LISTS_GET_INDEX_TOOLTIP_GET_LAST": "返回列表中的最后一项。", "LISTS_GET_INDEX_TOOLTIP_GET_RANDOM": "返回列表中的随机一项。", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM": "移除并返回列表中的指定位置的项。", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST": "移除并返回列表中的第一项。", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST": "移除并返回列表中的最后一项。", "LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM": "移除并返回列表中的随机一项。", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM": "移除在列表中的指定位置的项。", "LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST": "移除列表中的第一项", "LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST": "移除列表中的最后一项", "LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM": "删除列表中的随机一项。", "LISTS_SET_INDEX_SET": "设置", "LISTS_SET_INDEX_INSERT": "插入在", "LISTS_SET_INDEX_INPUT_TO": "值为", "LISTS_SET_INDEX_TOOLTIP_SET_FROM": "设置在列表中指定位置的项。", "LISTS_SET_INDEX_TOOLTIP_SET_FIRST": "设置列表中的第一项。", "LISTS_SET_INDEX_TOOLTIP_SET_LAST": "设置列表中的最后一项。", "LISTS_SET_INDEX_TOOLTIP_SET_RANDOM": "设置列表中的随机一项。", "LISTS_SET_INDEX_TOOLTIP_INSERT_FROM": "在列表中指定位置插入项。", "LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST": "在列表的起始处添加该项。", "LISTS_SET_INDEX_TOOLTIP_INSERT_LAST": "在列表的末尾处添加该项。", "LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM": "在列表的随机位置插入该项。", "LISTS_GET_SUBLIST_START_FROM_START": "获取子列表，从第#项", "LISTS_GET_SUBLIST_START_FROM_END": "获取子列表，从倒数第#项", "LISTS_GET_SUBLIST_START_FIRST": "获取子列表，从第一项", "LISTS_GET_SUBLIST_END_FROM_START": "到第#项", "LISTS_GET_SUBLIST_END_FROM_END": "到倒数第#项", "LISTS_GET_SUBLIST_END_LAST": "到最后一项", "LISTS_GET_SUBLIST_TAIL": "-", "LISTS_GET_SUBLIST_TOOLTIP": "复制列表中指定的部分。", "LISTS_SORT_HELPURL": "https://github.com/google/blockly/wiki/Lists#sorting-a-list", "LISTS_SORT_TITLE": "排序%1 %2 %3", "LISTS_SORT_TOOLTIP": "排序一个列表，返回副本。", "LISTS_SORT_ORDER_ASCENDING": "升序", "LISTS_SORT_ORDER_DESCENDING": "降序", "LISTS_SORT_TYPE_NUMERIC": "按数字", "LISTS_SORT_TYPE_TEXT": "按字母", "LISTS_SORT_TYPE_IGNORECASE": "按字母（忽略大小写）", "LISTS_SPLIT_HELPURL": "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists", "LISTS_SPLIT_LIST_FROM_TEXT": "从文本制作列表", "LISTS_SPLIT_TEXT_FROM_LIST": "将列表合并为文本", "LISTS_SPLIT_WITH_DELIMITER": "分隔符：", "LISTS_SPLIT_TOOLTIP_SPLIT": "将文本按指定的分隔符拆分为文本组成的列表。", "LISTS_SPLIT_TOOLTIP_JOIN": "加入文本列表至一个文本，由分隔符分隔。", "LISTS_REVERSE_HELPURL": "https://github.com/google/blockly/wiki/Lists#reversing-a-list", "LISTS_REVERSE_MESSAGE0": "倒转%1", "LISTS_REVERSE_TOOLTIP": "倒转一个列表，返回副本。", "ORDINAL_NUMBER_SUFFIX": "-", "VARIABLES_GET_TOOLTIP": "返回此变量的值。", "VARIABLES_GET_CREATE_SET": "创建“设定%1”", "VARIABLES_SET": "赋值 %1 为 %2", "VARIABLES_SET_TOOLTIP": "设置此变量，以使它和输入值相等。", "VARIABLES_SET_CREATE_GET": "创建“获得%1”", "PROCEDURES_DEFNORETURN_HELPURL": "https://zh.wikipedia.org/wiki/子程序", "PROCEDURES_DEFNORETURN_TITLE": "至", "PROCEDURES_DEFNORETURN_PROCEDURE": "做点什么", "PROCEDURES_BEFORE_PARAMS": "与：", "PROCEDURES_CALL_BEFORE_PARAMS": "与：", "PROCEDURES_DEFNORETURN_DO": "-", "PROCEDURES_DEFNORETURN_TOOLTIP": "创建一个不带输出值的函数。", "PROCEDURES_DEFNORETURN_COMMENT": "描述该功能...", "PROCEDURES_DEFRETURN_HELPURL": "https://zh.wikipedia.org/wiki/子程序", "PROCEDURES_DEFRETURN_RETURN": "返回", "PROCEDURES_DEFRETURN_TOOLTIP": "创建一个有输出值的函数。", "PROCEDURES_ALLOW_STATEMENTS": "允许声明", "PROCEDURES_DEF_DUPLICATE_WARNING": "警告：此函数具有重复参数。", "PROCEDURES_CALLNORETURN_HELPURL": "https://zh.wikipedia.org/wiki/子程序", "PROCEDURES_CALLNORETURN_TOOLTIP": "运行用户定义的函数“%1”。", "PROCEDURES_CALLRETURN_HELPURL": "https://zh.wikipedia.org/wiki/子程序", "PROCEDURES_CALLRETURN_TOOLTIP": "运行用户定义的函数“%1”，并使用它的输出值。", "PROCEDURES_MUTATORCONTAINER_TITLE": "输入", "PROCEDURES_MUTATORCONTAINER_TOOLTIP": "添加、移除或重新排此函数的输入。", "PROCEDURES_MUTATORARG_TITLE": "输入名称：", "PROCEDURES_MUTATORARG_TOOLTIP": "添加函数输入。", "PROCEDURES_HIGHLIGHT_DEF": "突出显示函数定义", "PROCEDURES_CREATE_DO": "创建“%1”", "PROCEDURES_IFRETURN_TOOLTIP": "如果值为真，则返回第二个值。", "PROCEDURES_IFRETURN_HELPURL": "http://c2.com/cgi/wiki?GuardClause", "PROCEDURES_IFRETURN_WARNING": "警告：这个块只能在函数内部使用。", "WORKSPACE_COMMENT_DEFAULT_TEXT": "说点什么...", "WORKSPACE_ARIA_LABEL": "Blockly工作区", "COLLAPSED_WARNINGS_WARNING": "已收起的信息块内包含警告。", "DIALOG_OK": "确认", "DIALOG_CANCEL": "取消"}