package org.jeecg.modules.teaching.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.teaching.entity.ExamPaper;
import org.jeecg.modules.teaching.entity.ExamRecord;
import org.jeecg.modules.teaching.service.IExamPaperService;
import org.jeecg.modules.teaching.service.IExamRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

/**
 * @Description: 在线考试
 * @Author: jeecg-boot
 * @Date:   2023-08-30
 * @Version: V1.0
 */
@Api(tags="在线考试")
@RestController
@RequestMapping("/teaching/examSystem/onlineExam")
@Slf4j
public class ExamOnlineController extends JeecgController<ExamRecord, IExamRecordService> {

    @Autowired
    private IExamRecordService examRecordService;

    @Autowired
    private IExamPaperService examPaperService;

    /**
     * 获取在线考试列表
     *
     * @param subject 科目
     * @param level 级别
     * @param type 类型
     * @param year 年份
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 试卷列表
     */
    @AutoLog(value = "在线考试-获取考试列表")
    @ApiOperation(value = "在线考试-获取考试列表", notes = "获取可用于在线考试的试卷列表")
    @GetMapping(value = "/list")
    public Result<?> getExamList(
            @ApiParam(value = "科目", required = false) @RequestParam(required = false) String subject,
            @ApiParam(value = "级别", required = false) @RequestParam(required = false) String level,
            @ApiParam(value = "类型", required = false) @RequestParam(required = false) String type,
            @ApiParam(value = "年份", required = false) @RequestParam(required = false) String year,
            @ApiParam(value = "页码", required = false) @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页数量", required = false) @RequestParam(name = "pageSize", defaultValue = "12") Integer pageSize) {

        try {
            // 构建查询条件
            QueryWrapper<ExamPaper> queryWrapper = new QueryWrapper<>();

            // 按科目筛选
            if (subject != null && !subject.isEmpty()) {
                queryWrapper.eq("subject", subject);
            }

            // 按级别筛选
            if (level != null && !level.isEmpty()) {
                queryWrapper.eq("level", level);
            }

            // 按类型筛选
            if (type != null && !type.isEmpty()) {
                queryWrapper.eq("type", type);
            }

            // 按年份筛选
            if (year != null && !year.isEmpty()) {
                queryWrapper.eq("year", year);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<ExamPaper> page = new Page<>(pageNo, pageSize);
            IPage<ExamPaper> pageList = examPaperService.page(page, queryWrapper);

            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("获取考试列表失败", e);
            return Result.error("获取考试列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取试卷详情
     *
     * @param paperId 试卷ID
     * @return 试卷信息及题目列表
     */
    @AutoLog(value = "在线考试-获取试卷详情")
    @ApiOperation(value = "在线考试-获取试卷详情", notes = "获取试卷信息及题目列表（隐去答案和解析）")
    @GetMapping(value = "/getPaperDetail/{paperId}")
    public Result<?> getPaperDetail(@PathVariable String paperId) {
        return examRecordService.getPaperDetail(paperId);
    }

    // 注意：已移除开始考试接口，因为不再需要"进行中"状态
    // 考试记录将在提交时直接创建

    /**
     * 提交试卷
     *
     * @param params 提交参数
     * @param request HTTP请求
     * @return 考试结果
     */
    @AutoLog(value = "在线考试-提交试卷")
    @ApiOperation(value = "在线考试-提交试卷", notes = "提交答案，计算分数，直接创建考试记录")
    @PostMapping(value = "/submit")
    public Result<?> submitExam(@RequestBody JSONObject params, HttpServletRequest request) {
        String paperId = params.getString("paperId");
        String startTimeStr = params.getString("startTime");
        Map<String, Object> answers = params.getJSONObject("answers");

        if (paperId == null || paperId.isEmpty()) {
            return Result.error("试卷ID不能为空");
        }

        if (startTimeStr == null || startTimeStr.isEmpty()) {
            return Result.error("考试开始时间不能为空");
        }

        if (answers == null || answers.isEmpty()) {
            return Result.error("答案不能为空");
        }

        // 获取当前登录用户ID
        LoginUser loginUser = getCurrentUser();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        String userId = loginUser.getId();

        try {
            // 解析开始时间
            Date startTime = new Date(Long.parseLong(startTimeStr));

            return examRecordService.submitExam(userId, paperId, startTime, answers);
        } catch (Exception e) {
            log.error("提交试卷失败", e);
            return Result.error("提交试卷失败: " + e.getMessage());
        }
    }
} 