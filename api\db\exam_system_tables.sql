 -- 考试系统数据库表创建脚本
-- 创建日期：2023-06-17

-- 题库表
CREATE TABLE IF NOT EXISTS `exam_question` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '题目描述/标题',
  `question_type` int(1) NOT NULL COMMENT '题目类型 (1:单选题, 2:判断题, 3:编程题)',
  `subject` varchar(100) NOT NULL COMMENT '所属科目 (Scratch, Python, C++)',
  `level` varchar(50) NOT NULL COMMENT '题目级别 (Scratch:1-4, Python/C++:1-8)',
  `difficulty` int(1) NOT NULL DEFAULT 1 COMMENT '难度 (1:简单, 2:中等, 3:困难)',
  `content` longtext NOT NULL COMMENT '题目内容 (JSON格式)',
  `author` varchar(100) DEFAULT NULL COMMENT '作者/出题人',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_subject_level` (`subject`, `level`),
  KEY `idx_question_type` (`question_type`),
  KEY `idx_difficulty` (`difficulty`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题库表';

-- 试卷表
CREATE TABLE IF NOT EXISTS `exam_paper` (
  `id` varchar(36) NOT NULL COMMENT '主键ID (试卷ID)',
  `title` varchar(255) NOT NULL COMMENT '试卷标题',
  `subject` varchar(100) NOT NULL COMMENT '所属科目 (Scratch, Python, C++)',
  `level` varchar(50) NOT NULL COMMENT '所属级别 (Scratch:1-4, Python/C++:1-8)',
  `difficulty` int(1) NOT NULL DEFAULT 1 COMMENT '难度 (1:简单, 2:中等, 3:困难)',
  `type` varchar(50) NOT NULL COMMENT '类型 (真题或模拟)',
  `year` int(4) NOT NULL COMMENT '年份',
  `author` varchar(100) DEFAULT NULL COMMENT '作者',
  `exam_duration` int(11) NOT NULL DEFAULT 120 COMMENT '考试时长 (分钟)',
  `content` longtext NOT NULL COMMENT '试卷内容 (JSON格式，存储题目ID列表及分数)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_subject_level` (`subject`, `level`),
  KEY `idx_type_year` (`type`, `year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='试卷表';

-- 考试记录表
CREATE TABLE IF NOT EXISTS `exam_record` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '考生ID',
  `paper_id` varchar(36) NOT NULL COMMENT '试卷ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `score` int(11) DEFAULT NULL COMMENT '考试得分',
  `answers` longtext DEFAULT NULL COMMENT '用户答案 (JSON格式)',
  `status` int(1) NOT NULL DEFAULT 1 COMMENT '状态 (1:进行中, 2:已提交, 3:已批阅)',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_paper_id` (`paper_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考试记录表';

-- 错题记录表
CREATE TABLE IF NOT EXISTS `exam_mistake` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `question_id` varchar(36) NOT NULL COMMENT '题目ID',
  `last_answer` text DEFAULT NULL COMMENT '上次错误答案',
  `mistake_count` int(11) NOT NULL DEFAULT 1 COMMENT '错误次数',
  `last_mistake_time` datetime NOT NULL COMMENT '最近一次答错时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_question` (`user_id`, `question_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错题记录表';