{"@metadata": {"author": "<PERSON> <<EMAIL>>", "lastupdated": "2014-03-24 23:00:00.000000", "locale": "tlh", "messagedocumentation": "qqq"}, "VARIABLES_DEFAULT_NAME": "<PERSON><PERSON>", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_BLOCK": "velqa' chenmoH", "ADD_COMMENT": "QInHom chel", "REMOVE_COMMENT": "QInHom chelHa'", "EXTERNAL_INPUTS": "<PERSON>r rar", "INLINE_INPUTS": "qoD rar", "DELETE_BLOCK": "<PERSON><PERSON><PERSON>'", "DELETE_X_BLOCKS": "%1 ng<PERSON><PERSON><PERSON>'", "DELETE_ALL_BLOCKS": "Hoch %1 ngog<PERSON><PERSON>'?", "CLEAN_UP": "ngoghmeyvaD tlhegh rurmoH", "COLLAPSE_BLOCK": "<PERSON><PERSON><PERSON>", "COLLAPSE_ALL": "<PERSON><PERSON><PERSON><PERSON>", "EXPAND_BLOCK": "<PERSON><PERSON><PERSON>moH", "EXPAND_ALL": "<PERSON><PERSON><PERSON><PERSON>H", "DISABLE_BLOCK": "<PERSON><PERSON><PERSON>", "ENABLE_BLOCK": "ngogh <PERSON>'", "HELP": "QaH", "UNDO": "vangHa'", "REDO": "<PERSON><PERSON><PERSON>'", "CHANGE_VALUE_TITLE": "choH:", "NEW_VARIABLE": "lIw chu'...", "NEW_VARIABLE_TITLE": "lIw chu' pong:", "RENAME_VARIABLE": "lIw pong choH...", "RENAME_VARIABLE_TITLE": "Hoch \"%1\" lIwmey pongmey choH:", "COLOUR_RANDOM_TITLE": "rItlh vISaHbe'", "COLOUR_RGB_TITLE": "rItlh wIv", "COLOUR_RGB_RED": "'Iw rItlh", "COLOUR_RGB_GREEN": "tI rItlh", "COLOUR_RGB_BLUE": "chal rItlh", "COLOUR_BLEND_TITLE": "DuD", "COLOUR_BLEND_COLOUR1": "rItlh wa'", "COLOUR_BLEND_COLOUR2": "rItlh cha'", "COLOUR_BLEND_RATIO": "'ar", "CONTROLS_REPEAT_TITLE": "%1-logh qaSmoH", "CONTROLS_REPEAT_INPUT_DO": "ruch", "CONTROLS_WHILEUNTIL_OPERATOR_WHILE": "teHtaHvIS qaSmoH", "CONTROLS_WHILEUNTIL_OPERATOR_UNTIL": "teHpa' qaSmoH", "CONTROLS_FOR_TITLE": "togh %1 mung %2 ghoch %3 Do %4", "CONTROLS_FOREACH_TITLE": "ngIq Doch %1 ngaSbogh tetlh %2 nuDDI'", "CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK": "gho Haw'", "CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE": "gho ta<PERSON><PERSON>'", "CONTROLS_FLOW_STATEMENTS_WARNING": "yIqIm! ghoDaq neH ngoghvam lo'laH vay'.", "CONTROLS_IF_MSG_IF": "teHchugh", "CONTROLS_IF_MSG_ELSEIF": "pagh teHchugh", "CONTROLS_IF_MSG_ELSE": "pagh", "LOGIC_OPERATION_AND": "'ej", "LOGIC_OPERATION_OR": "qoj", "LOGIC_NEGATE_TITLE": "yoymoH %1", "LOGIC_BOOLEAN_TRUE": "teH", "LOGIC_BOOLEAN_FALSE": "teHbe'", "LOGIC_NULL": "paghna'", "LOGIC_TERNARY_CONDITION": "chov", "LOGIC_TERNARY_IF_TRUE": "teHchugh", "LOGIC_TERNARY_IF_FALSE": "te<PERSON><PERSON>'chugh", "MATH_ADDITION_SYMBOL": "+", "MATH_SUBTRACTION_SYMBOL": "-", "MATH_DIVISION_SYMBOL": "÷", "MATH_MULTIPLICATION_SYMBOL": "×", "MATH_POWER_SYMBOL": "^", "MATH_TRIG_SIN": "sin", "MATH_TRIG_COS": "cos", "MATH_TRIG_TAN": "tan", "MATH_TRIG_ASIN": "asin", "MATH_TRIG_ACOS": "acos", "MATH_TRIG_ATAN": "atan", "MATH_SINGLE_OP_ROOT": "cha'DIch wav", "MATH_SINGLE_OP_ABSOLUTE": "Dung pagh choH", "MATH_IS_EVEN": "lang'a' mI'", "MATH_IS_ODD": "ror'a' mI'", "MATH_IS_PRIME": "potlh'a' mI'", "MATH_IS_WHOLE": "ngoHlaHbe''a'", "MATH_IS_POSITIVE": "Dung pagh", "MATH_IS_NEGATIVE": "bIng pagh", "MATH_IS_DIVISIBLE_BY": "wav'a'", "MATH_CHANGE_TITLE": "choH %1 chel %2", "MATH_ROUND_OPERATOR_ROUND": "ngoH", "MATH_ROUND_OPERATOR_ROUNDUP": "Dung ngoH", "MATH_ROUND_OPERATOR_ROUNDDOWN": "bIng ngoH", "MATH_ONLIST_OPERATOR_SUM": "chelwI' SIm tetlh", "MATH_ONLIST_OPERATOR_MIN": "machwI''a' SIm tetlh", "MATH_ONLIST_OPERATOR_MAX": "tInwI''a' SIm tetlh", "MATH_ONLIST_OPERATOR_AVERAGE": "beQwI' SIm tetlh", "MATH_ONLIST_OPERATOR_MEDIAN": "beQwI'botlh SIm tetlh", "MATH_ONLIST_OPERATOR_MODE": "beQwI' motlh SIm tetlh", "MATH_ONLIST_OPERATOR_STD_DEV": "motlhbe'wI' SIm tetlh", "MATH_ONLIST_OPERATOR_RANDOM": "SaHbe' SIm tetlh", "MATH_MODULO_TITLE": "ratlwI' SIm %1 ÷ %2", "MATH_CONSTRAIN_TITLE": "jon %1 bIng %2 Dung %3", "MATH_RANDOM_INT_TITLE": "ngoH mI'SaHbe' bIng %1 Dung %2", "MATH_RANDOM_FLOAT_TITLE_RANDOM": "mI'HomSaHbe'", "TEXT_JOIN_TITLE_CREATEWITH": "ghItlh ghom", "TEXT_CREATE_JOIN_TITLE_JOIN": "ghom", "TEXT_APPEND_TITLE": "chel %1 ghItlh %2", "TEXT_LENGTH_TITLE": "chuq %1", "TEXT_ISEMPTY_TITLE": "%1 chIm'a'", "TEXT_INDEXOF_TITLE": "ghItlhDaq %1 %2 %3", "TEXT_INDEXOF_OPERATOR_FIRST": "ghItlh wa'DIch Sam", "TEXT_INDEXOF_OPERATOR_LAST": "gh<PERSON><PERSON><PERSON> <PERSON>av <PERSON>", "TEXT_CHARAT_FROM_START": "mu'Hom #", "TEXT_CHARAT_FROM_END": "mu'Hom # Qav", "TEXT_CHARAT_FIRST": "mu'<PERSON><PERSON> wa'D<PERSON>ch", "TEXT_CHARAT_LAST": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TEXT_CHARAT_RANDOM": "mu'Ho<PERSON>'", "TEXT_CHARAT_TAIL": "Suq", "TEXT_GET_SUBSTRING_INPUT_IN_TEXT": "ghItlhDaq", "TEXT_GET_SUBSTRING_START_FROM_START": "ghItlhHom moHaq mu'Hom #", "TEXT_GET_SUBSTRING_START_FROM_END": "ghItlhHom moHaq mu'Hom # Qav", "TEXT_GET_SUBSTRING_START_FIRST": "ghItlhHom moHaq mu'Hom wa'DIch", "TEXT_GET_SUBSTRING_END_FROM_START": "mojaq mu'Hom #", "TEXT_GET_SUBSTRING_END_FROM_END": "mojaq mu'Hom # Qav", "TEXT_GET_SUBSTRING_END_LAST": "mojaq mu<PERSON><PERSON><PERSON>", "TEXT_GET_SUBSTRING_TAIL": "Suq", "TEXT_CHANGECASE_OPERATOR_UPPERCASE": "tInchoH", "TEXT_CHANGECASE_OPERATOR_LOWERCASE": "machchoH", "TEXT_CHANGECASE_OPERATOR_TITLECASE": "Doj<PERSON><PERSON>", "TEXT_TRIM_OPERATOR_BOTH": "poSnIHlogh pei", "TEXT_TRIM_OPERATOR_LEFT": "po<PERSON><PERSON>h pei", "TEXT_TRIM_OPERATOR_RIGHT": "nIHlogh pei", "TEXT_PRINT_TITLE": "maq %1", "TEXT_PROMPT_TYPE_TEXT": "ghItln tlhob 'ej maq", "TEXT_PROMPT_TYPE_NUMBER": "mI' t<PERSON><PERSON><PERSON> 'ej maq", "LISTS_CREATE_EMPTY_TITLE": "tetlh chIm", "LISTS_CREATE_WITH_INPUT_WITH": "tetlh ghom", "LISTS_CREATE_WITH_CONTAINER_TITLE_ADD": "tetlh", "LISTS_REPEAT_TITLE": "tetlh ghom %2 Dochmey %1 pus", "LISTS_LENGTH_TITLE": "chuq %1", "LISTS_ISEMPTY_TITLE": "%1 chIm'a'", "LISTS_INLIST": "tetlhDaq", "LISTS_INDEX_OF_FIRST": "<PERSON>h sam wa'DIch", "LISTS_INDEX_OF_LAST": "<PERSON><PERSON> sam <PERSON>", "LISTS_GET_INDEX_GET": "Suq", "LISTS_GET_INDEX_GET_REMOVE": "Suq vaj pej", "LISTS_GET_INDEX_REMOVE": "pej", "LISTS_GET_INDEX_FROM_START": "#", "LISTS_GET_INDEX_FROM_END": "# Qav", "LISTS_GET_INDEX_FIRST": "wa'DIch", "LISTS_GET_INDEX_LAST": "<PERSON><PERSON>", "LISTS_GET_INDEX_RANDOM": "Sa<PERSON>be'", "LISTS_GET_INDEX_TAIL": "", "LISTS_SET_INDEX_SET": "choH", "LISTS_SET_INDEX_INSERT": "lIH", "LISTS_SET_INDEX_INPUT_TO": "<PERSON><PERSON>", "LISTS_GET_SUBLIST_START_FROM_START": "tetlhHom moHaq #", "LISTS_GET_SUBLIST_START_FROM_END": "tetlhHom moHaq # Qav", "LISTS_GET_SUBLIST_START_FIRST": "tetlhHom moHaq wa'DIch", "LISTS_GET_SUBLIST_END_FROM_START": "mojaQ #", "LISTS_GET_SUBLIST_END_FROM_END": "mojaQ # Qav", "LISTS_GET_SUBLIST_END_LAST": "<PERSON><PERSON><PERSON>", "LISTS_GET_SUBLIST_TAIL": "Suq", "LISTS_SPLIT_LIST_FROM_TEXT": "tetlh ghermeH ghItlh wav", "LISTS_SPLIT_TEXT_FROM_LIST": "ghItlh chenmoHmeH tetlh gherHa'", "LISTS_SPLIT_WITH_DELIMITER": "rarwI'Hom lo'", "ORDINAL_NUMBER_SUFFIX": "", "VARIABLES_GET_CREATE_SET": "chel 'choH %1'", "VARIABLES_SET": "choH %1 %2", "VARIABLES_SET_CREATE_GET": "chel 'Suq %1'", "PROCEDURES_DEFNORETURN_TITLE": "ruch", "PROCEDURES_DEFNORETURN_PROCEDURE": "mIw", "PROCEDURES_BEFORE_PARAMS": "qel:", "PROCEDURES_CALL_BEFORE_PARAMS": "qel:", "PROCEDURES_DEFNORETURN_DO": "", "PROCEDURES_DEFNORETURN_COMMENT": "mIw yIDel...", "PROCEDURES_DEFRETURN_RETURN": "chegh", "PROCEDURES_ALLOW_STATEMENTS": "mu'tlhegh chaw'", "PROCEDURES_DEF_DUPLICATE_WARNING": "ghuHmoHna': qelw<PERSON>' cha'logh chen.", "PROCEDURES_MUTATORCONTAINER_TITLE": "qelw<PERSON>'mey", "PROCEDURES_MUTATORARG_TITLE": "pong:", "PROCEDURES_HIGHLIGHT_DEF": "mIwna' wew", "PROCEDURES_CREATE_DO": "chel '%1'", "PROCEDURES_IFRETURN_WARNING": "ghoHmoHna': ngoghvam ngaSbe' mIwDaq."}