# Teaching系统开发工作流程

## 开发环境准备

### 1. 必需软件
- **JDK**: 1.8+
- **Maven**: 3.6+
- **Node.js**: 14+
- **MySQL**: 5.7+
- **Redis**: 6.0+
- **IDE**: IntelliJ IDEA 或 Eclipse

### 2. 项目启动步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd teaching

# 2. 启动数据库和Redis
docker-compose up -d mysql redis

# 3. 导入数据库
mysql -u root -p teaching < db/teaching.sql

# 4. 启动后端
cd api
mvn clean compile
mvn spring-boot:run

# 5. 启动前端
cd web
npm install
npm run serve

# 6. 启动HOJ服务（如需判题功能）
cd HOJ/hoj-springboot
docker-compose up -d
```

## 新功能开发流程

### 1. 需求分析阶段
- [ ] 明确功能需求和业务逻辑
- [ ] 设计数据库表结构
- [ ] 设计API接口规范
- [ ] 确定前端页面结构

### 2. 后端开发阶段
```bash
# 开发顺序
1. 创建Entity实体类
2. 创建Mapper接口
3. 创建Service接口和实现
4. 创建Controller控制器
5. 编写单元测试
6. 集成测试验证
```

**Entity开发模板**:
```java
@Data
@TableName("exam_new_feature")
@ApiModel(value = "NewFeature对象", description = "新功能")
public class NewFeature implements Serializable {
    
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "功能名称")
    private String name;
    
    // 基础字段
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
```

### 3. 前端开发阶段
```bash
# 开发顺序
1. 创建API接口文件
2. 创建页面组件
3. 创建表单组件
4. 实现CRUD功能
5. 添加权限控制
6. 样式优化
```

**API接口模板**:
```javascript
// api/newFeature.js
import { axios } from '@/utils/request'

const api = {
  list: '/teaching/newFeature/list',
  add: '/teaching/newFeature/add',
  edit: '/teaching/newFeature/edit',
  delete: '/teaching/newFeature/delete',
  deleteBatch: '/teaching/newFeature/deleteBatch'
}

export const getNewFeatureList = (params) => {
  return axios({
    url: api.list,
    method: 'get',
    params
  })
}

export const addNewFeature = (data) => {
  return axios({
    url: api.add,
    method: 'post',
    data
  })
}

export default api
```

## 代码提交规范

### 1. Git提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(exam): 添加题目自动格式化功能

- 实现题目模板自动格式化
- 支持多种题型格式识别
- 添加格式化结果预览

Closes #123
```

### 2. 分支管理策略
```
master/main     # 主分支，生产环境代码
develop         # 开发分支，集成最新功能
feature/*       # 功能分支，开发新功能
hotfix/*        # 热修复分支，紧急修复
release/*       # 发布分支，准备发布版本
```

**分支操作流程**:
```bash
# 1. 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/exam-auto-format

# 2. 开发完成后提交
git add .
git commit -m "feat(exam): 添加题目自动格式化功能"
git push origin feature/exam-auto-format

# 3. 创建Pull Request
# 4. 代码审查通过后合并到develop
# 5. 删除功能分支
git branch -d feature/exam-auto-format
```

## 代码审查清单

### 1. 后端代码审查
- [ ] 是否遵循命名规范
- [ ] 是否添加了必要的注释和文档
- [ ] 是否正确处理异常
- [ ] 是否添加了日志记录
- [ ] 是否进行了参数验证
- [ ] 是否正确使用事务
- [ ] 是否存在SQL注入风险
- [ ] 是否正确使用Result返回值（避免重载陷阱）
- [ ] 是否添加了权限控制
- [ ] 是否编写了单元测试

### 2. 前端代码审查
- [ ] 组件命名是否规范
- [ ] 是否正确处理异步请求
- [ ] 是否添加了错误处理
- [ ] 是否进行了表单验证
- [ ] 是否正确使用Vue响应式特性
- [ ] 是否存在内存泄漏风险
- [ ] 样式是否使用scoped
- [ ] 是否遵循无障碍访问标准

## 测试策略

### 1. 单元测试
```java
@SpringBootTest
@Transactional
class NewFeatureServiceTest {
    
    @Autowired
    private INewFeatureService newFeatureService;
    
    @Test
    @DisplayName("测试新功能创建")
    void testCreateNewFeature() {
        // Given
        NewFeature feature = new NewFeature();
        feature.setName("测试功能");
        
        // When
        boolean result = newFeatureService.save(feature);
        
        // Then
        assertTrue(result);
        assertNotNull(feature.getId());
    }
}
```

### 2. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class NewFeatureControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testNewFeatureApi() {
        // 测试API完整流程
    }
}
```

### 3. 前端测试
```javascript
// tests/unit/NewFeature.spec.js
import { shallowMount } from '@vue/test-utils'
import NewFeature from '@/views/NewFeature.vue'

describe('NewFeature.vue', () => {
  it('renders correctly', () => {
    const wrapper = shallowMount(NewFeature)
    expect(wrapper.find('.new-feature').exists()).toBe(true)
  })
})
```

## 性能优化指南

### 1. 后端优化
- 使用分页查询避免大量数据加载
- 合理使用缓存减少数据库访问
- 优化SQL查询，添加必要索引
- 使用异步处理耗时操作
- 合理设置连接池参数

### 2. 前端优化
- 使用懒加载减少初始加载时间
- 合理使用组件缓存
- 优化图片资源，使用适当格式
- 使用CDN加速静态资源
- 避免不必要的重新渲染

## 部署发布流程

### 1. 预发布检查
- [ ] 代码审查通过
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 安全扫描通过
- [ ] 文档更新完成

### 2. 发布步骤
```bash
# 1. 构建后端
cd api
mvn clean package -P prod

# 2. 构建前端
cd web
npm run build

# 3. 构建Docker镜像
docker build -t teaching-app:v1.0.0 .

# 4. 部署到生产环境
docker-compose -f docker-compose.prod.yml up -d

# 5. 健康检查
curl http://localhost:8081/api/actuator/health

# 6. 验证功能
# 执行冒烟测试
```

### 3. 回滚方案
```bash
# 如果发现问题，立即回滚
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d --scale teaching-app=0
docker run -d --name teaching-app-rollback teaching-app:v0.9.0
```

## 监控和维护

### 1. 日常监控
- 应用性能监控（APM）
- 数据库性能监控
- 服务器资源监控
- 错误日志监控
- 用户行为监控

### 2. 定期维护
- 数据库备份和恢复测试
- 日志文件清理
- 缓存清理和优化
- 安全补丁更新
- 依赖库版本更新

## 文档维护

### 1. 必需文档
- API接口文档（Swagger）
- 数据库设计文档
- 部署运维文档
- 用户使用手册
- 开发者指南

### 2. 文档更新时机
- 新功能开发完成时
- API接口变更时
- 数据库结构变更时
- 部署方式变更时
- 重要bug修复时

## 应急响应

### 1. 故障等级
- **P0**: 系统完全不可用
- **P1**: 核心功能不可用
- **P2**: 部分功能异常
- **P3**: 性能问题或小功能异常

### 2. 应急处理流程
1. 故障发现和报告
2. 故障等级评估
3. 应急响应团队组建
4. 故障定位和分析
5. 临时解决方案实施
6. 根本原因分析
7. 永久解决方案实施
8. 故障复盘和改进
